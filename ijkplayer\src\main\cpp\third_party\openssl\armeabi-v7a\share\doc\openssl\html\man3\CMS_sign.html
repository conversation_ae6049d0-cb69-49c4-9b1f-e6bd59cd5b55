<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CMS_sign</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>CMS_sign - create a CMS SignedData structure</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/cms.h&gt;

CMS_ContentInfo *CMS_sign(X509 *signcert, EVP_PKEY *pkey, STACK_OF(X509) *certs,
                          BIO *data, unsigned int flags);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>CMS_sign() creates and returns a CMS SignedData structure. <b>signcert</b> is the certificate to sign with, <b>pkey</b> is the corresponding private key. <b>certs</b> is an optional additional set of certificates to include in the CMS structure (for example any intermediate CAs in the chain). Any or all of these parameters can be <b>NULL</b>, see <b>NOTES</b> below.</p>

<p>The data to be signed is read from BIO <b>data</b>.</p>

<p><b>flags</b> is an optional set of flags.</p>

<h1 id="NOTES">NOTES</h1>

<p>Any of the following flags (ored together) can be passed in the <b>flags</b> parameter.</p>

<p>Many S/MIME clients expect the signed content to include valid MIME headers. If the <b>CMS_TEXT</b> flag is set MIME headers for type <b>text/plain</b> are prepended to the data.</p>

<p>If <b>CMS_NOCERTS</b> is set the signer&#39;s certificate will not be included in the CMS_ContentInfo structure, the signer&#39;s certificate must still be supplied in the <b>signcert</b> parameter though. This can reduce the size of the signature if the signers certificate can be obtained by other means: for example a previously signed message.</p>

<p>The data being signed is included in the CMS_ContentInfo structure, unless <b>CMS_DETACHED</b> is set in which case it is omitted. This is used for CMS_ContentInfo detached signatures which are used in S/MIME plaintext signed messages for example.</p>

<p>Normally the supplied content is translated into MIME canonical format (as required by the S/MIME specifications) if <b>CMS_BINARY</b> is set no translation occurs. This option should be used if the supplied data is in binary format otherwise the translation will corrupt it.</p>

<p>The SignedData structure includes several CMS signedAttributes including the signing time, the CMS content type and the supported list of ciphers in an SMIMECapabilities attribute. If <b>CMS_NOATTR</b> is set then no signedAttributes will be used. If <b>CMS_NOSMIMECAP</b> is set then just the SMIMECapabilities are omitted.</p>

<p>If present the SMIMECapabilities attribute indicates support for the following algorithms in preference order: 256 bit AES, Gost R3411-94, Gost 28147-89, 192 bit AES, 128 bit AES, triple DES, 128 bit RC2, 64 bit RC2, DES and 40 bit RC2. If any of these algorithms is not available then it will not be included: for example the GOST algorithms will not be included if the GOST ENGINE is not loaded.</p>

<p>OpenSSL will by default identify signing certificates using issuer name and serial number. If <b>CMS_USE_KEYID</b> is set it will use the subject key identifier value instead. An error occurs if the signing certificate does not have a subject key identifier extension.</p>

<p>If the flags <b>CMS_STREAM</b> is set then the returned <b>CMS_ContentInfo</b> structure is just initialized ready to perform the signing operation. The signing is however <b>not</b> performed and the data to be signed is not read from the <b>data</b> parameter. Signing is deferred until after the data has been written. In this way data can be signed in a single pass.</p>

<p>If the <b>CMS_PARTIAL</b> flag is set a partial <b>CMS_ContentInfo</b> structure is output to which additional signers and capabilities can be added before finalization.</p>

<p>If the flag <b>CMS_STREAM</b> is set the returned <b>CMS_ContentInfo</b> structure is <b>not</b> complete and outputting its contents via a function that does not properly finalize the <b>CMS_ContentInfo</b> structure will give unpredictable results.</p>

<p>Several functions including SMIME_write_CMS(), i2d_CMS_bio_stream(), PEM_write_bio_CMS_stream() finalize the structure. Alternatively finalization can be performed by obtaining the streaming ASN1 <b>BIO</b> directly using BIO_new_CMS().</p>

<p>If a signer is specified it will use the default digest for the signing algorithm. This is <b>SHA1</b> for both RSA and DSA keys.</p>

<p>If <b>signcert</b> and <b>pkey</b> are NULL then a certificates only CMS structure is output.</p>

<p>The function CMS_sign() is a basic CMS signing function whose output will be suitable for many purposes. For finer control of the output format the <b>certs</b>, <b>signcert</b> and <b>pkey</b> parameters can all be <b>NULL</b> and the <b>CMS_PARTIAL</b> flag set. Then one or more signers can be added using the function CMS_add1_signer(), non default digests can be used and custom attributes added. CMS_final() must then be called to finalize the structure if streaming is not enabled.</p>

<h1 id="BUGS">BUGS</h1>

<p>Some attributes such as counter signatures are not supported.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>CMS_sign() returns either a valid CMS_ContentInfo structure or NULL if an error occurred. The error can be obtained from ERR_get_error(3).</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/CMS_verify.html">CMS_verify(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The <b>CMS_STREAM</b> flag is only supported for detached data in OpenSSL 0.9.8, it is supported for embedded data in OpenSSL 1.0.0 and later.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2008-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


