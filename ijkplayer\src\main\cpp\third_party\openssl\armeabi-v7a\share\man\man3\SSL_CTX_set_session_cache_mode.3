.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_SESSION_CACHE_MODE 3"
.TH SSL_CTX_SET_SESSION_CACHE_MODE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_session_cache_mode, SSL_CTX_get_session_cache_mode \- enable/disable session caching
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_set_session_cache_mode(SSL_CTX ctx, long mode);
\& long SSL_CTX_get_session_cache_mode(SSL_CTX ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_session_cache_mode()\fR enables/disables session caching
by setting the operational mode for \fBctx\fR to <mode>.
.PP
\&\fBSSL_CTX_get_session_cache_mode()\fR returns the currently used cache mode.
.SH NOTES
.IX Header "NOTES"
The OpenSSL library can store/retrieve SSL/TLS sessions for later reuse.
The sessions can be held in memory for each \fBctx\fR, if more than one
SSL_CTX object is being maintained, the sessions are unique for each SSL_CTX
object.
.PP
In order to reuse a session, a client must send the session's id to the
server. It can only send exactly one id.  The server then either
agrees to reuse the session or it starts a full handshake (to create a new
session).
.PP
A server will look up the session in its internal session storage. If the
session is not found in internal storage or lookups for the internal storage
have been deactivated (SSL_SESS_CACHE_NO_INTERNAL_LOOKUP), the server will try
the external storage if available.
.PP
Since a client may try to reuse a session intended for use in a different
context, the session id context must be set by the server (see
\&\fBSSL_CTX_set_session_id_context\fR\|(3)).
.PP
The following session cache modes and modifiers are available:
.IP SSL_SESS_CACHE_OFF 4
.IX Item "SSL_SESS_CACHE_OFF"
No session caching for client or server takes place.
.IP SSL_SESS_CACHE_CLIENT 4
.IX Item "SSL_SESS_CACHE_CLIENT"
Client sessions are added to the session cache. As there is no reliable way
for the OpenSSL library to know whether a session should be reused or which
session to choose (due to the abstract BIO layer the SSL engine does not
have details about the connection), the application must select the session
to be reused by using the \fBSSL_set_session\fR\|(3)
function. This option is not activated by default.
.IP SSL_SESS_CACHE_SERVER 4
.IX Item "SSL_SESS_CACHE_SERVER"
Server sessions are added to the session cache. When a client proposes a
session to be reused, the server looks for the corresponding session in (first)
the internal session cache (unless SSL_SESS_CACHE_NO_INTERNAL_LOOKUP is set),
then (second) in the external cache if available. If the session is found, the
server will try to reuse the session.  This is the default.
.IP SSL_SESS_CACHE_BOTH 4
.IX Item "SSL_SESS_CACHE_BOTH"
Enable both SSL_SESS_CACHE_CLIENT and SSL_SESS_CACHE_SERVER at the same time.
.IP SSL_SESS_CACHE_NO_AUTO_CLEAR 4
.IX Item "SSL_SESS_CACHE_NO_AUTO_CLEAR"
Normally the session cache is checked for expired sessions every
255 connections using the
\&\fBSSL_CTX_flush_sessions\fR\|(3) function. Since
this may lead to a delay which cannot be controlled, the automatic
flushing may be disabled and
\&\fBSSL_CTX_flush_sessions\fR\|(3) can be called
explicitly by the application.
.IP SSL_SESS_CACHE_NO_INTERNAL_LOOKUP 4
.IX Item "SSL_SESS_CACHE_NO_INTERNAL_LOOKUP"
By setting this flag, session-resume operations in an SSL/TLS server will not
automatically look up sessions in the internal cache, even if sessions are
automatically stored there. If external session caching callbacks are in use,
this flag guarantees that all lookups are directed to the external cache.
As automatic lookup only applies for SSL/TLS servers, the flag has no effect on
clients.
.IP SSL_SESS_CACHE_NO_INTERNAL_STORE 4
.IX Item "SSL_SESS_CACHE_NO_INTERNAL_STORE"
Depending on the presence of SSL_SESS_CACHE_CLIENT and/or SSL_SESS_CACHE_SERVER,
sessions negotiated in an SSL/TLS handshake may be cached for possible reuse.
Normally a new session is added to the internal cache as well as any external
session caching (callback) that is configured for the SSL_CTX. This flag will
prevent sessions being stored in the internal cache (though the application can
add them manually using \fBSSL_CTX_add_session\fR\|(3)). Note:
in any SSL/TLS servers where external caching is configured, any successful
session lookups in the external cache (i.e. for session-resume requests) would
normally be copied into the local cache before processing continues \- this flag
prevents these additions to the internal cache as well.
.IP SSL_SESS_CACHE_NO_INTERNAL 4
.IX Item "SSL_SESS_CACHE_NO_INTERNAL"
Enable both SSL_SESS_CACHE_NO_INTERNAL_LOOKUP and
SSL_SESS_CACHE_NO_INTERNAL_STORE at the same time.
.PP
The default mode is SSL_SESS_CACHE_SERVER.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_session_cache_mode()\fR returns the previously set cache mode.
.PP
\&\fBSSL_CTX_get_session_cache_mode()\fR returns the currently set cache mode.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_set_session\fR\|(3),
\&\fBSSL_session_reused\fR\|(3),
\&\fBSSL_CTX_add_session\fR\|(3),
\&\fBSSL_CTX_sess_number\fR\|(3),
\&\fBSSL_CTX_sess_set_cache_size\fR\|(3),
\&\fBSSL_CTX_sess_set_get_cb\fR\|(3),
\&\fBSSL_CTX_set_session_id_context\fR\|(3),
\&\fBSSL_CTX_set_timeout\fR\|(3),
\&\fBSSL_CTX_flush_sessions\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
