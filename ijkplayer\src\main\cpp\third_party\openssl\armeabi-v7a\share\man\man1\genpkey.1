.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "GENPKEY 1"
.TH GENPKEY 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-genpkey,
genpkey \- generate a private key
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBgenpkey\fR
[\fB\-help\fR]
[\fB\-out filename\fR]
[\fB\-outform PEM|DER\fR]
[\fB\-pass arg\fR]
[\fB\-\fR\f(BIcipher\fR]
[\fB\-engine id\fR]
[\fB\-paramfile file\fR]
[\fB\-algorithm alg\fR]
[\fB\-pkeyopt opt:value\fR]
[\fB\-genparam\fR]
[\fB\-text\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBgenpkey\fR command generates a private key.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
Output the key to the specified file. If this argument is not specified then
standard output is used.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format DER or PEM. The default format is PEM.
.IP "\fB\-pass arg\fR" 4
.IX Item "-pass arg"
The output file password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP \fB\-\fR\f(BIcipher\fR 4
.IX Item "-cipher"
This option encrypts the private key with the supplied cipher. Any algorithm
name accepted by \fBEVP_get_cipherbyname()\fR is acceptable such as \fBdes3\fR.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBgenpkey\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms. If used this option should precede all other
options.
.IP "\fB\-algorithm alg\fR" 4
.IX Item "-algorithm alg"
Public key algorithm to use such as RSA, DSA or DH. If used this option must
precede any \fB\-pkeyopt\fR options. The options \fB\-paramfile\fR and \fB\-algorithm\fR
are mutually exclusive. Engines may add algorithms in addition to the standard
built-in ones.
.Sp
Valid built-in algorithm names for private key generation are RSA, RSA-PSS, EC,
X25519, X448, ED25519 and ED448.
.Sp
Valid built-in algorithm names for parameter generation (see the \fB\-genparam\fR
option) are DH, DSA and EC.
.Sp
Note that the algorithm name X9.42 DH may be used as a synonym for the DH
algorithm. These are identical and do not indicate the type of parameters that
will be generated. Use the \fBdh_paramgen_type\fR option to indicate whether PKCS#3
or X9.42 DH parameters are required. See "DH Parameter Generation Options"
below for more details.
.IP "\fB\-pkeyopt opt:value\fR" 4
.IX Item "-pkeyopt opt:value"
Set the public key algorithm option \fBopt\fR to \fBvalue\fR. The precise set of
options supported depends on the public key algorithm used and its
implementation. See "KEY GENERATION OPTIONS" and
"PARAMETER GENERATION OPTIONS" below for more details.
.IP \fB\-genparam\fR 4
.IX Item "-genparam"
Generate a set of parameters instead of a private key. If used this option must
precede any \fB\-algorithm\fR, \fB\-paramfile\fR or \fB\-pkeyopt\fR options.
.IP "\fB\-paramfile filename\fR" 4
.IX Item "-paramfile filename"
Some public key algorithms generate a private key based on a set of parameters.
They can be supplied using this option. If this option is used the public key
algorithm used is determined by the parameters. If used this option must
precede any \fB\-pkeyopt\fR options. The options \fB\-paramfile\fR and \fB\-algorithm\fR
are mutually exclusive.
.IP \fB\-text\fR 4
.IX Item "-text"
Print an (unencrypted) text representation of private and public keys and
parameters along with the PEM or DER structure.
.SH "KEY GENERATION OPTIONS"
.IX Header "KEY GENERATION OPTIONS"
The options supported by each algorithm and indeed each implementation of an
algorithm can vary. The options for the OpenSSL implementations are detailed
below. There are no key generation options defined for the X25519, X448, ED25519
or ED448 algorithms.
.SS "RSA Key Generation Options"
.IX Subsection "RSA Key Generation Options"
.IP \fBrsa_keygen_bits:numbits\fR 4
.IX Item "rsa_keygen_bits:numbits"
The number of bits in the generated key. If not specified 2048 is used.
.IP \fBrsa_keygen_primes:numprimes\fR 4
.IX Item "rsa_keygen_primes:numprimes"
The number of primes in the generated key. If not specified 2 is used.
.IP \fBrsa_keygen_pubexp:value\fR 4
.IX Item "rsa_keygen_pubexp:value"
The RSA public exponent value. This can be a large decimal or
hexadecimal value if preceded by \fB0x\fR. Default value is 65537.
.SS "RSA-PSS Key Generation Options"
.IX Subsection "RSA-PSS Key Generation Options"
Note: by default an \fBRSA-PSS\fR key has no parameter restrictions.
.IP "\fBrsa_keygen_bits:numbits\fR, \fBrsa_keygen_primes:numprimes\fR,  \fBrsa_keygen_pubexp:value\fR" 4
.IX Item "rsa_keygen_bits:numbits, rsa_keygen_primes:numprimes, rsa_keygen_pubexp:value"
These options have the same meaning as the \fBRSA\fR algorithm.
.IP \fBrsa_pss_keygen_md:digest\fR 4
.IX Item "rsa_pss_keygen_md:digest"
If set the key is restricted and can only use \fBdigest\fR for signing.
.IP \fBrsa_pss_keygen_mgf1_md:digest\fR 4
.IX Item "rsa_pss_keygen_mgf1_md:digest"
If set the key is restricted and can only use \fBdigest\fR as it's MGF1
parameter.
.IP \fBrsa_pss_keygen_saltlen:len\fR 4
.IX Item "rsa_pss_keygen_saltlen:len"
If set the key is restricted and \fBlen\fR specifies the minimum salt length.
.SS "EC Key Generation Options"
.IX Subsection "EC Key Generation Options"
The EC key generation options can also be used for parameter generation.
.IP \fBec_paramgen_curve:curve\fR 4
.IX Item "ec_paramgen_curve:curve"
The EC curve to use. OpenSSL supports NIST curve names such as "P\-256".
.IP \fBec_param_enc:encoding\fR 4
.IX Item "ec_param_enc:encoding"
The encoding to use for parameters. The "encoding" parameter must be either
"named_curve" or "explicit". The default value is "named_curve".
.SH "PARAMETER GENERATION OPTIONS"
.IX Header "PARAMETER GENERATION OPTIONS"
The options supported by each algorithm and indeed each implementation of an
algorithm can vary. The options for the OpenSSL implementations are detailed
below.
.SS "DSA Parameter Generation Options"
.IX Subsection "DSA Parameter Generation Options"
.IP \fBdsa_paramgen_bits:numbits\fR 4
.IX Item "dsa_paramgen_bits:numbits"
The number of bits in the generated prime. If not specified 2048 is used.
.IP \fBdsa_paramgen_q_bits:numbits\fR 4
.IX Item "dsa_paramgen_q_bits:numbits"
The number of bits in the q parameter. Must be one of 160, 224 or 256. If not
specified 224 is used.
.IP \fBdsa_paramgen_md:digest\fR 4
.IX Item "dsa_paramgen_md:digest"
The digest to use during parameter generation. Must be one of \fBsha1\fR, \fBsha224\fR
or \fBsha256\fR. If set, then the number of bits in \fBq\fR will match the output size
of the specified digest and the \fBdsa_paramgen_q_bits\fR parameter will be
ignored. If not set, then a digest will be used that gives an output matching
the number of bits in \fBq\fR, i.e. \fBsha1\fR if q length is 160, \fBsha224\fR if it 224
or \fBsha256\fR if it is 256.
.SS "DH Parameter Generation Options"
.IX Subsection "DH Parameter Generation Options"
.IP \fBdh_paramgen_prime_len:numbits\fR 4
.IX Item "dh_paramgen_prime_len:numbits"
The number of bits in the prime parameter \fBp\fR. The default is 2048.
.IP \fBdh_paramgen_subprime_len:numbits\fR 4
.IX Item "dh_paramgen_subprime_len:numbits"
The number of bits in the sub prime parameter \fBq\fR. The default is 256 if the
prime is at least 2048 bits long or 160 otherwise. Only relevant if used in
conjunction with the \fBdh_paramgen_type\fR option to generate X9.42 DH parameters.
.IP \fBdh_paramgen_generator:value\fR 4
.IX Item "dh_paramgen_generator:value"
The value to use for the generator \fBg\fR. The default is 2.
.IP \fBdh_paramgen_type:value\fR 4
.IX Item "dh_paramgen_type:value"
The type of DH parameters to generate. Use 0 for PKCS#3 DH and 1 for X9.42 DH.
The default is 0.
.IP \fBdh_rfc5114:num\fR 4
.IX Item "dh_rfc5114:num"
If this option is set, then the appropriate RFC5114 parameters are used
instead of generating new parameters. The value \fBnum\fR can take the
values 1, 2 or 3 corresponding to RFC5114 DH parameters consisting of
1024 bit group with 160 bit subgroup, 2048 bit group with 224 bit subgroup
and 2048 bit group with 256 bit subgroup as mentioned in RFC5114 sections
2.1, 2.2 and 2.3 respectively. If present this overrides all other DH parameter
options.
.SS "EC Parameter Generation Options"
.IX Subsection "EC Parameter Generation Options"
The EC parameter generation options are the same as for key generation. See
"EC Key Generation Options" above.
.SH NOTES
.IX Header "NOTES"
The use of the genpkey program is encouraged over the algorithm specific
utilities because additional algorithm options and ENGINE provided algorithms
can be used.
.SH EXAMPLES
.IX Header "EXAMPLES"
Generate an RSA private key using default parameters:
.PP
.Vb 1
\& openssl genpkey \-algorithm RSA \-out key.pem
.Ve
.PP
Encrypt output private key using 128 bit AES and the passphrase "hello":
.PP
.Vb 1
\& openssl genpkey \-algorithm RSA \-out key.pem \-aes\-128\-cbc \-pass pass:hello
.Ve
.PP
Generate a 2048 bit RSA key using 3 as the public exponent:
.PP
.Vb 2
\& openssl genpkey \-algorithm RSA \-out key.pem \e
\&     \-pkeyopt rsa_keygen_bits:2048 \-pkeyopt rsa_keygen_pubexp:3
.Ve
.PP
Generate 2048 bit DSA parameters:
.PP
.Vb 2
\& openssl genpkey \-genparam \-algorithm DSA \-out dsap.pem \e
\&     \-pkeyopt dsa_paramgen_bits:2048
.Ve
.PP
Generate DSA key from parameters:
.PP
.Vb 1
\& openssl genpkey \-paramfile dsap.pem \-out dsakey.pem
.Ve
.PP
Generate 2048 bit DH parameters:
.PP
.Vb 2
\& openssl genpkey \-genparam \-algorithm DH \-out dhp.pem \e
\&     \-pkeyopt dh_paramgen_prime_len:2048
.Ve
.PP
Generate 2048 bit X9.42 DH parameters:
.PP
.Vb 3
\& openssl genpkey \-genparam \-algorithm DH \-out dhpx.pem \e
\&     \-pkeyopt dh_paramgen_prime_len:2048 \e
\&     \-pkeyopt dh_paramgen_type:1
.Ve
.PP
Output RFC5114 2048 bit DH parameters with 224 bit subgroup:
.PP
.Vb 1
\& openssl genpkey \-genparam \-algorithm DH \-out dhp.pem \-pkeyopt dh_rfc5114:2
.Ve
.PP
Generate DH key from parameters:
.PP
.Vb 1
\& openssl genpkey \-paramfile dhp.pem \-out dhkey.pem
.Ve
.PP
Generate EC parameters:
.PP
.Vb 3
\& openssl genpkey \-genparam \-algorithm EC \-out ecp.pem \e
\&        \-pkeyopt ec_paramgen_curve:secp384r1 \e
\&        \-pkeyopt ec_param_enc:named_curve
.Ve
.PP
Generate EC key from parameters:
.PP
.Vb 1
\& openssl genpkey \-paramfile ecp.pem \-out eckey.pem
.Ve
.PP
Generate EC key directly:
.PP
.Vb 3
\& openssl genpkey \-algorithm EC \-out eckey.pem \e
\&        \-pkeyopt ec_paramgen_curve:P\-384 \e
\&        \-pkeyopt ec_param_enc:named_curve
.Ve
.PP
Generate an X25519 private key:
.PP
.Vb 1
\& openssl genpkey \-algorithm X25519 \-out xkey.pem
.Ve
.PP
Generate an ED448 private key:
.PP
.Vb 1
\& openssl genpkey \-algorithm ED448 \-out xkey.pem
.Ve
.SH HISTORY
.IX Header "HISTORY"
The ability to use NIST curve names, and to generate an EC key directly,
were added in OpenSSL 1.0.2.
The ability to generate X25519 keys was added in OpenSSL 1.1.0.
The ability to generate X448, ED25519 and ED448 keys was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
