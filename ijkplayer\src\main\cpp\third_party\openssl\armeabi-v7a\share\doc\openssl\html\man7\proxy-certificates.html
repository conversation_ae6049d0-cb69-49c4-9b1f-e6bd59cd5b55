<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>proxy-certificates</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Enabling-proxy-certificate-verification">Enabling proxy certificate verification</a></li>
      <li><a href="#Creating-proxy-certificates">Creating proxy certificates</a></li>
      <li><a href="#Using-proxy-certs-in-applications">Using proxy certs in applications</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>proxy-certificates - Proxy certificates in OpenSSL</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Proxy certificates are defined in RFC 3820. They are used to extend rights to some other entity (a computer process, typically, or sometimes to the user itself). This allows the entity to perform operations on behalf of the owner of the EE (End Entity) certificate.</p>

<p>The requirements for a valid proxy certificate are:</p>

<ul>

<li><p>They are issued by an End Entity, either a normal EE certificate, or another proxy certificate.</p>

</li>
<li><p>They must not have the <b>subjectAltName</b> or <b>issuerAltName</b> extensions.</p>

</li>
<li><p>They must have the <b>proxyCertInfo</b> extension.</p>

</li>
<li><p>They must have the subject of their issuer, with one <b>commonName</b> added.</p>

</li>
</ul>

<h2 id="Enabling-proxy-certificate-verification">Enabling proxy certificate verification</h2>

<p>OpenSSL expects applications that want to use proxy certificates to be specially aware of them, and make that explicit. This is done by setting an X509 verification flag:</p>

<pre><code>X509_STORE_CTX_set_flags(ctx, X509_V_FLAG_ALLOW_PROXY_CERTS);</code></pre>

<p>or</p>

<pre><code>X509_VERIFY_PARAM_set_flags(param, X509_V_FLAG_ALLOW_PROXY_CERTS);</code></pre>

<p>See <a href="#NOTES">&quot;NOTES&quot;</a> for a discussion on this requirement.</p>

<h2 id="Creating-proxy-certificates">Creating proxy certificates</h2>

<p>Creating proxy certificates can be done using the <a href="../man1/openssl-x509.html">openssl-x509(1)</a> command, with some extra extensions:</p>

<pre><code>[ v3_proxy ]
# A proxy certificate MUST NEVER be a CA certificate.
basicConstraints=CA:FALSE

# Usual authority key ID
authorityKeyIdentifier=keyid,issuer:always

# The extension which marks this certificate as a proxy
proxyCertInfo=critical,language:id-ppl-anyLanguage,pathlen:1,policy:text:AB</code></pre>

<p>It&#39;s also possible to specify the proxy extension in a separate section:</p>

<pre><code>proxyCertInfo=critical,@proxy_ext

[ proxy_ext ]
language=id-ppl-anyLanguage
pathlen=0
policy=text:BC</code></pre>

<p>The policy value has a specific syntax, <i>syntag</i>:<i>string</i>, where the <i>syntag</i> determines what will be done with the string. The following <i>syntag</i>s are recognised:</p>

<dl>

<dt id="text"><b>text</b></dt>
<dd>

<p>indicates that the string is a byte sequence, without any encoding:</p>

<pre><code>policy=text:r&auml;ksm&ouml;rg&aring;s</code></pre>

</dd>
<dt id="hex"><b>hex</b></dt>
<dd>

<p>indicates the string is encoded hexadecimal encoded binary data, with colons between each byte (every second hex digit):</p>

<pre><code>policy=hex:72:E4:6B:73:6D:F6:72:67:E5:73</code></pre>

</dd>
<dt id="file"><b>file</b></dt>
<dd>

<p>indicates that the text of the policy should be taken from a file. The string is then a filename. This is useful for policies that are large (more than a few lines, e.g. XML documents).</p>

</dd>
</dl>

<p><i>NOTE: The proxy policy value is what determines the rights granted to the process during the proxy certificate. It&#39;s up to the application to interpret and combine these policies.</i></p>

<p>With a proxy extension, creating a proxy certificate is a matter of two commands:</p>

<pre><code>openssl req -new -config proxy.cnf \
    -out proxy.req -keyout proxy.key \
    -subj &quot;/DC=org/DC=openssl/DC=users/CN=proxy 1&quot;

openssl x509 -req -CAcreateserial -in proxy.req -out proxy.crt \
    -CA user.crt -CAkey user.key -days 7 \
    -extfile proxy.cnf -extensions v3_proxy1</code></pre>

<p>You can also create a proxy certificate using another proxy certificate as issuer (note: using a different configuration section for the proxy extensions):</p>

<pre><code>openssl req -new -config proxy.cnf \
    -out proxy2.req -keyout proxy2.key \
    -subj &quot;/DC=org/DC=openssl/DC=users/CN=proxy 1/CN=proxy 2&quot;

openssl x509 -req -CAcreateserial -in proxy2.req -out proxy2.crt \
    -CA proxy.crt -CAkey proxy.key -days 7 \
    -extfile proxy.cnf -extensions v3_proxy2</code></pre>

<h2 id="Using-proxy-certs-in-applications">Using proxy certs in applications</h2>

<p>To interpret proxy policies, the application would normally start with some default rights (perhaps none at all), then compute the resulting rights by checking the rights against the chain of proxy certificates, user certificate and CA certificates.</p>

<p>The complicated part is figuring out how to pass data between your application and the certificate validation procedure.</p>

<p>The following ingredients are needed for such processing:</p>

<ul>

<li><p>a callback function that will be called for every certificate being validated. The callback is called several times for each certificate, so you must be careful to do the proxy policy interpretation at the right time. You also need to fill in the defaults when the EE certificate is checked.</p>

</li>
<li><p>a data structure that is shared between your application code and the callback.</p>

</li>
<li><p>a wrapper function that sets it all up.</p>

</li>
<li><p>an ex_data index function that creates an index into the generic ex_data store that is attached to an X509 validation context.</p>

</li>
</ul>

<p>The following skeleton code can be used as a starting point:</p>

<pre><code>#include &lt;string.h&gt;
#include &lt;netdb.h&gt;
#include &lt;openssl/x509.h&gt;
#include &lt;openssl/x509v3.h&gt;

#define total_rights 25

/*
 * In this example, I will use a view of granted rights as a bit
 * array, one bit for each possible right.
 */
typedef struct your_rights {
    unsigned char rights[(total_rights + 7) / 8];
} YOUR_RIGHTS;

/*
 * The following procedure will create an index for the ex_data
 * store in the X509 validation context the first time it&#39;s
 * called.  Subsequent calls will return the same index.
 */
static int get_proxy_auth_ex_data_idx(X509_STORE_CTX *ctx)
{
    static volatile int idx = -1;

    if (idx &lt; 0) {
        X509_STORE_lock(X509_STORE_CTX_get0_store(ctx));
        if (idx &lt; 0) {
            idx = X509_STORE_CTX_get_ex_new_index(0,
                                                  &quot;for verify callback&quot;,
                                                  NULL,NULL,NULL);
        }
        X509_STORE_unlock(X509_STORE_CTX_get0_store(ctx));
    }
    return idx;
}

/* Callback to be given to the X509 validation procedure.  */
static int verify_callback(int ok, X509_STORE_CTX *ctx)
{
    if (ok == 1) {
        /*
         * It&#39;s REALLY important you keep the proxy policy check
         * within this section.  It&#39;s important to know that when
         * ok is 1, the certificates are checked from top to
         * bottom.  You get the CA root first, followed by the
         * possible chain of intermediate CAs, followed by the EE
         * certificate, followed by the possible proxy
         * certificates. 
         */
        X509 *xs = X509_STORE_CTX_get_current_cert(ctx);

        if (X509_get_extension_flags(xs) &amp; EXFLAG_PROXY) {
            YOUR_RIGHTS *rights =
                (YOUR_RIGHTS *)X509_STORE_CTX_get_ex_data(ctx,
                    get_proxy_auth_ex_data_idx(ctx));
            PROXY_CERT_INFO_EXTENSION *pci =
                X509_get_ext_d2i(xs, NID_proxyCertInfo, NULL, NULL);

            switch (OBJ_obj2nid(pci-&gt;proxyPolicy-&gt;policyLanguage)) {
            case NID_Independent:
                /*
                 * Do whatever you need to grant explicit rights
                 * to this particular proxy certificate, usually
                 * by pulling them from some database.  If there
                 * are none to be found, clear all rights (making
                 * this and any subsequent proxy certificate void
                 * of any rights). 
                 */
                memset(rights-&gt;rights, 0, sizeof(rights-&gt;rights));
                break;
            case NID_id_ppl_inheritAll:
                /*
                 * This is basically a NOP, we simply let the
                 * current rights stand as they are.
                 */
                break;
            default:
                /*
                 * This is usually the most complex section of
                 * code.  You really do whatever you want as long
                 * as you follow RFC 3820.  In the example we use
                 * here, the simplest thing to do is to build
                 * another, temporary bit array and fill it with
                 * the rights granted by the current proxy
                 * certificate, then use it as a mask on the
                 * accumulated rights bit array, and voil&agrave;, you
                 * now have a new accumulated rights bit array.
                 */
                {
                    int i;
                    YOUR_RIGHTS tmp_rights;
                    memset(tmp_rights.rights, 0,
                           sizeof(tmp_rights.rights));

                    /*
                     * process_rights() is supposed to be a
                     * procedure that takes a string and its
                     * length, interprets it and sets the bits
                     * in the YOUR_RIGHTS pointed at by the
                     * third argument.
                     */
                    process_rights((char *) pci-&gt;proxyPolicy-&gt;policy-&gt;data,
                                   pci-&gt;proxyPolicy-&gt;policy-&gt;length,
                                   &amp;tmp_rights);

                    for(i = 0; i &lt; total_rights / 8; i++)
                        rights-&gt;rights[i] &amp;= tmp_rights.rights[i];
                }
                break;
            }
            PROXY_CERT_INFO_EXTENSION_free(pci);
        } else if (!(X509_get_extension_flags(xs) &amp; EXFLAG_CA)) {
            /* We have an EE certificate, let&#39;s use it to set default! */
            YOUR_RIGHTS *rights =
                (YOUR_RIGHTS *)X509_STORE_CTX_get_ex_data(ctx,
                    get_proxy_auth_ex_data_idx(ctx));

            /*
             * The following procedure finds out what rights the
             * owner of the current certificate has, and sets them
             * in the YOUR_RIGHTS structure pointed at by the
             * second argument.
             */
            set_default_rights(xs, rights);
        }
    }
    return ok;
}

static int my_X509_verify_cert(X509_STORE_CTX *ctx,
                               YOUR_RIGHTS *needed_rights)
{
    int ok;
    int (*save_verify_cb)(int ok,X509_STORE_CTX *ctx) =
        X509_STORE_CTX_get_verify_cb(ctx);
    YOUR_RIGHTS rights;

    X509_STORE_CTX_set_verify_cb(ctx, verify_callback);
    X509_STORE_CTX_set_ex_data(ctx, get_proxy_auth_ex_data_idx(ctx),
                               &amp;rights);
    X509_STORE_CTX_set_flags(ctx, X509_V_FLAG_ALLOW_PROXY_CERTS);
    ok = X509_verify_cert(ctx);

    if (ok == 1) {
        ok = check_needed_rights(rights, needed_rights);
    }

    X509_STORE_CTX_set_verify_cb(ctx, save_verify_cb);

    return ok;
}</code></pre>

<p>If you use SSL or TLS, you can easily set up a callback to have the certificates checked properly, using the code above:</p>

<pre><code>SSL_CTX_set_cert_verify_callback(s_ctx, my_X509_verify_cert,
                                 &amp;needed_rights);</code></pre>

<h1 id="NOTES">NOTES</h1>

<p>To this date, it seems that proxy certificates have only been used in environments that are aware of them, and no one seems to have investigated how they can be used or misused outside of such an environment.</p>

<p>For that reason, OpenSSL requires that applications aware of proxy certificates must also make that explicit.</p>

<p><b>subjectAltName</b> and <b>issuerAltName</b> are forbidden in proxy certificates, and this is enforced in OpenSSL. The subject must be the same as the issuer, with one commonName added on.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/X509_STORE_CTX_set_flags.html">X509_STORE_CTX_set_flags(3)</a>, <a href="../man3/X509_STORE_CTX_set_verify_cb.html">X509_STORE_CTX_set_verify_cb(3)</a>, <a href="../man3/X509_VERIFY_PARAM_set_flags.html">X509_VERIFY_PARAM_set_flags(3)</a>, <a href="../man3/SSL_CTX_set_cert_verify_callback.html">SSL_CTX_set_cert_verify_callback(3)</a>, <a href="../man1/openssl-req.html">openssl-req(1)</a>, <a href="../man1/openssl-x509.html">openssl-x509(1)</a>, <a href="https://tools.ietf.org/html/rfc3820">RFC 3820</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


