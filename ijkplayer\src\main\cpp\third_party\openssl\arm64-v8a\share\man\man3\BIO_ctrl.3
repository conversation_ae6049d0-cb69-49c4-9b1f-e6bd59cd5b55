.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_CTRL 3"
.TH BIO_CTRL 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_ctrl, BIO_callback_ctrl, BIO_ptr_ctrl, BIO_int_ctrl, BIO_reset,
BIO_seek, BIO_tell, BIO_flush, BIO_eof, BIO_set_close, BIO_get_close,
BIO_pending, BIO_wpending, BIO_ctrl_pending, BIO_ctrl_wpending,
BIO_get_info_callback, BIO_set_info_callback, BIO_info_cb
\&\- BIO control operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& typedef int BIO_info_cb(BIO *b, int state, int res);
\&
\& long BIO_ctrl(BIO *bp, int cmd, long larg, void *parg);
\& long BIO_callback_ctrl(BIO *b, int cmd, BIO_info_cb *cb);
\& void *BIO_ptr_ctrl(BIO *bp, int cmd, long larg);
\& long BIO_int_ctrl(BIO *bp, int cmd, long larg, int iarg);
\&
\& int BIO_reset(BIO *b);
\& int BIO_seek(BIO *b, int ofs);
\& int BIO_tell(BIO *b);
\& int BIO_flush(BIO *b);
\& int BIO_eof(BIO *b);
\& int BIO_set_close(BIO *b, long flag);
\& int BIO_get_close(BIO *b);
\& int BIO_pending(BIO *b);
\& int BIO_wpending(BIO *b);
\& size_t BIO_ctrl_pending(BIO *b);
\& size_t BIO_ctrl_wpending(BIO *b);
\&
\& int BIO_get_info_callback(BIO *b, BIO_info_cb **cbp);
\& int BIO_set_info_callback(BIO *b, BIO_info_cb *cb);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_ctrl()\fR, \fBBIO_callback_ctrl()\fR, \fBBIO_ptr_ctrl()\fR and \fBBIO_int_ctrl()\fR
are BIO "control" operations taking arguments of various types.
These functions are not normally called directly, various macros
are used instead. The standard macros are described below, macros
specific to a particular type of BIO are described in the specific
BIOs manual page as well as any special features of the standard
calls.
.PP
\&\fBBIO_reset()\fR typically resets a BIO to some initial state, in the case
of file related BIOs for example it rewinds the file pointer to the
start of the file.
.PP
\&\fBBIO_seek()\fR resets a file related BIO's (that is file descriptor and
FILE BIOs) file position pointer to \fBofs\fR bytes from start of file.
.PP
\&\fBBIO_tell()\fR returns the current file position of a file related BIO.
.PP
\&\fBBIO_flush()\fR normally writes out any internally buffered data, in some
cases it is used to signal EOF and that no more data will be written.
.PP
\&\fBBIO_eof()\fR returns 1 if the BIO has read EOF, the precise meaning of
"EOF" varies according to the BIO type.
.PP
\&\fBBIO_set_close()\fR sets the BIO \fBb\fR close flag to \fBflag\fR. \fBflag\fR can
take the value BIO_CLOSE or BIO_NOCLOSE. Typically BIO_CLOSE is used
in a source/sink BIO to indicate that the underlying I/O stream should
be closed when the BIO is freed.
.PP
\&\fBBIO_get_close()\fR returns the BIOs close flag.
.PP
\&\fBBIO_pending()\fR, \fBBIO_ctrl_pending()\fR, \fBBIO_wpending()\fR and \fBBIO_ctrl_wpending()\fR
return the number of pending characters in the BIOs read and write buffers.
Not all BIOs support these calls. \fBBIO_ctrl_pending()\fR and \fBBIO_ctrl_wpending()\fR
return a size_t type and are functions, \fBBIO_pending()\fR and \fBBIO_wpending()\fR are
macros which call \fBBIO_ctrl()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_reset()\fR normally returns 1 for success and 0 or \-1 for failure. File
BIOs are an exception, they return 0 for success and \-1 for failure.
.PP
\&\fBBIO_seek()\fR and \fBBIO_tell()\fR both return the current file position on success
and \-1 for failure, except file BIOs which for \fBBIO_seek()\fR always return 0
for success and \-1 for failure.
.PP
\&\fBBIO_flush()\fR returns 1 for success and 0 or \-1 for failure.
.PP
\&\fBBIO_eof()\fR returns 1 if EOF has been reached 0 otherwise.
.PP
\&\fBBIO_set_close()\fR always returns 1.
.PP
\&\fBBIO_get_close()\fR returns the close flag value: BIO_CLOSE or BIO_NOCLOSE.
.PP
\&\fBBIO_pending()\fR, \fBBIO_ctrl_pending()\fR, \fBBIO_wpending()\fR and \fBBIO_ctrl_wpending()\fR
return the amount of pending data.
.SH NOTES
.IX Header "NOTES"
\&\fBBIO_flush()\fR, because it can write data may return 0 or \-1 indicating
that the call should be retried later in a similar manner to \fBBIO_write_ex()\fR.
The \fBBIO_should_retry()\fR call should be used and appropriate action taken
is the call fails.
.PP
The return values of \fBBIO_pending()\fR and \fBBIO_wpending()\fR may not reliably
determine the amount of pending data in all cases. For example in the
case of a file BIO some data may be available in the FILE structures
internal buffers but it is not possible to determine this in a
portably way. For other types of BIO they may not be supported.
.PP
Filter BIOs if they do not internally handle a particular \fBBIO_ctrl()\fR
operation usually pass the operation to the next BIO in the chain.
This often means there is no need to locate the required BIO for
a particular operation, it can be called on a chain and it will
be automatically passed to the relevant BIO. However, this can cause
unexpected results: for example no current filter BIOs implement
\&\fBBIO_seek()\fR, but this may still succeed if the chain ends in a FILE
or file descriptor BIO.
.PP
Source/sink BIOs return an 0 if they do not recognize the \fBBIO_ctrl()\fR
operation.
.SH BUGS
.IX Header "BUGS"
Some of the return values are ambiguous and care should be taken. In
particular a return value of 0 can be returned if an operation is not
supported, if an error occurred, if EOF has not been reached and in
the case of \fBBIO_seek()\fR on a file BIO for a successful operation.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
