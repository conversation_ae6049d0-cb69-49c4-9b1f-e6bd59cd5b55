.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_DRBG_GENERATE 3"
.TH RAND_DRBG_GENERATE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_DRBG_generate,
RAND_DRBG_bytes
\&\- generate random bytes using the given drbg instance
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand_drbg.h>
\&
\& int RAND_DRBG_generate(RAND_DRBG *drbg,
\&                        unsigned char *out, size_t outlen,
\&                        int prediction_resistance,
\&                        const unsigned char *adin, size_t adinlen);
\&
\& int RAND_DRBG_bytes(RAND_DRBG *drbg,
\&                     unsigned char *out, size_t outlen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBRAND_DRBG_generate()\fR generates \fBoutlen\fR random bytes using the given
DRBG instance \fBdrbg\fR and stores them in the buffer at \fBout\fR.
.PP
Before generating the output, the DRBG instance checks whether the maximum
number of generate requests (\fIreseed interval\fR) or the maximum timespan
(\fIreseed time interval\fR) since its last seeding have been reached.
If this is the case, the DRBG reseeds automatically.
Additionally, an immediate reseeding can be requested by setting the
\&\fBprediction_resistance\fR flag to 1. See NOTES section for more details.
.PP
The caller can optionally provide additional data to be used for reseeding
by passing a pointer \fBadin\fR to a buffer of length \fBadinlen\fR.
This additional data is mixed into the internal state of the random
generator but does not contribute to the entropy count.
The additional data can be omitted by setting \fBadin\fR to NULL and
\&\fBadinlen\fR to 0;
.PP
\&\fBRAND_DRBG_bytes()\fR generates \fBoutlen\fR random bytes using the given
DRBG instance \fBdrbg\fR and stores them in the buffer at \fBout\fR.
This function is a wrapper around the \fBRAND_DRBG_generate()\fR call,
which collects some additional data from low entropy sources
(e.g., a high resolution timer) and calls
RAND_DRBG_generate(drbg, out, outlen, 0, adin, adinlen).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_DRBG_generate()\fR and \fBRAND_DRBG_bytes()\fR return 1 on success,
and 0 on failure.
.SH NOTES
.IX Header "NOTES"
The \fIreseed interval\fR and \fIreseed time interval\fR of the \fBdrbg\fR are set to
reasonable default values, which in general do not have to be adjusted.
If necessary, they can be changed using \fBRAND_DRBG_set_reseed_interval\fR\|(3)
and \fBRAND_DRBG_set_reseed_time_interval\fR\|(3), respectively.
.PP
A request for prediction resistance can only be satisfied by pulling fresh
entropy from one of the approved entropy sources listed in section 5.5.2 of
[NIST SP 800\-90C].
Since the default DRBG implementation does not have access to such an approved
entropy source, a request for prediction resistance will always fail.
In other words, prediction resistance is currently not supported yet by the DRBG.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND_bytes\fR\|(3),
\&\fBRAND_DRBG_set_reseed_interval\fR\|(3),
\&\fBRAND_DRBG_set_reseed_time_interval\fR\|(3),
\&\fBRAND_DRBG\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The RAND_DRBG functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
