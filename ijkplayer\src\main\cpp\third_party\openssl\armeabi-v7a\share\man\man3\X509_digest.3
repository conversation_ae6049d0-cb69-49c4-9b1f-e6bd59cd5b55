.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_DIGEST 3"
.TH X509_DIGEST 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_digest, X509_CRL_digest,
X509_pubkey_digest,
X509_NAME_digest,
X509_REQ_digest,
PKCS7_ISSUER_AND_SERIAL_digest
\&\- get digest of various objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_digest(const X509 *data, const EVP_MD *type, unsigned char *md,
\&                 unsigned int *len);
\&
\& int X509_CRL_digest(const X509_CRL *data, const EVP_MD *type, unsigned char *md,
\&                     unsigned int *len);
\&
\& int X509_pubkey_digest(const X509 *data, const EVP_MD *type,
\&                        unsigned char *md, unsigned int *len);
\&
\& int X509_REQ_digest(const X509_REQ *data, const EVP_MD *type,
\&                     unsigned char *md, unsigned int *len);
\&
\& int X509_NAME_digest(const X509_NAME *data, const EVP_MD *type,
\&                      unsigned char *md, unsigned int *len);
\&
\& #include <openssl/pkcs7.h>
\&
\& int PKCS7_ISSUER_AND_SERIAL_digest(PKCS7_ISSUER_AND_SERIAL *data,
\&                                    const EVP_MD *type, unsigned char *md,
\&                                    unsigned int *len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_pubkey_digest()\fR returns a digest of the DER representation of the public
key in the specified X509 \fBdata\fR object.
All other functions described here return a digest of the DER representation
of their entire \fBdata\fR objects.
.PP
The \fBtype\fR parameter specifies the digest to
be used, such as \fBEVP_sha1()\fR. The \fBmd\fR is a pointer to the buffer where the
digest will be copied and is assumed to be large enough; the constant
\&\fBEVP_MAX_MD_SIZE\fR is suggested. The \fBlen\fR parameter, if not NULL, points
to a place where the digest size will be stored.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All functions described here return 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_sha1\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
