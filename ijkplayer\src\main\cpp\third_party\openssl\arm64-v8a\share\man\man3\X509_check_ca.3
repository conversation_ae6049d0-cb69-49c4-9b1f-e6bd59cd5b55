.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_CHECK_CA 3"
.TH X509_CHECK_CA 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_check_ca \- check if given certificate is CA certificate
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509v3.h>
\&
\& int X509_check_ca(X509 *cert);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This function checks if given certificate is CA certificate (can be used
to sign other certificates).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Function return 0, if it is not CA certificate, 1 if it is proper X509v3
CA certificate with \fBbasicConstraints\fR extension CA:TRUE,
3, if it is self-signed X509 v1 certificate, 4, if it is certificate with
\&\fBkeyUsage\fR extension with bit \fBkeyCertSign\fR set, but without
\&\fBbasicConstraints\fR, and 5 if it has outdated Netscape Certificate Type
extension telling that it is CA certificate.
.PP
Actually, any nonzero value means that this certificate could have been
used to sign other certificates.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_verify_cert\fR\|(3),
\&\fBX509_check_issued\fR\|(3),
\&\fBX509_check_purpose\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
