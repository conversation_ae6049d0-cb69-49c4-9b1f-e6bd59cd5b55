.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_SET1_RSA 3"
.TH EVP_PKEY_SET1_RSA 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_set1_RSA, EVP_PKEY_set1_DSA, EVP_PKEY_set1_DH, EVP_PKEY_set1_EC_KEY,
EVP_PKEY_get1_RSA, EVP_PKEY_get1_DSA, EVP_PKEY_get1_DH, EVP_PKEY_get1_EC_KEY,
EVP_PKEY_get0_RSA, EVP_PKEY_get0_DSA, EVP_PKEY_get0_DH, EVP_PKEY_get0_EC_KEY,
EVP_PKEY_assign_RSA, EVP_PKEY_assign_DSA, EVP_PKEY_assign_DH,
EVP_PKEY_assign_EC_KEY, EVP_PKEY_assign_POLY1305, EVP_PKEY_assign_SIPHASH,
EVP_PKEY_get0_hmac, EVP_PKEY_get0_poly1305, EVP_PKEY_get0_siphash,
EVP_PKEY_type, EVP_PKEY_id, EVP_PKEY_base_id, EVP_PKEY_set_alias_type,
EVP_PKEY_set1_engine, EVP_PKEY_get0_engine \- EVP_PKEY assignment functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_set1_RSA(EVP_PKEY *pkey, RSA *key);
\& int EVP_PKEY_set1_DSA(EVP_PKEY *pkey, DSA *key);
\& int EVP_PKEY_set1_DH(EVP_PKEY *pkey, DH *key);
\& int EVP_PKEY_set1_EC_KEY(EVP_PKEY *pkey, EC_KEY *key);
\&
\& RSA *EVP_PKEY_get1_RSA(EVP_PKEY *pkey);
\& DSA *EVP_PKEY_get1_DSA(EVP_PKEY *pkey);
\& DH *EVP_PKEY_get1_DH(EVP_PKEY *pkey);
\& EC_KEY *EVP_PKEY_get1_EC_KEY(EVP_PKEY *pkey);
\&
\& const unsigned char *EVP_PKEY_get0_hmac(const EVP_PKEY *pkey, size_t *len);
\& const unsigned char *EVP_PKEY_get0_poly1305(const EVP_PKEY *pkey, size_t *len);
\& const unsigned char *EVP_PKEY_get0_siphash(const EVP_PKEY *pkey, size_t *len);
\& RSA *EVP_PKEY_get0_RSA(EVP_PKEY *pkey);
\& DSA *EVP_PKEY_get0_DSA(EVP_PKEY *pkey);
\& DH *EVP_PKEY_get0_DH(EVP_PKEY *pkey);
\& EC_KEY *EVP_PKEY_get0_EC_KEY(EVP_PKEY *pkey);
\&
\& int EVP_PKEY_assign_RSA(EVP_PKEY *pkey, RSA *key);
\& int EVP_PKEY_assign_DSA(EVP_PKEY *pkey, DSA *key);
\& int EVP_PKEY_assign_DH(EVP_PKEY *pkey, DH *key);
\& int EVP_PKEY_assign_EC_KEY(EVP_PKEY *pkey, EC_KEY *key);
\& int EVP_PKEY_assign_POLY1305(EVP_PKEY *pkey, ASN1_OCTET_STRING *key);
\& int EVP_PKEY_assign_SIPHASH(EVP_PKEY *pkey, ASN1_OCTET_STRING *key);
\&
\& int EVP_PKEY_id(const EVP_PKEY *pkey);
\& int EVP_PKEY_base_id(const EVP_PKEY *pkey);
\& int EVP_PKEY_type(int type);
\& int EVP_PKEY_set_alias_type(EVP_PKEY *pkey, int type);
\&
\& ENGINE *EVP_PKEY_get0_engine(const EVP_PKEY *pkey);
\& int EVP_PKEY_set1_engine(EVP_PKEY *pkey, ENGINE *engine);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_set1_RSA()\fR, \fBEVP_PKEY_set1_DSA()\fR, \fBEVP_PKEY_set1_DH()\fR and
\&\fBEVP_PKEY_set1_EC_KEY()\fR set the key referenced by \fBpkey\fR to \fBkey\fR.
.PP
\&\fBEVP_PKEY_get1_RSA()\fR, \fBEVP_PKEY_get1_DSA()\fR, \fBEVP_PKEY_get1_DH()\fR and
\&\fBEVP_PKEY_get1_EC_KEY()\fR return the referenced key in \fBpkey\fR or
\&\fBNULL\fR if the key is not of the correct type.
.PP
\&\fBEVP_PKEY_get0_hmac()\fR, \fBEVP_PKEY_get0_poly1305()\fR, \fBEVP_PKEY_get0_siphash()\fR,
\&\fBEVP_PKEY_get0_RSA()\fR, \fBEVP_PKEY_get0_DSA()\fR, \fBEVP_PKEY_get0_DH()\fR
and \fBEVP_PKEY_get0_EC_KEY()\fR also return the referenced key in \fBpkey\fR or \fBNULL\fR
if the key is not of the correct type but the reference count of the
returned key is \fBnot\fR incremented and so must not be freed up after use.
.PP
\&\fBEVP_PKEY_assign_RSA()\fR, \fBEVP_PKEY_assign_DSA()\fR, \fBEVP_PKEY_assign_DH()\fR,
\&\fBEVP_PKEY_assign_EC_KEY()\fR, \fBEVP_PKEY_assign_POLY1305()\fR and
\&\fBEVP_PKEY_assign_SIPHASH()\fR also set the referenced key to \fBkey\fR
however these use the supplied \fBkey\fR internally and so \fBkey\fR
will be freed when the parent \fBpkey\fR is freed.
.PP
\&\fBEVP_PKEY_base_id()\fR returns the type of \fBpkey\fR. For example
an RSA key will return \fBEVP_PKEY_RSA\fR.
.PP
\&\fBEVP_PKEY_id()\fR returns the actual OID associated with \fBpkey\fR. Historically keys
using the same algorithm could use different OIDs. For example an RSA key could
use the OIDs corresponding to the NIDs \fBNID_rsaEncryption\fR (equivalent to
\&\fBEVP_PKEY_RSA\fR) or \fBNID_rsa\fR (equivalent to \fBEVP_PKEY_RSA2\fR). The use of
alternative non-standard OIDs is now rare so \fBEVP_PKEY_RSA2\fR et al are not
often seen in practice.
.PP
\&\fBEVP_PKEY_type()\fR returns the underlying type of the NID \fBtype\fR. For example
EVP_PKEY_type(EVP_PKEY_RSA2) will return \fBEVP_PKEY_RSA\fR.
.PP
\&\fBEVP_PKEY_get0_engine()\fR returns a reference to the ENGINE handling \fBpkey\fR.
.PP
\&\fBEVP_PKEY_set1_engine()\fR sets the ENGINE handling \fBpkey\fR to \fBengine\fR. It
must be called after the key algorithm and components are set up.
If \fBengine\fR does not include an \fBEVP_PKEY_METHOD\fR for \fBpkey\fR an
error occurs.
.PP
\&\fBEVP_PKEY_set_alias_type()\fR allows modifying a EVP_PKEY to use a
different set of algorithms than the default. This is currently used
to support SM2 keys, which use an identical encoding to ECDSA.
.SH NOTES
.IX Header "NOTES"
In accordance with the OpenSSL naming convention the key obtained
from or assigned to the \fBpkey\fR using the \fB1\fR functions must be
freed as well as \fBpkey\fR.
.PP
\&\fBEVP_PKEY_assign_RSA()\fR, \fBEVP_PKEY_assign_DSA()\fR, \fBEVP_PKEY_assign_DH()\fR,
\&\fBEVP_PKEY_assign_EC_KEY()\fR, \fBEVP_PKEY_assign_POLY1305()\fR
and \fBEVP_PKEY_assign_SIPHASH()\fR are implemented as macros.
.PP
Most applications wishing to know a key type will simply call
\&\fBEVP_PKEY_base_id()\fR and will not care about the actual type:
which will be identical in almost all cases.
.PP
Previous versions of this document suggested using EVP_PKEY_type(pkey\->type)
to determine the type of a key. Since \fBEVP_PKEY\fR is now opaque this
is no longer possible: the equivalent is EVP_PKEY_base_id(pkey).
.PP
\&\fBEVP_PKEY_set1_engine()\fR is typically used by an ENGINE returning an HSM
key as part of its routine to load a private key.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_set1_RSA()\fR, \fBEVP_PKEY_set1_DSA()\fR, \fBEVP_PKEY_set1_DH()\fR and
\&\fBEVP_PKEY_set1_EC_KEY()\fR return 1 for success or 0 for failure.
.PP
\&\fBEVP_PKEY_get1_RSA()\fR, \fBEVP_PKEY_get1_DSA()\fR, \fBEVP_PKEY_get1_DH()\fR and
\&\fBEVP_PKEY_get1_EC_KEY()\fR return the referenced key or \fBNULL\fR if
an error occurred.
.PP
\&\fBEVP_PKEY_assign_RSA()\fR, \fBEVP_PKEY_assign_DSA()\fR, \fBEVP_PKEY_assign_DH()\fR,
\&\fBEVP_PKEY_assign_EC_KEY()\fR, \fBEVP_PKEY_assign_POLY1305()\fR
and \fBEVP_PKEY_assign_SIPHASH()\fR return 1 for success and 0 for failure.
.PP
\&\fBEVP_PKEY_base_id()\fR, \fBEVP_PKEY_id()\fR and \fBEVP_PKEY_type()\fR return a key
type or \fBNID_undef\fR (equivalently \fBEVP_PKEY_NONE\fR) on error.
.PP
\&\fBEVP_PKEY_set1_engine()\fR returns 1 for success and 0 for failure.
.PP
\&\fBEVP_PKEY_set_alias_type()\fR returns 1 for success and 0 for error.
.SH EXAMPLES
.IX Header "EXAMPLES"
After loading an ECC key, it is possible to convert it to using SM2
algorithms with EVP_PKEY_set_alias_type:
.PP
.Vb 1
\& EVP_PKEY_set_alias_type(pkey, EVP_PKEY_SM2);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
