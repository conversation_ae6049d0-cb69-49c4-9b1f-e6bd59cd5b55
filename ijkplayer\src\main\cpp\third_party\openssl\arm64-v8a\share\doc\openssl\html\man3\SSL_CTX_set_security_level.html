<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CTX_set_security_level</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#DEFAULT-CALLBACK-BEHAVIOUR">DEFAULT CALLBACK BEHAVIOUR</a></li>
  <li><a href="#APPLICATION-DEFINED-SECURITY-CALLBACKS">APPLICATION DEFINED SECURITY CALLBACKS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CTX_set_security_level, SSL_set_security_level, SSL_CTX_get_security_level, SSL_get_security_level, SSL_CTX_set_security_callback, SSL_set_security_callback, SSL_CTX_get_security_callback, SSL_get_security_callback, SSL_CTX_set0_security_ex_data, SSL_set0_security_ex_data, SSL_CTX_get0_security_ex_data, SSL_get0_security_ex_data - SSL/TLS security framework</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

void SSL_CTX_set_security_level(SSL_CTX *ctx, int level);
void SSL_set_security_level(SSL *s, int level);

int SSL_CTX_get_security_level(const SSL_CTX *ctx);
int SSL_get_security_level(const SSL *s);

void SSL_CTX_set_security_callback(SSL_CTX *ctx,
                                   int (*cb)(SSL *s, SSL_CTX *ctx, int op,
                                             int bits, int nid,
                                             void *other, void *ex));

void SSL_set_security_callback(SSL *s, int (*cb)(SSL *s, SSL_CTX *ctx, int op,
                                                 int bits, int nid,
                                                 void *other, void *ex));

int (*SSL_CTX_get_security_callback(const SSL_CTX *ctx))(SSL *s, SSL_CTX *ctx, int op,
                                                         int bits, int nid, void *other,
                                                         void *ex);
int (*SSL_get_security_callback(const SSL *s))(SSL *s, SSL_CTX *ctx, int op,
                                               int bits, int nid, void *other,
                                               void *ex);

void SSL_CTX_set0_security_ex_data(SSL_CTX *ctx, void *ex);
void SSL_set0_security_ex_data(SSL *s, void *ex);

void *SSL_CTX_get0_security_ex_data(const SSL_CTX *ctx);
void *SSL_get0_security_ex_data(const SSL *s);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The functions SSL_CTX_set_security_level() and SSL_set_security_level() set the security level to <b>level</b>. If not set the library default security level is used.</p>

<p>The functions SSL_CTX_get_security_level() and SSL_get_security_level() retrieve the current security level.</p>

<p>SSL_CTX_set_security_callback(), SSL_set_security_callback(), SSL_CTX_get_security_callback() and SSL_get_security_callback() get or set the security callback associated with <b>ctx</b> or <b>s</b>. If not set a default security callback is used. The meaning of the parameters and the behaviour of the default callbacks is described below.</p>

<p>SSL_CTX_set0_security_ex_data(), SSL_set0_security_ex_data(), SSL_CTX_get0_security_ex_data() and SSL_get0_security_ex_data() set the extra data pointer passed to the <b>ex</b> parameter of the callback. This value is passed to the callback verbatim and can be set to any convenient application specific value.</p>

<h1 id="DEFAULT-CALLBACK-BEHAVIOUR">DEFAULT CALLBACK BEHAVIOUR</h1>

<p>If an application doesn&#39;t set its own security callback the default callback is used. It is intended to provide sane defaults. The meaning of each level is described below.</p>

<dl>

<dt id="Level-0"><b>Level 0</b></dt>
<dd>

<p>Everything is permitted. This retains compatibility with previous versions of OpenSSL.</p>

</dd>
<dt id="Level-1"><b>Level 1</b></dt>
<dd>

<p>The security level corresponds to a minimum of 80 bits of security. Any parameters offering below 80 bits of security are excluded. As a result RSA, DSA and DH keys shorter than 1024 bits and ECC keys shorter than 160 bits are prohibited. All export cipher suites are prohibited since they all offer less than 80 bits of security. SSL version 2 is prohibited. Any cipher suite using MD5 for the MAC is also prohibited.</p>

</dd>
<dt id="Level-2"><b>Level 2</b></dt>
<dd>

<p>Security level set to 112 bits of security. As a result RSA, DSA and DH keys shorter than 2048 bits and ECC keys shorter than 224 bits are prohibited. In addition to the level 1 exclusions any cipher suite using RC4 is also prohibited. SSL version 3 is also not allowed. Compression is disabled.</p>

</dd>
<dt id="Level-3"><b>Level 3</b></dt>
<dd>

<p>Security level set to 128 bits of security. As a result RSA, DSA and DH keys shorter than 3072 bits and ECC keys shorter than 256 bits are prohibited. In addition to the level 2 exclusions cipher suites not offering forward secrecy are prohibited. TLS versions below 1.1 are not permitted. Session tickets are disabled.</p>

</dd>
<dt id="Level-4"><b>Level 4</b></dt>
<dd>

<p>Security level set to 192 bits of security. As a result RSA, DSA and DH keys shorter than 7680 bits and ECC keys shorter than 384 bits are prohibited. Cipher suites using SHA1 for the MAC are prohibited. TLS versions below 1.2 are not permitted.</p>

</dd>
<dt id="Level-5"><b>Level 5</b></dt>
<dd>

<p>Security level set to 256 bits of security. As a result RSA, DSA and DH keys shorter than 15360 bits and ECC keys shorter than 512 bits are prohibited.</p>

</dd>
</dl>

<h1 id="APPLICATION-DEFINED-SECURITY-CALLBACKS">APPLICATION DEFINED SECURITY CALLBACKS</h1>

<p><i>Documentation to be provided.</i></p>

<h1 id="NOTES">NOTES</h1>

<p>The default security level can be configured when OpenSSL is compiled by setting <b>-DOPENSSL_TLS_SECURITY_LEVEL=level</b>. If not set then 1 is used.</p>

<p>The security framework disables or reject parameters inconsistent with the set security level. In the past this was difficult as applications had to set a number of distinct parameters (supported ciphers, supported curves supported signature algorithms) to achieve this end and some cases (DH parameter size for example) could not be checked at all.</p>

<p>By setting an appropriate security level much of this complexity can be avoided.</p>

<p>The bits of security limits affect all relevant parameters including cipher suite encryption algorithms, supported ECC curves, supported signature algorithms, DH parameter sizes, certificate key sizes and signature algorithms. This limit applies no matter what other custom settings an application has set: so if the cipher suite is set to <b>ALL</b> then only cipher suites consistent with the security level are permissible.</p>

<p>See SP800-57 for how the security limits are related to individual algorithms.</p>

<p>Some security levels require large key sizes for non-ECC public key algorithms which can severely degrade performance. For example 256 bits of security requires the use of RSA keys of at least 15360 bits in size.</p>

<p>Some restrictions can be gracefully handled: for example cipher suites offering insufficient security are not sent by the client and will not be selected by the server. Other restrictions such as the peer certificate key size or the DH parameter size will abort the handshake with a fatal alert.</p>

<p>Attempts to set certificates or parameters with insufficient security are also blocked. For example trying to set a certificate using a 512 bit RSA key using SSL_CTX_use_certificate() at level 1. Applications which do not check the return values for errors will misbehave: for example it might appear that a certificate is not set at all because it had been rejected.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_CTX_set_security_level() and SSL_set_security_level() do not return values.</p>

<p>SSL_CTX_get_security_level() and SSL_get_security_level() return a integer that represents the security level with <b>SSL_CTX</b> or <b>SSL</b>, respectively.</p>

<p>SSL_CTX_set_security_callback() and SSL_set_security_callback() do not return values.</p>

<p>SSL_CTX_get_security_callback() and SSL_get_security_callback() return the pointer to the security callback or NULL if the callback is not set.</p>

<p>SSL_CTX_get0_security_ex_data() and SSL_get0_security_ex_data() return the extra data pointer or NULL if the ex data is not set.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2014-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


