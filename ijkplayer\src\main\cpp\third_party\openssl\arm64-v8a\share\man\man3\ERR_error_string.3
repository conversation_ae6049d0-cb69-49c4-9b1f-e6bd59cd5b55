.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ERR_ERROR_STRING 3"
.TH ERR_ERROR_STRING 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ERR_error_string, ERR_error_string_n, ERR_lib_error_string,
ERR_func_error_string, ERR_reason_error_string \- obtain human\-readable
error message
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/err.h>
\&
\& char *ERR_error_string(unsigned long e, char *buf);
\& void ERR_error_string_n(unsigned long e, char *buf, size_t len);
\&
\& const char *ERR_lib_error_string(unsigned long e);
\& const char *ERR_func_error_string(unsigned long e);
\& const char *ERR_reason_error_string(unsigned long e);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBERR_error_string()\fR generates a human-readable string representing the
error code \fIe\fR, and places it at \fIbuf\fR. \fIbuf\fR must be at least 256
bytes long. If \fIbuf\fR is \fBNULL\fR, the error string is placed in a
static buffer.
Note that this function is not thread-safe and does no checks on the size
of the buffer; use \fBERR_error_string_n()\fR instead.
.PP
\&\fBERR_error_string_n()\fR is a variant of \fBERR_error_string()\fR that writes
at most \fIlen\fR characters (including the terminating 0)
and truncates the string if necessary.
For \fBERR_error_string_n()\fR, \fIbuf\fR may not be \fBNULL\fR.
.PP
The string will have the following format:
.PP
.Vb 1
\& error:[error code]:[library name]:[function name]:[reason string]
.Ve
.PP
\&\fIerror code\fR is an 8 digit hexadecimal number, \fIlibrary name\fR,
\&\fIfunction name\fR and \fIreason string\fR are ASCII text.
.PP
\&\fBERR_lib_error_string()\fR, \fBERR_func_error_string()\fR and
\&\fBERR_reason_error_string()\fR return the library name, function
name and reason string respectively.
.PP
If there is no text string registered for the given error code,
the error string will contain the numeric code.
.PP
\&\fBERR_print_errors\fR\|(3) can be used to print
all error codes currently in the queue.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBERR_error_string()\fR returns a pointer to a static buffer containing the
string if \fIbuf\fR \fB== NULL\fR, \fIbuf\fR otherwise.
.PP
\&\fBERR_lib_error_string()\fR, \fBERR_func_error_string()\fR and
\&\fBERR_reason_error_string()\fR return the strings, and \fBNULL\fR if
none is registered for the error code.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBERR_print_errors\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
