.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "TS 1"
.TH TS 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-ts,
ts \- Time Stamping Authority tool (client/server)
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBts\fR
\&\fB\-query\fR
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-config\fR configfile]
[\fB\-data\fR file_to_hash]
[\fB\-digest\fR digest_bytes]
[\fB\-\fR\f(BIdigest\fR]
[\fB\-tspolicy\fR object_id]
[\fB\-no_nonce\fR]
[\fB\-cert\fR]
[\fB\-in\fR request.tsq]
[\fB\-out\fR request.tsq]
[\fB\-text\fR]
.PP
\&\fBopenssl\fR \fBts\fR
\&\fB\-reply\fR
[\fB\-config\fR configfile]
[\fB\-section\fR tsa_section]
[\fB\-queryfile\fR request.tsq]
[\fB\-passin\fR password_src]
[\fB\-signer\fR tsa_cert.pem]
[\fB\-inkey\fR file_or_id]
[\fB\-\fR\f(BIdigest\fR]
[\fB\-chain\fR certs_file.pem]
[\fB\-tspolicy\fR object_id]
[\fB\-in\fR response.tsr]
[\fB\-token_in\fR]
[\fB\-out\fR response.tsr]
[\fB\-token_out\fR]
[\fB\-text\fR]
[\fB\-engine\fR id]
.PP
\&\fBopenssl\fR \fBts\fR
\&\fB\-verify\fR
[\fB\-data\fR file_to_hash]
[\fB\-digest\fR digest_bytes]
[\fB\-queryfile\fR request.tsq]
[\fB\-in\fR response.tsr]
[\fB\-token_in\fR]
[\fB\-CApath\fR trusted_cert_path]
[\fB\-CAfile\fR trusted_certs.pem]
[\fB\-untrusted\fR cert_file.pem]
[\fIverify options\fR]
.PP
\&\fIverify options:\fR
[\-attime timestamp]
[\-check_ss_sig]
[\-crl_check]
[\-crl_check_all]
[\-explicit_policy]
[\-extended_crl]
[\-ignore_critical]
[\-inhibit_any]
[\-inhibit_map]
[\-issuer_checks]
[\-no_alt_chains]
[\-no_check_time]
[\-partial_chain]
[\-policy arg]
[\-policy_check]
[\-policy_print]
[\-purpose purpose]
[\-suiteB_128]
[\-suiteB_128_only]
[\-suiteB_192]
[\-trusted_first]
[\-use_deltas]
[\-auth_level num]
[\-verify_depth num]
[\-verify_email email]
[\-verify_hostname hostname]
[\-verify_ip ip]
[\-verify_name name]
[\-x509_strict]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBts\fR command is a basic Time Stamping Authority (TSA) client and server
application as specified in RFC 3161 (Time-Stamp Protocol, TSP). A
TSA can be part of a PKI deployment and its role is to provide long
term proof of the existence of a certain datum before a particular
time. Here is a brief description of the protocol:
.IP 1. 4
The TSA client computes a one-way hash value for a data file and sends
the hash to the TSA.
.IP 2. 4
The TSA attaches the current date and time to the received hash value,
signs them and sends the timestamp token back to the client. By
creating this token the TSA certifies the existence of the original
data file at the time of response generation.
.IP 3. 4
The TSA client receives the timestamp token and verifies the
signature on it. It also checks if the token contains the same hash
value that it had sent to the TSA.
.PP
There is one DER encoded protocol data unit defined for transporting 
a timestamp request to the TSA and one for sending the timestamp response
back to the client. The \fBts\fR command has three main functions:
creating a timestamp request based on a data file,
creating a timestamp response based on a request, verifying if a
response corresponds to a particular request or a data file.
.PP
There is no support for sending the requests/responses automatically
over HTTP or TCP yet as suggested in RFC 3161. The users must send the
requests either by ftp or e\-mail.
.SH OPTIONS
.IX Header "OPTIONS"
.SS "Time Stamp Request generation"
.IX Subsection "Time Stamp Request generation"
The \fB\-query\fR switch can be used for creating and printing a timestamp
request with the following options:
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP "\fB\-config\fR configfile" 4
.IX Item "-config configfile"
The configuration file to use.
Optional; for a description of the default value,
see "COMMAND SUMMARY" in \fBopenssl\fR\|(1).
.IP "\fB\-data\fR file_to_hash" 4
.IX Item "-data file_to_hash"
The data file for which the timestamp request needs to be
created. stdin is the default if neither the \fB\-data\fR nor the \fB\-digest\fR
parameter is specified. (Optional)
.IP "\fB\-digest\fR digest_bytes" 4
.IX Item "-digest digest_bytes"
It is possible to specify the message imprint explicitly without the data
file. The imprint must be specified in a hexadecimal format, two characters
per byte, the bytes optionally separated by colons (e.g. 1A:F6:01:... or
1AF601...). The number of bytes must match the message digest algorithm
in use. (Optional)
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
The message digest to apply to the data file.
Any digest supported by the OpenSSL \fBdgst\fR command can be used.
The default is SHA\-1. (Optional)
.IP "\fB\-tspolicy\fR object_id" 4
.IX Item "-tspolicy object_id"
The policy that the client expects the TSA to use for creating the
timestamp token. Either the dotted OID notation or OID names defined
in the config file can be used. If no policy is requested the TSA will
use its own default policy. (Optional)
.IP \fB\-no_nonce\fR 4
.IX Item "-no_nonce"
No nonce is specified in the request if this option is
given. Otherwise a 64 bit long pseudo-random none is
included in the request. It is recommended to use nonce to
protect against replay-attacks. (Optional)
.IP \fB\-cert\fR 4
.IX Item "-cert"
The TSA is expected to include its signing certificate in the
response. (Optional)
.IP "\fB\-in\fR request.tsq" 4
.IX Item "-in request.tsq"
This option specifies a previously created timestamp request in DER
format that will be printed into the output file. Useful when you need
to examine the content of a request in human-readable
format. (Optional)
.IP "\fB\-out\fR request.tsq" 4
.IX Item "-out request.tsq"
Name of the output file to which the request will be written. Default
is stdout. (Optional)
.IP \fB\-text\fR 4
.IX Item "-text"
If this option is specified the output is human-readable text format
instead of DER. (Optional)
.SS "Time Stamp Response generation"
.IX Subsection "Time Stamp Response generation"
A timestamp response (TimeStampResp) consists of a response status
and the timestamp token itself (ContentInfo), if the token generation was
successful. The \fB\-reply\fR command is for creating a timestamp
response or timestamp token based on a request and printing the
response/token in human-readable format. If \fB\-token_out\fR is not
specified the output is always a timestamp response (TimeStampResp),
otherwise it is a timestamp token (ContentInfo).
.IP "\fB\-config\fR configfile" 4
.IX Item "-config configfile"
The configuration file to use.
Optional; for a description of the default value,
see "COMMAND SUMMARY" in \fBopenssl\fR\|(1).
See \fBCONFIGURATION FILE OPTIONS\fR for configurable variables.
.IP "\fB\-section\fR tsa_section" 4
.IX Item "-section tsa_section"
The name of the config file section containing the settings for the
response generation. If not specified the default TSA section is
used, see \fBCONFIGURATION FILE OPTIONS\fR for details. (Optional)
.IP "\fB\-queryfile\fR request.tsq" 4
.IX Item "-queryfile request.tsq"
The name of the file containing a DER encoded timestamp request. (Optional)
.IP "\fB\-passin\fR password_src" 4
.IX Item "-passin password_src"
Specifies the password source for the private key of the TSA. See
"Pass Phrase Options" in \fBopenssl\fR\|(1). (Optional)
.IP "\fB\-signer\fR tsa_cert.pem" 4
.IX Item "-signer tsa_cert.pem"
The signer certificate of the TSA in PEM format. The TSA signing
certificate must have exactly one extended key usage assigned to it:
timeStamping. The extended key usage must also be critical, otherwise
the certificate is going to be refused. Overrides the \fBsigner_cert\fR
variable of the config file. (Optional)
.IP "\fB\-inkey\fR file_or_id" 4
.IX Item "-inkey file_or_id"
The signer private key of the TSA in PEM format. Overrides the
\&\fBsigner_key\fR config file option. (Optional)
If no engine is used, the argument is taken as a file; if an engine is
specified, the argument is given to the engine as a key identifier.
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
Signing digest to use. Overrides the \fBsigner_digest\fR config file
option. (Mandatory unless specified in the config file)
.IP "\fB\-chain\fR certs_file.pem" 4
.IX Item "-chain certs_file.pem"
The collection of certificates in PEM format that will all
be included in the response in addition to the signer certificate if
the \fB\-cert\fR option was used for the request. This file is supposed to
contain the certificate chain for the signer certificate from its
issuer upwards. The \fB\-reply\fR command does not build a certificate
chain automatically. (Optional)
.IP "\fB\-tspolicy\fR object_id" 4
.IX Item "-tspolicy object_id"
The default policy to use for the response unless the client
explicitly requires a particular TSA policy. The OID can be specified
either in dotted notation or with its name. Overrides the
\&\fBdefault_policy\fR config file option. (Optional)
.IP "\fB\-in\fR response.tsr" 4
.IX Item "-in response.tsr"
Specifies a previously created timestamp response or timestamp token
(if \fB\-token_in\fR is also specified) in DER format that will be written
to the output file. This option does not require a request, it is
useful e.g. when you need to examine the content of a response or
token or you want to extract the timestamp token from a response. If
the input is a token and the output is a timestamp response a default
\&'granted' status info is added to the token. (Optional)
.IP \fB\-token_in\fR 4
.IX Item "-token_in"
This flag can be used together with the \fB\-in\fR option and indicates
that the input is a DER encoded timestamp token (ContentInfo) instead
of a timestamp response (TimeStampResp). (Optional)
.IP "\fB\-out\fR response.tsr" 4
.IX Item "-out response.tsr"
The response is written to this file. The format and content of the
file depends on other options (see \fB\-text\fR, \fB\-token_out\fR). The default is
stdout. (Optional)
.IP \fB\-token_out\fR 4
.IX Item "-token_out"
The output is a timestamp token (ContentInfo) instead of timestamp
response (TimeStampResp). (Optional)
.IP \fB\-text\fR 4
.IX Item "-text"
If this option is specified the output is human-readable text format
instead of DER. (Optional)
.IP "\fB\-engine\fR id" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBts\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms. Default is builtin. (Optional)
.SS "Time Stamp Response verification"
.IX Subsection "Time Stamp Response verification"
The \fB\-verify\fR command is for verifying if a timestamp response or 
timestamp token is valid and matches a particular timestamp request or
data file. The \fB\-verify\fR command does not use the configuration file.
.IP "\fB\-data\fR file_to_hash" 4
.IX Item "-data file_to_hash"
The response or token must be verified against file_to_hash. The file
is hashed with the message digest algorithm specified in the token.
The \fB\-digest\fR and \fB\-queryfile\fR options must not be specified with this one.
(Optional)
.IP "\fB\-digest\fR digest_bytes" 4
.IX Item "-digest digest_bytes"
The response or token must be verified against the message digest specified
with this option. The number of bytes must match the message digest algorithm
specified in the token. The \fB\-data\fR and \fB\-queryfile\fR options must not be
specified with this one. (Optional)
.IP "\fB\-queryfile\fR request.tsq" 4
.IX Item "-queryfile request.tsq"
The original timestamp request in DER format. The \fB\-data\fR and \fB\-digest\fR
options must not be specified with this one. (Optional)
.IP "\fB\-in\fR response.tsr" 4
.IX Item "-in response.tsr"
The timestamp response that needs to be verified in DER format. (Mandatory)
.IP \fB\-token_in\fR 4
.IX Item "-token_in"
This flag can be used together with the \fB\-in\fR option and indicates
that the input is a DER encoded timestamp token (ContentInfo) instead
of a timestamp response (TimeStampResp). (Optional)
.IP "\fB\-CApath\fR trusted_cert_path" 4
.IX Item "-CApath trusted_cert_path"
The name of the directory containing the trusted CA certificates of the
client. See the similar option of \fBverify\fR\|(1) for additional
details. Either this option or \fB\-CAfile\fR must be specified. (Optional)
.IP "\fB\-CAfile\fR trusted_certs.pem" 4
.IX Item "-CAfile trusted_certs.pem"
The name of the file containing a set of trusted self-signed CA
certificates in PEM format. See the similar option of
\&\fBverify\fR\|(1) for additional details. Either this option
or \fB\-CApath\fR must be specified.
(Optional)
.IP "\fB\-untrusted\fR cert_file.pem" 4
.IX Item "-untrusted cert_file.pem"
Set of additional untrusted certificates in PEM format which may be
needed when building the certificate chain for the TSA's signing
certificate. This file must contain the TSA signing certificate and
all intermediate CA certificates unless the response includes them.
(Optional)
.IP "\fIverify options\fR" 4
.IX Item "verify options"
The options \fB\-attime timestamp\fR, \fB\-check_ss_sig\fR, \fB\-crl_check\fR,
\&\fB\-crl_check_all\fR, \fB\-explicit_policy\fR, \fB\-extended_crl\fR, \fB\-ignore_critical\fR,
\&\fB\-inhibit_any\fR, \fB\-inhibit_map\fR, \fB\-issuer_checks\fR, \fB\-no_alt_chains\fR,
\&\fB\-no_check_time\fR, \fB\-partial_chain\fR, \fB\-policy\fR, \fB\-policy_check\fR,
\&\fB\-policy_print\fR, \fB\-purpose\fR, \fB\-suiteB_128\fR, \fB\-suiteB_128_only\fR,
\&\fB\-suiteB_192\fR, \fB\-trusted_first\fR, \fB\-use_deltas\fR, \fB\-auth_level\fR,
\&\fB\-verify_depth\fR, \fB\-verify_email\fR, \fB\-verify_hostname\fR, \fB\-verify_ip\fR,
\&\fB\-verify_name\fR, and \fB\-x509_strict\fR can be used to control timestamp
verification.  See \fBverify\fR\|(1).
.SH "CONFIGURATION FILE OPTIONS"
.IX Header "CONFIGURATION FILE OPTIONS"
The \fB\-query\fR and \fB\-reply\fR commands make use of a configuration file.
See \fBconfig\fR\|(5)
for a general description of the syntax of the config file. The
\&\fB\-query\fR command uses only the symbolic OID names section
and it can work without it. However, the \fB\-reply\fR command needs the
config file for its operation.
.PP
When there is a command line switch equivalent of a variable the
switch always overrides the settings in the config file.
.IP "\fBtsa\fR section, \fBdefault_tsa\fR" 4
.IX Item "tsa section, default_tsa"
This is the main section and it specifies the name of another section
that contains all the options for the \fB\-reply\fR command. This default
section can be overridden with the \fB\-section\fR command line switch. (Optional)
.IP \fBoid_file\fR 4
.IX Item "oid_file"
See \fBca\fR\|(1) for description. (Optional)
.IP \fBoid_section\fR 4
.IX Item "oid_section"
See \fBca\fR\|(1) for description. (Optional)
.IP \fBRANDFILE\fR 4
.IX Item "RANDFILE"
See \fBca\fR\|(1) for description. (Optional)
.IP \fBserial\fR 4
.IX Item "serial"
The name of the file containing the hexadecimal serial number of the
last timestamp response created. This number is incremented by 1 for
each response. If the file does not exist at the time of response
generation a new file is created with serial number 1. (Mandatory)
.IP \fBcrypto_device\fR 4
.IX Item "crypto_device"
Specifies the OpenSSL engine that will be set as the default for
all available algorithms. The default value is builtin, you can specify
any other engines supported by OpenSSL (e.g. use chil for the NCipher HSM).
(Optional)
.IP \fBsigner_cert\fR 4
.IX Item "signer_cert"
TSA signing certificate in PEM format. The same as the \fB\-signer\fR
command line option. (Optional)
.IP \fBcerts\fR 4
.IX Item "certs"
A file containing a set of PEM encoded certificates that need to be
included in the response. The same as the \fB\-chain\fR command line
option. (Optional)
.IP \fBsigner_key\fR 4
.IX Item "signer_key"
The private key of the TSA in PEM format. The same as the \fB\-inkey\fR
command line option. (Optional)
.IP \fBsigner_digest\fR 4
.IX Item "signer_digest"
Signing digest to use. The same as the
\&\fB\-\fR\f(BIdigest\fR command line option. (Mandatory unless specified on the command
line)
.IP \fBdefault_policy\fR 4
.IX Item "default_policy"
The default policy to use when the request does not mandate any
policy. The same as the \fB\-tspolicy\fR command line option. (Optional)
.IP \fBother_policies\fR 4
.IX Item "other_policies"
Comma separated list of policies that are also acceptable by the TSA
and used only if the request explicitly specifies one of them. (Optional)
.IP \fBdigests\fR 4
.IX Item "digests"
The list of message digest algorithms that the TSA accepts. At least
one algorithm must be specified. (Mandatory)
.IP \fBaccuracy\fR 4
.IX Item "accuracy"
The accuracy of the time source of the TSA in seconds, milliseconds
and microseconds. E.g. secs:1, millisecs:500, microsecs:100. If any of
the components is missing zero is assumed for that field. (Optional)
.IP \fBclock_precision_digits\fR 4
.IX Item "clock_precision_digits"
Specifies the maximum number of digits, which represent the fraction of
seconds, that  need to be included in the time field. The trailing zeros
must be removed from the time, so there might actually be fewer digits,
or no fraction of seconds at all. Supported only on UNIX platforms.
The maximum value is 6, default is 0.
(Optional)
.IP \fBordering\fR 4
.IX Item "ordering"
If this option is yes the responses generated by this TSA can always
be ordered, even if the time difference between two responses is less
than the sum of their accuracies. Default is no. (Optional)
.IP \fBtsa_name\fR 4
.IX Item "tsa_name"
Set this option to yes if the subject name of the TSA must be included in
the TSA name field of the response. Default is no. (Optional)
.IP \fBess_cert_id_chain\fR 4
.IX Item "ess_cert_id_chain"
The SignedData objects created by the TSA always contain the
certificate identifier of the signing certificate in a signed
attribute (see RFC 2634, Enhanced Security Services). If this option
is set to yes and either the \fBcerts\fR variable or the \fB\-chain\fR option
is specified then the certificate identifiers of the chain will also
be included in the SigningCertificate signed attribute. If this
variable is set to no, only the signing certificate identifier is
included. Default is no. (Optional)
.IP \fBess_cert_id_alg\fR 4
.IX Item "ess_cert_id_alg"
This option specifies the hash function to be used to calculate the TSA's
public key certificate identifier. Default is sha1. (Optional)
.SH EXAMPLES
.IX Header "EXAMPLES"
All the examples below presume that \fBOPENSSL_CONF\fR is set to a proper
configuration file, e.g. the example configuration file
openssl/apps/openssl.cnf will do.
.SS "Time Stamp Request"
.IX Subsection "Time Stamp Request"
To create a timestamp request for design1.txt with SHA\-1
without nonce and policy and no certificate is required in the response:
.PP
.Vb 2
\&  openssl ts \-query \-data design1.txt \-no_nonce \e
\&        \-out design1.tsq
.Ve
.PP
To create a similar timestamp request with specifying the message imprint
explicitly:
.PP
.Vb 2
\&  openssl ts \-query \-digest b7e5d3f93198b38379852f2c04e78d73abdd0f4b \e
\&         \-no_nonce \-out design1.tsq
.Ve
.PP
To print the content of the previous request in human readable format:
.PP
.Vb 1
\&  openssl ts \-query \-in design1.tsq \-text
.Ve
.PP
To create a timestamp request which includes the MD\-5 digest
of design2.txt, requests the signer certificate and nonce,
specifies a policy id (assuming the tsa_policy1 name is defined in the
OID section of the config file):
.PP
.Vb 2
\&  openssl ts \-query \-data design2.txt \-md5 \e
\&        \-tspolicy tsa_policy1 \-cert \-out design2.tsq
.Ve
.SS "Time Stamp Response"
.IX Subsection "Time Stamp Response"
Before generating a response a signing certificate must be created for
the TSA that contains the \fBtimeStamping\fR critical extended key usage extension
without any other key usage extensions. You can add this line to the
user certificate section of the config file to generate a proper certificate;
.PP
.Vb 1
\&   extendedKeyUsage = critical,timeStamping
.Ve
.PP
See \fBreq\fR\|(1), \fBca\fR\|(1), and \fBx509\fR\|(1) for instructions. The examples
below assume that cacert.pem contains the certificate of the CA,
tsacert.pem is the signing certificate issued by cacert.pem and
tsakey.pem is the private key of the TSA.
.PP
To create a timestamp response for a request:
.PP
.Vb 2
\&  openssl ts \-reply \-queryfile design1.tsq \-inkey tsakey.pem \e
\&        \-signer tsacert.pem \-out design1.tsr
.Ve
.PP
If you want to use the settings in the config file you could just write:
.PP
.Vb 1
\&  openssl ts \-reply \-queryfile design1.tsq \-out design1.tsr
.Ve
.PP
To print a timestamp reply to stdout in human readable format:
.PP
.Vb 1
\&  openssl ts \-reply \-in design1.tsr \-text
.Ve
.PP
To create a timestamp token instead of timestamp response:
.PP
.Vb 1
\&  openssl ts \-reply \-queryfile design1.tsq \-out design1_token.der \-token_out
.Ve
.PP
To print a timestamp token to stdout in human readable format:
.PP
.Vb 1
\&  openssl ts \-reply \-in design1_token.der \-token_in \-text \-token_out
.Ve
.PP
To extract the timestamp token from a response:
.PP
.Vb 1
\&  openssl ts \-reply \-in design1.tsr \-out design1_token.der \-token_out
.Ve
.PP
To add 'granted' status info to a timestamp token thereby creating a
valid response:
.PP
.Vb 1
\&  openssl ts \-reply \-in design1_token.der \-token_in \-out design1.tsr
.Ve
.SS "Time Stamp Verification"
.IX Subsection "Time Stamp Verification"
To verify a timestamp reply against a request:
.PP
.Vb 2
\&  openssl ts \-verify \-queryfile design1.tsq \-in design1.tsr \e
\&        \-CAfile cacert.pem \-untrusted tsacert.pem
.Ve
.PP
To verify a timestamp reply that includes the certificate chain:
.PP
.Vb 2
\&  openssl ts \-verify \-queryfile design2.tsq \-in design2.tsr \e
\&        \-CAfile cacert.pem
.Ve
.PP
To verify a timestamp token against the original data file:
  openssl ts \-verify \-data design2.txt \-in design2.tsr \e
        \-CAfile cacert.pem
.PP
To verify a timestamp token against a message imprint:
  openssl ts \-verify \-digest b7e5d3f93198b38379852f2c04e78d73abdd0f4b \e
         \-in design2.tsr \-CAfile cacert.pem
.PP
You could also look at the 'test' directory for more examples.
.SH BUGS
.IX Header "BUGS"
.IP \(bu 2
No support for timestamps over SMTP, though it is quite easy
to implement an automatic e\-mail based TSA with \fBprocmail\fR\|(1)
and \fBperl\fR\|(1). HTTP server support is provided in the form of
a separate apache module. HTTP client support is provided by
\&\fBtsget\fR\|(1). Pure TCP/IP protocol is not supported.
.IP \(bu 2
The file containing the last serial number of the TSA is not
locked when being read or written. This is a problem if more than one
instance of \fBopenssl\fR\|(1) is trying to create a timestamp
response at the same time. This is not an issue when using the apache
server module, it does proper locking.
.IP \(bu 2
Look for the FIXME word in the source files.
.IP \(bu 2
The source code should really be reviewed by somebody else, too.
.IP \(bu 2
More testing is needed, I have done only some basic tests (see
test/testtsa).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBtsget\fR\|(1), \fBopenssl\fR\|(1), \fBreq\fR\|(1),
\&\fBx509\fR\|(1), \fBca\fR\|(1), \fBgenrsa\fR\|(1),
\&\fBconfig\fR\|(5)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
