<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_get0_peer_scts</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RESTRICTIONS">RESTRICTIONS</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_get0_peer_scts - get SCTs received</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

const STACK_OF(SCT) *SSL_get0_peer_scts(SSL *s);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_get0_peer_scts() returns the signed certificate timestamps (SCTs) that have been received. If this is the first time that this function has been called for a given <b>SSL</b> instance, it will examine the TLS extensions, OCSP response and the peer&#39;s certificate for SCTs. Future calls will return the same SCTs.</p>

<h1 id="RESTRICTIONS">RESTRICTIONS</h1>

<p>If no Certificate Transparency validation callback has been set (using <b>SSL_CTX_set_ct_validation_callback</b> or <b>SSL_set_ct_validation_callback</b>), this function is not guaranteed to return all of the SCTs that the peer is capable of sending.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_get0_peer_scts() returns a list of SCTs found, or NULL if an error occurs.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CTX_set_ct_validation_callback.html">SSL_CTX_set_ct_validation_callback(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


