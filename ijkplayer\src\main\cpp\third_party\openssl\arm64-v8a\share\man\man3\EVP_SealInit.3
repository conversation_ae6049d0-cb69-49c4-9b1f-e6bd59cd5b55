.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_SEALINIT 3"
.TH EVP_SEALINIT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_SealInit, EVP_SealUpdate, EVP_SealFinal \- EVP envelope encryption
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_SealInit(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                  unsigned char **ek, int *ekl, unsigned char *iv,
\&                  EVP_PKEY **pubk, int npubk);
\& int EVP_SealUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
\&                    int *outl, unsigned char *in, int inl);
\& int EVP_SealFinal(EVP_CIPHER_CTX *ctx, unsigned char *out, int *outl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP envelope routines are a high-level interface to envelope
encryption. They generate a random key and IV (if required) then
"envelope" it by using public key encryption. Data can then be
encrypted using this key.
.PP
\&\fBEVP_SealInit()\fR initializes a cipher context \fBctx\fR for encryption
with cipher \fBtype\fR using a random secret key and IV. \fBtype\fR is normally
supplied by a function such as \fBEVP_aes_256_cbc()\fR. The secret key is encrypted
using one or more public keys, this allows the same encrypted data to be
decrypted using any of the corresponding private keys. \fBek\fR is an array of
buffers where the public key encrypted secret key will be written, each buffer
must contain enough room for the corresponding encrypted key: that is
\&\fBek[i]\fR must have room for \fBEVP_PKEY_size(pubk[i])\fR bytes. The actual
size of each encrypted secret key is written to the array \fBekl\fR. \fBpubk\fR is
an array of \fBnpubk\fR public keys.
.PP
The \fBiv\fR parameter is a buffer where the generated IV is written to. It must
contain enough room for the corresponding cipher's IV, as determined by (for
example) EVP_CIPHER_iv_length(type).
.PP
If the cipher does not require an IV then the \fBiv\fR parameter is ignored
and can be \fBNULL\fR.
.PP
\&\fBEVP_SealUpdate()\fR and \fBEVP_SealFinal()\fR have exactly the same properties
as the \fBEVP_EncryptUpdate()\fR and \fBEVP_EncryptFinal()\fR routines, as
documented on the \fBEVP_EncryptInit\fR\|(3) manual
page.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_SealInit()\fR returns 0 on error or \fBnpubk\fR if successful.
.PP
\&\fBEVP_SealUpdate()\fR and \fBEVP_SealFinal()\fR return 1 for success and 0 for
failure.
.SH NOTES
.IX Header "NOTES"
Because a random secret key is generated the random number generator
must be seeded when \fBEVP_SealInit()\fR is called.
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see \fBRAND\fR\|(7)), the operation will fail.
.PP
The public key must be RSA because it is the only OpenSSL public key
algorithm that supports key transport.
.PP
Envelope encryption is the usual method of using public key encryption
on large amounts of data, this is because public key encryption is slow
but symmetric encryption is fast. So symmetric encryption is used for
bulk encryption and the small random symmetric key used is transferred
using public key encryption.
.PP
It is possible to call \fBEVP_SealInit()\fR twice in the same way as
\&\fBEVP_EncryptInit()\fR. The first call should have \fBnpubk\fR set to 0
and (after setting any cipher parameters) it should be called again
with \fBtype\fR set to NULL.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7), \fBRAND_bytes\fR\|(3),
\&\fBEVP_EncryptInit\fR\|(3),
\&\fBEVP_OpenInit\fR\|(3),
\&\fBRAND\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
