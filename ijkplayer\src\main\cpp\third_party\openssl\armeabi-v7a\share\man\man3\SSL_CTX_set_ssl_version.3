.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_SSL_VERSION 3"
.TH SSL_CTX_SET_SSL_VERSION 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_ssl_version, SSL_set_ssl_method, SSL_get_ssl_method
\&\- choose a new TLS/SSL method
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_set_ssl_version(SSL_CTX *ctx, const SSL_METHOD *method);
\& int SSL_set_ssl_method(SSL *s, const SSL_METHOD *method);
\& const SSL_METHOD *SSL_get_ssl_method(const SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_ssl_version()\fR sets a new default TLS/SSL \fBmethod\fR for SSL objects
newly created from this \fBctx\fR. SSL objects already created with
\&\fBSSL_new\fR\|(3) are not affected, except when
\&\fBSSL_clear\fR\|(3) is being called.
.PP
\&\fBSSL_set_ssl_method()\fR sets a new TLS/SSL \fBmethod\fR for a particular \fBssl\fR
object. It may be reset, when \fBSSL_clear()\fR is called.
.PP
\&\fBSSL_get_ssl_method()\fR returns a function pointer to the TLS/SSL method
set in \fBssl\fR.
.SH NOTES
.IX Header "NOTES"
The available \fBmethod\fR choices are described in
\&\fBSSL_CTX_new\fR\|(3).
.PP
When \fBSSL_clear\fR\|(3) is called and no session is connected to
an SSL object, the method of the SSL object is reset to the method currently
set in the corresponding SSL_CTX object.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur for \fBSSL_CTX_set_ssl_version()\fR
and \fBSSL_set_ssl_method()\fR:
.IP 0 4
The new choice failed, check the error stack to find out the reason.
.IP 1 4
.IX Item "1"
The operation succeeded.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CTX_new\fR\|(3), \fBSSL_new\fR\|(3),
\&\fBSSL_clear\fR\|(3), \fBssl\fR\|(7),
\&\fBSSL_set_connect_state\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
