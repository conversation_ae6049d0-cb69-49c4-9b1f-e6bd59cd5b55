<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>x509v3_config</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#STANDARD-EXTENSIONS">STANDARD EXTENSIONS</a>
    <ul>
      <li><a href="#Basic-Constraints">Basic Constraints.</a></li>
      <li><a href="#Key-Usage">Key Usage.</a></li>
      <li><a href="#Extended-Key-Usage">Extended Key Usage.</a></li>
      <li><a href="#Subject-Key-Identifier">Subject Key Identifier.</a></li>
      <li><a href="#Authority-Key-Identifier">Authority Key Identifier.</a></li>
      <li><a href="#Subject-Alternative-Name">Subject Alternative Name.</a></li>
      <li><a href="#Issuer-Alternative-Name">Issuer Alternative Name.</a></li>
      <li><a href="#Authority-Info-Access">Authority Info Access.</a></li>
      <li><a href="#CRL-distribution-points">CRL distribution points</a></li>
      <li><a href="#Issuing-Distribution-Point">Issuing Distribution Point</a></li>
      <li><a href="#Certificate-Policies">Certificate Policies.</a></li>
      <li><a href="#Policy-Constraints">Policy Constraints</a></li>
      <li><a href="#Inhibit-Any-Policy">Inhibit Any Policy</a></li>
      <li><a href="#Name-Constraints">Name Constraints</a></li>
      <li><a href="#OCSP-No-Check">OCSP No Check</a></li>
      <li><a href="#TLS-Feature-aka-Must-Staple">TLS Feature (aka Must Staple)</a></li>
    </ul>
  </li>
  <li><a href="#DEPRECATED-EXTENSIONS">DEPRECATED EXTENSIONS</a>
    <ul>
      <li><a href="#Netscape-String-extensions">Netscape String extensions.</a></li>
      <li><a href="#Netscape-Certificate-Type">Netscape Certificate Type</a></li>
    </ul>
  </li>
  <li><a href="#ARBITRARY-EXTENSIONS">ARBITRARY EXTENSIONS</a></li>
  <li><a href="#WARNINGS">WARNINGS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>x509v3_config - X509 V3 certificate extension configuration format</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Several of the OpenSSL utilities can add extensions to a certificate or certificate request based on the contents of a configuration file.</p>

<p>Typically the application will contain an option to point to an extension section. Each line of the extension section takes the form:</p>

<pre><code>extension_name=[critical,] extension_options</code></pre>

<p>If <b>critical</b> is present then the extension will be critical.</p>

<p>The format of <b>extension_options</b> depends on the value of <b>extension_name</b>.</p>

<p>There are four main types of extension: <i>string</i> extensions, <i>multi-valued</i> extensions, <i>raw</i> and <i>arbitrary</i> extensions.</p>

<p>String extensions simply have a string which contains either the value itself or how it is obtained.</p>

<p>For example:</p>

<pre><code>nsComment=&quot;This is a Comment&quot;</code></pre>

<p>Multi-valued extensions have a short form and a long form. The short form is a list of names and values:</p>

<pre><code>basicConstraints=critical,CA:true,pathlen:1</code></pre>

<p>The long form allows the values to be placed in a separate section:</p>

<pre><code>basicConstraints=critical,@bs_section

[bs_section]

CA=true
pathlen=1</code></pre>

<p>Both forms are equivalent.</p>

<p>The syntax of raw extensions is governed by the extension code: it can for example contain data in multiple sections. The correct syntax to use is defined by the extension code itself: check out the certificate policies extension for an example.</p>

<p>If an extension type is unsupported then the <i>arbitrary</i> extension syntax must be used, see the <a href="#ARBITRARY-EXTENSIONS">ARBITRARY EXTENSIONS</a> section for more details.</p>

<h1 id="STANDARD-EXTENSIONS">STANDARD EXTENSIONS</h1>

<p>The following sections describe each supported extension in detail.</p>

<h2 id="Basic-Constraints">Basic Constraints.</h2>

<p>This is a multi valued extension which indicates whether a certificate is a CA certificate. The first (mandatory) name is <b>CA</b> followed by <b>TRUE</b> or <b>FALSE</b>. If <b>CA</b> is <b>TRUE</b> then an optional <b>pathlen</b> name followed by a nonnegative value can be included.</p>

<p>For example:</p>

<pre><code>basicConstraints=CA:TRUE

basicConstraints=CA:FALSE

basicConstraints=critical,CA:TRUE, pathlen:0</code></pre>

<p>A CA certificate <b>must</b> include the basicConstraints value with the CA field set to TRUE. An end user certificate must either set CA to FALSE or exclude the extension entirely. Some software may require the inclusion of basicConstraints with CA set to FALSE for end entity certificates.</p>

<p>The pathlen parameter indicates the maximum number of CAs that can appear below this one in a chain. So if you have a CA with a pathlen of zero it can only be used to sign end user certificates and not further CAs.</p>

<h2 id="Key-Usage">Key Usage.</h2>

<p>Key usage is a multi valued extension consisting of a list of names of the permitted key usages.</p>

<p>The supported names are: digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment, keyAgreement, keyCertSign, cRLSign, encipherOnly and decipherOnly.</p>

<p>Examples:</p>

<pre><code>keyUsage=digitalSignature, nonRepudiation

keyUsage=critical, keyCertSign</code></pre>

<h2 id="Extended-Key-Usage">Extended Key Usage.</h2>

<p>This extensions consists of a list of usages indicating purposes for which the certificate public key can be used for,</p>

<p>These can either be object short names or the dotted numerical form of OIDs. While any OID can be used only certain values make sense. In particular the following PKIX, NS and MS values are meaningful:</p>

<pre><code>Value                  Meaning
-----                  -------
serverAuth             SSL/TLS Web Server Authentication.
clientAuth             SSL/TLS Web Client Authentication.
codeSigning            Code signing.
emailProtection        E-mail Protection (S/MIME).
timeStamping           Trusted Timestamping
OCSPSigning            OCSP Signing
ipsecIKE               ipsec Internet Key Exchange
msCodeInd              Microsoft Individual Code Signing (authenticode)
msCodeCom              Microsoft Commercial Code Signing (authenticode)
msCTLSign              Microsoft Trust List Signing
msEFS                  Microsoft Encrypted File System</code></pre>

<p>Examples:</p>

<pre><code>extendedKeyUsage=critical,codeSigning,*******
extendedKeyUsage=serverAuth,clientAuth</code></pre>

<h2 id="Subject-Key-Identifier">Subject Key Identifier.</h2>

<p>This is really a string extension and can take two possible values. Either the word <b>hash</b> which will automatically follow the guidelines in RFC3280 or a hex string giving the extension value to include. The use of the hex string is strongly discouraged.</p>

<p>Example:</p>

<pre><code>subjectKeyIdentifier=hash</code></pre>

<h2 id="Authority-Key-Identifier">Authority Key Identifier.</h2>

<p>The authority key identifier extension permits two options. keyid and issuer: both can take the optional value &quot;always&quot;.</p>

<p>If the keyid option is present an attempt is made to copy the subject key identifier from the parent certificate. If the value &quot;always&quot; is present then an error is returned if the option fails.</p>

<p>The issuer option copies the issuer and serial number from the issuer certificate. This will only be done if the keyid option fails or is not included unless the &quot;always&quot; flag will always include the value.</p>

<p>Example:</p>

<pre><code>authorityKeyIdentifier=keyid,issuer</code></pre>

<h2 id="Subject-Alternative-Name">Subject Alternative Name.</h2>

<p>The subject alternative name extension allows various literal values to be included in the configuration file. These include <b>email</b> (an email address) <b>URI</b> a uniform resource indicator, <b>DNS</b> (a DNS domain name), <b>RID</b> (a registered ID: OBJECT IDENTIFIER), <b>IP</b> (an IP address), <b>dirName</b> (a distinguished name) and otherName.</p>

<p>The email option include a special &#39;copy&#39; value. This will automatically include any email addresses contained in the certificate subject name in the extension.</p>

<p>The IP address used in the <b>IP</b> options can be in either IPv4 or IPv6 format.</p>

<p>The value of <b>dirName</b> should point to a section containing the distinguished name to use as a set of name value pairs. Multi values AVAs can be formed by prefacing the name with a <b>+</b> character.</p>

<p>otherName can include arbitrary data associated with an OID: the value should be the OID followed by a semicolon and the content in standard <a href="../man3/ASN1_generate_nconf.html">ASN1_generate_nconf(3)</a> format.</p>

<p>Examples:</p>

<pre><code>subjectAltName=email:copy,email:<EMAIL>,URI:http://my.url.here/
subjectAltName=IP:***********
subjectAltName=IP:13::17
subjectAltName=email:<EMAIL>,RID:*******
subjectAltName=otherName:*******;UTF8:some other identifier

subjectAltName=dirName:dir_sect

[dir_sect]
C=UK
O=My Organization
OU=My Unit
CN=My Name</code></pre>

<h2 id="Issuer-Alternative-Name">Issuer Alternative Name.</h2>

<p>The issuer alternative name option supports all the literal options of subject alternative name. It does <b>not</b> support the email:copy option because that would not make sense. It does support an additional issuer:copy option that will copy all the subject alternative name values from the issuer certificate (if possible).</p>

<p>Example:</p>

<pre><code>issuerAltName = issuer:copy</code></pre>

<h2 id="Authority-Info-Access">Authority Info Access.</h2>

<p>The authority information access extension gives details about how to access certain information relating to the CA. Its syntax is accessOID;location where <i>location</i> has the same syntax as subject alternative name (except that email:copy is not supported). accessOID can be any valid OID but only certain values are meaningful, for example OCSP and caIssuers.</p>

<p>Example:</p>

<pre><code>authorityInfoAccess = OCSP;URI:http://ocsp.my.host/
authorityInfoAccess = caIssuers;URI:http://my.ca/ca.html</code></pre>

<h2 id="CRL-distribution-points">CRL distribution points</h2>

<p>This is a multi-valued extension whose options can be either in name:value pair using the same form as subject alternative name or a single value representing a section name containing all the distribution point fields.</p>

<p>For a name:value pair a new DistributionPoint with the fullName field set to the given value both the cRLissuer and reasons fields are omitted in this case.</p>

<p>In the single option case the section indicated contains values for each field. In this section:</p>

<p>If the name is &quot;fullname&quot; the value field should contain the full name of the distribution point in the same format as subject alternative name.</p>

<p>If the name is &quot;relativename&quot; then the value field should contain a section name whose contents represent a DN fragment to be placed in this field.</p>

<p>The name &quot;CRLIssuer&quot; if present should contain a value for this field in subject alternative name format.</p>

<p>If the name is &quot;reasons&quot; the value field should consist of a comma separated field containing the reasons. Valid reasons are: &quot;keyCompromise&quot;, &quot;CACompromise&quot;, &quot;affiliationChanged&quot;, &quot;superseded&quot;, &quot;cessationOfOperation&quot;, &quot;certificateHold&quot;, &quot;privilegeWithdrawn&quot; and &quot;AACompromise&quot;.</p>

<p>Simple examples:</p>

<pre><code>crlDistributionPoints=URI:http://myhost.com/myca.crl
crlDistributionPoints=URI:http://my.com/my.crl,URI:http://oth.com/my.crl</code></pre>

<p>Full distribution point example:</p>

<pre><code>crlDistributionPoints=crldp1_section

[crldp1_section]

fullname=URI:http://myhost.com/myca.crl
CRLissuer=dirName:issuer_sect
reasons=keyCompromise, CACompromise

[issuer_sect]
C=UK
O=Organisation
CN=Some Name</code></pre>

<h2 id="Issuing-Distribution-Point">Issuing Distribution Point</h2>

<p>This extension should only appear in CRLs. It is a multi valued extension whose syntax is similar to the &quot;section&quot; pointed to by the CRL distribution points extension with a few differences.</p>

<p>The names &quot;reasons&quot; and &quot;CRLissuer&quot; are not recognized.</p>

<p>The name &quot;onlysomereasons&quot; is accepted which sets this field. The value is in the same format as the CRL distribution point &quot;reasons&quot; field.</p>

<p>The names &quot;onlyuser&quot;, &quot;onlyCA&quot;, &quot;onlyAA&quot; and &quot;indirectCRL&quot; are also accepted the values should be a boolean value (TRUE or FALSE) to indicate the value of the corresponding field.</p>

<p>Example:</p>

<pre><code>issuingDistributionPoint=critical, @idp_section

[idp_section]

fullname=URI:http://myhost.com/myca.crl
indirectCRL=TRUE
onlysomereasons=keyCompromise, CACompromise

[issuer_sect]
C=UK
O=Organisation
CN=Some Name</code></pre>

<h2 id="Certificate-Policies">Certificate Policies.</h2>

<p>This is a <i>raw</i> extension. All the fields of this extension can be set by using the appropriate syntax.</p>

<p>If you follow the PKIX recommendations and just using one OID then you just include the value of that OID. Multiple OIDs can be set separated by commas, for example:</p>

<pre><code>certificatePolicies= *******, *******</code></pre>

<p>If you wish to include qualifiers then the policy OID and qualifiers need to be specified in a separate section: this is done by using the @section syntax instead of a literal OID value.</p>

<p>The section referred to must include the policy OID using the name policyIdentifier, cPSuri qualifiers can be included using the syntax:</p>

<pre><code>CPS.nnn=value</code></pre>

<p>userNotice qualifiers can be set using the syntax:</p>

<pre><code>userNotice.nnn=@notice</code></pre>

<p>The value of the userNotice qualifier is specified in the relevant section. This section can include explicitText, organization and noticeNumbers options. explicitText and organization are text strings, noticeNumbers is a comma separated list of numbers. The organization and noticeNumbers options (if included) must BOTH be present. If you use the userNotice option with IE5 then you need the &#39;ia5org&#39; option at the top level to modify the encoding: otherwise it will not be interpreted properly.</p>

<p>Example:</p>

<pre><code>certificatePolicies=ia5org,*******,*******.8,@polsect

[polsect]

policyIdentifier = *******
CPS.1=&quot;http://my.host.name/&quot;
CPS.2=&quot;http://my.your.name/&quot;
userNotice.1=@notice

[notice]

explicitText=&quot;Explicit Text Here&quot;
organization=&quot;Organisation Name&quot;
noticeNumbers=1,2,3,4</code></pre>

<p>The <b>ia5org</b> option changes the type of the <i>organization</i> field. In RFC2459 it can only be of type DisplayText. In RFC3280 IA5String is also permissible. Some software (for example some versions of MSIE) may require ia5org.</p>

<p>ASN1 type of explicitText can be specified by prepending <b>UTF8</b>, <b>BMP</b> or <b>VISIBLE</b> prefix followed by colon. For example:</p>

<pre><code>[notice]
explicitText=&quot;UTF8:Explicit Text Here&quot;</code></pre>

<h2 id="Policy-Constraints">Policy Constraints</h2>

<p>This is a multi-valued extension which consisting of the names <b>requireExplicitPolicy</b> or <b>inhibitPolicyMapping</b> and a non negative integer value. At least one component must be present.</p>

<p>Example:</p>

<pre><code>policyConstraints = requireExplicitPolicy:3</code></pre>

<h2 id="Inhibit-Any-Policy">Inhibit Any Policy</h2>

<p>This is a string extension whose value must be a non negative integer.</p>

<p>Example:</p>

<pre><code>inhibitAnyPolicy = 2</code></pre>

<h2 id="Name-Constraints">Name Constraints</h2>

<p>The name constraints extension is a multi-valued extension. The name should begin with the word <b>permitted</b> or <b>excluded</b> followed by a <b>;</b>. The rest of the name and the value follows the syntax of subjectAltName except email:copy is not supported and the <b>IP</b> form should consist of an IP addresses and subnet mask separated by a <b>/</b>.</p>

<p>Examples:</p>

<pre><code>nameConstraints=permitted;IP:***********/***********

nameConstraints=permitted;email:.somedomain.com

nameConstraints=excluded;email:.com</code></pre>

<h2 id="OCSP-No-Check">OCSP No Check</h2>

<p>The OCSP No Check extension is a string extension but its value is ignored.</p>

<p>Example:</p>

<pre><code>noCheck = ignored</code></pre>

<h2 id="TLS-Feature-aka-Must-Staple">TLS Feature (aka Must Staple)</h2>

<p>This is a multi-valued extension consisting of a list of TLS extension identifiers. Each identifier may be a number (0..65535) or a supported name. When a TLS client sends a listed extension, the TLS server is expected to include that extension in its reply.</p>

<p>The supported names are: <b>status_request</b> and <b>status_request_v2</b>.</p>

<p>Example:</p>

<pre><code>tlsfeature = status_request</code></pre>

<h1 id="DEPRECATED-EXTENSIONS">DEPRECATED EXTENSIONS</h1>

<p>The following extensions are non standard, Netscape specific and largely obsolete. Their use in new applications is discouraged.</p>

<h2 id="Netscape-String-extensions">Netscape String extensions.</h2>

<p>Netscape Comment (<b>nsComment</b>) is a string extension containing a comment which will be displayed when the certificate is viewed in some browsers.</p>

<p>Example:</p>

<pre><code>nsComment = &quot;Some Random Comment&quot;</code></pre>

<p>Other supported extensions in this category are: <b>nsBaseUrl</b>, <b>nsRevocationUrl</b>, <b>nsCaRevocationUrl</b>, <b>nsRenewalUrl</b>, <b>nsCaPolicyUrl</b> and <b>nsSslServerName</b>.</p>

<h2 id="Netscape-Certificate-Type">Netscape Certificate Type</h2>

<p>This is a multi-valued extensions which consists of a list of flags to be included. It was used to indicate the purposes for which a certificate could be used. The basicConstraints, keyUsage and extended key usage extensions are now used instead.</p>

<p>Acceptable values for nsCertType are: <b>client</b>, <b>server</b>, <b>email</b>, <b>objsign</b>, <b>reserved</b>, <b>sslCA</b>, <b>emailCA</b>, <b>objCA</b>.</p>

<h1 id="ARBITRARY-EXTENSIONS">ARBITRARY EXTENSIONS</h1>

<p>If an extension is not supported by the OpenSSL code then it must be encoded using the arbitrary extension format. It is also possible to use the arbitrary format for supported extensions. Extreme care should be taken to ensure that the data is formatted correctly for the given extension type.</p>

<p>There are two ways to encode arbitrary extensions.</p>

<p>The first way is to use the word ASN1 followed by the extension content using the same syntax as <a href="../man3/ASN1_generate_nconf.html">ASN1_generate_nconf(3)</a>. For example:</p>

<pre><code>*******=critical,ASN1:UTF8String:Some random data

*******=ASN1:SEQUENCE:seq_sect

[seq_sect]

field1 = UTF8:field1
field2 = UTF8:field2</code></pre>

<p>It is also possible to use the word DER to include the raw encoded data in any extension.</p>

<pre><code>*******=critical,DER:01:02:03:04
*******=DER:01020304</code></pre>

<p>The value following DER is a hex dump of the DER encoding of the extension Any extension can be placed in this form to override the default behaviour. For example:</p>

<pre><code>basicConstraints=critical,DER:00:01:02:03</code></pre>

<h1 id="WARNINGS">WARNINGS</h1>

<p>There is no guarantee that a specific implementation will process a given extension. It may therefore be sometimes possible to use certificates for purposes prohibited by their extensions because a specific application does not recognize or honour the values of the relevant extensions.</p>

<p>The DER and ASN1 options should be used with caution. It is possible to create totally invalid extensions if they are not used carefully.</p>

<h1 id="NOTES">NOTES</h1>

<p>If an extension is multi-value and a field value must contain a comma the long form must be used otherwise the comma would be misinterpreted as a field separator. For example:</p>

<pre><code>subjectAltName=URI:ldap://somehost.com/CN=foo,OU=bar</code></pre>

<p>will produce an error but the equivalent form:</p>

<pre><code>subjectAltName=@subject_alt_section

[subject_alt_section]
subjectAltName=URI:ldap://somehost.com/CN=foo,OU=bar</code></pre>

<p>is valid.</p>

<p>Due to the behaviour of the OpenSSL <b>conf</b> library the same field name can only occur once in a section. This means that:</p>

<pre><code>subjectAltName=@alt_section

[alt_section]

email=steve@here
email=steve@there</code></pre>

<p>will only recognize the last value. This can be worked around by using the form:</p>

<pre><code>[alt_section]

email.1=steve@here
email.2=steve@there</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/req.html">req(1)</a>, <a href="../man1/ca.html">ca(1)</a>, <a href="../man1/x509.html">x509(1)</a>, <a href="../man3/ASN1_generate_nconf.html">ASN1_generate_nconf(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2004-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


