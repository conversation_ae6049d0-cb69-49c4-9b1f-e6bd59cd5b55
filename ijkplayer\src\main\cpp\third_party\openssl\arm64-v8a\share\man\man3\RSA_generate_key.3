.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_GENERATE_KEY 3"
.TH RSA_GENERATE_KEY 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_generate_key_ex, RSA_generate_key,
RSA_generate_multi_prime_key \- generate RSA key pair
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
\&
\& int RSA_generate_key_ex(RSA *rsa, int bits, BIGNUM *e, BN_GENCB *cb);
\& int RSA_generate_multi_prime_key(RSA *rsa, int bits, int primes, BIGNUM *e, BN_GENCB *cb);
.Ve
.PP
Deprecated:
.PP
.Vb 4
\& #if OPENSSL_API_COMPAT < 0x00908000L
\& RSA *RSA_generate_key(int bits, unsigned long e,
\&                       void (*callback)(int, int, void *), void *cb_arg);
\& #endif
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBRSA_generate_key_ex()\fR generates a 2\-prime RSA key pair and stores it in the
\&\fBRSA\fR structure provided in \fBrsa\fR. The pseudo-random number generator must
be seeded prior to calling \fBRSA_generate_key_ex()\fR.
.PP
\&\fBRSA_generate_multi_prime_key()\fR generates a multi-prime RSA key pair and stores
it in the \fBRSA\fR structure provided in \fBrsa\fR. The number of primes is given by
the \fBprimes\fR parameter. The random number generator must be seeded when
calling \fBRSA_generate_multi_prime_key()\fR.
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see \fBRAND\fR\|(7)), the operation will fail.
.PP
The modulus size will be of length \fBbits\fR, the number of primes to form the
modulus will be \fBprimes\fR, and the public exponent will be \fBe\fR. Key sizes
with \fBnum\fR < 1024 should be considered insecure. The exponent is an odd
number, typically 3, 17 or 65537.
.PP
In order to maintain adequate security level, the maximum number of permitted
\&\fBprimes\fR depends on modulus bit length:
.PP
.Vb 3
\&   <1024 | >=1024 | >=4096 | >=8192
\&   \-\-\-\-\-\-+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-
\&     2   |   3    |   4    |   5
.Ve
.PP
A callback function may be used to provide feedback about the
progress of the key generation. If \fBcb\fR is not \fBNULL\fR, it
will be called as follows using the \fBBN_GENCB_call()\fR function
described on the \fBBN_generate_prime\fR\|(3) page.
.PP
\&\fBRSA_generate_key()\fR is similar to \fBRSA_generate_key_ex()\fR but
expects an old-style callback function; see
\&\fBBN_generate_prime\fR\|(3) for information on the old-style callback.
.IP \(bu 2
While a random prime number is generated, it is called as
described in \fBBN_generate_prime\fR\|(3).
.IP \(bu 2
When the n\-th randomly generated prime is rejected as not
suitable for the key, \fBBN_GENCB_call(cb, 2, n)\fR is called.
.IP \(bu 2
When a random p has been found with p\-1 relatively prime to \fBe\fR,
it is called as \fBBN_GENCB_call(cb, 3, 0)\fR.
.PP
The process is then repeated for prime q and other primes (if any)
with \fBBN_GENCB_call(cb, 3, i)\fR where \fBi\fR indicates the i\-th prime.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRSA_generate_multi_prime_key()\fR returns 1 on success or 0 on error.
\&\fBRSA_generate_key_ex()\fR returns 1 on success or 0 on error.
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.PP
\&\fBRSA_generate_key()\fR returns a pointer to the RSA structure or
\&\fBNULL\fR if the key generation fails.
.SH BUGS
.IX Header "BUGS"
\&\fBBN_GENCB_call(cb, 2, x)\fR is used with two different meanings.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBRAND_bytes\fR\|(3), \fBBN_generate_prime\fR\|(3),
\&\fBRAND\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBRSA_generate_key()\fR was deprecated in OpenSSL 0.9.8; use
\&\fBRSA_generate_key_ex()\fR instead.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
