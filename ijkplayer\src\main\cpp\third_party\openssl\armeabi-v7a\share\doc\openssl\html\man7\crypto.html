<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>crypto</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>crypto - OpenSSL cryptographic library</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p>See the individual manual pages for details.</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The OpenSSL <b>crypto</b> library implements a wide range of cryptographic algorithms used in various Internet standards. The services provided by this library are used by the OpenSSL implementations of SSL, TLS and S/MIME, and they have also been used to implement SSH, OpenPGP, and other cryptographic standards.</p>

<p><b>libcrypto</b> consists of a number of sub-libraries that implement the individual algorithms.</p>

<p>The functionality includes symmetric encryption, public key cryptography and key agreement, certificate handling, cryptographic hash functions, cryptographic pseudo-random number generator, and various utilities.</p>

<h1 id="NOTES">NOTES</h1>

<p>Some of the newer functions follow a naming convention using the numbers <b>0</b> and <b>1</b>. For example the functions:</p>

<pre><code>int X509_CRL_add0_revoked(X509_CRL *crl, X509_REVOKED *rev);
int X509_add1_trust_object(X509 *x, const ASN1_OBJECT *obj);</code></pre>

<p>The <b>0</b> version uses the supplied structure pointer directly in the parent and it will be freed up when the parent is freed. In the above example <b>crl</b> would be freed but <b>rev</b> would not.</p>

<p>The <b>1</b> function uses a copy of the supplied structure pointer (or in some cases increases its link count) in the parent and so both (<b>x</b> and <b>obj</b> above) should be freed up.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>See the individual manual pages for details.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl.html">openssl(1)</a>, <a href="../man7/ssl.html">ssl(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


