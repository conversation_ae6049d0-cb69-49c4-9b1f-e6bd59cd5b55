.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_PSK_IDENTITY 3"
.TH SSL_GET_PSK_IDENTITY 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_psk_identity, SSL_get_psk_identity_hint \- get PSK client identity and hint
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& const char *SSL_get_psk_identity_hint(const SSL *ssl);
\& const char *SSL_get_psk_identity(const SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_psk_identity_hint()\fR is used to retrieve the PSK identity hint
used during the connection setup related to SSL object
\&\fBssl\fR. Similarly, \fBSSL_get_psk_identity()\fR is used to retrieve the PSK
identity used during the connection setup.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If non\-\fBNULL\fR, \fBSSL_get_psk_identity_hint()\fR returns the PSK identity
hint and \fBSSL_get_psk_identity()\fR returns the PSK identity. Both are
\&\fBNULL\fR\-terminated. \fBSSL_get_psk_identity_hint()\fR may return \fBNULL\fR if
no PSK identity hint was used during the connection setup.
.PP
Note that the return value is valid only during the lifetime of the
SSL object \fBssl\fR.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
