.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_S_ACCEPT 3"
.TH BIO_S_ACCEPT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_s_accept, BIO_set_accept_name, BIO_set_accept_port, BIO_get_accept_name,
BIO_get_accept_port, BIO_new_accept, BIO_set_nbio_accept, BIO_set_accept_bios,
BIO_get_peer_name, BIO_get_peer_port,
BIO_get_accept_ip_family, BIO_set_accept_ip_family,
BIO_set_bind_mode, BIO_get_bind_mode, BIO_do_accept \- accept BIO
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& const BIO_METHOD *BIO_s_accept(void);
\&
\& long BIO_set_accept_name(BIO *b, char *name);
\& char *BIO_get_accept_name(BIO *b);
\&
\& long BIO_set_accept_port(BIO *b, char *port);
\& char *BIO_get_accept_port(BIO *b);
\&
\& BIO *BIO_new_accept(char *host_port);
\&
\& long BIO_set_nbio_accept(BIO *b, int n);
\& long BIO_set_accept_bios(BIO *b, char *bio);
\&
\& char *BIO_get_peer_name(BIO *b);
\& char *BIO_get_peer_port(BIO *b);
\& long BIO_get_accept_ip_family(BIO *b);
\& long BIO_set_accept_ip_family(BIO *b, long family);
\&
\& long BIO_set_bind_mode(BIO *b, long mode);
\& long BIO_get_bind_mode(BIO *b);
\&
\& int BIO_do_accept(BIO *b);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_s_accept()\fR returns the accept BIO method. This is a wrapper
round the platform's TCP/IP socket accept routines.
.PP
Using accept BIOs, TCP/IP connections can be accepted and data
transferred using only BIO routines. In this way any platform
specific operations are hidden by the BIO abstraction.
.PP
Read and write operations on an accept BIO will perform I/O
on the underlying connection. If no connection is established
and the port (see below) is set up properly then the BIO
waits for an incoming connection.
.PP
Accept BIOs support \fBBIO_puts()\fR but not \fBBIO_gets()\fR.
.PP
If the close flag is set on an accept BIO then any active
connection on that chain is shutdown and the socket closed when
the BIO is freed.
.PP
Calling \fBBIO_reset()\fR on an accept BIO will close any active
connection and reset the BIO into a state where it awaits another
incoming connection.
.PP
\&\fBBIO_get_fd()\fR and \fBBIO_set_fd()\fR can be called to retrieve or set
the accept socket. See \fBBIO_s_fd\fR\|(3)
.PP
\&\fBBIO_set_accept_name()\fR uses the string \fBname\fR to set the accept
name. The name is represented as a string of the form "host:port",
where "host" is the interface to use and "port" is the port.
The host can be "*" or empty which is interpreted as meaning
any interface.  If the host is an IPv6 address, it has to be
enclosed in brackets, for example "[::1]:https".  "port" has the
same syntax as the port specified in \fBBIO_set_conn_port()\fR for
connect BIOs, that is it can be a numerical port string or a
string to lookup using \fBgetservbyname()\fR and a string table.
.PP
\&\fBBIO_set_accept_port()\fR uses the string \fBport\fR to set the accept
port.  "port" has the same syntax as the port specified in
\&\fBBIO_set_conn_port()\fR for connect BIOs, that is it can be a numerical
port string or a string to lookup using \fBgetservbyname()\fR and a string
table.
.PP
\&\fBBIO_new_accept()\fR combines \fBBIO_new()\fR and \fBBIO_set_accept_name()\fR into
a single call: that is it creates a new accept BIO with port
\&\fBhost_port\fR.
.PP
\&\fBBIO_set_nbio_accept()\fR sets the accept socket to blocking mode
(the default) if \fBn\fR is 0 or non blocking mode if \fBn\fR is 1.
.PP
\&\fBBIO_set_accept_bios()\fR can be used to set a chain of BIOs which
will be duplicated and prepended to the chain when an incoming
connection is received. This is useful if, for example, a
buffering or SSL BIO is required for each connection. The
chain of BIOs must not be freed after this call, they will
be automatically freed when the accept BIO is freed.
.PP
\&\fBBIO_set_bind_mode()\fR and \fBBIO_get_bind_mode()\fR set and retrieve
the current bind mode. If \fBBIO_BIND_NORMAL\fR (the default) is set
then another socket cannot be bound to the same port. If
\&\fBBIO_BIND_REUSEADDR\fR is set then other sockets can bind to the
same port. If \fBBIO_BIND_REUSEADDR_IF_UNUSED\fR is set then and
attempt is first made to use BIO_BIN_NORMAL, if this fails
and the port is not in use then a second attempt is made
using \fBBIO_BIND_REUSEADDR\fR.
.PP
\&\fBBIO_do_accept()\fR serves two functions. When it is first
called, after the accept BIO has been setup, it will attempt
to create the accept socket and bind an address to it. Second
and subsequent calls to \fBBIO_do_accept()\fR will await an incoming
connection, or request a retry in non blocking mode.
.SH NOTES
.IX Header "NOTES"
When an accept BIO is at the end of a chain it will await an
incoming connection before processing I/O calls. When an accept
BIO is not at then end of a chain it passes I/O calls to the next
BIO in the chain.
.PP
When a connection is established a new socket BIO is created for
the connection and appended to the chain. That is the chain is now
accept\->socket. This effectively means that attempting I/O on
an initial accept socket will await an incoming connection then
perform I/O on it.
.PP
If any additional BIOs have been set using \fBBIO_set_accept_bios()\fR
then they are placed between the socket and the accept BIO,
that is the chain will be accept\->otherbios\->socket.
.PP
If a server wishes to process multiple connections (as is normally
the case) then the accept BIO must be made available for further
incoming connections. This can be done by waiting for a connection and
then calling:
.PP
.Vb 1
\& connection = BIO_pop(accept);
.Ve
.PP
After this call \fBconnection\fR will contain a BIO for the recently
established connection and \fBaccept\fR will now be a single BIO
again which can be used to await further incoming connections.
If no further connections will be accepted the \fBaccept\fR can
be freed using \fBBIO_free()\fR.
.PP
If only a single connection will be processed it is possible to
perform I/O using the accept BIO itself. This is often undesirable
however because the accept BIO will still accept additional incoming
connections. This can be resolved by using \fBBIO_pop()\fR (see above)
and freeing up the accept BIO after the initial connection.
.PP
If the underlying accept socket is nonblocking and \fBBIO_do_accept()\fR is
called to await an incoming connection it is possible for
\&\fBBIO_should_io_special()\fR with the reason BIO_RR_ACCEPT. If this happens
then it is an indication that an accept attempt would block: the application
should take appropriate action to wait until the underlying socket has
accepted a connection and retry the call.
.PP
\&\fBBIO_set_accept_name()\fR, \fBBIO_get_accept_name()\fR, \fBBIO_set_accept_port()\fR,
\&\fBBIO_get_accept_port()\fR, \fBBIO_set_nbio_accept()\fR, \fBBIO_set_accept_bios()\fR,
\&\fBBIO_get_peer_name()\fR, \fBBIO_get_peer_port()\fR,
\&\fBBIO_get_accept_ip_family()\fR, \fBBIO_set_accept_ip_family()\fR,
\&\fBBIO_set_bind_mode()\fR, \fBBIO_get_bind_mode()\fR and \fBBIO_do_accept()\fR are macros.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_do_accept()\fR,
\&\fBBIO_set_accept_name()\fR, \fBBIO_set_accept_port()\fR, \fBBIO_set_nbio_accept()\fR,
\&\fBBIO_set_accept_bios()\fR, \fBBIO_set_accept_ip_family()\fR, and \fBBIO_set_bind_mode()\fR
return 1 for success and 0 or \-1 for failure.
.PP
\&\fBBIO_get_accept_name()\fR returns the accept name or NULL on error.
\&\fBBIO_get_peer_name()\fR returns the peer name or NULL on error.
.PP
\&\fBBIO_get_accept_port()\fR returns the accept port as a string or NULL on error.
\&\fBBIO_get_peer_port()\fR returns the peer port as a string or NULL on error.
\&\fBBIO_get_accept_ip_family()\fR returns the IP family or \-1 on error.
.PP
\&\fBBIO_get_bind_mode()\fR returns the set of \fBBIO_BIND\fR flags, or \-1 on failure.
.PP
\&\fBBIO_new_accept()\fR returns a BIO or NULL on error.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example accepts two connections on port 4444, sends messages
down each and finally closes both down.
.PP
.Vb 1
\& BIO *abio, *cbio, *cbio2;
\&
\& /* First call to BIO_accept() sets up accept BIO */
\& abio = BIO_new_accept("4444");
\& if (BIO_do_accept(abio) <= 0) {
\&     fprintf(stderr, "Error setting up accept\en");
\&     ERR_print_errors_fp(stderr);
\&     exit(1);
\& }
\&
\& /* Wait for incoming connection */
\& if (BIO_do_accept(abio) <= 0) {
\&     fprintf(stderr, "Error accepting connection\en");
\&     ERR_print_errors_fp(stderr);
\&     exit(1);
\& }
\& fprintf(stderr, "Connection 1 established\en");
\&
\& /* Retrieve BIO for connection */
\& cbio = BIO_pop(abio);
\& BIO_puts(cbio, "Connection 1: Sending out Data on initial connection\en");
\& fprintf(stderr, "Sent out data on connection 1\en");
\&
\& /* Wait for another connection */
\& if (BIO_do_accept(abio) <= 0) {
\&     fprintf(stderr, "Error accepting connection\en");
\&     ERR_print_errors_fp(stderr);
\&     exit(1);
\& }
\& fprintf(stderr, "Connection 2 established\en");
\&
\& /* Close accept BIO to refuse further connections */
\& cbio2 = BIO_pop(abio);
\& BIO_free(abio);
\& BIO_puts(cbio2, "Connection 2: Sending out Data on second\en");
\& fprintf(stderr, "Sent out data on connection 2\en");
\&
\& BIO_puts(cbio, "Connection 1: Second connection established\en");
\&
\& /* Close the two established connections */
\& BIO_free(cbio);
\& BIO_free(cbio2);
.Ve
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
