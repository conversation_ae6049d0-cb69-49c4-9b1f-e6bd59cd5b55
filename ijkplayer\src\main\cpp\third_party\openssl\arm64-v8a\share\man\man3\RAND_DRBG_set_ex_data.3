.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_DRBG_SET_EX_DATA 3"
.TH RAND_DRBG_SET_EX_DATA 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_DRBG_set_ex_data,
RAND_DRBG_get_ex_data,
RAND_DRBG_get_ex_new_index
\&\- store and retrieve extra data from the DRBG instance
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand_drbg.h>
\&
\& int RAND_DRBG_set_ex_data(RAND_DRBG *drbg, int idx, void *data);
\&
\& void *RAND_DRBG_get_ex_data(const RAND_DRBG *drbg, int idx);
\&
\& int RAND_DRBG_get_ex_new_index(long argl, void *argp,
\&                                CRYPTO_EX_new *new_func,
\&                                CRYPTO_EX_dup *dup_func,
\&                                CRYPTO_EX_free *free_func);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBRAND_DRBG_set_ex_data()\fR enables an application to store arbitrary application
specific data \fBdata\fR in a RAND_DRBG instance \fBdrbg\fR. The index \fBidx\fR should
be a value previously returned from a call to \fBRAND_DRBG_get_ex_new_index()\fR.
.PP
\&\fBRAND_DRBG_get_ex_data()\fR retrieves application specific data previously stored
in an RAND_DRBG instance \fBdrbg\fR. The \fBidx\fR value should be the same as that
used when originally storing the data.
.PP
For more detailed information see \fBCRYPTO_get_ex_data\fR\|(3) and
\&\fBCRYPTO_set_ex_data\fR\|(3) which implement these functions and
\&\fBCRYPTO_get_ex_new_index\fR\|(3) for generating a unique index.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_DRBG_set_ex_data()\fR returns 1 for success or 0 for failure.
.PP
\&\fBRAND_DRBG_get_ex_data()\fR returns the previously stored value or NULL on
failure. NULL may also be a valid value.
.SH NOTES
.IX Header "NOTES"
RAND_DRBG_get_ex_new_index(...) is implemented as a macro and equivalent to
CRYPTO_get_ex_new_index(CRYPTO_EX_INDEX_DRBG,...).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBCRYPTO_get_ex_data\fR\|(3),
\&\fBCRYPTO_set_ex_data\fR\|(3),
\&\fBCRYPTO_get_ex_new_index\fR\|(3),
\&\fBRAND_DRBG\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
