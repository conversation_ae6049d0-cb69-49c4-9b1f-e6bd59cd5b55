.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ED25519 7"
.TH ED25519 7 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
Ed25519,
Ed448
\&\- EVP_PKEY Ed25519 and Ed448 support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEd25519\fR and \fBEd448\fR EVP_PKEY implementation supports key generation,
one-shot digest sign and digest verify using PureEdDSA and \fBEd25519\fR or \fBEd448\fR
(see RFC8032). It has associated private and public key formats compatible with
RFC 8410.
.PP
No additional parameters can be set during key generation, one-shot signing or
verification. In particular, because PureEdDSA is used, a digest must \fBNOT\fR be
specified when signing or verifying.
.SH NOTES
.IX Header "NOTES"
The PureEdDSA algorithm does not support the streaming mechanism
of other signature algorithms using, for example, \fBEVP_DigestUpdate()\fR.
The message to sign or verify must be passed using the one-shot
\&\fBEVP_DigestSign()\fR and \fBEVP_DigestVerify()\fR functions.
.PP
When calling \fBEVP_DigestSignInit()\fR or \fBEVP_DigestVerifyInit()\fR, the
digest \fBtype\fR parameter \fBMUST\fR be set to \fBNULL\fR.
.PP
Applications wishing to sign certificates (or other structures such as
CRLs or certificate requests) using Ed25519 or Ed448 can either use \fBX509_sign()\fR
or \fBX509_sign_ctx()\fR in the usual way.
.PP
A context for the \fBEd25519\fR algorithm can be obtained by calling:
.PP
.Vb 1
\& EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_ED25519, NULL);
.Ve
.PP
For the \fBEd448\fR algorithm a context can be obtained by calling:
.PP
.Vb 1
\& EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_ED448, NULL);
.Ve
.PP
Ed25519 or Ed448 private keys can be set directly using
\&\fBEVP_PKEY_new_raw_private_key\fR\|(3) or loaded from a PKCS#8 private key file
using \fBPEM_read_bio_PrivateKey\fR\|(3) (or similar function). Completely new keys
can also be generated (see the example below). Setting a private key also sets
the associated public key.
.PP
Ed25519 or Ed448 public keys can be set directly using
\&\fBEVP_PKEY_new_raw_public_key\fR\|(3) or loaded from a SubjectPublicKeyInfo
structure in a PEM file using \fBPEM_read_bio_PUBKEY\fR\|(3) (or similar function).
.PP
Ed25519 and Ed448 can be tested within \fBspeed\fR\|(1) application since version 1.1.1.
Valid algorithm names are \fBed25519\fR, \fBed448\fR and \fBeddsa\fR. If \fBeddsa\fR is
specified, then both Ed25519 and Ed448 are benchmarked.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example generates an \fBED25519\fR private key and writes it to standard
output in PEM format:
.PP
.Vb 9
\& #include <openssl/evp.h>
\& #include <openssl/pem.h>
\& ...
\& EVP_PKEY *pkey = NULL;
\& EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_ED25519, NULL);
\& EVP_PKEY_keygen_init(pctx);
\& EVP_PKEY_keygen(pctx, &pkey);
\& EVP_PKEY_CTX_free(pctx);
\& PEM_write_PrivateKey(stdout, pkey, NULL, NULL, 0, NULL, NULL);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_keygen\fR\|(3),
\&\fBEVP_DigestSignInit\fR\|(3),
\&\fBEVP_DigestVerifyInit\fR\|(3),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
