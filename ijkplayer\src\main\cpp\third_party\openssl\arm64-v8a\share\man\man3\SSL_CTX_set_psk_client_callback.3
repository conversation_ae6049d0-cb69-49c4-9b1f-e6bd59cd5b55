.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_PSK_CLIENT_CALLBACK 3"
.TH SSL_CTX_SET_PSK_CLIENT_CALLBACK 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_psk_client_cb_func,
SSL_psk_use_session_cb_func,
SSL_CTX_set_psk_client_callback,
SSL_set_psk_client_callback,
SSL_CTX_set_psk_use_session_callback,
SSL_set_psk_use_session_callback
\&\- set PSK client callback
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& typedef int (*SSL_psk_use_session_cb_func)(SSL *ssl, const EVP_MD *md,
\&                                            const unsigned char **id,
\&                                            size_t *idlen,
\&                                            SSL_SESSION **sess);
\&
\&
\& void SSL_CTX_set_psk_use_session_callback(SSL_CTX *ctx,
\&                                           SSL_psk_use_session_cb_func cb);
\& void SSL_set_psk_use_session_callback(SSL *s, SSL_psk_use_session_cb_func cb);
\&
\&
\& typedef unsigned int (*SSL_psk_client_cb_func)(SSL *ssl,
\&                                                const char *hint,
\&                                                char *identity,
\&                                                unsigned int max_identity_len,
\&                                                unsigned char *psk,
\&                                                unsigned int max_psk_len);
\&
\& void SSL_CTX_set_psk_client_callback(SSL_CTX *ctx, SSL_psk_client_cb_func cb);
\& void SSL_set_psk_client_callback(SSL *ssl, SSL_psk_client_cb_func cb);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A client application wishing to use TLSv1.3 PSKs should use either
\&\fBSSL_CTX_set_psk_use_session_callback()\fR or \fBSSL_set_psk_use_session_callback()\fR as
appropriate. These functions cannot be used for TLSv1.2 and below PSKs.
.PP
The callback function is given a pointer to the SSL connection in \fBssl\fR.
.PP
The first time the callback is called for a connection the \fBmd\fR parameter is
NULL. In some circumstances the callback will be called a second time. In that
case the server will have specified a ciphersuite to use already and the PSK
must be compatible with the digest for that ciphersuite. The digest will be
given in \fBmd\fR. The PSK returned by the callback is allowed to be different
between the first and second time it is called.
.PP
On successful completion the callback must store a pointer to an identifier for
the PSK in \fB*id\fR. The identifier length in bytes should be stored in \fB*idlen\fR.
The memory pointed to by \fB*id\fR remains owned by the application and should
be freed by it as required at any point after the handshake is complete.
.PP
Additionally the callback should store a pointer to an SSL_SESSION object in
\&\fB*sess\fR. This is used as the basis for the PSK, and should, at a minimum, have
the following fields set:
.IP "The master key" 4
.IX Item "The master key"
This can be set via a call to \fBSSL_SESSION_set1_master_key\fR\|(3).
.IP "A ciphersuite" 4
.IX Item "A ciphersuite"
Only the handshake digest associated with the ciphersuite is relevant for the
PSK (the server may go on to negotiate any ciphersuite which is compatible with
the digest). The application can use any TLSv1.3 ciphersuite. If \fBmd\fR is
not NULL the handshake digest for the ciphersuite should be the same.
The ciphersuite can be set via a call to <\fBSSL_SESSION_set_cipher\fR\|(3)>. The
handshake digest of an SSL_CIPHER object can be checked using
<\fBSSL_CIPHER_get_handshake_digest\fR\|(3)>.
.IP "The protocol version" 4
.IX Item "The protocol version"
This can be set via a call to \fBSSL_SESSION_set_protocol_version\fR\|(3) and should
be TLS1_3_VERSION.
.PP
Additionally the maximum early data value should be set via a call to
\&\fBSSL_SESSION_set_max_early_data\fR\|(3) if the PSK will be used for sending early
data.
.PP
Alternatively an SSL_SESSION created from a previous non-PSK handshake may also
be used as the basis for a PSK.
.PP
Ownership of the SSL_SESSION object is passed to the OpenSSL library and so it
should not be freed by the application.
.PP
It is also possible for the callback to succeed but not supply a PSK. In this
case no PSK will be sent to the server but the handshake will continue. To do
this the callback should return successfully and ensure that \fB*sess\fR is
NULL. The contents of \fB*id\fR and \fB*idlen\fR will be ignored.
.PP
A client application wishing to use PSK ciphersuites for TLSv1.2 and below must
provide a different callback function. This function will be called when the
client is sending the ClientKeyExchange message to the server.
.PP
The purpose of the callback function is to select the PSK identity and
the pre-shared key to use during the connection setup phase.
.PP
The callback is set using functions \fBSSL_CTX_set_psk_client_callback()\fR
or \fBSSL_set_psk_client_callback()\fR. The callback function is given the
connection in parameter \fBssl\fR, a \fBNULL\fR\-terminated PSK identity hint
sent by the server in parameter \fBhint\fR, a buffer \fBidentity\fR of
length \fBmax_identity_len\fR bytes where the resulting
\&\fBNUL\fR\-terminated identity is to be stored, and a buffer \fBpsk\fR of
length \fBmax_psk_len\fR bytes where the resulting pre-shared key is to
be stored.
.PP
The callback for use in TLSv1.2 will also work in TLSv1.3 although it is
recommended to use \fBSSL_CTX_set_psk_use_session_callback()\fR
or \fBSSL_set_psk_use_session_callback()\fR for this purpose instead. If TLSv1.3 has
been negotiated then OpenSSL will first check to see if a callback has been set
via \fBSSL_CTX_set_psk_use_session_callback()\fR or \fBSSL_set_psk_use_session_callback()\fR
and it will use that in preference. If no such callback is present then it will
check to see if a callback has been set via \fBSSL_CTX_set_psk_client_callback()\fR or
\&\fBSSL_set_psk_client_callback()\fR and use that. In this case the \fBhint\fR value will
always be NULL and the handshake digest will default to SHA\-256 for any returned
PSK. TLSv1.3 early data exchanges are possible in PSK connections only with the
\&\fBSSL_psk_use_session_cb_func\fR callback, and are not possible with the
\&\fBSSL_psk_client_cb_func\fR callback.
.SH NOTES
.IX Header "NOTES"
Note that parameter \fBhint\fR given to the callback may be \fBNULL\fR.
.PP
A connection established via a TLSv1.3 PSK will appear as if session resumption
has occurred so that \fBSSL_session_reused\fR\|(3) will return true.
.PP
There are no known security issues with sharing the same PSK between TLSv1.2 (or
below) and TLSv1.3. However, the RFC has this note of caution:
.PP
"While there is no known way in which the same PSK might produce related output
in both versions, only limited analysis has been done.  Implementations can
ensure safety from cross-protocol related output by not reusing PSKs between
TLS 1.3 and TLS 1.2."
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Return values from the \fBSSL_psk_client_cb_func\fR callback are interpreted as
follows:
.PP
On success (callback found a PSK identity and a pre-shared key to use)
the length (> 0) of \fBpsk\fR in bytes is returned.
.PP
Otherwise or on errors the callback should return 0. In this case
the connection setup fails.
.PP
The SSL_psk_use_session_cb_func callback should return 1 on success or 0 on
failure. In the event of failure the connection setup fails.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CTX_set_psk_find_session_callback\fR\|(3),
\&\fBSSL_set_psk_find_session_callback\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBSSL_CTX_set_psk_use_session_callback()\fR and \fBSSL_set_psk_use_session_callback()\fR
were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
