.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_GET_DEFAULT_DIGEST_NID 3"
.TH EVP_PKEY_GET_DEFAULT_DIGEST_NID 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_get_default_digest_nid \- get default signature digest
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/evp.h>
\& int EVP_PKEY_get_default_digest_nid(EVP_PKEY *pkey, int *pnid);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_get_default_digest_nid()\fR function sets \fBpnid\fR to the default
message digest NID for the public key signature operations associated with key
\&\fBpkey\fR. Note that some signature algorithms (i.e. Ed25519 and Ed448) do not use
a digest during signing. In this case \fBpnid\fR will be set to NID_undef.
.SH NOTES
.IX Header "NOTES"
For all current standard OpenSSL public key algorithms SHA1 is returned.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBEVP_PKEY_get_default_digest_nid()\fR function returns 1 if the message digest
is advisory (that is other digests can be used) and 2 if it is mandatory (other
digests can not be used).  It returns 0 or a negative value for failure. In
particular a return value of \-2 indicates the operation is not supported by the
public key algorithm.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
.SH HISTORY
.IX Header "HISTORY"
This function was added in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
