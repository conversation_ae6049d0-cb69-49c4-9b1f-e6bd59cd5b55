<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>DH_get0_pqg</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>DH_get0_pqg, DH_set0_pqg, DH_get0_key, DH_set0_key, DH_get0_p, DH_get0_q, DH_get0_g, DH_get0_priv_key, DH_get0_pub_key, DH_clear_flags, DH_test_flags, DH_set_flags, DH_get0_engine, DH_get_length, DH_set_length - Routines for getting and setting data in a DH object</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/dh.h&gt;

void DH_get0_pqg(const DH *dh,
                 const BIGNUM **p, const BIGNUM **q, const BIGNUM **g);
int DH_set0_pqg(DH *dh, BIGNUM *p, BIGNUM *q, BIGNUM *g);
void DH_get0_key(const DH *dh,
                 const BIGNUM **pub_key, const BIGNUM **priv_key);
int DH_set0_key(DH *dh, BIGNUM *pub_key, BIGNUM *priv_key);
const BIGNUM *DH_get0_p(const DH *dh);
const BIGNUM *DH_get0_q(const DH *dh);
const BIGNUM *DH_get0_g(const DH *dh);
const BIGNUM *DH_get0_priv_key(const DH *dh);
const BIGNUM *DH_get0_pub_key(const DH *dh);
void DH_clear_flags(DH *dh, int flags);
int DH_test_flags(const DH *dh, int flags);
void DH_set_flags(DH *dh, int flags);
ENGINE *DH_get0_engine(DH *d);
long DH_get_length(const DH *dh);
int DH_set_length(DH *dh, long length);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>A DH object contains the parameters <b>p</b>, <b>q</b> and <b>g</b>. Note that the <b>q</b> parameter is optional. It also contains a public key (<b>pub_key</b>) and (optionally) a private key (<b>priv_key</b>).</p>

<p>The <b>p</b>, <b>q</b> and <b>g</b> parameters can be obtained by calling DH_get0_pqg(). If the parameters have not yet been set then <b>*p</b>, <b>*q</b> and <b>*g</b> will be set to NULL. Otherwise they are set to pointers to their respective values. These point directly to the internal representations of the values and therefore should not be freed directly. Any of the out parameters <b>p</b>, <b>q</b>, and <b>g</b> can be NULL, in which case no value will be returned for that parameter.</p>

<p>The <b>p</b>, <b>q</b> and <b>g</b> values can be set by calling DH_set0_pqg() and passing the new values for <b>p</b>, <b>q</b> and <b>g</b> as parameters to the function. Calling this function transfers the memory management of the values to the DH object, and therefore the values that have been passed in should not be freed directly after this function has been called. The <b>q</b> parameter may be NULL.</p>

<p>To get the public and private key values use the DH_get0_key() function. A pointer to the public key will be stored in <b>*pub_key</b>, and a pointer to the private key will be stored in <b>*priv_key</b>. Either may be NULL if they have not been set yet, although if the private key has been set then the public key must be. The values point to the internal representation of the public key and private key values. This memory should not be freed directly. Any of the out parameters <b>pub_key</b> and <b>priv_key</b> can be NULL, in which case no value will be returned for that parameter.</p>

<p>The public and private key values can be set using DH_set0_key(). Either parameter may be NULL, which means the corresponding DH field is left untouched. As with DH_set0_pqg() this function transfers the memory management of the key values to the DH object, and therefore they should not be freed directly after this function has been called.</p>

<p>Any of the values <b>p</b>, <b>q</b>, <b>g</b>, <b>priv_key</b>, and <b>pub_key</b> can also be retrieved separately by the corresponding function DH_get0_p(), DH_get0_q(), DH_get0_g(), DH_get0_priv_key(), and DH_get0_pub_key(), respectively.</p>

<p>DH_set_flags() sets the flags in the <b>flags</b> parameter on the DH object. Multiple flags can be passed in one go (bitwise ORed together). Any flags that are already set are left set. DH_test_flags() tests to see whether the flags passed in the <b>flags</b> parameter are currently set in the DH object. Multiple flags can be tested in one go. All flags that are currently set are returned, or zero if none of the flags are set. DH_clear_flags() clears the specified flags within the DH object.</p>

<p>DH_get0_engine() returns a handle to the ENGINE that has been set for this DH object, or NULL if no such ENGINE has been set.</p>

<p>The DH_get_length() and DH_set_length() functions get and set the optional length parameter associated with this DH object. If the length is nonzero then it is used, otherwise it is ignored. The <b>length</b> parameter indicates the length of the secret exponent (private key) in bits.</p>

<h1 id="NOTES">NOTES</h1>

<p>Values retrieved with DH_get0_key() are owned by the DH object used in the call and may therefore <i>not</i> be passed to DH_set0_key(). If needed, duplicate the received value using BN_dup() and pass the duplicate. The same applies to DH_get0_pqg() and DH_set0_pqg().</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>DH_set0_pqg() and DH_set0_key() return 1 on success or 0 on failure.</p>

<p>DH_get0_p(), DH_get0_q(), DH_get0_g(), DH_get0_priv_key(), and DH_get0_pub_key() return the respective value, or NULL if it is unset.</p>

<p>DH_test_flags() returns the current state of the flags in the DH object.</p>

<p>DH_get0_engine() returns the ENGINE set for the DH object or NULL if no ENGINE has been set.</p>

<p>DH_get_length() returns the length of the secret exponent (private key) in bits, or zero if no such length has been explicitly set.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/DH_new.html">DH_new(3)</a>, <a href="../man3/DH_new.html">DH_new(3)</a>, <a href="../man3/DH_generate_parameters.html">DH_generate_parameters(3)</a>, <a href="../man3/DH_generate_key.html">DH_generate_key(3)</a>, <a href="../man3/DH_set_method.html">DH_set_method(3)</a>, <a href="../man3/DH_size.html">DH_size(3)</a>, <a href="../man3/DH_meth_new.html">DH_meth_new(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The functions described here were added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


