.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_MALLOC 3"
.TH OPENSSL_MALLOC 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_malloc_init,
OPENSSL_malloc, OPENSSL_zalloc, OPENSSL_realloc, OPENSSL_free,
OPENSSL_clear_realloc, OPENSSL_clear_free, OPENSSL_cleanse,
CRYPTO_malloc, CRYPTO_zalloc, CRYPTO_realloc, CRYPTO_free,
OPENSSL_strdup, OPENSSL_strndup,
OPENSSL_memdup, OPENSSL_strlcpy, OPENSSL_strlcat,
OPENSSL_hexstr2buf, OPENSSL_buf2hexstr, OPENSSL_hexchar2int,
CRYPTO_strdup, CRYPTO_strndup,
OPENSSL_mem_debug_push, OPENSSL_mem_debug_pop,
CRYPTO_mem_debug_push, CRYPTO_mem_debug_pop,
CRYPTO_clear_realloc, CRYPTO_clear_free,
CRYPTO_get_mem_functions, CRYPTO_set_mem_functions,
CRYPTO_get_alloc_counts,
CRYPTO_set_mem_debug, CRYPTO_mem_ctrl,
CRYPTO_mem_leaks, CRYPTO_mem_leaks_fp, CRYPTO_mem_leaks_cb,
OPENSSL_MALLOC_FAILURES,
OPENSSL_MALLOC_FD
\&\- Memory allocation functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crypto.h>
\&
\& int OPENSSL_malloc_init(void)
\&
\& void *OPENSSL_malloc(size_t num)
\& void *OPENSSL_zalloc(size_t num)
\& void *OPENSSL_realloc(void *addr, size_t num)
\& void OPENSSL_free(void *addr)
\& char *OPENSSL_strdup(const char *str)
\& char *OPENSSL_strndup(const char *str, size_t s)
\& size_t OPENSSL_strlcat(char *dst, const char *src, size_t size);
\& size_t OPENSSL_strlcpy(char *dst, const char *src, size_t size);
\& void *OPENSSL_memdup(void *data, size_t s)
\& void *OPENSSL_clear_realloc(void *p, size_t old_len, size_t num)
\& void OPENSSL_clear_free(void *str, size_t num)
\& void OPENSSL_cleanse(void *ptr, size_t len);
\&
\& unsigned char *OPENSSL_hexstr2buf(const char *str, long *len);
\& char *OPENSSL_buf2hexstr(const unsigned char *buffer, long len);
\& int OPENSSL_hexchar2int(unsigned char c);
\&
\& void *CRYPTO_malloc(size_t num, const char *file, int line)
\& void *CRYPTO_zalloc(size_t num, const char *file, int line)
\& void *CRYPTO_realloc(void *p, size_t num, const char *file, int line)
\& void CRYPTO_free(void *str, const char *, int)
\& char *CRYPTO_strdup(const char *p, const char *file, int line)
\& char *CRYPTO_strndup(const char *p, size_t num, const char *file, int line)
\& void *CRYPTO_clear_realloc(void *p, size_t old_len, size_t num,
\&                            const char *file, int line)
\& void CRYPTO_clear_free(void *str, size_t num, const char *, int)
\&
\& void CRYPTO_get_mem_functions(
\&         void *(**m)(size_t, const char *, int),
\&         void *(**r)(void *, size_t, const char *, int),
\&         void (**f)(void *, const char *, int))
\& int CRYPTO_set_mem_functions(
\&         void *(*m)(size_t, const char *, int),
\&         void *(*r)(void *, size_t, const char *, int),
\&         void (*f)(void *, const char *, int))
\&
\& void CRYPTO_get_alloc_counts(int *m, int *r, int *f)
\&
\& int CRYPTO_set_mem_debug(int onoff)
\&
\& env OPENSSL_MALLOC_FAILURES=... <application>
\& env OPENSSL_MALLOC_FD=... <application>
\&
\& int CRYPTO_mem_ctrl(int mode);
\&
\& int OPENSSL_mem_debug_push(const char *info)
\& int OPENSSL_mem_debug_pop(void);
\&
\& int CRYPTO_mem_debug_push(const char *info, const char *file, int line);
\& int CRYPTO_mem_debug_pop(void);
\&
\& int CRYPTO_mem_leaks(BIO *b);
\& int CRYPTO_mem_leaks_fp(FILE *fp);
\& int CRYPTO_mem_leaks_cb(int (*cb)(const char *str, size_t len, void *u),
\&                         void *u);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
OpenSSL memory allocation is handled by the \fBOPENSSL_xxx\fR API. These are
generally macro's that add the standard C \fB_\|_FILE_\|_\fR and \fB_\|_LINE_\|_\fR
parameters and call a lower-level \fBCRYPTO_xxx\fR API.
Some functions do not add those parameters, but exist for consistency.
.PP
\&\fBOPENSSL_malloc_init()\fR does nothing and does not need to be called. It is
included for compatibility with older versions of OpenSSL.
.PP
\&\fBOPENSSL_malloc()\fR, \fBOPENSSL_realloc()\fR, and \fBOPENSSL_free()\fR are like the
C \fBmalloc()\fR, \fBrealloc()\fR, and \fBfree()\fR functions.
\&\fBOPENSSL_zalloc()\fR calls \fBmemset()\fR to zero the memory before returning.
.PP
\&\fBOPENSSL_clear_realloc()\fR and \fBOPENSSL_clear_free()\fR should be used
when the buffer at \fBaddr\fR holds sensitive information.
The old buffer is filled with zero's by calling \fBOPENSSL_cleanse()\fR
before ultimately calling \fBOPENSSL_free()\fR.
.PP
\&\fBOPENSSL_cleanse()\fR fills \fBptr\fR of size \fBlen\fR with a string of 0's.
Use \fBOPENSSL_cleanse()\fR with care if the memory is a mapping of a file.
If the storage controller uses write compression, then it's possible
that sensitive tail bytes will survive zeroization because the block of
zeros will be compressed. If the storage controller uses wear leveling,
then the old sensitive data will not be overwritten; rather, a block of
0's will be written at a new physical location.
.PP
\&\fBOPENSSL_strdup()\fR, \fBOPENSSL_strndup()\fR and \fBOPENSSL_memdup()\fR are like the
equivalent C functions, except that memory is allocated by calling the
\&\fBOPENSSL_malloc()\fR and should be released by calling \fBOPENSSL_free()\fR.
.PP
\&\fBOPENSSL_strlcpy()\fR,
\&\fBOPENSSL_strlcat()\fR and \fBOPENSSL_strnlen()\fR are equivalents of the common C
library functions and are provided for portability.
.PP
\&\fBOPENSSL_hexstr2buf()\fR parses \fBstr\fR as a hex string and returns a
pointer to the parsed value. The memory is allocated by calling
\&\fBOPENSSL_malloc()\fR and should be released by calling \fBOPENSSL_free()\fR.
If \fBlen\fR is not NULL, it is filled in with the output length.
Colons between two-character hex "bytes" are ignored.
An odd number of hex digits is an error.
.PP
\&\fBOPENSSL_buf2hexstr()\fR takes the specified buffer and length, and returns
a hex string for value, or NULL on error.
\&\fBBuffer\fR cannot be NULL; if \fBlen\fR is 0 an empty string is returned.
.PP
\&\fBOPENSSL_hexchar2int()\fR converts a character to the hexadecimal equivalent,
or returns \-1 on error.
.PP
If no allocations have been done, it is possible to "swap out" the default
implementations for \fBOPENSSL_malloc()\fR, OPENSSL_realloc and \fBOPENSSL_free()\fR
and replace them with alternate versions (hooks).
\&\fBCRYPTO_get_mem_functions()\fR function fills in the given arguments with the
function pointers for the current implementations.
With \fBCRYPTO_set_mem_functions()\fR, you can specify a different set of functions.
If any of \fBm\fR, \fBr\fR, or \fBf\fR are NULL, then the function is not changed.
.PP
The default implementation can include some debugging capability (if enabled
at build-time).
This adds some overhead by keeping a list of all memory allocations, and
removes items from the list when they are free'd.
This is most useful for identifying memory leaks.
\&\fBCRYPTO_set_mem_debug()\fR turns this tracking on and off.  In order to have
any effect, is must be called before any of the allocation functions
(e.g., \fBCRYPTO_malloc()\fR) are called, and is therefore normally one of the
first lines of \fBmain()\fR in an application.
\&\fBCRYPTO_mem_ctrl()\fR provides fine-grained control of memory leak tracking.
To enable tracking call \fBCRYPTO_mem_ctrl()\fR with a \fBmode\fR argument of
the \fBCRYPTO_MEM_CHECK_ON\fR.
To disable tracking call \fBCRYPTO_mem_ctrl()\fR with a \fBmode\fR argument of
the \fBCRYPTO_MEM_CHECK_OFF\fR.
.PP
While checking memory, it can be useful to store additional context
about what is being done.
For example, identifying the field names when parsing a complicated
data structure.
\&\fBOPENSSL_mem_debug_push()\fR (which calls \fBCRYPTO_mem_debug_push()\fR)
attaches an identifying string to the allocation stack.
This must be a global or other static string; it is not copied.
\&\fBOPENSSL_mem_debug_pop()\fR removes identifying state from the stack.
.PP
At the end of the program, calling \fBCRYPTO_mem_leaks()\fR or
\&\fBCRYPTO_mem_leaks_fp()\fR will report all "leaked" memory, writing it
to the specified BIO \fBb\fR or FILE \fBfp\fR. These functions return 1 if
there are no leaks, 0 if there are leaks and \-1 if an error occurred.
.PP
\&\fBCRYPTO_mem_leaks_cb()\fR does the same as \fBCRYPTO_mem_leaks()\fR, but instead
of writing to a given BIO, the callback function is called for each
output string with the string, length, and userdata \fBu\fR as the callback
parameters.
.PP
If the library is built with the \f(CW\*(C`crypto\-mdebug\*(C'\fR option, then one
function, \fBCRYPTO_get_alloc_counts()\fR, and two additional environment
variables, \fBOPENSSL_MALLOC_FAILURES\fR and \fBOPENSSL_MALLOC_FD\fR,
are available.
.PP
The function \fBCRYPTO_get_alloc_counts()\fR fills in the number of times
each of \fBCRYPTO_malloc()\fR, \fBCRYPTO_realloc()\fR, and \fBCRYPTO_free()\fR have been
called, into the values pointed to by \fBmcount\fR, \fBrcount\fR, and \fBfcount\fR,
respectively.  If a pointer is NULL, then the corresponding count is not stored.
.PP
The variable
\&\fBOPENSSL_MALLOC_FAILURES\fR controls how often allocations should fail.
It is a set of fields separated by semicolons, which each field is a count
(defaulting to zero) and an optional atsign and percentage (defaulting
to 100).  If the count is zero, then it lasts forever.  For example,
\&\f(CW\*(C`100;@25\*(C'\fR or \f(CW\*(C`100@0;0@25\*(C'\fR means the first 100 allocations pass, then all
other allocations (until the program exits or crashes) have a 25% chance of
failing.
.PP
If the variable \fBOPENSSL_MALLOC_FD\fR is parsed as a positive integer, then
it is taken as an open file descriptor, and a record of all allocations is
written to that descriptor.  If an allocation will fail, and the platform
supports it, then a backtrace will be written to the descriptor.  This can
be useful because a malloc may fail but not be checked, and problems will
only occur later.  The following example in classic shell syntax shows how
to use this (will not work on all platforms):
.PP
.Vb 5
\&  OPENSSL_MALLOC_FAILURES=\*(Aq200;@10\*(Aq
\&  export OPENSSL_MALLOC_FAILURES
\&  OPENSSL_MALLOC_FD=3
\&  export OPENSSL_MALLOC_FD
\&  ...app invocation... 3>/tmp/log$$
.Ve
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOPENSSL_malloc_init()\fR, \fBOPENSSL_free()\fR, \fBOPENSSL_clear_free()\fR
\&\fBCRYPTO_free()\fR, \fBCRYPTO_clear_free()\fR and \fBCRYPTO_get_mem_functions()\fR
return no value.
.PP
\&\fBCRYPTO_mem_leaks()\fR, \fBCRYPTO_mem_leaks_fp()\fR and \fBCRYPTO_mem_leaks_cb()\fR return 1 if
there are no leaks, 0 if there are leaks and \-1 if an error occurred.
.PP
\&\fBOPENSSL_malloc()\fR, \fBOPENSSL_zalloc()\fR, \fBOPENSSL_realloc()\fR,
\&\fBOPENSSL_clear_realloc()\fR,
\&\fBCRYPTO_malloc()\fR, \fBCRYPTO_zalloc()\fR, \fBCRYPTO_realloc()\fR,
\&\fBCRYPTO_clear_realloc()\fR,
\&\fBOPENSSL_buf2hexstr()\fR, \fBOPENSSL_hexstr2buf()\fR,
\&\fBOPENSSL_strdup()\fR, and \fBOPENSSL_strndup()\fR
return a pointer to allocated memory or NULL on error.
.PP
\&\fBCRYPTO_set_mem_functions()\fR and \fBCRYPTO_set_mem_debug()\fR
return 1 on success or 0 on failure (almost
always because allocations have already happened).
.PP
\&\fBCRYPTO_mem_ctrl()\fR returns \-1 if an error occurred, otherwise the
previous value of the mode.
.PP
\&\fBOPENSSL_mem_debug_push()\fR and \fBOPENSSL_mem_debug_pop()\fR
return 1 on success or 0 on failure.
.SH NOTES
.IX Header "NOTES"
While it's permitted to swap out only a few and not all the functions
with \fBCRYPTO_set_mem_functions()\fR, it's recommended to swap them all out
at once.  \fIThis applies specially if OpenSSL was built with the
configuration option\fR \f(CW\*(C`crypto\-mdebug\*(C'\fR \fIenabled.  In case, swapping out
only, say, the \fR\f(BImalloc()\fR\fI implementation is outright dangerous.\fR
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
