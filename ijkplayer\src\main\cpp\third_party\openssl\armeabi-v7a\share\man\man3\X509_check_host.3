.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_CHECK_HOST 3"
.TH X509_CHECK_HOST 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_check_host, X509_check_email, X509_check_ip, X509_check_ip_asc \- X.509 certificate matching
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509v3.h>
\&
\& int X509_check_host(X509 *, const char *name, size_t namelen,
\&                     unsigned int flags, char **peername);
\& int X509_check_email(X509 *, const char *address, size_t addresslen,
\&                      unsigned int flags);
\& int X509_check_ip(X509 *, const unsigned char *address, size_t addresslen,
\&                   unsigned int flags);
\& int X509_check_ip_asc(X509 *, const char *address, unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The certificate matching functions are used to check whether a
certificate matches a given hostname, email address, or IP address.
The validity of the certificate and its trust level has to be checked by
other means.
.PP
\&\fBX509_check_host()\fR checks if the certificate Subject Alternative
Name (SAN) or Subject CommonName (CN) matches the specified hostname, 
which must be encoded in the preferred name syntax described
in section 3.5 of RFC 1034.  By default, wildcards are supported
and they match  only in the left-most label; but they may match
part of that label with an explicit prefix or suffix.  For example,
by default, the host \fBname\fR "www.example.com" would match a
certificate with a SAN or CN value of "*.example.com", "w*.example.com"
or "*w.example.com".
.PP
Per section 6.4.2 of RFC 6125, \fBname\fR values representing international
domain names must be given in A\-label form.  The \fBnamelen\fR argument
must be the number of characters in the name string or zero in which
case the length is calculated with strlen(\fBname\fR).  When \fBname\fR starts
with a dot (e.g. ".example.com"), it will be matched by a certificate
valid for any sub-domain of \fBname\fR, (see also
\&\fBX509_CHECK_FLAG_SINGLE_LABEL_SUBDOMAINS\fR below).
.PP
When the certificate is matched, and \fBpeername\fR is not NULL, a
pointer to a copy of the matching SAN or CN from the peer certificate
is stored at the address passed in \fBpeername\fR.  The application
is responsible for freeing the peername via \fBOPENSSL_free()\fR when it
is no longer needed.
.PP
\&\fBX509_check_email()\fR checks if the certificate matches the specified
email \fBaddress\fR.  Only the mailbox syntax of RFC 822 is supported,
comments are not allowed, and no attempt is made to normalize quoted
characters.  The \fBaddresslen\fR argument must be the number of
characters in the address string or zero in which case the length
is calculated with strlen(\fBaddress\fR).
.PP
\&\fBX509_check_ip()\fR checks if the certificate matches a specified IPv4 or
IPv6 address.  The \fBaddress\fR array is in binary format, in network
byte order.  The length is either 4 (IPv4) or 16 (IPv6).  Only
explicitly marked addresses in the certificates are considered; IP
addresses stored in DNS names and Common Names are ignored.
.PP
\&\fBX509_check_ip_asc()\fR is similar, except that the NUL-terminated
string \fBaddress\fR is first converted to the internal representation.
.PP
The \fBflags\fR argument is usually 0.  It can be the bitwise OR of the
flags:
.IP \fBX509_CHECK_FLAG_ALWAYS_CHECK_SUBJECT\fR, 4
.IX Item "X509_CHECK_FLAG_ALWAYS_CHECK_SUBJECT,"
.PD 0
.IP \fBX509_CHECK_FLAG_NEVER_CHECK_SUBJECT\fR, 4
.IX Item "X509_CHECK_FLAG_NEVER_CHECK_SUBJECT,"
.IP \fBX509_CHECK_FLAG_NO_WILDCARDS\fR, 4
.IX Item "X509_CHECK_FLAG_NO_WILDCARDS,"
.IP \fBX509_CHECK_FLAG_NO_PARTIAL_WILDCARDS\fR, 4
.IX Item "X509_CHECK_FLAG_NO_PARTIAL_WILDCARDS,"
.IP \fBX509_CHECK_FLAG_MULTI_LABEL_WILDCARDS\fR. 4
.IX Item "X509_CHECK_FLAG_MULTI_LABEL_WILDCARDS."
.IP \fBX509_CHECK_FLAG_SINGLE_LABEL_SUBDOMAINS\fR. 4
.IX Item "X509_CHECK_FLAG_SINGLE_LABEL_SUBDOMAINS."
.PD
.PP
The \fBX509_CHECK_FLAG_ALWAYS_CHECK_SUBJECT\fR flag causes the function
to consider the subject DN even if the certificate contains at least
one subject alternative name of the right type (DNS name or email
address as appropriate); the default is to ignore the subject DN
when at least one corresponding subject alternative names is present.
.PP
The \fBX509_CHECK_FLAG_NEVER_CHECK_SUBJECT\fR flag causes the function to never
consider the subject DN even if the certificate contains no subject alternative
names of the right type (DNS name or email address as appropriate); the default
is to use the subject DN when no corresponding subject alternative names are
present.
If both \fBX509_CHECK_FLAG_ALWAYS_CHECK_SUBJECT\fR and
\&\fBX509_CHECK_FLAG_NEVER_CHECK_SUBJECT\fR are specified, the latter takes
precedence and the subject DN is not checked for matching names.
.PP
If set, \fBX509_CHECK_FLAG_NO_WILDCARDS\fR disables wildcard
expansion; this only applies to \fBX509_check_host\fR.
.PP
If set, \fBX509_CHECK_FLAG_NO_PARTIAL_WILDCARDS\fR suppresses support
for "*" as wildcard pattern in labels that have a prefix or suffix,
such as: "www*" or "*www"; this only applies to \fBX509_check_host\fR.
.PP
If set, \fBX509_CHECK_FLAG_MULTI_LABEL_WILDCARDS\fR allows a "*" that
constitutes the complete label of a DNS name (e.g. "*.example.com")
to match more than one label in \fBname\fR; this flag only applies
to \fBX509_check_host\fR.
.PP
If set, \fBX509_CHECK_FLAG_SINGLE_LABEL_SUBDOMAINS\fR restricts \fBname\fR
values which start with ".", that would otherwise match any sub-domain
in the peer certificate, to only match direct child sub-domains.
Thus, for instance, with this flag set a \fBname\fR of ".example.com"
would match a peer certificate with a DNS name of "www.example.com",
but would not match a peer certificate with a DNS name of
"www.sub.example.com"; this flag only applies to \fBX509_check_host\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The functions return 1 for a successful match, 0 for a failed match
and \-1 for an internal error: typically a memory allocation failure
or an ASN.1 decoding error.
.PP
All functions can also return \-2 if the input is malformed. For example,
\&\fBX509_check_host()\fR returns \-2 if the provided \fBname\fR contains embedded
NULs.
.SH NOTES
.IX Header "NOTES"
Applications are encouraged to use \fBX509_VERIFY_PARAM_set1_host()\fR
rather than explicitly calling \fBX509_check_host\fR\|(3). Host name
checks may be out of scope with the \fBDANE\-EE\fR\|(3) certificate usage,
and the internal checks will be suppressed as appropriate when
DANE support is enabled.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_get_verify_result\fR\|(3),
\&\fBX509_VERIFY_PARAM_set1_host\fR\|(3),
\&\fBX509_VERIFY_PARAM_add1_host\fR\|(3),
\&\fBX509_VERIFY_PARAM_set1_email\fR\|(3),
\&\fBX509_VERIFY_PARAM_set1_ip\fR\|(3),
\&\fBX509_VERIFY_PARAM_set1_ipasc\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.2.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2012\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
