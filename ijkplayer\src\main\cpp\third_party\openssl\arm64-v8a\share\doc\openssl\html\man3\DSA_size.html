<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>DSA_size</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>DSA_size, DSA_bits, DSA_security_bits - get DSA signature size, key bits or security bits</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/dsa.h&gt;

int DSA_size(const DSA *dsa);
int DSA_bits(const DSA *dsa);
int DSA_security_bits(const DSA *dsa);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>DSA_size() returns the maximum size of an ASN.1 encoded DSA signature for key <b>dsa</b> in bytes. It can be used to determine how much memory must be allocated for a DSA signature.</p>

<p><b>dsa-&gt;q</b> must not be <b>NULL</b>.</p>

<p>DSA_bits() returns the number of bits in key <b>dsa</b>: this is the number of bits in the <b>p</b> parameter.</p>

<p>DSA_security_bits() returns the number of security bits of the given <b>dsa</b> key. See <a href="../man3/BN_security_bits.html">BN_security_bits(3)</a>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>DSA_size() returns the signature size in bytes.</p>

<p>DSA_bits() returns the number of bits in the key.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/DSA_new.html">DSA_new(3)</a>, <a href="../man3/DSA_sign.html">DSA_sign(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


