.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12 1"
.TH PKCS12 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-pkcs12,
pkcs12 \- PKCS#12 file utility
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBpkcs12\fR
[\fB\-help\fR]
[\fB\-export\fR]
[\fB\-chain\fR]
[\fB\-inkey file_or_id\fR]
[\fB\-certfile filename\fR]
[\fB\-name name\fR]
[\fB\-caname name\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-noout\fR]
[\fB\-nomacver\fR]
[\fB\-nocerts\fR]
[\fB\-clcerts\fR]
[\fB\-cacerts\fR]
[\fB\-nokeys\fR]
[\fB\-info\fR]
[\fB\-des | \-des3 | \-idea | \-aes128 | \-aes192 | \-aes256 | \-aria128 | \-aria192 | \-aria256 | \-camellia128 | \-camellia192 | \-camellia256 | \-nodes\fR]
[\fB\-noiter\fR]
[\fB\-maciter | \-nomaciter | \-nomac\fR]
[\fB\-twopass\fR]
[\fB\-descert\fR]
[\fB\-certpbe cipher\fR]
[\fB\-keypbe cipher\fR]
[\fB\-macalg digest\fR]
[\fB\-keyex\fR]
[\fB\-keysig\fR]
[\fB\-password arg\fR]
[\fB\-passin arg\fR]
[\fB\-passout arg\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-CAfile file\fR]
[\fB\-CApath dir\fR]
[\fB\-no\-CAfile\fR]
[\fB\-no\-CApath\fR]
[\fB\-CSP name\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBpkcs12\fR command allows PKCS#12 files (sometimes referred to as
PFX files) to be created and parsed. PKCS#12 files are used by several
programs including Netscape, MSIE and MS Outlook.
.SH OPTIONS
.IX Header "OPTIONS"
There are a lot of options the meaning of some depends of whether a PKCS#12 file
is being created or parsed. By default a PKCS#12 file is parsed. A PKCS#12
file can be created by using the \fB\-export\fR option (see below).
.SH "PARSING OPTIONS"
.IX Header "PARSING OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies filename of the PKCS#12 file to be parsed. Standard input is used
by default.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
The filename to write certificates and private keys to, standard output by
default.  They are all written in PEM format.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
The PKCS#12 file (i.e. input file) password source. For more information about
the format of \fBarg\fR see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-passout arg\fR" 4
.IX Item "-passout arg"
Pass phrase source to encrypt any outputted private keys with. For more
information about the format of \fBarg\fR see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-password arg\fR" 4
.IX Item "-password arg"
With \-export, \-password is equivalent to \-passout.
Otherwise, \-password is equivalent to \-passin.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option inhibits output of the keys and certificates to the output file
version of the PKCS#12 file.
.IP \fB\-clcerts\fR 4
.IX Item "-clcerts"
Only output client certificates (not CA certificates).
.IP \fB\-cacerts\fR 4
.IX Item "-cacerts"
Only output CA certificates (not client certificates).
.IP \fB\-nocerts\fR 4
.IX Item "-nocerts"
No certificates at all will be output.
.IP \fB\-nokeys\fR 4
.IX Item "-nokeys"
No private keys will be output.
.IP \fB\-info\fR 4
.IX Item "-info"
Output additional information about the PKCS#12 file structure, algorithms
used and iteration counts.
.IP \fB\-des\fR 4
.IX Item "-des"
Use DES to encrypt private keys before outputting.
.IP \fB\-des3\fR 4
.IX Item "-des3"
Use triple DES to encrypt private keys before outputting, this is the default.
.IP \fB\-idea\fR 4
.IX Item "-idea"
Use IDEA to encrypt private keys before outputting.
.IP "\fB\-aes128\fR, \fB\-aes192\fR, \fB\-aes256\fR" 4
.IX Item "-aes128, -aes192, -aes256"
Use AES to encrypt private keys before outputting.
.IP "\fB\-aria128\fR, \fB\-aria192\fR, \fB\-aria256\fR" 4
.IX Item "-aria128, -aria192, -aria256"
Use ARIA to encrypt private keys before outputting.
.IP "\fB\-camellia128\fR, \fB\-camellia192\fR, \fB\-camellia256\fR" 4
.IX Item "-camellia128, -camellia192, -camellia256"
Use Camellia to encrypt private keys before outputting.
.IP \fB\-nodes\fR 4
.IX Item "-nodes"
Don't encrypt the private keys at all.
.IP \fB\-nomacver\fR 4
.IX Item "-nomacver"
Don't attempt to verify the integrity MAC before reading the file.
.IP \fB\-twopass\fR 4
.IX Item "-twopass"
Prompt for separate integrity and encryption passwords: most software
always assumes these are the same so this option will render such
PKCS#12 files unreadable. Cannot be used in combination with the options
\&\-password, \-passin (if importing) or \-passout (if exporting).
.SH "FILE CREATION OPTIONS"
.IX Header "FILE CREATION OPTIONS"
.IP \fB\-export\fR 4
.IX Item "-export"
This option specifies that a PKCS#12 file will be created rather than
parsed.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
This specifies filename to write the PKCS#12 file to. Standard output is used
by default.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
The filename to read certificates and private keys from, standard input by
default.  They must all be in PEM format. The order doesn't matter but one
private key and its corresponding certificate should be present. If additional
certificates are present they will also be included in the PKCS#12 file.
.IP "\fB\-inkey file_or_id\fR" 4
.IX Item "-inkey file_or_id"
File to read private key from. If not present then a private key must be present
in the input file.
If no engine is used, the argument is taken as a file; if an engine is
specified, the argument is given to the engine as a key identifier.
.IP "\fB\-name friendlyname\fR" 4
.IX Item "-name friendlyname"
This specifies the "friendly name" for the certificate and private key. This
name is typically displayed in list boxes by software importing the file.
.IP "\fB\-certfile filename\fR" 4
.IX Item "-certfile filename"
A filename to read additional certificates from.
.IP "\fB\-caname friendlyname\fR" 4
.IX Item "-caname friendlyname"
This specifies the "friendly name" for other certificates. This option may be
used multiple times to specify names for all certificates in the order they
appear. Netscape ignores friendly names on other certificates whereas MSIE
displays them.
.IP "\fB\-pass arg\fR, \fB\-passout arg\fR" 4
.IX Item "-pass arg, -passout arg"
The PKCS#12 file (i.e. output file) password source. For more information about
the format of \fBarg\fR see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-passin password\fR" 4
.IX Item "-passin password"
Pass phrase source to decrypt any input private keys with. For more information
about the format of \fBarg\fR see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP \fB\-chain\fR 4
.IX Item "-chain"
If this option is present then an attempt is made to include the entire
certificate chain of the user certificate. The standard CA store is used
for this search. If the search fails it is considered a fatal error.
.IP \fB\-descert\fR 4
.IX Item "-descert"
Encrypt the certificate using triple DES, this may render the PKCS#12
file unreadable by some "export grade" software. By default the private
key is encrypted using triple DES and the certificate using 40 bit RC2
unless RC2 is disabled in which case triple DES is used.
.IP "\fB\-keypbe alg\fR, \fB\-certpbe alg\fR" 4
.IX Item "-keypbe alg, -certpbe alg"
These options allow the algorithm used to encrypt the private key and
certificates to be selected. Any PKCS#5 v1.5 or PKCS#12 PBE algorithm name
can be used (see \fBNOTES\fR section for more information). If a cipher name
(as output by the \fBlist-cipher-algorithms\fR command is specified then it
is used with PKCS#5 v2.0. For interoperability reasons it is advisable to only
use PKCS#12 algorithms.
.IP \fB\-keyex|\-keysig\fR 4
.IX Item "-keyex|-keysig"
Specifies that the private key is to be used for key exchange or just signing.
This option is only interpreted by MSIE and similar MS software. Normally
"export grade" software will only allow 512 bit RSA keys to be used for
encryption purposes but arbitrary length keys for signing. The \fB\-keysig\fR
option marks the key for signing only. Signing only keys can be used for
S/MIME signing, authenticode (ActiveX control signing)  and SSL client
authentication, however, due to a bug only MSIE 5.0 and later support
the use of signing only keys for SSL client authentication.
.IP "\fB\-macalg digest\fR" 4
.IX Item "-macalg digest"
Specify the MAC digest algorithm. If not included them SHA1 will be used.
.IP "\fB\-nomaciter\fR, \fB\-noiter\fR" 4
.IX Item "-nomaciter, -noiter"
These options affect the iteration counts on the MAC and key algorithms.
Unless you wish to produce files compatible with MSIE 4.0 you should leave
these options alone.
.Sp
To discourage attacks by using large dictionaries of common passwords the
algorithm that derives keys from passwords can have an iteration count applied
to it: this causes a certain part of the algorithm to be repeated and slows it
down. The MAC is used to check the file integrity but since it will normally
have the same password as the keys and certificates it could also be attacked.
By default both MAC and encryption iteration counts are set to 2048, using
these options the MAC and encryption iteration counts can be set to 1, since
this reduces the file security you should not use these options unless you
really have to. Most software supports both MAC and key iteration counts.
MSIE 4.0 doesn't support MAC iteration counts so it needs the \fB\-nomaciter\fR
option.
.IP \fB\-maciter\fR 4
.IX Item "-maciter"
This option is included for compatibility with previous versions, it used
to be needed to use MAC iterations counts but they are now used by default.
.IP \fB\-nomac\fR 4
.IX Item "-nomac"
Don't attempt to provide the MAC integrity.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP "\fB\-CAfile file\fR" 4
.IX Item "-CAfile file"
CA storage as a file.
.IP "\fB\-CApath dir\fR" 4
.IX Item "-CApath dir"
CA storage as a directory. This directory must be a standard certificate
directory: that is a hash of each subject name (using \fBx509 \-hash\fR) should be
linked to each certificate.
.IP \fB\-no\-CAfile\fR 4
.IX Item "-no-CAfile"
Do not load the trusted CA certificates from the default file location.
.IP \fB\-no\-CApath\fR 4
.IX Item "-no-CApath"
Do not load the trusted CA certificates from the default directory location.
.IP "\fB\-CSP name\fR" 4
.IX Item "-CSP name"
Write \fBname\fR as a Microsoft CSP name.
.SH NOTES
.IX Header "NOTES"
Although there are a large number of options most of them are very rarely
used. For PKCS#12 file parsing only \fB\-in\fR and \fB\-out\fR need to be used
for PKCS#12 file creation \fB\-export\fR and \fB\-name\fR are also used.
.PP
If none of the \fB\-clcerts\fR, \fB\-cacerts\fR or \fB\-nocerts\fR options are present
then all certificates will be output in the order they appear in the input
PKCS#12 files. There is no guarantee that the first certificate present is
the one corresponding to the private key. Certain software which requires
a private key and certificate and assumes the first certificate in the
file is the one corresponding to the private key: this may not always
be the case. Using the \fB\-clcerts\fR option will solve this problem by only
outputting the certificate corresponding to the private key. If the CA
certificates are required then they can be output to a separate file using
the \fB\-nokeys \-cacerts\fR options to just output CA certificates.
.PP
The \fB\-keypbe\fR and \fB\-certpbe\fR algorithms allow the precise encryption
algorithms for private keys and certificates to be specified. Normally
the defaults are fine but occasionally software can't handle triple DES
encrypted private keys, then the option \fB\-keypbe PBE\-SHA1\-RC2\-40\fR can
be used to reduce the private key encryption to 40 bit RC2. A complete
description of all algorithms is contained in the \fBpkcs8\fR manual page.
.PP
Prior 1.1 release passwords containing non-ASCII characters were encoded
in non-compliant manner, which limited interoperability, in first hand
with Windows. But switching to standard-compliant password encoding
poses problem accessing old data protected with broken encoding. For
this reason even legacy encodings is attempted when reading the
data. If you use PKCS#12 files in production application you are advised
to convert the data, because implemented heuristic approach is not
MT-safe, its sole goal is to facilitate the data upgrade with this
utility.
.SH EXAMPLES
.IX Header "EXAMPLES"
Parse a PKCS#12 file and output it to a file:
.PP
.Vb 1
\& openssl pkcs12 \-in file.p12 \-out file.pem
.Ve
.PP
Output only client certificates to a file:
.PP
.Vb 1
\& openssl pkcs12 \-in file.p12 \-clcerts \-out file.pem
.Ve
.PP
Don't encrypt the private key:
.PP
.Vb 1
\& openssl pkcs12 \-in file.p12 \-out file.pem \-nodes
.Ve
.PP
Print some info about a PKCS#12 file:
.PP
.Vb 1
\& openssl pkcs12 \-in file.p12 \-info \-noout
.Ve
.PP
Create a PKCS#12 file:
.PP
.Vb 1
\& openssl pkcs12 \-export \-in file.pem \-out file.p12 \-name "My Certificate"
.Ve
.PP
Include some extra certificates:
.PP
.Vb 2
\& openssl pkcs12 \-export \-in file.pem \-out file.p12 \-name "My Certificate" \e
\&  \-certfile othercerts.pem
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBpkcs8\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
