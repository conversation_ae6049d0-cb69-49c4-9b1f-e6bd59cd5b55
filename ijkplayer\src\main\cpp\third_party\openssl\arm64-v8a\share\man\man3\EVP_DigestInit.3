.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_DIGESTINIT 3"
.TH EVP_DIGESTINIT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_MD_CTX_new, EVP_MD_CTX_reset, EVP_MD_CTX_free, EVP_MD_CTX_copy,
EVP_MD_CTX_copy_ex, EVP_MD_CTX_ctrl, EVP_MD_CTX_set_flags,
EVP_MD_CTX_clear_flags, EVP_MD_CTX_test_flags,
EVP_Digest, EVP_DigestInit_ex, EVP_DigestInit, EVP_DigestUpdate,
EVP_DigestFinal_ex, EVP_DigestFinalXOF, EVP_DigestFinal,
EVP_MD_type, EVP_MD_pkey_type, EVP_MD_size, EVP_MD_block_size, EVP_MD_flags,
EVP_MD_CTX_md, EVP_MD_CTX_type, EVP_MD_CTX_size, EVP_MD_CTX_block_size,
EVP_MD_CTX_md_data, EVP_MD_CTX_update_fn, EVP_MD_CTX_set_update_fn,
EVP_md_null,
EVP_get_digestbyname, EVP_get_digestbynid, EVP_get_digestbyobj,
EVP_MD_CTX_pkey_ctx, EVP_MD_CTX_set_pkey_ctx \- EVP digest routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_MD_CTX *EVP_MD_CTX_new(void);
\& int EVP_MD_CTX_reset(EVP_MD_CTX *ctx);
\& void EVP_MD_CTX_free(EVP_MD_CTX *ctx);
\& void EVP_MD_CTX_ctrl(EVP_MD_CTX *ctx, int cmd, int p1, void* p2);
\& void EVP_MD_CTX_set_flags(EVP_MD_CTX *ctx, int flags);
\& void EVP_MD_CTX_clear_flags(EVP_MD_CTX *ctx, int flags);
\& int EVP_MD_CTX_test_flags(const EVP_MD_CTX *ctx, int flags);
\&
\& int EVP_Digest(const void *data, size_t count, unsigned char *md,
\&                unsigned int *size, const EVP_MD *type, ENGINE *impl);
\& int EVP_DigestInit_ex(EVP_MD_CTX *ctx, const EVP_MD *type, ENGINE *impl);
\& int EVP_DigestUpdate(EVP_MD_CTX *ctx, const void *d, size_t cnt);
\& int EVP_DigestFinal_ex(EVP_MD_CTX *ctx, unsigned char *md, unsigned int *s);
\& int EVP_DigestFinalXOF(EVP_MD_CTX *ctx, unsigned char *md, size_t len);
\&
\& int EVP_MD_CTX_copy_ex(EVP_MD_CTX *out, const EVP_MD_CTX *in);
\&
\& int EVP_DigestInit(EVP_MD_CTX *ctx, const EVP_MD *type);
\& int EVP_DigestFinal(EVP_MD_CTX *ctx, unsigned char *md, unsigned int *s);
\&
\& int EVP_MD_CTX_copy(EVP_MD_CTX *out, EVP_MD_CTX *in);
\&
\& int EVP_MD_type(const EVP_MD *md);
\& int EVP_MD_pkey_type(const EVP_MD *md);
\& int EVP_MD_size(const EVP_MD *md);
\& int EVP_MD_block_size(const EVP_MD *md);
\& unsigned long EVP_MD_flags(const EVP_MD *md);
\&
\& const EVP_MD *EVP_MD_CTX_md(const EVP_MD_CTX *ctx);
\& int EVP_MD_CTX_size(const EVP_MD_CTX *ctx);
\& int EVP_MD_CTX_block_size(const EVP_MD_CTX *ctx);
\& int EVP_MD_CTX_type(const EVP_MD_CTX *ctx);
\& void *EVP_MD_CTX_md_data(const EVP_MD_CTX *ctx);
\& int (*EVP_MD_CTX_update_fn(EVP_MD_CTX *ctx))(EVP_MD_CTX *ctx,
\&                                              const void *data, size_t count);
\& void EVP_MD_CTX_set_update_fn(EVP_MD_CTX *ctx,
\&                               int (*update)(EVP_MD_CTX *ctx,
\&                                             const void *data, size_t count));
\&
\& const EVP_MD *EVP_md_null(void);
\&
\& const EVP_MD *EVP_get_digestbyname(const char *name);
\& const EVP_MD *EVP_get_digestbynid(int type);
\& const EVP_MD *EVP_get_digestbyobj(const ASN1_OBJECT *o);
\&
\& EVP_PKEY_CTX *EVP_MD_CTX_pkey_ctx(const EVP_MD_CTX *ctx);
\& void EVP_MD_CTX_set_pkey_ctx(EVP_MD_CTX *ctx, EVP_PKEY_CTX *pctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP digest routines are a high-level interface to message digests,
and should be used instead of the cipher-specific functions.
.IP \fBEVP_MD_CTX_new()\fR 4
.IX Item "EVP_MD_CTX_new()"
Allocates and returns a digest context.
.IP \fBEVP_MD_CTX_reset()\fR 4
.IX Item "EVP_MD_CTX_reset()"
Resets the digest context \fBctx\fR.  This can be used to reuse an already
existing context.
.IP \fBEVP_MD_CTX_free()\fR 4
.IX Item "EVP_MD_CTX_free()"
Cleans up digest context \fBctx\fR and frees up the space allocated to it.
.IP \fBEVP_MD_CTX_ctrl()\fR 4
.IX Item "EVP_MD_CTX_ctrl()"
Performs digest-specific control actions on context \fBctx\fR. The control command
is indicated in \fBcmd\fR and any additional arguments in \fBp1\fR and \fBp2\fR.
\&\fBEVP_MD_CTX_ctrl()\fR must be called after \fBEVP_DigestInit_ex()\fR. Other restrictions
may apply depending on the control type and digest implementation.
See "CONTROLS" below for more information.
.IP "\fBEVP_MD_CTX_set_flags()\fR, \fBEVP_MD_CTX_clear_flags()\fR, \fBEVP_MD_CTX_test_flags()\fR" 4
.IX Item "EVP_MD_CTX_set_flags(), EVP_MD_CTX_clear_flags(), EVP_MD_CTX_test_flags()"
Sets, clears and tests \fBctx\fR flags.  See "FLAGS" below for more information.
.IP \fBEVP_Digest()\fR 4
.IX Item "EVP_Digest()"
A wrapper around the Digest Init_ex, Update and Final_ex functions.
Hashes \fBcount\fR bytes of data at \fBdata\fR using a digest \fBtype\fR from ENGINE
\&\fBimpl\fR. The digest value is placed in \fBmd\fR and its length is written at \fBsize\fR
if the pointer is not NULL. At most \fBEVP_MAX_MD_SIZE\fR bytes will be written.
If \fBimpl\fR is NULL the default implementation of digest \fBtype\fR is used.
.IP \fBEVP_DigestInit_ex()\fR 4
.IX Item "EVP_DigestInit_ex()"
Sets up digest context \fBctx\fR to use a digest \fBtype\fR from ENGINE \fBimpl\fR.
\&\fBtype\fR will typically be supplied by a function such as \fBEVP_sha1()\fR.  If
\&\fBimpl\fR is NULL then the default implementation of digest \fBtype\fR is used.
.IP \fBEVP_DigestUpdate()\fR 4
.IX Item "EVP_DigestUpdate()"
Hashes \fBcnt\fR bytes of data at \fBd\fR into the digest context \fBctx\fR. This
function can be called several times on the same \fBctx\fR to hash additional
data.
.IP \fBEVP_DigestFinal_ex()\fR 4
.IX Item "EVP_DigestFinal_ex()"
Retrieves the digest value from \fBctx\fR and places it in \fBmd\fR. If the \fBs\fR
parameter is not NULL then the number of bytes of data written (i.e. the
length of the digest) will be written to the integer at \fBs\fR, at most
\&\fBEVP_MAX_MD_SIZE\fR bytes will be written.  After calling \fBEVP_DigestFinal_ex()\fR
no additional calls to \fBEVP_DigestUpdate()\fR can be made, but
\&\fBEVP_DigestInit_ex()\fR can be called to initialize a new digest operation.
.IP \fBEVP_DigestFinalXOF()\fR 4
.IX Item "EVP_DigestFinalXOF()"
Interfaces to extendable-output functions, XOFs, such as SHAKE128 and SHAKE256.
It retrieves the digest value from \fBctx\fR and places it in \fBlen\fR\-sized <B>md.
After calling this function no additional calls to \fBEVP_DigestUpdate()\fR can be
made, but \fBEVP_DigestInit_ex()\fR can be called to initialize a new operation.
.IP \fBEVP_MD_CTX_copy_ex()\fR 4
.IX Item "EVP_MD_CTX_copy_ex()"
Can be used to copy the message digest state from \fBin\fR to \fBout\fR. This is
useful if large amounts of data are to be hashed which only differ in the last
few bytes.
.IP \fBEVP_DigestInit()\fR 4
.IX Item "EVP_DigestInit()"
Behaves in the same way as \fBEVP_DigestInit_ex()\fR except it always uses the
default digest implementation and calls \fBEVP_MD_CTX_reset()\fR.
.IP \fBEVP_DigestFinal()\fR 4
.IX Item "EVP_DigestFinal()"
Similar to \fBEVP_DigestFinal_ex()\fR except the digest context \fBctx\fR is
automatically cleaned up.
.IP \fBEVP_MD_CTX_copy()\fR 4
.IX Item "EVP_MD_CTX_copy()"
Similar to \fBEVP_MD_CTX_copy_ex()\fR except the destination \fBout\fR does not have to
be initialized.
.IP "\fBEVP_MD_size()\fR, \fBEVP_MD_CTX_size()\fR" 4
.IX Item "EVP_MD_size(), EVP_MD_CTX_size()"
Return the size of the message digest when passed an \fBEVP_MD\fR or an
\&\fBEVP_MD_CTX\fR structure, i.e. the size of the hash.
.IP "\fBEVP_MD_block_size()\fR, \fBEVP_MD_CTX_block_size()\fR" 4
.IX Item "EVP_MD_block_size(), EVP_MD_CTX_block_size()"
Return the block size of the message digest when passed an \fBEVP_MD\fR or an
\&\fBEVP_MD_CTX\fR structure.
.IP "\fBEVP_MD_type()\fR, \fBEVP_MD_CTX_type()\fR" 4
.IX Item "EVP_MD_type(), EVP_MD_CTX_type()"
Return the NID of the OBJECT IDENTIFIER representing the given message digest
when passed an \fBEVP_MD\fR structure.  For example, \f(CW\*(C`EVP_MD_type(EVP_sha1())\*(C'\fR
returns \fBNID_sha1\fR. This function is normally used when setting ASN1 OIDs.
.IP \fBEVP_MD_CTX_md_data()\fR 4
.IX Item "EVP_MD_CTX_md_data()"
Return the digest method private data for the passed \fBEVP_MD_CTX\fR.
The space is allocated by OpenSSL and has the size originally set with
\&\fBEVP_MD_meth_set_app_datasize()\fR.
.IP \fBEVP_MD_CTX_md()\fR 4
.IX Item "EVP_MD_CTX_md()"
Returns the \fBEVP_MD\fR structure corresponding to the passed \fBEVP_MD_CTX\fR.
.IP \fBEVP_MD_CTX_set_update_fn()\fR 4
.IX Item "EVP_MD_CTX_set_update_fn()"
Sets the update function for \fBctx\fR to \fBupdate\fR.
This is the function that is called by EVP_DigestUpdate. If not set, the
update function from the \fBEVP_MD\fR type specified at initialization is used.
.IP \fBEVP_MD_CTX_update_fn()\fR 4
.IX Item "EVP_MD_CTX_update_fn()"
Returns the update function for \fBctx\fR.
.IP \fBEVP_MD_flags()\fR 4
.IX Item "EVP_MD_flags()"
Returns the \fBmd\fR flags. Note that these are different from the \fBEVP_MD_CTX\fR
ones. See \fBEVP_MD_meth_set_flags\fR\|(3) for more information.
.IP \fBEVP_MD_pkey_type()\fR 4
.IX Item "EVP_MD_pkey_type()"
Returns the NID of the public key signing algorithm associated with this
digest. For example \fBEVP_sha1()\fR is associated with RSA so this will return
\&\fBNID_sha1WithRSAEncryption\fR. Since digests and signature algorithms are no
longer linked this function is only retained for compatibility reasons.
.IP \fBEVP_md_null()\fR 4
.IX Item "EVP_md_null()"
A "null" message digest that does nothing: i.e. the hash it returns is of zero
length.
.IP "\fBEVP_get_digestbyname()\fR, \fBEVP_get_digestbynid()\fR, \fBEVP_get_digestbyobj()\fR" 4
.IX Item "EVP_get_digestbyname(), EVP_get_digestbynid(), EVP_get_digestbyobj()"
Returns an \fBEVP_MD\fR structure when passed a digest name, a digest \fBNID\fR or an
\&\fBASN1_OBJECT\fR structure respectively.
.IP \fBEVP_MD_CTX_pkey_ctx()\fR 4
.IX Item "EVP_MD_CTX_pkey_ctx()"
Returns the \fBEVP_PKEY_CTX\fR assigned to \fBctx\fR. The returned pointer should not
be freed by the caller.
.IP \fBEVP_MD_CTX_set_pkey_ctx()\fR 4
.IX Item "EVP_MD_CTX_set_pkey_ctx()"
Assigns an \fBEVP_PKEY_CTX\fR to \fBEVP_MD_CTX\fR. This is usually used to provide
a customized \fBEVP_PKEY_CTX\fR to \fBEVP_DigestSignInit\fR\|(3) or
\&\fBEVP_DigestVerifyInit\fR\|(3). The \fBpctx\fR passed to this function should be freed
by the caller. A NULL \fBpctx\fR pointer is also allowed to clear the \fBEVP_PKEY_CTX\fR
assigned to \fBctx\fR. In such case, freeing the cleared \fBEVP_PKEY_CTX\fR or not
depends on how the \fBEVP_PKEY_CTX\fR is created.
.SH CONTROLS
.IX Header "CONTROLS"
\&\fBEVP_MD_CTX_ctrl()\fR can be used to send the following standard controls:
.IP EVP_MD_CTRL_MICALG 4
.IX Item "EVP_MD_CTRL_MICALG"
Gets the digest Message Integrity Check algorithm string. This is used when
creating S/MIME multipart/signed messages, as specified in RFC 3851.
The string value is written to \fBp2\fR.
.IP EVP_MD_CTRL_XOF_LEN 4
.IX Item "EVP_MD_CTRL_XOF_LEN"
This control sets the digest length for extendable output functions to \fBp1\fR.
Sending this control directly should not be necessary, the use of
\&\f(CWEVP_DigestFinalXOF()\fR is preferred.
Currently used by SHAKE.
.SH FLAGS
.IX Header "FLAGS"
\&\fBEVP_MD_CTX_set_flags()\fR, \fBEVP_MD_CTX_clear_flags()\fR and \fBEVP_MD_CTX_test_flags()\fR
can be used the manipulate and test these \fBEVP_MD_CTX\fR flags:
.IP EVP_MD_CTX_FLAG_ONESHOT 4
.IX Item "EVP_MD_CTX_FLAG_ONESHOT"
This flag instructs the digest to optimize for one update only, if possible.
.IP EVP_MD_CTX_FLAG_NO_INIT 4
.IX Item "EVP_MD_CTX_FLAG_NO_INIT"
This flag instructs \fBEVP_DigestInit()\fR and similar not to initialise the
implementation specific data.
.IP EVP_MD_CTX_FLAG_FINALISE 4
.IX Item "EVP_MD_CTX_FLAG_FINALISE"
Some functions such as EVP_DigestSign only finalise copies of internal
contexts so additional data can be included after the finalisation call.
This is inefficient if this functionality is not required, and can be
disabled with this flag.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
.IP "\fBEVP_DigestInit_ex()\fR, \fBEVP_DigestUpdate()\fR, \fBEVP_DigestFinal_ex()\fR" 4
.IX Item "EVP_DigestInit_ex(), EVP_DigestUpdate(), EVP_DigestFinal_ex()"
Returns 1 for
success and 0 for failure.
.IP \fBEVP_MD_CTX_ctrl()\fR 4
.IX Item "EVP_MD_CTX_ctrl()"
Returns 1 if successful or 0 for failure.
.IP \fBEVP_MD_CTX_copy_ex()\fR 4
.IX Item "EVP_MD_CTX_copy_ex()"
Returns 1 if successful or 0 for failure.
.IP "\fBEVP_MD_type()\fR, \fBEVP_MD_pkey_type()\fR" 4
.IX Item "EVP_MD_type(), EVP_MD_pkey_type()"
Returns the NID of the corresponding OBJECT IDENTIFIER or NID_undef if none
exists.
.IP "\fBEVP_MD_size()\fR, \fBEVP_MD_block_size()\fR, \fBEVP_MD_CTX_size()\fR, \fBEVP_MD_CTX_block_size()\fR" 4
.IX Item "EVP_MD_size(), EVP_MD_block_size(), EVP_MD_CTX_size(), EVP_MD_CTX_block_size()"
Returns the digest or block size in bytes.
.IP \fBEVP_md_null()\fR 4
.IX Item "EVP_md_null()"
Returns a pointer to the \fBEVP_MD\fR structure of the "null" message digest.
.IP "\fBEVP_get_digestbyname()\fR, \fBEVP_get_digestbynid()\fR, \fBEVP_get_digestbyobj()\fR" 4
.IX Item "EVP_get_digestbyname(), EVP_get_digestbynid(), EVP_get_digestbyobj()"
Returns either an \fBEVP_MD\fR structure or NULL if an error occurs.
.IP \fBEVP_MD_CTX_set_pkey_ctx()\fR 4
.IX Item "EVP_MD_CTX_set_pkey_ctx()"
This function has no return value.
.SH NOTES
.IX Header "NOTES"
The \fBEVP\fR interface to message digests should almost always be used in
preference to the low-level interfaces. This is because the code then becomes
transparent to the digest used and much more flexible.
.PP
New applications should use the SHA\-2 (such as \fBEVP_sha256\fR\|(3)) or the SHA\-3
digest algorithms (such as \fBEVP_sha3_512\fR\|(3)). The other digest algorithms
are still in common use.
.PP
For most applications the \fBimpl\fR parameter to \fBEVP_DigestInit_ex()\fR will be
set to NULL to use the default digest implementation.
.PP
The functions \fBEVP_DigestInit()\fR, \fBEVP_DigestFinal()\fR and \fBEVP_MD_CTX_copy()\fR are
obsolete but are retained to maintain compatibility with existing code. New
applications should use \fBEVP_DigestInit_ex()\fR, \fBEVP_DigestFinal_ex()\fR and
\&\fBEVP_MD_CTX_copy_ex()\fR because they can efficiently reuse a digest context
instead of initializing and cleaning it up on each call and allow non default
implementations of digests to be specified.
.PP
If digest contexts are not cleaned up after use,
memory leaks will occur.
.PP
\&\fBEVP_MD_CTX_size()\fR, \fBEVP_MD_CTX_block_size()\fR, \fBEVP_MD_CTX_type()\fR,
\&\fBEVP_get_digestbynid()\fR and \fBEVP_get_digestbyobj()\fR are defined as
macros.
.PP
\&\fBEVP_MD_CTX_ctrl()\fR sends commands to message digests for additional configuration
or control.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example digests the data "Test Message\en" and "Hello World\en", using the
digest name passed on the command line.
.PP
.Vb 3
\& #include <stdio.h>
\& #include <string.h>
\& #include <openssl/evp.h>
\&
\& int main(int argc, char *argv[])
\& {
\&     EVP_MD_CTX *mdctx;
\&     const EVP_MD *md;
\&     char mess1[] = "Test Message\en";
\&     char mess2[] = "Hello World\en";
\&     unsigned char md_value[EVP_MAX_MD_SIZE];
\&     unsigned int md_len, i;
\&
\&     if (argv[1] == NULL) {
\&         printf("Usage: mdtest digestname\en");
\&         exit(1);
\&     }
\&
\&     md = EVP_get_digestbyname(argv[1]);
\&     if (md == NULL) {
\&         printf("Unknown message digest %s\en", argv[1]);
\&         exit(1);
\&     }
\&
\&     mdctx = EVP_MD_CTX_new();
\&     EVP_DigestInit_ex(mdctx, md, NULL);
\&     EVP_DigestUpdate(mdctx, mess1, strlen(mess1));
\&     EVP_DigestUpdate(mdctx, mess2, strlen(mess2));
\&     EVP_DigestFinal_ex(mdctx, md_value, &md_len);
\&     EVP_MD_CTX_free(mdctx);
\&
\&     printf("Digest is: ");
\&     for (i = 0; i < md_len; i++)
\&         printf("%02x", md_value[i]);
\&     printf("\en");
\&
\&     exit(0);
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_MD_meth_new\fR\|(3),
\&\fBdgst\fR\|(1),
\&\fBevp\fR\|(7)
.PP
The full list of digest algorithms are provided below.
.PP
\&\fBEVP_blake2b512\fR\|(3),
\&\fBEVP_md2\fR\|(3),
\&\fBEVP_md4\fR\|(3),
\&\fBEVP_md5\fR\|(3),
\&\fBEVP_mdc2\fR\|(3),
\&\fBEVP_ripemd160\fR\|(3),
\&\fBEVP_sha1\fR\|(3),
\&\fBEVP_sha224\fR\|(3),
\&\fBEVP_sha3_224\fR\|(3),
\&\fBEVP_sm3\fR\|(3),
\&\fBEVP_whirlpool\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBEVP_MD_CTX_create()\fR and \fBEVP_MD_CTX_destroy()\fR functions were renamed to
\&\fBEVP_MD_CTX_new()\fR and \fBEVP_MD_CTX_free()\fR in OpenSSL 1.1.0, respectively.
.PP
The link between digests and signing algorithms was fixed in OpenSSL 1.0 and
later, so now \fBEVP_sha1()\fR can be used with RSA and DSA.
.PP
The \fBEVP_dss1()\fR function was removed in OpenSSL 1.1.0.
.PP
The \fBEVP_MD_CTX_set_pkey_ctx()\fR function was added in 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
