.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CONF_MODULES_FREE 3"
.TH CONF_MODULES_FREE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CONF_modules_free, CONF_modules_finish, CONF_modules_unload \-
OpenSSL configuration cleanup functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/conf.h>
\&
\& void CONF_modules_finish(void);
\& void CONF_modules_unload(int all);
.Ve
.PP
Deprecated:
.PP
.Vb 3
\& #if OPENSSL_API_COMPAT < 0x10100000L
\& void CONF_modules_free(void)
\& #endif
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCONF_modules_free()\fR closes down and frees up all memory allocated by all
configuration modules.  Normally, in versions of OpenSSL prior to 1.1.0,
applications called
\&\fBCONF_modules_free()\fR at exit to tidy up any configuration performed.
.PP
\&\fBCONF_modules_finish()\fR calls each configuration modules \fBfinish\fR handler
to free up any configuration that module may have performed.
.PP
\&\fBCONF_modules_unload()\fR finishes and unloads configuration modules. If
\&\fBall\fR is set to \fB0\fR only modules loaded from DSOs will be unloads. If
\&\fBall\fR is \fB1\fR all modules, including builtin modules will be unloaded.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
None of the functions return a value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBconfig\fR\|(5), \fBOPENSSL_config\fR\|(3),
\&\fBCONF_modules_load_file\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBCONF_modules_free()\fR was deprecated in OpenSSL 1.1.0; do not use it.
For more information see \fBOPENSSL_init_crypto\fR\|(3).
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2004\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
