.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_GET0_SIGNATURE 3"
.TH X509_GET0_SIGNATURE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_get0_signature, X509_REQ_set0_signature, X509_REQ_set1_signature_algo,
X509_get_signature_nid, X509_get0_tbs_sigalg, X509_REQ_get0_signature, 
X509_REQ_get_signature_nid, X509_CRL_get0_signature, X509_CRL_get_signature_nid, 
X509_get_signature_info, X509_SIG_INFO_get, X509_SIG_INFO_set \- signature information
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& void X509_get0_signature(const ASN1_BIT_STRING **psig,
\&                          const X509_ALGOR **palg,
\&                          const X509 *x);
\& void X509_REQ_set0_signature(X509_REQ *req, ASN1_BIT_STRING *psig);
\& int X509_REQ_set1_signature_algo(X509_REQ *req, X509_ALGOR *palg);
\& int X509_get_signature_nid(const X509 *x);
\& const X509_ALGOR *X509_get0_tbs_sigalg(const X509 *x);
\&
\& void X509_REQ_get0_signature(const X509_REQ *crl,
\&                              const ASN1_BIT_STRING **psig,
\&                              const X509_ALGOR **palg);
\& int X509_REQ_get_signature_nid(const X509_REQ *crl);
\&
\& void X509_CRL_get0_signature(const X509_CRL *crl,
\&                              const ASN1_BIT_STRING **psig,
\&                              const X509_ALGOR **palg);
\& int X509_CRL_get_signature_nid(const X509_CRL *crl);
\&
\& int X509_get_signature_info(X509 *x, int *mdnid, int *pknid, int *secbits,
\&                             uint32_t *flags);
\&
\& int X509_SIG_INFO_get(const X509_SIG_INFO *siginf, int *mdnid, int *pknid,
\&                      int *secbits, uint32_t *flags);
\& void X509_SIG_INFO_set(X509_SIG_INFO *siginf, int mdnid, int pknid,
\&                        int secbits, uint32_t flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_get0_signature()\fR sets \fB*psig\fR to the signature of \fBx\fR and \fB*palg\fR
to the signature algorithm of \fBx\fR. The values returned are internal
pointers which \fBMUST NOT\fR be freed up after the call.
.PP
\&\fBX509_set0_signature()\fR and \fBX509_REQ_set1_signature_algo()\fR are the
equivalent setters for the two values of \fBX509_get0_signature()\fR.
.PP
\&\fBX509_get0_tbs_sigalg()\fR returns the signature algorithm in the signed
portion of \fBx\fR.
.PP
\&\fBX509_get_signature_nid()\fR returns the NID corresponding to the signature
algorithm of \fBx\fR.
.PP
\&\fBX509_REQ_get0_signature()\fR, \fBX509_REQ_get_signature_nid()\fR
\&\fBX509_CRL_get0_signature()\fR and \fBX509_CRL_get_signature_nid()\fR perform the
same function for certificate requests and CRLs.
.PP
\&\fBX509_get_signature_info()\fR retrieves information about the signature of
certificate \fBx\fR. The NID of the signing digest is written to \fB*mdnid\fR,
the public key algorithm to \fB*pknid\fR, the effective security bits to
\&\fB*secbits\fR and flag details to \fB*flags\fR. Any of the parameters can
be set to \fBNULL\fR if the information is not required.
.PP
\&\fBX509_SIG_INFO_get()\fR and \fBX509_SIG_INFO_set()\fR get and set information
about a signature in an \fBX509_SIG_INFO\fR structure. They are only
used by implementations of algorithms which need to set custom
signature information: most applications will never need to call
them.
.SH NOTES
.IX Header "NOTES"
These functions provide lower level access to signatures in certificates
where an application wishes to analyse or generate a signature in a form
where \fBX509_sign()\fR et al is not appropriate (for example a non standard
or unsupported format).
.PP
The security bits returned by \fBX509_get_signature_info()\fR refers to information
available from the certificate signature (such as the signing digest). In some
cases the actual security of the signature is less because the signing
key is less secure: for example a certificate signed using SHA\-512 and a
1024 bit RSA key.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_get_signature_nid()\fR, \fBX509_REQ_get_signature_nid()\fR and
\&\fBX509_CRL_get_signature_nid()\fR return a NID.
.PP
\&\fBX509_get0_signature()\fR, \fBX509_REQ_get0_signature()\fR and
\&\fBX509_CRL_get0_signature()\fR do not return values.
.PP
\&\fBX509_get_signature_info()\fR returns 1 if the signature information
returned is valid or 0 if the information is not available (e.g.
unknown algorithms or malformed parameters).
.PP
\&\fBX509_REQ_set1_signature_algo()\fR returns 0 on success; or 1 on an
error (e.g. null ALGO pointer). X509_REQ_set0_signature does
not return an error value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3),
\&\fBERR_get_error\fR\|(3),
\&\fBX509_CRL_get0_by_serial\fR\|(3),
\&\fBX509_get_ext_d2i\fR\|(3),
\&\fBX509_get_extension_flags\fR\|(3),
\&\fBX509_get_pubkey\fR\|(3),
\&\fBX509_get_subject_name\fR\|(3),
\&\fBX509_get_version\fR\|(3),
\&\fBX509_NAME_add_entry_by_txt\fR\|(3),
\&\fBX509_NAME_ENTRY_get_object\fR\|(3),
\&\fBX509_NAME_get_index_by_NID\fR\|(3),
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBX509_new\fR\|(3),
\&\fBX509_sign\fR\|(3),
\&\fBX509V3_get_d2i\fR\|(3),
\&\fBX509_verify_cert\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The
\&\fBX509_get0_signature()\fR and \fBX509_get_signature_nid()\fR functions were
added in OpenSSL 1.0.2.
.PP
The
\&\fBX509_REQ_get0_signature()\fR, \fBX509_REQ_get_signature_nid()\fR,
\&\fBX509_CRL_get0_signature()\fR and \fBX509_CRL_get_signature_nid()\fR were
added in OpenSSL 1.1.0.
.PP
The \fBX509_REQ_set0_signature()\fR and \fBX509_REQ_set1_signature_algo()\fR
were added in OpenSSL 1.1.1e.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
