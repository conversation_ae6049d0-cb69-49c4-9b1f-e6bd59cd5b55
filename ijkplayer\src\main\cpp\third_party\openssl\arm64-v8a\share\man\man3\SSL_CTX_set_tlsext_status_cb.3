.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_TLSEXT_STATUS_CB 3"
.TH SSL_CTX_SET_TLSEXT_STATUS_CB 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_tlsext_status_cb,
SSL_CTX_get_tlsext_status_cb,
SSL_CTX_set_tlsext_status_arg,
SSL_CTX_get_tlsext_status_arg,
SSL_CTX_set_tlsext_status_type,
SSL_CTX_get_tlsext_status_type,
SSL_set_tlsext_status_type,
SSL_get_tlsext_status_type,
SSL_get_tlsext_status_ocsp_resp,
SSL_set_tlsext_status_ocsp_resp
\&\- OCSP Certificate Status Request functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/tls1.h>
\&
\& long SSL_CTX_set_tlsext_status_cb(SSL_CTX *ctx, int (*callback)(SSL *, void *));
\& long SSL_CTX_get_tlsext_status_cb(SSL_CTX *ctx, int (**callback)(SSL *, void *));
\&
\& long SSL_CTX_set_tlsext_status_arg(SSL_CTX *ctx, void *arg);
\& long SSL_CTX_get_tlsext_status_arg(SSL_CTX *ctx, void **arg);
\&
\& long SSL_CTX_set_tlsext_status_type(SSL_CTX *ctx, int type);
\& long SSL_CTX_get_tlsext_status_type(SSL_CTX *ctx);
\&
\& long SSL_set_tlsext_status_type(SSL *s, int type);
\& long SSL_get_tlsext_status_type(SSL *s);
\&
\& long SSL_get_tlsext_status_ocsp_resp(ssl, unsigned char **resp);
\& long SSL_set_tlsext_status_ocsp_resp(ssl, unsigned char *resp, int len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A client application may request that a server send back an OCSP status response
(also known as OCSP stapling). To do so the client should call the
\&\fBSSL_CTX_set_tlsext_status_type()\fR function prior to the creation of any SSL
objects. Alternatively an application can call the \fBSSL_set_tlsext_status_type()\fR
function on an individual SSL object prior to the start of the handshake.
Currently the only supported type is \fBTLSEXT_STATUSTYPE_ocsp\fR. This value
should be passed in the \fBtype\fR argument. Calling
\&\fBSSL_CTX_get_tlsext_status_type()\fR will return the type \fBTLSEXT_STATUSTYPE_ocsp\fR
previously set via \fBSSL_CTX_set_tlsext_status_type()\fR or \-1 if not set.
.PP
The client should additionally provide a callback function to decide what to do
with the returned OCSP response by calling \fBSSL_CTX_set_tlsext_status_cb()\fR. The
callback function should determine whether the returned OCSP response is
acceptable or not. The callback will be passed as an argument the value
previously set via a call to \fBSSL_CTX_set_tlsext_status_arg()\fR. Note that the
callback will not be called in the event of a handshake where session resumption
occurs (because there are no Certificates exchanged in such a handshake).
The callback previously set via \fBSSL_CTX_set_tlsext_status_cb()\fR can be retrieved
by calling \fBSSL_CTX_get_tlsext_status_cb()\fR, and the argument by calling
\&\fBSSL_CTX_get_tlsext_status_arg()\fR.
.PP
On the client side \fBSSL_get_tlsext_status_type()\fR can be used to determine whether
the client has previously called \fBSSL_set_tlsext_status_type()\fR. It will return
\&\fBTLSEXT_STATUSTYPE_ocsp\fR if it has been called or \-1 otherwise. On the server
side \fBSSL_get_tlsext_status_type()\fR can be used to determine whether the client
requested OCSP stapling. If the client requested it then this function will
return \fBTLSEXT_STATUSTYPE_ocsp\fR, or \-1 otherwise.
.PP
The response returned by the server can be obtained via a call to
\&\fBSSL_get_tlsext_status_ocsp_resp()\fR. The value \fB*resp\fR will be updated to point
to the OCSP response data and the return value will be the length of that data.
Typically a callback would obtain an OCSP_RESPONSE object from this data via a
call to the \fBd2i_OCSP_RESPONSE()\fR function. If the server has not provided any
response data then \fB*resp\fR will be NULL and the return value from
\&\fBSSL_get_tlsext_status_ocsp_resp()\fR will be \-1.
.PP
A server application must also call the \fBSSL_CTX_set_tlsext_status_cb()\fR function
if it wants to be able to provide clients with OCSP Certificate Status
responses. Typically the server callback would obtain the server certificate
that is being sent back to the client via a call to \fBSSL_get_certificate()\fR;
obtain the OCSP response to be sent back; and then set that response data by
calling \fBSSL_set_tlsext_status_ocsp_resp()\fR. A pointer to the response data should
be provided in the \fBresp\fR argument, and the length of that data should be in
the \fBlen\fR argument.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The callback when used on the client side should return a negative value on
error; 0 if the response is not acceptable (in which case the handshake will
fail) or a positive value if it is acceptable.
.PP
The callback when used on the server side should return with either
SSL_TLSEXT_ERR_OK (meaning that the OCSP response that has been set should be
returned), SSL_TLSEXT_ERR_NOACK (meaning that an OCSP response should not be
returned) or SSL_TLSEXT_ERR_ALERT_FATAL (meaning that a fatal error has
occurred).
.PP
\&\fBSSL_CTX_set_tlsext_status_cb()\fR, \fBSSL_CTX_set_tlsext_status_arg()\fR,
\&\fBSSL_CTX_set_tlsext_status_type()\fR, \fBSSL_set_tlsext_status_type()\fR and
\&\fBSSL_set_tlsext_status_ocsp_resp()\fR return 0 on error or 1 on success.
.PP
\&\fBSSL_CTX_get_tlsext_status_type()\fR returns the value previously set by
\&\fBSSL_CTX_set_tlsext_status_type()\fR, or \-1 if not set.
.PP
\&\fBSSL_get_tlsext_status_ocsp_resp()\fR returns the length of the OCSP response data
or \-1 if there is no OCSP response data.
.PP
\&\fBSSL_get_tlsext_status_type()\fR returns \fBTLSEXT_STATUSTYPE_ocsp\fR on the client
side if \fBSSL_set_tlsext_status_type()\fR was previously called, or on the server
side if the client requested OCSP stapling. Otherwise \-1 is returned.
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_get_tlsext_status_type()\fR, \fBSSL_CTX_get_tlsext_status_type()\fR
and \fBSSL_CTX_set_tlsext_status_type()\fR functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
