.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_VERSION_NUMBER 3"
.TH OPENSSL_VERSION_NUMBER 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_VERSION_NUMBER, OPENSSL_VERSION_TEXT, OpenSSL_version,
OpenSSL_version_num \- get OpenSSL version number
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 3
\& #include <openssl/opensslv.h>
\& #define OPENSSL_VERSION_NUMBER 0xnnnnnnnnnL
\& #define OPENSSL_VERSION_TEXT "OpenSSL x.y.z xx XXX xxxx"
\&
\& #include <openssl/crypto.h>
\&
\& unsigned long OpenSSL_version_num();
\& const char *OpenSSL_version(int t);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
OPENSSL_VERSION_NUMBER is a numeric release version identifier:
.PP
.Vb 1
\& MNNFFPPS: major minor fix patch status
.Ve
.PP
The status nibble has one of the values 0 for development, 1 to e for betas
1 to 14, and f for release.
.PP
for example
.PP
.Vb 3
\& 0x000906000 == 0.9.6 dev
\& 0x000906023 == 0.9.6b beta 3
\& 0x00090605f == 0.9.6e release
.Ve
.PP
Versions prior to 0.9.3 have identifiers < 0x0930.
Versions between 0.9.3 and 0.9.5 had a version identifier with this
interpretation:
.PP
.Vb 1
\& MMNNFFRBB major minor fix final beta/patch
.Ve
.PP
for example
.PP
.Vb 2
\& 0x000904100 == 0.9.4 release
\& 0x000905000 == 0.9.5 dev
.Ve
.PP
Version 0.9.5a had an interim interpretation that is like the current one,
except the patch level got the highest bit set, to keep continuity.  The
number was therefore 0x0090581f.
.PP
OPENSSL_VERSION_TEXT is the text variant of the version number and the
release date.  For example,
"OpenSSL 1.0.1a 15 Oct 2015".
.PP
\&\fBOpenSSL_version_num()\fR returns the version number.
.PP
\&\fBOpenSSL_version()\fR returns different strings depending on \fBt\fR:
.IP OPENSSL_VERSION 4
.IX Item "OPENSSL_VERSION"
The text variant of the version number and the release date.  For example,
"OpenSSL 1.0.1a 15 Oct 2015".
.IP OPENSSL_CFLAGS 4
.IX Item "OPENSSL_CFLAGS"
The compiler flags set for the compilation process in the form
"compiler: ..."  if available or "compiler: information not available"
otherwise.
.IP OPENSSL_BUILT_ON 4
.IX Item "OPENSSL_BUILT_ON"
The date of the build process in the form "built on: ..." if available
or "built on: date not available" otherwise.
.IP OPENSSL_PLATFORM 4
.IX Item "OPENSSL_PLATFORM"
The "Configure" target of the library build in the form "platform: ..."
if available or "platform: information not available" otherwise.
.IP OPENSSL_DIR 4
.IX Item "OPENSSL_DIR"
The "OPENSSLDIR" setting of the library build in the form "OPENSSLDIR: "...""
if available or "OPENSSLDIR: N/A" otherwise.
.IP OPENSSL_ENGINES_DIR 4
.IX Item "OPENSSL_ENGINES_DIR"
The "ENGINESDIR" setting of the library build in the form "ENGINESDIR: "...""
if available or "ENGINESDIR: N/A" otherwise.
.PP
For an unknown \fBt\fR, the text "not available" is returned.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOpenSSL_version_num()\fR returns the version number.
.PP
\&\fBOpenSSL_version()\fR returns requested version strings.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
