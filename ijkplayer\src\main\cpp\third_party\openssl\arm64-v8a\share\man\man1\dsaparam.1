.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DSAPARAM 1"
.TH DSAPARAM 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-dsaparam,
dsaparam \- DSA parameter manipulation and generation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl dsaparam\fR
[\fB\-help\fR]
[\fB\-inform DER|PEM\fR]
[\fB\-outform DER|PEM\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-noout\fR]
[\fB\-text\fR]
[\fB\-C\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-genkey\fR]
[\fB\-engine id\fR]
[\fBnumbits\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to manipulate or generate DSA parameter files.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format. The \fBDER\fR option uses an ASN1 DER encoded
form compatible with RFC2459 (PKIX) DSS-Parms that is a SEQUENCE consisting
of p, q and g respectively. The PEM form is the default format: it consists
of the \fBDER\fR format base64 encoded with additional header and footer lines.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format, the options have the same meaning and default
as the \fB\-inform\fR option.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read parameters from or standard input if
this option is not specified. If the \fBnumbits\fR parameter is included then
this option will be ignored.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
This specifies the output filename parameters to. Standard output is used
if this option is not present. The output filename should \fBnot\fR be the same
as the input filename.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option inhibits the output of the encoded version of the parameters.
.IP \fB\-text\fR 4
.IX Item "-text"
This option prints out the DSA parameters in human readable form.
.IP \fB\-C\fR 4
.IX Item "-C"
This option converts the parameters into C code. The parameters can then
be loaded by calling the \fBget_dsaXXX()\fR function.
.IP \fB\-genkey\fR 4
.IX Item "-genkey"
This option will generate a DSA either using the specified or generated
parameters.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP \fBnumbits\fR 4
.IX Item "numbits"
This option specifies that a parameter set should be generated of size
\&\fBnumbits\fR. It must be the last option. If this option is included then
the input file (if any) is ignored.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBdsaparam\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.SH NOTES
.IX Header "NOTES"
PEM format DSA parameters use the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN DSA PARAMETERS\-\-\-\-\-
\& \-\-\-\-\-END DSA PARAMETERS\-\-\-\-\-
.Ve
.PP
DSA parameter generation is a slow process and as a result the same set of
DSA parameters is often used to generate several distinct keys.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBgendsa\fR\|(1), \fBdsa\fR\|(1), \fBgenrsa\fR\|(1),
\&\fBrsa\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
