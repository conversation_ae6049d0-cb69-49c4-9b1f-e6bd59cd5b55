<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_md5</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_md5, EVP_md5_sha1 - MD5 For EVP</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

const EVP_MD *EVP_md5(void);
const EVP_MD *EVP_md5_sha1(void);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>MD5 is a cryptographic hash function standardized in RFC 1321 and designed by Ronald Rivest.</p>

<p>The CMU Software Engineering Institute considers MD5 unsuitable for further use since its security has been severely compromised.</p>

<dl>

<dt id="EVP_md5">EVP_md5()</dt>
<dd>

<p>The MD5 algorithm which produces a 128-bit output from a given input.</p>

</dd>
<dt id="EVP_md5_sha1">EVP_md5_sha1()</dt>
<dd>

<p>A hash algorithm of SSL v3 that combines MD5 with SHA-1 as described in RFC 6101.</p>

<p>WARNING: this algorithm is not intended for non-SSL usage.</p>

</dd>
</dl>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>These functions return a <b>EVP_MD</b> structure that contains the implementation of the symmetric cipher. See <a href="../man3/EVP_MD_meth_new.html">EVP_MD_meth_new(3)</a> for details of the <b>EVP_MD</b> structure.</p>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>IETF RFC 1321.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/evp.html">evp(7)</a>, <a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2019 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


