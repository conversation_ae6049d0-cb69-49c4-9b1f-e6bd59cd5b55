.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_EXTENSION_SUPPORTED 3"
.TH SSL_EXTENSION_SUPPORTED 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_extension_supported,
SSL_CTX_add_custom_ext,
SSL_CTX_add_client_custom_ext, SSL_CTX_add_server_custom_ext,
custom_ext_add_cb, custom_ext_free_cb, custom_ext_parse_cb
\&\- custom TLS extension handling
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& typedef int (*SSL_custom_ext_add_cb_ex) (SSL *s, unsigned int ext_type,
\&                                          unsigned int context,
\&                                          const unsigned char **out,
\&                                          size_t *outlen, X509 *x,
\&                                          size_t chainidx, int *al,
\&                                          void *add_arg);
\&
\& typedef void (*SSL_custom_ext_free_cb_ex) (SSL *s, unsigned int ext_type,
\&                                            unsigned int context,
\&                                            const unsigned char *out,
\&                                            void *add_arg);
\&
\& typedef int (*SSL_custom_ext_parse_cb_ex) (SSL *s, unsigned int ext_type,
\&                                            unsigned int context,
\&                                            const unsigned char *in,
\&                                            size_t inlen, X509 *x,
\&                                            size_t chainidx, int *al,
\&                                            void *parse_arg);
\&
\& int SSL_CTX_add_custom_ext(SSL_CTX *ctx, unsigned int ext_type,
\&                            unsigned int context,
\&                            SSL_custom_ext_add_cb_ex add_cb,
\&                            SSL_custom_ext_free_cb_ex free_cb,
\&                            void *add_arg,
\&                            SSL_custom_ext_parse_cb_ex parse_cb,
\&                            void *parse_arg);
\&
\& typedef int (*custom_ext_add_cb)(SSL *s, unsigned int ext_type,
\&                                  const unsigned char **out,
\&                                  size_t *outlen, int *al,
\&                                  void *add_arg);
\&
\& typedef void (*custom_ext_free_cb)(SSL *s, unsigned int ext_type,
\&                                    const unsigned char *out,
\&                                    void *add_arg);
\&
\& typedef int (*custom_ext_parse_cb)(SSL *s, unsigned int ext_type,
\&                                    const unsigned char *in,
\&                                    size_t inlen, int *al,
\&                                    void *parse_arg);
\&
\& int SSL_CTX_add_client_custom_ext(SSL_CTX *ctx, unsigned int ext_type,
\&                                   custom_ext_add_cb add_cb,
\&                                   custom_ext_free_cb free_cb, void *add_arg,
\&                                   custom_ext_parse_cb parse_cb,
\&                                   void *parse_arg);
\&
\& int SSL_CTX_add_server_custom_ext(SSL_CTX *ctx, unsigned int ext_type,
\&                                   custom_ext_add_cb add_cb,
\&                                   custom_ext_free_cb free_cb, void *add_arg,
\&                                   custom_ext_parse_cb parse_cb,
\&                                   void *parse_arg);
\&
\& int SSL_extension_supported(unsigned int ext_type);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_add_custom_ext()\fR adds a custom extension for a TLS/DTLS client or server
for all supported protocol versions with extension type \fBext_type\fR and
callbacks \fBadd_cb\fR, \fBfree_cb\fR and \fBparse_cb\fR (see the
"EXTENSION CALLBACKS" section below). The \fBcontext\fR value determines
which messages and under what conditions the extension will be added/parsed (see
the "EXTENSION CONTEXTS" section below).
.PP
\&\fBSSL_CTX_add_client_custom_ext()\fR adds a custom extension for a TLS/DTLS client
with extension type \fBext_type\fR and callbacks \fBadd_cb\fR, \fBfree_cb\fR and
\&\fBparse_cb\fR. This function is similar to \fBSSL_CTX_add_custom_ext()\fR except it only
applies to clients, uses the older style of callbacks, and implicitly sets the
\&\fBcontext\fR value to:
.PP
.Vb 2
\& SSL_EXT_TLS1_2_AND_BELOW_ONLY | SSL_EXT_CLIENT_HELLO
\& | SSL_EXT_TLS1_2_SERVER_HELLO | SSL_EXT_IGNORE_ON_RESUMPTION
.Ve
.PP
\&\fBSSL_CTX_add_server_custom_ext()\fR adds a custom extension for a TLS/DTLS server
with extension type \fBext_type\fR and callbacks \fBadd_cb\fR, \fBfree_cb\fR and
\&\fBparse_cb\fR. This function is similar to \fBSSL_CTX_add_custom_ext()\fR except it
only applies to servers, uses the older style of callbacks, and implicitly sets
the \fBcontext\fR value to the same as for \fBSSL_CTX_add_client_custom_ext()\fR above.
.PP
The \fBext_type\fR parameter corresponds to the \fBextension_type\fR field of
RFC5246 et al. It is \fBnot\fR a NID. In all cases the extension type must not be
handled by OpenSSL internally or an error occurs.
.PP
\&\fBSSL_extension_supported()\fR returns 1 if the extension \fBext_type\fR is handled
internally by OpenSSL and 0 otherwise.
.SH "EXTENSION CALLBACKS"
.IX Header "EXTENSION CALLBACKS"
The callback \fBadd_cb\fR is called to send custom extension data to be
included in various TLS messages. The \fBext_type\fR parameter is set to the
extension type which will be added and \fBadd_arg\fR to the value set when the
extension handler was added. When using the new style callbacks the \fBcontext\fR
parameter will indicate which message is currently being constructed e.g. for
the ClientHello it will be set to \fBSSL_EXT_CLIENT_HELLO\fR.
.PP
If the application wishes to include the extension \fBext_type\fR it should
set \fB*out\fR to the extension data, set \fB*outlen\fR to the length of the
extension data and return 1.
.PP
If the \fBadd_cb\fR does not wish to include the extension it must return 0.
.PP
If \fBadd_cb\fR returns \-1 a fatal handshake error occurs using the TLS
alert value specified in \fB*al\fR.
.PP
When constructing the ClientHello, if \fBadd_cb\fR is set to NULL a zero length
extension is added for \fBext_type\fR. For all other messages if \fBadd_cb\fR is set
to NULL then no extension is added.
.PP
When constructing a Certificate message the callback will be called for each
certificate in the message. The \fBx\fR parameter will indicate the
current certificate and the \fBchainidx\fR parameter will indicate the position
of the certificate in the message. The first certificate is always the end
entity certificate and has a \fBchainidx\fR value of 0. The certificates are in the
order that they were received in the Certificate message.
.PP
For all messages except the ServerHello and EncryptedExtensions every
registered \fBadd_cb\fR is always called to see if the application wishes to add an
extension (as long as all requirements of the specified \fBcontext\fR are met).
.PP
For the ServerHello and EncryptedExtension messages every registered \fBadd_cb\fR
is called once if and only if the requirements of the specified \fBcontext\fR are
met and the corresponding extension was received in the ClientHello. That is, if
no corresponding extension was received in the ClientHello then \fBadd_cb\fR will
not be called.
.PP
If an extension is added (that is \fBadd_cb\fR returns 1) \fBfree_cb\fR is called
(if it is set) with the value of \fBout\fR set by the add callback. It can be
used to free up any dynamic extension data set by \fBadd_cb\fR. Since \fBout\fR is
constant (to permit use of constant data in \fBadd_cb\fR) applications may need to
cast away const to free the data.
.PP
The callback \fBparse_cb\fR receives data for TLS extensions. The callback is only
called if the extension is present and relevant for the context (see
"EXTENSION CONTEXTS" below).
.PP
The extension data consists of \fBinlen\fR bytes in the buffer \fBin\fR for the
extension \fBext_type\fR.
.PP
If the message being parsed is a TLSv1.3 compatible Certificate message then
\&\fBparse_cb\fR will be called for each certificate contained within the message.
The \fBx\fR parameter will indicate the current certificate and the \fBchainidx\fR
parameter will indicate the position of the certificate in the message. The
first certificate is always the end entity certificate and has a \fBchainidx\fR
value of 0.
.PP
If the \fBparse_cb\fR considers the extension data acceptable it must return
1. If it returns 0 or a negative value a fatal handshake error occurs
using the TLS alert value specified in \fB*al\fR.
.PP
The buffer \fBin\fR is a temporary internal buffer which will not be valid after
the callback returns.
.SH "EXTENSION CONTEXTS"
.IX Header "EXTENSION CONTEXTS"
An extension context defines which messages and under which conditions an
extension should be added or expected. The context is built up by performing
a bitwise OR of multiple pre-defined values together. The valid context values
are:
.IP SSL_EXT_TLS_ONLY 4
.IX Item "SSL_EXT_TLS_ONLY"
The extension is only allowed in TLS
.IP SSL_EXT_DTLS_ONLY 4
.IX Item "SSL_EXT_DTLS_ONLY"
The extension is only allowed in DTLS
.IP SSL_EXT_TLS_IMPLEMENTATION_ONLY 4
.IX Item "SSL_EXT_TLS_IMPLEMENTATION_ONLY"
The extension is allowed in DTLS, but there is only a TLS implementation
available (so it is ignored in DTLS).
.IP SSL_EXT_SSL3_ALLOWED 4
.IX Item "SSL_EXT_SSL3_ALLOWED"
Extensions are not typically defined for SSLv3. Setting this value will allow
the extension in SSLv3. Applications will not typically need to use this.
.IP SSL_EXT_TLS1_2_AND_BELOW_ONLY 4
.IX Item "SSL_EXT_TLS1_2_AND_BELOW_ONLY"
The extension is only defined for TLSv1.2/DTLSv1.2 and below. Servers will
ignore this extension if it is present in the ClientHello and TLSv1.3 is
negotiated.
.IP SSL_EXT_TLS1_3_ONLY 4
.IX Item "SSL_EXT_TLS1_3_ONLY"
The extension is only defined for TLS1.3 and above. Servers will ignore this
extension if it is present in the ClientHello and TLSv1.2 or below is
negotiated.
.IP SSL_EXT_IGNORE_ON_RESUMPTION 4
.IX Item "SSL_EXT_IGNORE_ON_RESUMPTION"
The extension will be ignored during parsing if a previous session is being
successfully resumed.
.IP SSL_EXT_CLIENT_HELLO 4
.IX Item "SSL_EXT_CLIENT_HELLO"
The extension may be present in the ClientHello message.
.IP SSL_EXT_TLS1_2_SERVER_HELLO 4
.IX Item "SSL_EXT_TLS1_2_SERVER_HELLO"
The extension may be present in a TLSv1.2 or below compatible ServerHello
message.
.IP SSL_EXT_TLS1_3_SERVER_HELLO 4
.IX Item "SSL_EXT_TLS1_3_SERVER_HELLO"
The extension may be present in a TLSv1.3 compatible ServerHello message.
.IP SSL_EXT_TLS1_3_ENCRYPTED_EXTENSIONS 4
.IX Item "SSL_EXT_TLS1_3_ENCRYPTED_EXTENSIONS"
The extension may be present in an EncryptedExtensions message.
.IP SSL_EXT_TLS1_3_HELLO_RETRY_REQUEST 4
.IX Item "SSL_EXT_TLS1_3_HELLO_RETRY_REQUEST"
The extension may be present in a HelloRetryRequest message.
.IP SSL_EXT_TLS1_3_CERTIFICATE 4
.IX Item "SSL_EXT_TLS1_3_CERTIFICATE"
The extension may be present in a TLSv1.3 compatible Certificate message.
.IP SSL_EXT_TLS1_3_NEW_SESSION_TICKET 4
.IX Item "SSL_EXT_TLS1_3_NEW_SESSION_TICKET"
The extension may be present in a TLSv1.3 compatible NewSessionTicket message.
.IP SSL_EXT_TLS1_3_CERTIFICATE_REQUEST 4
.IX Item "SSL_EXT_TLS1_3_CERTIFICATE_REQUEST"
The extension may be present in a TLSv1.3 compatible CertificateRequest message.
.PP
The context must include at least one message value (otherwise the extension
will never be used).
.SH NOTES
.IX Header "NOTES"
The \fBadd_arg\fR and \fBparse_arg\fR parameters can be set to arbitrary values
which will be passed to the corresponding callbacks. They can, for example,
be used to store the extension data received in a convenient structure or
pass the extension data to be added or freed when adding extensions.
.PP
If the same custom extension type is received multiple times a fatal
\&\fBdecode_error\fR alert is sent and the handshake aborts. If a custom extension
is received in a ServerHello/EncryptedExtensions message which was not sent in
the ClientHello a fatal \fBunsupported_extension\fR alert is sent and the
handshake is aborted. The ServerHello/EncryptedExtensions \fBadd_cb\fR callback is
only called if the corresponding extension was received in the ClientHello. This
is compliant with the TLS specifications. This behaviour ensures that each
callback is called at most once and that an application can never send
unsolicited extensions.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_add_custom_ext()\fR, \fBSSL_CTX_add_client_custom_ext()\fR and
\&\fBSSL_CTX_add_server_custom_ext()\fR return 1 for success and 0 for failure. A
failure can occur if an attempt is made to add the same \fBext_type\fR more than
once, if an attempt is made to use an extension type handled internally by
OpenSSL or if an internal error occurs (for example a memory allocation
failure).
.PP
\&\fBSSL_extension_supported()\fR returns 1 if the extension \fBext_type\fR is handled
internally by OpenSSL and 0 otherwise.
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_CTX_add_custom_ext()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2014\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
