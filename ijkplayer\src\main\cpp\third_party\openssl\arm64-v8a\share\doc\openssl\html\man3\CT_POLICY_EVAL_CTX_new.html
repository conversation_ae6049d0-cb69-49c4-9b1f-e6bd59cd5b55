<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CT_POLICY_EVAL_CTX_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>CT_POLICY_EVAL_CTX_new, CT_POLICY_EVAL_CTX_free, CT_POLICY_EVAL_CTX_get0_cert, CT_POLICY_EVAL_CTX_set1_cert, CT_POLICY_EVAL_CTX_get0_issuer, CT_POLICY_EVAL_CTX_set1_issuer, CT_POLICY_EVAL_CTX_get0_log_store, CT_POLICY_EVAL_CTX_set_shared_CTLOG_STORE, CT_POLICY_EVAL_CTX_get_time, CT_POLICY_EVAL_CTX_set_time - Encapsulates the data required to evaluate whether SCTs meet a Certificate Transparency policy</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ct.h&gt;

CT_POLICY_EVAL_CTX *CT_POLICY_EVAL_CTX_new(void);
void CT_POLICY_EVAL_CTX_free(CT_POLICY_EVAL_CTX *ctx);
X509* CT_POLICY_EVAL_CTX_get0_cert(const CT_POLICY_EVAL_CTX *ctx);
int CT_POLICY_EVAL_CTX_set1_cert(CT_POLICY_EVAL_CTX *ctx, X509 *cert);
X509* CT_POLICY_EVAL_CTX_get0_issuer(const CT_POLICY_EVAL_CTX *ctx);
int CT_POLICY_EVAL_CTX_set1_issuer(CT_POLICY_EVAL_CTX *ctx, X509 *issuer);
const CTLOG_STORE *CT_POLICY_EVAL_CTX_get0_log_store(const CT_POLICY_EVAL_CTX *ctx);
void CT_POLICY_EVAL_CTX_set_shared_CTLOG_STORE(CT_POLICY_EVAL_CTX *ctx,
                                               CTLOG_STORE *log_store);
uint64_t CT_POLICY_EVAL_CTX_get_time(const CT_POLICY_EVAL_CTX *ctx);
void CT_POLICY_EVAL_CTX_set_time(CT_POLICY_EVAL_CTX *ctx, uint64_t time_in_ms);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>A <b>CT_POLICY_EVAL_CTX</b> is used by functions that evaluate whether Signed Certificate Timestamps (SCTs) fulfil a Certificate Transparency (CT) policy. This policy may be, for example, that at least one valid SCT is available. To determine this, an SCT&#39;s timestamp and signature must be verified. This requires:</p>

<ul>

<li><p>the public key of the log that issued the SCT</p>

</li>
<li><p>the certificate that the SCT was issued for</p>

</li>
<li><p>the issuer certificate (if the SCT was issued for a pre-certificate)</p>

</li>
<li><p>the current time</p>

</li>
</ul>

<p>The above requirements are met using the setters described below.</p>

<p>CT_POLICY_EVAL_CTX_new() creates an empty policy evaluation context. This should then be populated using:</p>

<ul>

<li><p>CT_POLICY_EVAL_CTX_set1_cert() to provide the certificate the SCTs were issued for</p>

<p>Increments the reference count of the certificate.</p>

</li>
<li><p>CT_POLICY_EVAL_CTX_set1_issuer() to provide the issuer certificate</p>

<p>Increments the reference count of the certificate.</p>

</li>
<li><p>CT_POLICY_EVAL_CTX_set_shared_CTLOG_STORE() to provide a list of logs that are trusted as sources of SCTs</p>

<p>Holds a pointer to the CTLOG_STORE, so the CTLOG_STORE must outlive the CT_POLICY_EVAL_CTX.</p>

</li>
<li><p>CT_POLICY_EVAL_CTX_set_time() to set the time SCTs should be compared with to determine if they are valid</p>

<p>The SCT timestamp will be compared to this time to check whether the SCT was issued in the future. RFC6962 states that &quot;TLS clients MUST reject SCTs whose timestamp is in the future&quot;. By default, this will be set to 5 minutes in the future (e.g. (time() + 300) * 1000), to allow for clock drift.</p>

<p>The time should be in milliseconds since the Unix epoch.</p>

</li>
</ul>

<p>Each setter has a matching getter for accessing the current value.</p>

<p>When no longer required, the <b>CT_POLICY_EVAL_CTX</b> should be passed to CT_POLICY_EVAL_CTX_free() to delete it.</p>

<h1 id="NOTES">NOTES</h1>

<p>The issuer certificate only needs to be provided if at least one of the SCTs was issued for a pre-certificate. This will be the case for SCTs embedded in a certificate (i.e. those in an X.509 extension), but may not be the case for SCTs found in the TLS SCT extension or OCSP response.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>CT_POLICY_EVAL_CTX_new() will return NULL if malloc fails.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ct.html">ct(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


