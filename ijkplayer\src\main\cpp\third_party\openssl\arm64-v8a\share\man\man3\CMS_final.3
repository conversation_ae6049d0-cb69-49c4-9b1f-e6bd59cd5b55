.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_FINAL 3"
.TH CMS_FINAL 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_final \- finalise a CMS_ContentInfo structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& int CMS_final(CMS_ContentInfo *cms, BIO *data, BIO *dcont, unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_final()\fR finalises the structure \fBcms\fR. Its purpose is to perform any
operations necessary on \fBcms\fR (digest computation for example) and set the
appropriate fields. The parameter \fBdata\fR contains the content to be
processed. The \fBdcont\fR parameter contains a BIO to write content to after
processing: this is only used with detached data and will usually be set to
NULL.
.SH NOTES
.IX Header "NOTES"
This function will normally be called when the \fBCMS_PARTIAL\fR flag is used. It
should only be used when streaming is not performed because the streaming
I/O functions perform finalisation operations internally.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_final()\fR returns 1 for success or 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_sign\fR\|(3),
\&\fBCMS_encrypt\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
