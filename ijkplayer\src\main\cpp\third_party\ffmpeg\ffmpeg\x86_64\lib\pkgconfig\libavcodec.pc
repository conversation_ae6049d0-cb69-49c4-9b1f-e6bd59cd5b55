prefix=/home/<USER>/work/ijkaction/ijkaction/tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/x86_64
exec_prefix=${prefix}
libdir=/home/<USER>/work/ijkaction/ijkaction/tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/x86_64/lib
includedir=/home/<USER>/work/ijkaction/ijkaction/tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/x86_64/include

Name: libavcodec
Description: FFmpeg codec library
Version: 58.18.100
Requires: libswresample >= 3.1.100, libavutil >= 56.14.100
Requires.private: 
Conflicts:
Libs: -L${libdir}  -lavcodec -pthread -lm -lz -L/home/<USER>/work/ijkaction/ijkaction/tpc_c_cplusplus/lycium/usr/openh264/x86_64/lib -lopenh264 -lstdc++ -lpthread -lm
Libs.private: 
Cflags: -I${includedir}
