.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "NSEQ 1"
.TH NSEQ 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-nseq,
nseq \- create or examine a Netscape certificate sequence
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBnseq\fR
[\fB\-help\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-toseq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBnseq\fR command takes a file containing a Netscape certificate
sequence and prints out the certificates contained in it or takes a
file of certificates and converts it into a Netscape certificate
sequence.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read or standard input if this
option is not specified.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
Specifies the output filename or standard output by default.
.IP \fB\-toseq\fR 4
.IX Item "-toseq"
Normally a Netscape certificate sequence will be input and the output
is the certificates contained in it. With the \fB\-toseq\fR option the
situation is reversed: a Netscape certificate sequence is created from
a file of certificates.
.SH EXAMPLES
.IX Header "EXAMPLES"
Output the certificates in a Netscape certificate sequence
.PP
.Vb 1
\& openssl nseq \-in nseq.pem \-out certs.pem
.Ve
.PP
Create a Netscape certificate sequence
.PP
.Vb 1
\& openssl nseq \-in certs.pem \-toseq \-out nseq.pem
.Ve
.SH NOTES
.IX Header "NOTES"
The \fBPEM\fR encoded form uses the same headers and footers as a certificate:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN CERTIFICATE\-\-\-\-\-
\& \-\-\-\-\-END CERTIFICATE\-\-\-\-\-
.Ve
.PP
A Netscape certificate sequence is a Netscape specific format that can be sent
to browsers as an alternative to the standard PKCS#7 format when several
certificates are sent to the browser: for example during certificate enrollment.
It is used by Netscape certificate server for example.
.SH BUGS
.IX Header "BUGS"
This program needs a few more options: like allowing DER or PEM input and
output files and allowing multiple certificate files to be used.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
