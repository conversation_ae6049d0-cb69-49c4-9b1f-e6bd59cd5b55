.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_GET0_RECIPIENTINFOS 3"
.TH CMS_GET0_RECIPIENTINFOS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_get0_RecipientInfos, CMS_RecipientInfo_type,
CMS_RecipientInfo_ktri_get0_signer_id, CMS_RecipientInfo_ktri_cert_cmp,
CMS_RecipientInfo_set0_pkey, CMS_RecipientInfo_kekri_get0_id,
CMS_RecipientInfo_kekri_id_cmp, CMS_RecipientInfo_set0_key,
CMS_RecipientInfo_decrypt, CMS_RecipientInfo_encrypt
\&\- CMS envelopedData RecipientInfo routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& STACK_OF(CMS_RecipientInfo) *CMS_get0_RecipientInfos(CMS_ContentInfo *cms);
\& int CMS_RecipientInfo_type(CMS_RecipientInfo *ri);
\&
\& int CMS_RecipientInfo_ktri_get0_signer_id(CMS_RecipientInfo *ri,
\&                                           ASN1_OCTET_STRING **keyid,
\&                                           X509_NAME **issuer,
\&                                           ASN1_INTEGER **sno);
\& int CMS_RecipientInfo_ktri_cert_cmp(CMS_RecipientInfo *ri, X509 *cert);
\& int CMS_RecipientInfo_set0_pkey(CMS_RecipientInfo *ri, EVP_PKEY *pkey);
\&
\& int CMS_RecipientInfo_kekri_get0_id(CMS_RecipientInfo *ri, X509_ALGOR **palg,
\&                                     ASN1_OCTET_STRING **pid,
\&                                     ASN1_GENERALIZEDTIME **pdate,
\&                                     ASN1_OBJECT **potherid,
\&                                     ASN1_TYPE **pothertype);
\& int CMS_RecipientInfo_kekri_id_cmp(CMS_RecipientInfo *ri,
\&                                    const unsigned char *id, size_t idlen);
\& int CMS_RecipientInfo_set0_key(CMS_RecipientInfo *ri,
\&                                unsigned char *key, size_t keylen);
\&
\& int CMS_RecipientInfo_decrypt(CMS_ContentInfo *cms, CMS_RecipientInfo *ri);
\& int CMS_RecipientInfo_encrypt(CMS_ContentInfo *cms, CMS_RecipientInfo *ri);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The function \fBCMS_get0_RecipientInfos()\fR returns all the CMS_RecipientInfo
structures associated with a CMS EnvelopedData structure.
.PP
\&\fBCMS_RecipientInfo_type()\fR returns the type of CMS_RecipientInfo structure \fBri\fR.
It will currently return CMS_RECIPINFO_TRANS, CMS_RECIPINFO_AGREE,
CMS_RECIPINFO_KEK, CMS_RECIPINFO_PASS, or CMS_RECIPINFO_OTHER.
.PP
\&\fBCMS_RecipientInfo_ktri_get0_signer_id()\fR retrieves the certificate recipient
identifier associated with a specific CMS_RecipientInfo structure \fBri\fR, which
must be of type CMS_RECIPINFO_TRANS. Either the keyidentifier will be set in
\&\fBkeyid\fR or \fBboth\fR issuer name and serial number in \fBissuer\fR and \fBsno\fR.
.PP
\&\fBCMS_RecipientInfo_ktri_cert_cmp()\fR compares the certificate \fBcert\fR against the
CMS_RecipientInfo structure \fBri\fR, which must be of type CMS_RECIPINFO_TRANS.
It returns zero if the comparison is successful and non zero if not.
.PP
\&\fBCMS_RecipientInfo_set0_pkey()\fR associates the private key \fBpkey\fR with
the CMS_RecipientInfo structure \fBri\fR, which must be of type
CMS_RECIPINFO_TRANS.
.PP
\&\fBCMS_RecipientInfo_kekri_get0_id()\fR retrieves the key information from the
CMS_RecipientInfo structure \fBri\fR which must be of type CMS_RECIPINFO_KEK.  Any
of the remaining parameters can be NULL if the application is not interested in
the value of a field. Where a field is optional and absent NULL will be written
to the corresponding parameter. The keyEncryptionAlgorithm field is written to
\&\fBpalg\fR, the \fBkeyIdentifier\fR field is written to \fBpid\fR, the \fBdate\fR field if
present is written to \fBpdate\fR, if the \fBother\fR field is present the components
\&\fBkeyAttrId\fR and \fBkeyAttr\fR are written to parameters \fBpotherid\fR and
\&\fBpothertype\fR.
.PP
\&\fBCMS_RecipientInfo_kekri_id_cmp()\fR compares the ID in the \fBid\fR and \fBidlen\fR
parameters against the \fBkeyIdentifier\fR CMS_RecipientInfo structure \fBri\fR,
which must be of type CMS_RECIPINFO_KEK.  It returns zero if the comparison is
successful and non zero if not.
.PP
\&\fBCMS_RecipientInfo_set0_key()\fR associates the symmetric key \fBkey\fR of length
\&\fBkeylen\fR with the CMS_RecipientInfo structure \fBri\fR, which must be of type
CMS_RECIPINFO_KEK.
.PP
\&\fBCMS_RecipientInfo_decrypt()\fR attempts to decrypt CMS_RecipientInfo structure
\&\fBri\fR in structure \fBcms\fR. A key must have been associated with the structure
first.
.PP
\&\fBCMS_RecipientInfo_encrypt()\fR attempts to encrypt CMS_RecipientInfo structure
\&\fBri\fR in structure \fBcms\fR. A key must have been associated with the structure
first and the content encryption key must be available: for example by a
previous call to \fBCMS_RecipientInfo_decrypt()\fR.
.SH NOTES
.IX Header "NOTES"
The main purpose of these functions is to enable an application to lookup
recipient keys using any appropriate technique when the simpler method
of \fBCMS_decrypt()\fR is not appropriate.
.PP
In typical usage and application will retrieve all CMS_RecipientInfo structures
using \fBCMS_get0_RecipientInfos()\fR and check the type of each using
\&\fBCMS_RecipientInfo_type()\fR. Depending on the type the CMS_RecipientInfo structure
can be ignored or its key identifier data retrieved using an appropriate
function. Then if the corresponding secret or private key can be obtained by
any appropriate means it can then associated with the structure and
\&\fBCMS_RecipientInfo_decrypt()\fR called. If successful \fBCMS_decrypt()\fR can be called
with a NULL key to decrypt the enveloped content.
.PP
The \fBCMS_RecipientInfo_encrypt()\fR can be used to add a new recipient to an
existing enveloped data structure. Typically an application will first decrypt
an appropriate CMS_RecipientInfo structure to make the content encrypt key
available, it will then add a new recipient using a function such as
\&\fBCMS_add1_recipient_cert()\fR and finally encrypt the content encryption key
using \fBCMS_RecipientInfo_encrypt()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_get0_RecipientInfos()\fR returns all CMS_RecipientInfo structures, or NULL if
an error occurs.
.PP
\&\fBCMS_RecipientInfo_ktri_get0_signer_id()\fR, \fBCMS_RecipientInfo_set0_pkey()\fR,
\&\fBCMS_RecipientInfo_kekri_get0_id()\fR, \fBCMS_RecipientInfo_set0_key()\fR and
\&\fBCMS_RecipientInfo_decrypt()\fR return 1 for success or 0 if an error occurs.
\&\fBCMS_RecipientInfo_encrypt()\fR return 1 for success or 0 if an error occurs.
.PP
\&\fBCMS_RecipientInfo_ktri_cert_cmp()\fR and \fBCMS_RecipientInfo_kekri_cmp()\fR return 0
for a successful comparison and non zero otherwise.
.PP
Any error can be obtained from \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_decrypt\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
