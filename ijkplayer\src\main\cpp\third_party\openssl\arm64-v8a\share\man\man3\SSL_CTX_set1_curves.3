.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET1_CURVES 3"
.TH SSL_CTX_SET1_CURVES 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set1_groups, SSL_CTX_set1_groups_list, SSL_set1_groups,
SSL_set1_groups_list, SSL_get1_groups, SSL_get_shared_group,
SSL_CTX_set1_curves, SSL_CTX_set1_curves_list, SSL_set1_curves,
SSL_set1_curves_list, SSL_get1_curves, SSL_get_shared_curve
\&\- EC supported curve functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_set1_groups(SSL_CTX *ctx, int *glist, int glistlen);
\& int SSL_CTX_set1_groups_list(SSL_CTX *ctx, char *list);
\&
\& int SSL_set1_groups(SSL *ssl, int *glist, int glistlen);
\& int SSL_set1_groups_list(SSL *ssl, char *list);
\&
\& int SSL_get1_groups(SSL *ssl, int *groups);
\& int SSL_get_shared_group(SSL *s, int n);
\&
\& int SSL_CTX_set1_curves(SSL_CTX *ctx, int *clist, int clistlen);
\& int SSL_CTX_set1_curves_list(SSL_CTX *ctx, char *list);
\&
\& int SSL_set1_curves(SSL *ssl, int *clist, int clistlen);
\& int SSL_set1_curves_list(SSL *ssl, char *list);
\&
\& int SSL_get1_curves(SSL *ssl, int *curves);
\& int SSL_get_shared_curve(SSL *s, int n);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
For all of the functions below that set the supported groups there must be at
least one group in the list.
.PP
\&\fBSSL_CTX_set1_groups()\fR sets the supported groups for \fBctx\fR to \fBglistlen\fR
groups in the array \fBglist\fR. The array consist of all NIDs of groups in
preference order. For a TLS client the groups are used directly in the
supported groups extension. For a TLS server the groups are used to
determine the set of shared groups.
.PP
\&\fBSSL_CTX_set1_groups_list()\fR sets the supported groups for \fBctx\fR to
string \fBlist\fR. The string is a colon separated list of group NIDs or
names, for example "P\-521:P\-384:P\-256".
.PP
\&\fBSSL_set1_groups()\fR and \fBSSL_set1_groups_list()\fR are similar except they set
supported groups for the SSL structure \fBssl\fR.
.PP
\&\fBSSL_get1_groups()\fR returns the set of supported groups sent by a client
in the supported groups extension. It returns the total number of
supported groups. The \fBgroups\fR parameter can be \fBNULL\fR to simply
return the number of groups for memory allocation purposes. The
\&\fBgroups\fR array is in the form of a set of group NIDs in preference
order. It can return zero if the client did not send a supported groups
extension.
.PP
\&\fBSSL_get_shared_group()\fR returns shared group \fBn\fR for a server-side
SSL \fBssl\fR. If \fBn\fR is \-1 then the total number of shared groups is
returned, which may be zero. Other than for diagnostic purposes,
most applications will only be interested in the first shared group
so \fBn\fR is normally set to zero. If the value \fBn\fR is out of range,
NID_undef is returned.
.PP
All these functions are implemented as macros.
.PP
The curve functions are synonyms for the equivalently named group functions and
are identical in every respect. They exist because, prior to TLS1.3, there was
only the concept of supported curves. In TLS1.3 this was renamed to supported
groups, and extended to include Diffie Hellman groups. The group functions
should be used in preference.
.SH NOTES
.IX Header "NOTES"
If an application wishes to make use of several of these functions for
configuration purposes either on a command line or in a file it should
consider using the SSL_CONF interface instead of manually parsing options.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set1_groups()\fR, \fBSSL_CTX_set1_groups_list()\fR, \fBSSL_set1_groups()\fR and
\&\fBSSL_set1_groups_list()\fR, return 1 for success and 0 for failure.
.PP
\&\fBSSL_get1_groups()\fR returns the number of groups, which may be zero.
.PP
\&\fBSSL_get_shared_group()\fR returns the NID of shared group \fBn\fR or NID_undef if there
is no shared group \fBn\fR; or the total number of shared groups if \fBn\fR
is \-1.
.PP
When called on a client \fBssl\fR, \fBSSL_get_shared_group()\fR has no meaning and
returns \-1.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The curve functions were added in OpenSSL 1.0.2. The equivalent group
functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2013\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
