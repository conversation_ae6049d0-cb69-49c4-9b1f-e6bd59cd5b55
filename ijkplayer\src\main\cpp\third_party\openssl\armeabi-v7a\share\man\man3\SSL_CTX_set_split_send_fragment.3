.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_SPLIT_SEND_FRAGMENT 3"
.TH SSL_CTX_SET_SPLIT_SEND_FRAGMENT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_max_send_fragment, SSL_set_max_send_fragment,
SSL_CTX_set_split_send_fragment, SSL_set_split_send_fragment,
SSL_CTX_set_max_pipelines, SSL_set_max_pipelines,
SSL_CTX_set_default_read_buffer_len, SSL_set_default_read_buffer_len,
SSL_CTX_set_tlsext_max_fragment_length,
SSL_set_tlsext_max_fragment_length,
SSL_SESSION_get_max_fragment_length \- Control fragment size settings and pipelining operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_set_max_send_fragment(SSL_CTX *ctx, long);
\& long SSL_set_max_send_fragment(SSL *ssl, long m);
\&
\& long SSL_CTX_set_max_pipelines(SSL_CTX *ctx, long m);
\& long SSL_set_max_pipelines(SSL_CTX *ssl, long m);
\&
\& long SSL_CTX_set_split_send_fragment(SSL_CTX *ctx, long m);
\& long SSL_set_split_send_fragment(SSL *ssl, long m);
\&
\& void SSL_CTX_set_default_read_buffer_len(SSL_CTX *ctx, size_t len);
\& void SSL_set_default_read_buffer_len(SSL *s, size_t len);
\&
\& int SSL_CTX_set_tlsext_max_fragment_length(SSL_CTX *ctx, uint8_t mode);
\& int SSL_set_tlsext_max_fragment_length(SSL *ssl, uint8_t mode);
\& uint8_t SSL_SESSION_get_max_fragment_length(SSL_SESSION *session);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Some engines are able to process multiple simultaneous crypto operations. This
capability could be utilised to parallelise the processing of a single
connection. For example a single write can be split into multiple records and
each one encrypted independently and in parallel. Note: this will only work in
TLS1.1+. There is no support in SSLv3, TLSv1.0 or DTLS (any version). This
capability is known as "pipelining" within OpenSSL.
.PP
In order to benefit from the pipelining capability. You need to have an engine
that provides ciphers that support this. The OpenSSL "dasync" engine provides
AES128\-SHA based ciphers that have this capability. However, these are for
development and test purposes only.
.PP
\&\fBSSL_CTX_set_max_send_fragment()\fR and \fBSSL_set_max_send_fragment()\fR set the
\&\fBmax_send_fragment\fR parameter for SSL_CTX and SSL objects respectively. This
value restricts the amount of plaintext bytes that will be sent in any one
SSL/TLS record. By default its value is SSL3_RT_MAX_PLAIN_LENGTH (16384). These
functions will only accept a value in the range 512 \- SSL3_RT_MAX_PLAIN_LENGTH.
.PP
\&\fBSSL_CTX_set_max_pipelines()\fR and \fBSSL_set_max_pipelines()\fR set the maximum number
of pipelines that will be used at any one time. This value applies to both
"read" pipelining and "write" pipelining. By default only one pipeline will be
used (i.e. normal non-parallel operation). The number of pipelines set must be
in the range 1 \- SSL_MAX_PIPELINES (32). Setting this to a value > 1 will also
automatically turn on "read_ahead" (see \fBSSL_CTX_set_read_ahead\fR\|(3)). This is
explained further below. OpenSSL will only every use more than one pipeline if
a cipher suite is negotiated that uses a pipeline capable cipher provided by an
engine.
.PP
Pipelining operates slightly differently for reading encrypted data compared to
writing encrypted data. \fBSSL_CTX_set_split_send_fragment()\fR and
\&\fBSSL_set_split_send_fragment()\fR define how data is split up into pipelines when
writing encrypted data. The number of pipelines used will be determined by the
amount of data provided to the \fBSSL_write_ex()\fR or \fBSSL_write()\fR call divided by
\&\fBsplit_send_fragment\fR.
.PP
For example if \fBsplit_send_fragment\fR is set to 2000 and \fBmax_pipelines\fR is 4
then:
.PP
SSL_write/SSL_write_ex called with 0\-2000 bytes == 1 pipeline used
.PP
SSL_write/SSL_write_ex called with 2001\-4000 bytes == 2 pipelines used
.PP
SSL_write/SSL_write_ex called with 4001\-6000 bytes == 3 pipelines used
.PP
SSL_write/SSL_write_ex called with 6001+ bytes == 4 pipelines used
.PP
\&\fBsplit_send_fragment\fR must always be less than or equal to
\&\fBmax_send_fragment\fR. By default it is set to be equal to \fBmax_send_fragment\fR.
This will mean that the same number of records will always be created as would
have been created in the non-parallel case, although the data will be
apportioned differently. In the parallel case data will be spread equally
between the pipelines.
.PP
Read pipelining is controlled in a slightly different way than with write
pipelining. While reading we are constrained by the number of records that the
peer (and the network) can provide to us in one go. The more records we can get
in one go the more opportunity we have to parallelise the processing. As noted
above when setting \fBmax_pipelines\fR to a value greater than one, \fBread_ahead\fR
is automatically set. The \fBread_ahead\fR parameter causes OpenSSL to attempt to
read as much data into the read buffer as the network can provide and will fit
into the buffer. Without this set data is read into the read buffer one record
at a time. The more data that can be read, the more opportunity there is for
parallelising the processing at the cost of increased memory overhead per
connection. Setting \fBread_ahead\fR can impact the behaviour of the \fBSSL_pending()\fR
function (see \fBSSL_pending\fR\|(3)).
.PP
The \fBSSL_CTX_set_default_read_buffer_len()\fR and \fBSSL_set_default_read_buffer_len()\fR
functions control the size of the read buffer that will be used. The \fBlen\fR
parameter sets the size of the buffer. The value will only be used if it is
greater than the default that would have been used anyway. The normal default
value depends on a number of factors but it will be at least
SSL3_RT_MAX_PLAIN_LENGTH + SSL3_RT_MAX_ENCRYPTED_OVERHEAD (16704) bytes.
.PP
\&\fBSSL_CTX_set_tlsext_max_fragment_length()\fR sets the default maximum fragment
length negotiation mode via value \fBmode\fR to \fBctx\fR.
This setting affects only SSL instances created after this function is called.
It affects the client-side as only its side may initiate this extension use.
.PP
\&\fBSSL_set_tlsext_max_fragment_length()\fR sets the maximum fragment length
negotiation mode via value \fBmode\fR to \fBssl\fR.
This setting will be used during a handshake when extensions are exchanged
between client and server.
So it only affects SSL sessions created after this function is called.
It affects the client-side as only its side may initiate this extension use.
.PP
\&\fBSSL_SESSION_get_max_fragment_length()\fR gets the maximum fragment length
negotiated in \fBsession\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All non-void functions return 1 on success and 0 on failure.
.SH NOTES
.IX Header "NOTES"
The Maximum Fragment Length extension support is optional on the server side.
If the server does not support this extension then
\&\fBSSL_SESSION_get_max_fragment_length()\fR will return:
TLSEXT_max_fragment_length_DISABLED.
.PP
The following modes are available:
.IP TLSEXT_max_fragment_length_DISABLED 4
.IX Item "TLSEXT_max_fragment_length_DISABLED"
Disables Maximum Fragment Length Negotiation (default).
.IP TLSEXT_max_fragment_length_512 4
.IX Item "TLSEXT_max_fragment_length_512"
Sets Maximum Fragment Length to 512 bytes.
.IP TLSEXT_max_fragment_length_1024 4
.IX Item "TLSEXT_max_fragment_length_1024"
Sets Maximum Fragment Length to 1024.
.IP TLSEXT_max_fragment_length_2048 4
.IX Item "TLSEXT_max_fragment_length_2048"
Sets Maximum Fragment Length to 2048.
.IP TLSEXT_max_fragment_length_4096 4
.IX Item "TLSEXT_max_fragment_length_4096"
Sets Maximum Fragment Length to 4096.
.PP
With the exception of \fBSSL_CTX_set_default_read_buffer_len()\fR
\&\fBSSL_set_default_read_buffer_len()\fR, \fBSSL_CTX_set_tlsext_max_fragment_length()\fR,
\&\fBSSL_set_tlsext_max_fragment_length()\fR and \fBSSL_SESSION_get_max_fragment_length()\fR
all these functions are implemented using macros.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CTX_set_read_ahead\fR\|(3), \fBSSL_pending\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_CTX_set_max_pipelines()\fR, \fBSSL_set_max_pipelines()\fR,
\&\fBSSL_CTX_set_split_send_fragment()\fR, \fBSSL_set_split_send_fragment()\fR,
\&\fBSSL_CTX_set_default_read_buffer_len()\fR and  \fBSSL_set_default_read_buffer_len()\fR
functions were added in OpenSSL 1.1.0.
.PP
The \fBSSL_CTX_set_tlsext_max_fragment_length()\fR, \fBSSL_set_tlsext_max_fragment_length()\fR
and \fBSSL_SESSION_get_max_fragment_length()\fR functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
