.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_LOAD_VERIFY_LOCATIONS 3"
.TH SSL_CTX_LOAD_VERIFY_LOCATIONS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_load_verify_locations, SSL_CTX_set_default_verify_paths,
SSL_CTX_set_default_verify_dir, SSL_CTX_set_default_verify_file \- set
default locations for trusted CA certificates
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_load_verify_locations(SSL_CTX *ctx, const char *CAfile,
\&                                   const char *CApath);
\&
\& int SSL_CTX_set_default_verify_paths(SSL_CTX *ctx);
\&
\& int SSL_CTX_set_default_verify_dir(SSL_CTX *ctx);
\&
\& int SSL_CTX_set_default_verify_file(SSL_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_load_verify_locations()\fR specifies the locations for \fBctx\fR, at
which CA certificates for verification purposes are located. The certificates
available via \fBCAfile\fR and \fBCApath\fR are trusted.
.PP
\&\fBSSL_CTX_set_default_verify_paths()\fR specifies that the default locations from
which CA certificates are loaded should be used. There is one default directory
and one default file. The default CA certificates directory is called "certs" in
the default OpenSSL directory. Alternatively the SSL_CERT_DIR environment
variable can be defined to override this location. The default CA certificates
file is called "cert.pem" in the default OpenSSL directory. Alternatively the
SSL_CERT_FILE environment variable can be defined to override this location.
.PP
\&\fBSSL_CTX_set_default_verify_dir()\fR is similar to
\&\fBSSL_CTX_set_default_verify_paths()\fR except that just the default directory is
used.
.PP
\&\fBSSL_CTX_set_default_verify_file()\fR is similar to
\&\fBSSL_CTX_set_default_verify_paths()\fR except that just the default file is
used.
.SH NOTES
.IX Header "NOTES"
If \fBCAfile\fR is not NULL, it points to a file of CA certificates in PEM
format. The file can contain several CA certificates identified by
.PP
.Vb 3
\& \-\-\-\-\-BEGIN CERTIFICATE\-\-\-\-\-
\& ... (CA certificate in base64 encoding) ...
\& \-\-\-\-\-END CERTIFICATE\-\-\-\-\-
.Ve
.PP
sequences. Before, between, and after the certificates text is allowed
which can be used e.g. for descriptions of the certificates.
.PP
The \fBCAfile\fR is processed on execution of the \fBSSL_CTX_load_verify_locations()\fR
function.
.PP
If \fBCApath\fR is not NULL, it points to a directory containing CA certificates
in PEM format. The files each contain one CA certificate. The files are
looked up by the CA subject name hash value, which must hence be available.
If more than one CA certificate with the same name hash value exist, the
extension must be different (e.g. 9d66eef0.0, 9d66eef0.1 etc). The search
is performed in the ordering of the extension number, regardless of other
properties of the certificates.
Use the \fBc_rehash\fR utility to create the necessary links.
.PP
The certificates in \fBCApath\fR are only looked up when required, e.g. when
building the certificate chain or when actually performing the verification
of a peer certificate.
.PP
When looking up CA certificates, the OpenSSL library will first search the
certificates in \fBCAfile\fR, then those in \fBCApath\fR. Certificate matching
is done based on the subject name, the key identifier (if present), and the
serial number as taken from the certificate to be verified. If these data
do not match, the next certificate will be tried. If a first certificate
matching the parameters is found, the verification process will be performed;
no other certificates for the same parameters will be searched in case of
failure.
.PP
In server mode, when requesting a client certificate, the server must send
the list of CAs of which it will accept client certificates. This list
is not influenced by the contents of \fBCAfile\fR or \fBCApath\fR and must
explicitly be set using the
\&\fBSSL_CTX_set_client_CA_list\fR\|(3)
family of functions.
.PP
When building its own certificate chain, an OpenSSL client/server will
try to fill in missing certificates from \fBCAfile\fR/\fBCApath\fR, if the
certificate chain was not explicitly specified (see
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3),
\&\fBSSL_CTX_use_certificate\fR\|(3).
.SH WARNINGS
.IX Header "WARNINGS"
If several CA certificates matching the name, key identifier, and serial
number condition are available, only the first one will be examined. This
may lead to unexpected results if the same CA certificate is available
with different expiration dates. If a "certificate expired" verification
error occurs, no other certificate will be searched. Make sure to not
have expired certificates mixed with valid ones.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
For SSL_CTX_load_verify_locations the following return values can occur:
.IP 0 4
The operation failed because \fBCAfile\fR and \fBCApath\fR are NULL or the
processing at one of the locations specified failed. Check the error
stack to find out the reason.
.IP 1 4
.IX Item "1"
The operation succeeded.
.PP
\&\fBSSL_CTX_set_default_verify_paths()\fR, \fBSSL_CTX_set_default_verify_dir()\fR and
\&\fBSSL_CTX_set_default_verify_file()\fR all return 1 on success or 0 on failure. A
missing default location is still treated as a success.
.SH EXAMPLES
.IX Header "EXAMPLES"
Generate a CA certificate file with descriptive text from the CA certificates
ca1.pem ca2.pem ca3.pem:
.PP
.Vb 5
\& #!/bin/sh
\& rm CAfile.pem
\& for i in ca1.pem ca2.pem ca3.pem ; do
\&     openssl x509 \-in $i \-text >> CAfile.pem
\& done
.Ve
.PP
Prepare the directory /some/where/certs containing several CA certificates
for use as \fBCApath\fR:
.PP
.Vb 2
\& cd /some/where/certs
\& c_rehash .
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_set_client_CA_list\fR\|(3),
\&\fBSSL_get_client_CA_list\fR\|(3),
\&\fBSSL_CTX_use_certificate\fR\|(3),
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3),
\&\fBSSL_CTX_set_cert_store\fR\|(3),
\&\fBSSL_CTX_set_client_CA_list\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
