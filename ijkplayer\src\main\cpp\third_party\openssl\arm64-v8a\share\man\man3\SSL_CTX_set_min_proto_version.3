.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_MIN_PROTO_VERSION 3"
.TH SSL_CTX_SET_MIN_PROTO_VERSION 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_min_proto_version, SSL_CTX_set_max_proto_version,
SSL_CTX_get_min_proto_version, SSL_CTX_get_max_proto_version,
SSL_set_min_proto_version, SSL_set_max_proto_version,
SSL_get_min_proto_version, SSL_get_max_proto_version \- Get and set minimum
and maximum supported protocol version
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_set_min_proto_version(SSL_CTX *ctx, int version);
\& int SSL_CTX_set_max_proto_version(SSL_CTX *ctx, int version);
\& int SSL_CTX_get_min_proto_version(SSL_CTX *ctx);
\& int SSL_CTX_get_max_proto_version(SSL_CTX *ctx);
\&
\& int SSL_set_min_proto_version(SSL *ssl, int version);
\& int SSL_set_max_proto_version(SSL *ssl, int version);
\& int SSL_get_min_proto_version(SSL *ssl);
\& int SSL_get_max_proto_version(SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The functions get or set the minimum and maximum supported protocol versions
for the \fBctx\fR or \fBssl\fR.
This works in combination with the options set via
\&\fBSSL_CTX_set_options\fR\|(3) that also make it possible to disable
specific protocol versions.
Use these functions instead of disabling specific protocol versions.
.PP
Setting the minimum or maximum version to 0, will enable protocol
versions down to the lowest version, or up to the highest version
supported by the library, respectively.
.PP
Getters return 0 in case \fBctx\fR or \fBssl\fR have been configured to
automatically use the lowest or highest version supported by the library.
.PP
Currently supported versions are \fBSSL3_VERSION\fR, \fBTLS1_VERSION\fR,
\&\fBTLS1_1_VERSION\fR, \fBTLS1_2_VERSION\fR, \fBTLS1_3_VERSION\fR for TLS and
\&\fBDTLS1_VERSION\fR, \fBDTLS1_2_VERSION\fR for DTLS.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These setter functions return 1 on success and 0 on failure. The getter
functions return the configured version or 0 for auto-configuration of
lowest or highest protocol, respectively.
.SH NOTES
.IX Header "NOTES"
All these functions are implemented using macros.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CTX_set_options\fR\|(3), \fBSSL_CONF_cmd\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The setter functions were added in OpenSSL 1.1.0. The getter functions
were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
