prefix=/home/<USER>/work/ijkaction/ijkaction/tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/armeabi-v7a
exec_prefix=${prefix}
libdir=/home/<USER>/work/ijkaction/ijkaction/tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/armeabi-v7a/lib
includedir=/home/<USER>/work/ijkaction/ijkaction/tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/armeabi-v7a/include

Name: libavformat
Description: FFmpeg container format library
Version: 58.12.100
Requires: libavcodec >= 58.18.100, libswresample >= 3.1.100, libavutil >= 56.14.100
Requires.private: 
Conflicts:
Libs: -L${libdir}  -lavformat -lm -lz -L/home/<USER>/work/ijkaction/ijkaction/tpc_c_cplusplus/lycium/usr/openssl_1_1_1w/armeabi-v7a/lib -lssl -lcrypto
Libs.private: 
Cflags: -I${includedir}
