.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PEM_READ_CMS 3"
.TH PEM_READ_CMS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DECLARE_PEM_rw,
PEM_read_CMS,
PEM_read_bio_CMS,
PEM_write_CMS,
PEM_write_bio_CMS,
PEM_write_DHxparams,
PEM_write_bio_DHxparams,
PEM_read_ECPKParameters,
PEM_read_bio_ECPKParameters,
PEM_write_ECPKParameters,
PEM_write_bio_ECPKParameters,
PEM_read_ECPrivateKey,
PEM_write_ECPrivateKey,
PEM_write_bio_ECPrivateKey,
PEM_read_EC_PUBKEY,
PEM_read_bio_EC_PUBKEY,
PEM_write_EC_PUBKEY,
PEM_write_bio_EC_PUBKEY,
PEM_read_NETSCAPE_CERT_SEQUENCE,
PEM_read_bio_NETSCAPE_CERT_SEQUENCE,
PEM_write_NETSCAPE_CERT_SEQUENCE,
PEM_write_bio_NETSCAPE_CERT_SEQUENCE,
PEM_read_PKCS8,
PEM_read_bio_PKCS8,
PEM_write_PKCS8,
PEM_write_bio_PKCS8,
PEM_write_PKCS8_PRIV_KEY_INFO,
PEM_read_bio_PKCS8_PRIV_KEY_INFO,
PEM_read_PKCS8_PRIV_KEY_INFO,
PEM_write_bio_PKCS8_PRIV_KEY_INFO,
PEM_read_SSL_SESSION,
PEM_read_bio_SSL_SESSION,
PEM_write_SSL_SESSION,
PEM_write_bio_SSL_SESSION
\&\- PEM object encoding routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pem.h>
\&
\& DECLARE_PEM_rw(name, TYPE)
\&
\& TYPE *PEM_read_TYPE(FILE *fp, TYPE **a, pem_password_cb *cb, void *u);
\& TYPE *PEM_read_bio_TYPE(BIO *bp, TYPE **a, pem_password_cb *cb, void *u);
\& int PEM_write_TYPE(FILE *fp, const TYPE *a);
\& int PEM_write_bio_TYPE(BIO *bp, const TYPE *a);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
In the description below, \fITYPE\fR is used
as a placeholder for any of the OpenSSL datatypes, such as \fIX509\fR.
The macro \fBDECLARE_PEM_rw\fR expands to the set of declarations shown in
the next four lines of the synopsis.
.PP
These routines convert between local instances of ASN1 datatypes and
the PEM encoding.  For more information on the templates, see
\&\fBASN1_ITEM\fR\|(3).  For more information on the lower-level routines used
by the functions here, see \fBPEM_read\fR\|(3).
.PP
\&\fBPEM_read_TYPE()\fR reads a PEM-encoded object of \fITYPE\fR from the file \fBfp\fR
and returns it.  The \fBcb\fR and \fBu\fR parameters are as described in
\&\fBpem_password_cb\fR\|(3).
.PP
\&\fBPEM_read_bio_TYPE()\fR is similar to \fBPEM_read_TYPE()\fR but reads from the BIO \fBbp\fR.
.PP
\&\fBPEM_write_TYPE()\fR writes the PEM encoding of the object \fBa\fR to the file \fBfp\fR.
.PP
\&\fBPEM_write_bio_TYPE()\fR similarly writes to the BIO \fBbp\fR.
.SH NOTES
.IX Header "NOTES"
These functions make no assumption regarding the pass phrase received from the
password callback.
It will simply be treated as a byte sequence.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPEM_read_TYPE()\fR and \fBPEM_read_bio_TYPE()\fR return a pointer to an allocated
object, which should be released by calling \fBTYPE_free()\fR, or NULL on error.
.PP
\&\fBPEM_write_TYPE()\fR and \fBPEM_write_bio_TYPE()\fR return the number of bytes written
or zero on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPEM_read\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 1998\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
