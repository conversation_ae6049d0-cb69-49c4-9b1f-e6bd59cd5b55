<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY_CTX_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY_CTX_new, EVP_PKEY_CTX_new_id, EVP_PKEY_CTX_dup, EVP_PKEY_CTX_free - public key algorithm context functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

EVP_PKEY_CTX *EVP_PKEY_CTX_new(EVP_PKEY *pkey, ENGINE *e);
EVP_PKEY_CTX *EVP_PKEY_CTX_new_id(int id, ENGINE *e);
EVP_PKEY_CTX *EVP_PKEY_CTX_dup(EVP_PKEY_CTX *ctx);
void EVP_PKEY_CTX_free(EVP_PKEY_CTX *ctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The EVP_PKEY_CTX_new() function allocates public key algorithm context using the algorithm specified in <b>pkey</b> and ENGINE <b>e</b>.</p>

<p>The EVP_PKEY_CTX_new_id() function allocates public key algorithm context using the algorithm specified by <b>id</b> and ENGINE <b>e</b>. It is normally used when no <b>EVP_PKEY</b> structure is associated with the operations, for example during parameter generation of key generation for some algorithms.</p>

<p>EVP_PKEY_CTX_dup() duplicates the context <b>ctx</b>.</p>

<p>EVP_PKEY_CTX_free() frees up the context <b>ctx</b>. If <b>ctx</b> is NULL, nothing is done.</p>

<h1 id="NOTES">NOTES</h1>

<p>The <b>EVP_PKEY_CTX</b> structure is an opaque public key algorithm context used by the OpenSSL high-level public key API. Contexts <b>MUST NOT</b> be shared between threads: that is it is not permissible to use the same context simultaneously in two threads.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_PKEY_CTX_new(), EVP_PKEY_CTX_new_id(), EVP_PKEY_CTX_dup() returns either the newly allocated <b>EVP_PKEY_CTX</b> structure of <b>NULL</b> if an error occurred.</p>

<p>EVP_PKEY_CTX_free() does not return a value.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_PKEY_new.html">EVP_PKEY_new(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 1.0.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2006-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


