.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CTLOG_NEW 3"
.TH CTLOG_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CTLOG_new, CTLOG_new_from_base64, CTLOG_free,
CTLOG_get0_name, CTLOG_get0_log_id, CTLOG_get0_public_key \-
encapsulates information about a Certificate Transparency log
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ct.h>
\&
\& CTLOG *CTLOG_new(EVP_PKEY *public_key, const char *name);
\& int CTLOG_new_from_base64(CTLOG ** ct_log,
\&                           const char *pkey_base64, const char *name);
\& void CTLOG_free(CTLOG *log);
\& const char *CTLOG_get0_name(const CTLOG *log);
\& void CTLOG_get0_log_id(const CTLOG *log, const uint8_t **log_id,
\&                        size_t *log_id_len);
\& EVP_PKEY *CTLOG_get0_public_key(const CTLOG *log);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCTLOG_new()\fR returns a new CTLOG that represents the Certificate Transparency
(CT) log with the given public key. A name must also be provided that can be
used to help users identify this log. Ownership of the public key is
transferred.
.PP
\&\fBCTLOG_new_from_base64()\fR also creates a new CTLOG, but takes the public key in
base64\-encoded DER form and sets the ct_log pointer to point to the new CTLOG.
The base64 will be decoded and the public key parsed.
.PP
Regardless of whether \fBCTLOG_new()\fR or \fBCTLOG_new_from_base64()\fR is used, it is the
caller's responsibility to pass the CTLOG to \fBCTLOG_free()\fR once it is no longer
needed. This will delete it and, if created by \fBCTLOG_new()\fR, the EVP_PKEY that
was passed to it.
.PP
\&\fBCTLOG_get0_name()\fR returns the name of the log, as provided when the CTLOG was
created. Ownership of the string remains with the CTLOG.
.PP
\&\fBCTLOG_get0_log_id()\fR sets *log_id to point to a string containing that log's
LogID (see RFC 6962). It sets *log_id_len to the length of that LogID. For a
v1 CT log, the LogID will be a SHA\-256 hash (i.e. 32 bytes long). Ownership of
the string remains with the CTLOG.
.PP
\&\fBCTLOG_get0_public_key()\fR returns the public key of the CT log. Ownership of the
EVP_PKEY remains with the CTLOG.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCTLOG_new()\fR will return NULL if an error occurs.
.PP
\&\fBCTLOG_new_from_base64()\fR will return 1 on success, 0 otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBct\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
