.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DSA_SIZE 3"
.TH DSA_SIZE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DSA_size, DSA_bits, DSA_security_bits \- get DSA signature size, key bits or security bits
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dsa.h>
\&
\& int DSA_size(const DSA *dsa);
\& int DSA_bits(const DSA *dsa);
\& int DSA_security_bits(const DSA *dsa);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBDSA_size()\fR returns the maximum size of an ASN.1 encoded DSA signature
for key \fBdsa\fR in bytes. It can be used to determine how much memory must
be allocated for a DSA signature.
.PP
\&\fBdsa\->q\fR must not be \fBNULL\fR.
.PP
\&\fBDSA_bits()\fR returns the number of bits in key \fBdsa\fR: this is the number
of bits in the \fBp\fR parameter.
.PP
\&\fBDSA_security_bits()\fR returns the number of security bits of the given \fBdsa\fR
key. See \fBBN_security_bits\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDSA_size()\fR returns the signature size in bytes.
.PP
\&\fBDSA_bits()\fR returns the number of bits in the key.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDSA_new\fR\|(3), \fBDSA_sign\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
