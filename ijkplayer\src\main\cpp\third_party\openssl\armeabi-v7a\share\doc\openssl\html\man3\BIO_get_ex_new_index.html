<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BIO_get_ex_new_index</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BIO_get_ex_new_index, BIO_set_ex_data, BIO_get_ex_data, ENGINE_get_ex_new_index, ENGINE_set_ex_data, ENGINE_get_ex_data, UI_get_ex_new_index, UI_set_ex_data, UI_get_ex_data, X509_get_ex_new_index, X509_set_ex_data, X509_get_ex_data, X509_STORE_get_ex_new_index, X509_STORE_set_ex_data, X509_STORE_get_ex_data, X509_STORE_CTX_get_ex_new_index, X509_STORE_CTX_set_ex_data, X509_STORE_CTX_get_ex_data, DH_get_ex_new_index, DH_set_ex_data, DH_get_ex_data, DSA_get_ex_new_index, DSA_set_ex_data, DSA_get_ex_data, ECDH_get_ex_new_index, ECDH_set_ex_data, ECDH_get_ex_data, EC_KEY_get_ex_new_index, EC_KEY_set_ex_data, EC_KEY_get_ex_data, RSA_get_ex_new_index, RSA_set_ex_data, RSA_get_ex_data - application-specific data</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

int TYPE_get_ex_new_index(long argl, void *argp,
                          CRYPTO_EX_new *new_func,
                          CRYPTO_EX_dup *dup_func,
                          CRYPTO_EX_free *free_func);

int TYPE_set_ex_data(TYPE *d, int idx, void *arg);

void *TYPE_get_ex_data(TYPE *d, int idx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>In the description here, <i>TYPE</i> is used a placeholder for any of the OpenSSL datatypes listed in <a href="../man3/CRYPTO_get_ex_new_index.html">CRYPTO_get_ex_new_index(3)</a>.</p>

<p>These functions handle application-specific data for OpenSSL data structures.</p>

<p>TYPE_get_ex_new_index() is a macro that calls CRYPTO_get_ex_new_index() with the correct <b>index</b> value.</p>

<p>TYPE_set_ex_data() is a function that calls CRYPTO_set_ex_data() with an offset into the opaque exdata part of the TYPE object.</p>

<p>TYPE_get_ex_data() is a function that calls CRYPTO_get_ex_data() with an offset into the opaque exdata part of the TYPE object.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>TYPE_get_ex_new_index() returns a new index on success or -1 on error.</p>

<p>TYPE_set_ex_data() returns 1 on success or 0 on error.</p>

<p>TYPE_get_ex_data() returns the application data or NULL if an error occurred.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/CRYPTO_get_ex_new_index.html">CRYPTO_get_ex_new_index(3)</a>.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


