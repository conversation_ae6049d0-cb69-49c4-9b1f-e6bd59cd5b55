<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ERR_load_crypto_strings</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ERR_load_crypto_strings, SSL_load_error_strings, ERR_free_strings - load and free error strings</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p>Deprecated:</p>

<pre><code>#include &lt;openssl/err.h&gt;

#if OPENSSL_API_COMPAT &lt; 0x10100000L
void ERR_load_crypto_strings(void);
void ERR_free_strings(void);
#endif

#include &lt;openssl/ssl.h&gt;

#if OPENSSL_API_COMPAT &lt; 0x10100000L
void SSL_load_error_strings(void);
#endif</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>ERR_load_crypto_strings() registers the error strings for all <b>libcrypto</b> functions. SSL_load_error_strings() does the same, but also registers the <b>libssl</b> error strings.</p>

<p>In versions prior to OpenSSL 1.1.0, ERR_free_strings() releases any resources created by the above functions.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>ERR_load_crypto_strings(), SSL_load_error_strings() and ERR_free_strings() return no values.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_error_string.html">ERR_error_string(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The ERR_load_crypto_strings(), SSL_load_error_strings(), and ERR_free_strings() functions were deprecated in OpenSSL 1.1.0 by OPENSSL_init_crypto() and OPENSSL_init_ssl() and should not be used.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2017 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


