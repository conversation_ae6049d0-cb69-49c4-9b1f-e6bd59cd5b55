.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_PRINT_PRIVATE 3"
.TH EVP_PKEY_PRINT_PRIVATE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_print_public, EVP_PKEY_print_private, EVP_PKEY_print_params \- public key algorithm printing routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_print_public(BIO *out, const EVP_PKEY *pkey,
\&                           int indent, ASN1_PCTX *pctx);
\& int EVP_PKEY_print_private(BIO *out, const EVP_PKEY *pkey,
\&                            int indent, ASN1_PCTX *pctx);
\& int EVP_PKEY_print_params(BIO *out, const EVP_PKEY *pkey,
\&                           int indent, ASN1_PCTX *pctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The functions \fBEVP_PKEY_print_public()\fR, \fBEVP_PKEY_print_private()\fR and
\&\fBEVP_PKEY_print_params()\fR print out the public, private or parameter components
of key \fBpkey\fR respectively. The key is sent to BIO \fBout\fR in human readable
form. The parameter \fBindent\fR indicated how far the printout should be indented.
.PP
The \fBpctx\fR parameter allows the print output to be finely tuned by using
ASN1 printing options. If \fBpctx\fR is set to NULL then default values will
be used.
.SH NOTES
.IX Header "NOTES"
Currently no public key algorithms include any options in the \fBpctx\fR parameter.
.PP
If the key does not include all the components indicated by the function then
only those contained in the key will be printed. For example passing a public
key to \fBEVP_PKEY_print_private()\fR will only print the public components.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions all return 1 for success and 0 or a negative value for failure.
In particular a return value of \-2 indicates the operation is not supported by
the public key algorithm.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_keygen\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
