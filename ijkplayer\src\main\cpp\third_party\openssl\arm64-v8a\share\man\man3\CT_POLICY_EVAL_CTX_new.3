.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CT_POLICY_EVAL_CTX_NEW 3"
.TH CT_POLICY_EVAL_CTX_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CT_POLICY_EVAL_CTX_new, CT_POLICY_EVAL_CTX_free,
CT_POLICY_EVAL_CTX_get0_cert, CT_POLICY_EVAL_CTX_set1_cert,
CT_POLICY_EVAL_CTX_get0_issuer, CT_POLICY_EVAL_CTX_set1_issuer,
CT_POLICY_EVAL_CTX_get0_log_store, CT_POLICY_EVAL_CTX_set_shared_CTLOG_STORE,
CT_POLICY_EVAL_CTX_get_time, CT_POLICY_EVAL_CTX_set_time \-
Encapsulates the data required to evaluate whether SCTs meet a Certificate Transparency policy
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ct.h>
\&
\& CT_POLICY_EVAL_CTX *CT_POLICY_EVAL_CTX_new(void);
\& void CT_POLICY_EVAL_CTX_free(CT_POLICY_EVAL_CTX *ctx);
\& X509* CT_POLICY_EVAL_CTX_get0_cert(const CT_POLICY_EVAL_CTX *ctx);
\& int CT_POLICY_EVAL_CTX_set1_cert(CT_POLICY_EVAL_CTX *ctx, X509 *cert);
\& X509* CT_POLICY_EVAL_CTX_get0_issuer(const CT_POLICY_EVAL_CTX *ctx);
\& int CT_POLICY_EVAL_CTX_set1_issuer(CT_POLICY_EVAL_CTX *ctx, X509 *issuer);
\& const CTLOG_STORE *CT_POLICY_EVAL_CTX_get0_log_store(const CT_POLICY_EVAL_CTX *ctx);
\& void CT_POLICY_EVAL_CTX_set_shared_CTLOG_STORE(CT_POLICY_EVAL_CTX *ctx,
\&                                                CTLOG_STORE *log_store);
\& uint64_t CT_POLICY_EVAL_CTX_get_time(const CT_POLICY_EVAL_CTX *ctx);
\& void CT_POLICY_EVAL_CTX_set_time(CT_POLICY_EVAL_CTX *ctx, uint64_t time_in_ms);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A \fBCT_POLICY_EVAL_CTX\fR is used by functions that evaluate whether Signed
Certificate Timestamps (SCTs) fulfil a Certificate Transparency (CT) policy.
This policy may be, for example, that at least one valid SCT is available. To
determine this, an SCT's timestamp and signature must be verified.
This requires:
.IP \(bu 2
the public key of the log that issued the SCT
.IP \(bu 2
the certificate that the SCT was issued for
.IP \(bu 2
the issuer certificate (if the SCT was issued for a pre-certificate)
.IP \(bu 2
the current time
.PP
The above requirements are met using the setters described below.
.PP
\&\fBCT_POLICY_EVAL_CTX_new()\fR creates an empty policy evaluation context. This
should then be populated using:
.IP \(bu 2
\&\fBCT_POLICY_EVAL_CTX_set1_cert()\fR to provide the certificate the SCTs were issued for
.Sp
Increments the reference count of the certificate.
.IP \(bu 2
\&\fBCT_POLICY_EVAL_CTX_set1_issuer()\fR to provide the issuer certificate
.Sp
Increments the reference count of the certificate.
.IP \(bu 2
\&\fBCT_POLICY_EVAL_CTX_set_shared_CTLOG_STORE()\fR to provide a list of logs that are trusted as sources of SCTs
.Sp
Holds a pointer to the CTLOG_STORE, so the CTLOG_STORE must outlive the
CT_POLICY_EVAL_CTX.
.IP \(bu 2
\&\fBCT_POLICY_EVAL_CTX_set_time()\fR to set the time SCTs should be compared with to determine if they are valid
.Sp
The SCT timestamp will be compared to this time to check whether the SCT was
issued in the future. RFC6962 states that "TLS clients MUST reject SCTs whose
timestamp is in the future". By default, this will be set to 5 minutes in the
future (e.g. (\fBtime()\fR + 300) * 1000), to allow for clock drift.
.Sp
The time should be in milliseconds since the Unix epoch.
.PP
Each setter has a matching getter for accessing the current value.
.PP
When no longer required, the \fBCT_POLICY_EVAL_CTX\fR should be passed to
\&\fBCT_POLICY_EVAL_CTX_free()\fR to delete it.
.SH NOTES
.IX Header "NOTES"
The issuer certificate only needs to be provided if at least one of the SCTs
was issued for a pre-certificate. This will be the case for SCTs embedded in a
certificate (i.e. those in an X.509 extension), but may not be the case for SCTs
found in the TLS SCT extension or OCSP response.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCT_POLICY_EVAL_CTX_new()\fR will return NULL if malloc fails.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBct\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
