.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EC_GFP_SIMPLE_METHOD 3"
.TH EC_GFP_SIMPLE_METHOD 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EC_GFp_simple_method, EC_GFp_mont_method, EC_GFp_nist_method, EC_GFp_nistp224_method, EC_GFp_nistp256_method, EC_GFp_nistp521_method, EC_GF2m_simple_method, EC_METHOD_get_field_type \- Functions for obtaining EC_METHOD objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ec.h>
\&
\& const EC_METHOD *EC_GFp_simple_method(void);
\& const EC_METHOD *EC_GFp_mont_method(void);
\& const EC_METHOD *EC_GFp_nist_method(void);
\& const EC_METHOD *EC_GFp_nistp224_method(void);
\& const EC_METHOD *EC_GFp_nistp256_method(void);
\& const EC_METHOD *EC_GFp_nistp521_method(void);
\&
\& const EC_METHOD *EC_GF2m_simple_method(void);
\&
\& int EC_METHOD_get_field_type(const EC_METHOD *meth);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The Elliptic Curve library provides a number of different implementations through a single common interface.
When constructing a curve using EC_GROUP_new (see \fBEC_GROUP_new\fR\|(3)) an
implementation method must be provided. The functions described here all return a const pointer to an
\&\fBEC_METHOD\fR structure that can be passed to EC_GROUP_NEW. It is important that the correct implementation
type for the form of curve selected is used.
.PP
For F2^m curves there is only one implementation choice, i.e. EC_GF2_simple_method.
.PP
For Fp curves the lowest common denominator implementation is the EC_GFp_simple_method implementation. All
other implementations are based on this one. EC_GFp_mont_method builds on EC_GFp_simple_method but adds the
use of montgomery multiplication (see \fBBN_mod_mul_montgomery\fR\|(3)). EC_GFp_nist_method
offers an implementation optimised for use with NIST recommended curves (NIST curves are available through
EC_GROUP_new_by_curve_name as described in \fBEC_GROUP_new\fR\|(3)).
.PP
The functions EC_GFp_nistp224_method, EC_GFp_nistp256_method and EC_GFp_nistp521_method offer 64 bit
optimised implementations for the NIST P224, P256 and P521 curves respectively. Note, however, that these
implementations are not available on all platforms.
.PP
EC_METHOD_get_field_type identifies what type of field the EC_METHOD structure supports, which will be either
F2^m or Fp. If the field type is Fp then the value \fBNID_X9_62_prime_field\fR is returned. If the field type is
F2^m then the value \fBNID_X9_62_characteristic_two_field\fR is returned. These values are defined in the
obj_mac.h header file.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All EC_GFp* functions and EC_GF2m_simple_method always return a const pointer to an EC_METHOD structure.
.PP
EC_METHOD_get_field_type returns an integer that identifies the type of field the EC_METHOD structure supports.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBEC_GROUP_new\fR\|(3), \fBEC_GROUP_copy\fR\|(3),
\&\fBEC_POINT_new\fR\|(3), \fBEC_POINT_add\fR\|(3), \fBEC_KEY_new\fR\|(3),
\&\fBd2i_ECPKParameters\fR\|(3),
\&\fBBN_mod_mul_montgomery\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2013\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
