.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_METH_NEW 3"
.TH BIO_METH_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_get_new_index,
BIO_meth_new, BIO_meth_free, BIO_meth_get_read_ex, BIO_meth_set_read_ex,
BIO_meth_get_write_ex, BIO_meth_set_write_ex, BIO_meth_get_write,
BIO_meth_set_write, BIO_meth_get_read, BIO_meth_set_read, BIO_meth_get_puts,
BIO_meth_set_puts, BIO_meth_get_gets, BIO_meth_set_gets, BIO_meth_get_ctrl,
BIO_meth_set_ctrl, BIO_meth_get_create, BIO_meth_set_create,
BIO_meth_get_destroy, BIO_meth_set_destroy, BIO_meth_get_callback_ctrl,
BIO_meth_set_callback_ctrl \- Routines to build up BIO methods
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& int BIO_get_new_index(void);
\&
\& BIO_METHOD *BIO_meth_new(int type, const char *name);
\&
\& void BIO_meth_free(BIO_METHOD *biom);
\&
\& int (*BIO_meth_get_write_ex(const BIO_METHOD *biom))(BIO *, const char *, size_t,
\&                                                size_t *);
\& int (*BIO_meth_get_write(const BIO_METHOD *biom))(BIO *, const char *, int);
\& int BIO_meth_set_write_ex(BIO_METHOD *biom,
\&                           int (*bwrite)(BIO *, const char *, size_t, size_t *));
\& int BIO_meth_set_write(BIO_METHOD *biom,
\&                        int (*write)(BIO *, const char *, int));
\&
\& int (*BIO_meth_get_read_ex(const BIO_METHOD *biom))(BIO *, char *, size_t, size_t *);
\& int (*BIO_meth_get_read(const BIO_METHOD *biom))(BIO *, char *, int);
\& int BIO_meth_set_read_ex(BIO_METHOD *biom,
\&                          int (*bread)(BIO *, char *, size_t, size_t *));
\& int BIO_meth_set_read(BIO_METHOD *biom, int (*read)(BIO *, char *, int));
\&
\& int (*BIO_meth_get_puts(const BIO_METHOD *biom))(BIO *, const char *);
\& int BIO_meth_set_puts(BIO_METHOD *biom, int (*puts)(BIO *, const char *));
\&
\& int (*BIO_meth_get_gets(const BIO_METHOD *biom))(BIO *, char *, int);
\& int BIO_meth_set_gets(BIO_METHOD *biom,
\&                       int (*gets)(BIO *, char *, int));
\&
\& long (*BIO_meth_get_ctrl(const BIO_METHOD *biom))(BIO *, int, long, void *);
\& int BIO_meth_set_ctrl(BIO_METHOD *biom,
\&                       long (*ctrl)(BIO *, int, long, void *));
\&
\& int (*BIO_meth_get_create(const BIO_METHOD *bion))(BIO *);
\& int BIO_meth_set_create(BIO_METHOD *biom, int (*create)(BIO *));
\&
\& int (*BIO_meth_get_destroy(const BIO_METHOD *biom))(BIO *);
\& int BIO_meth_set_destroy(BIO_METHOD *biom, int (*destroy)(BIO *));
\&
\& long (*BIO_meth_get_callback_ctrl(const BIO_METHOD *biom))(BIO *, int, BIO_info_cb *);
\& int BIO_meth_set_callback_ctrl(BIO_METHOD *biom,
\&                                long (*callback_ctrl)(BIO *, int, BIO_info_cb *));
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBBIO_METHOD\fR type is a structure used for the implementation of new BIO
types. It provides a set of functions used by OpenSSL for the implementation
of the various BIO capabilities. See the bio page for more information.
.PP
\&\fBBIO_meth_new()\fR creates a new \fBBIO_METHOD\fR structure. It should be given a
unique integer \fBtype\fR and a string that represents its \fBname\fR.
Use \fBBIO_get_new_index()\fR to get the value for \fBtype\fR.
.PP
The set of
standard OpenSSL provided BIO types is provided in \fBbio.h\fR. Some examples
include \fBBIO_TYPE_BUFFER\fR and \fBBIO_TYPE_CIPHER\fR. Filter BIOs should have a
type which have the "filter" bit set (\fBBIO_TYPE_FILTER\fR). Source/sink BIOs
should have the "source/sink" bit set (\fBBIO_TYPE_SOURCE_SINK\fR). File descriptor
based BIOs (e.g. socket, fd, connect, accept etc) should additionally have the
"descriptor" bit set (\fBBIO_TYPE_DESCRIPTOR\fR). See the BIO_find_type page for
more information.
.PP
\&\fBBIO_meth_free()\fR destroys a \fBBIO_METHOD\fR structure and frees up any memory
associated with it.
.PP
\&\fBBIO_meth_get_write_ex()\fR and \fBBIO_meth_set_write_ex()\fR get and set the function
used for writing arbitrary length data to the BIO respectively. This function
will be called in response to the application calling \fBBIO_write_ex()\fR or
\&\fBBIO_write()\fR. The parameters for the function have the same meaning as for
\&\fBBIO_write_ex()\fR. Older code may call \fBBIO_meth_get_write()\fR and
\&\fBBIO_meth_set_write()\fR instead. Applications should not call both
\&\fBBIO_meth_set_write_ex()\fR and \fBBIO_meth_set_write()\fR or call \fBBIO_meth_get_write()\fR
when the function was set with \fBBIO_meth_set_write_ex()\fR.
.PP
\&\fBBIO_meth_get_read_ex()\fR and \fBBIO_meth_set_read_ex()\fR get and set the function used
for reading arbitrary length data from the BIO respectively. This function will
be called in response to the application calling \fBBIO_read_ex()\fR or \fBBIO_read()\fR.
The parameters for the function have the same meaning as for \fBBIO_read_ex()\fR.
Older code may call \fBBIO_meth_get_read()\fR and \fBBIO_meth_set_read()\fR instead.
Applications should not call both \fBBIO_meth_set_read_ex()\fR and \fBBIO_meth_set_read()\fR
or call \fBBIO_meth_get_read()\fR when the function was set with
\&\fBBIO_meth_set_read_ex()\fR.
.PP
\&\fBBIO_meth_get_puts()\fR and \fBBIO_meth_set_puts()\fR get and set the function used for
writing a NULL terminated string to the BIO respectively. This function will be
called in response to the application calling \fBBIO_puts()\fR. The parameters for
the function have the same meaning as for \fBBIO_puts()\fR.
.PP
\&\fBBIO_meth_get_gets()\fR and \fBBIO_meth_set_gets()\fR get and set the function typically
used for reading a line of data from the BIO respectively (see the \fBBIO_gets\fR\|(3)
page for more information). This function will be called in response to the
application calling \fBBIO_gets()\fR. The parameters for the function have the same
meaning as for \fBBIO_gets()\fR.
.PP
\&\fBBIO_meth_get_ctrl()\fR and \fBBIO_meth_set_ctrl()\fR get and set the function used for
processing ctrl messages in the BIO respectively. See the BIO_ctrl page for
more information. This function will be called in response to the application
calling \fBBIO_ctrl()\fR. The parameters for the function have the same meaning as for
\&\fBBIO_ctrl()\fR.
.PP
\&\fBBIO_meth_get_create()\fR and \fBBIO_meth_set_create()\fR get and set the function used
for creating a new instance of the BIO respectively. This function will be
called in response to the application calling \fBBIO_new()\fR and passing
in a pointer to the current BIO_METHOD. The \fBBIO_new()\fR function will allocate the
memory for the new BIO, and a pointer to this newly allocated structure will
be passed as a parameter to the function.
.PP
\&\fBBIO_meth_get_destroy()\fR and \fBBIO_meth_set_destroy()\fR get and set the function used
for destroying an instance of a BIO respectively. This function will be
called in response to the application calling \fBBIO_free()\fR. A pointer to the BIO
to be destroyed is passed as a parameter. The destroy function should be used
for BIO specific clean up. The memory for the BIO itself should not be freed by
this function.
.PP
\&\fBBIO_meth_get_callback_ctrl()\fR and \fBBIO_meth_set_callback_ctrl()\fR get and set the
function used for processing callback ctrl messages in the BIO respectively. See
the \fBBIO_callback_ctrl\fR\|(3) page for more information. This function will be called
in response to the application calling \fBBIO_callback_ctrl()\fR. The parameters for
the function have the same meaning as for \fBBIO_callback_ctrl()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_get_new_index()\fR returns the new BIO type value or \-1 if an error occurred.
.PP
BIO_meth_new(int type, const char *name) returns a valid \fBBIO_METHOD\fR or NULL
if an error occurred.
.PP
The \fBBIO_meth_set\fR functions return 1 on success or 0 on error.
.PP
The \fBBIO_meth_get\fR functions return the corresponding function pointers.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
bio, BIO_find_type, BIO_ctrl, BIO_read_ex, BIO_new
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
