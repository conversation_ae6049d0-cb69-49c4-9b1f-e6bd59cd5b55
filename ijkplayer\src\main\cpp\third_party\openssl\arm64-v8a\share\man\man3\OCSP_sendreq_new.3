.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OCSP_SENDREQ_NEW 3"
.TH OCSP_SENDREQ_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OCSP_sendreq_new,
OCSP_sendreq_nbio,
OCSP_REQ_CTX_free,
OCSP_set_max_response_length,
OCSP_REQ_CTX_add1_header,
OCSP_REQ_CTX_set1_req,
OCSP_sendreq_bio,
OCSP_REQ_CTX_i2d
\&\- OCSP responder query functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ocsp.h>
\&
\& OCSP_REQ_CTX *OCSP_sendreq_new(BIO *io, const char *path, OCSP_REQUEST *req,
\&                                int maxline);
\&
\& int OCSP_sendreq_nbio(OCSP_RESPONSE **presp, OCSP_REQ_CTX *rctx);
\&
\& void OCSP_REQ_CTX_free(OCSP_REQ_CTX *rctx);
\&
\& void OCSP_set_max_response_length(OCSP_REQ_CTX *rctx, unsigned long len);
\&
\& int OCSP_REQ_CTX_add1_header(OCSP_REQ_CTX *rctx,
\&                              const char *name, const char *value);
\&
\& int OCSP_REQ_CTX_set1_req(OCSP_REQ_CTX *rctx, OCSP_REQUEST *req);
\&
\& OCSP_RESPONSE *OCSP_sendreq_bio(BIO *io, const char *path, OCSP_REQUEST *req);
\&
\& int OCSP_REQ_CTX_i2d(OCSP_REQ_CTX *rctx, const char *content_type,
\&                      const ASN1_ITEM *it, ASN1_VALUE *req);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The function \fBOCSP_sendreq_new()\fR returns an \fBOCSP_CTX\fR structure using the
responder \fBio\fR, the URL path \fBpath\fR, the OCSP request \fBreq\fR and with a
response header maximum line length of \fBmaxline\fR. If \fBmaxline\fR is zero a
default value of 4k is used. The OCSP request \fBreq\fR may be set to \fBNULL\fR
and provided later if required.
.PP
\&\fBOCSP_sendreq_nbio()\fR performs nonblocking I/O on the OCSP request context
\&\fBrctx\fR. When the operation is complete it returns the response in \fB*presp\fR.
.PP
\&\fBOCSP_REQ_CTX_free()\fR frees up the OCSP context \fBrctx\fR.
.PP
\&\fBOCSP_set_max_response_length()\fR sets the maximum response length for \fBrctx\fR
to \fBlen\fR. If the response exceeds this length an error occurs. If not
set a default value of 100k is used.
.PP
\&\fBOCSP_REQ_CTX_add1_header()\fR adds header \fBname\fR with value \fBvalue\fR to the
context \fBrctx\fR. It can be called more than once to add multiple headers.
It \fBMUST\fR be called before any calls to \fBOCSP_sendreq_nbio()\fR. The \fBreq\fR
parameter in the initial to \fBOCSP_sendreq_new()\fR call MUST be set to \fBNULL\fR if
additional headers are set.
.PP
\&\fBOCSP_REQ_CTX_set1_req()\fR sets the OCSP request in \fBrctx\fR to \fBreq\fR. This
function should be called after any calls to \fBOCSP_REQ_CTX_add1_header()\fR.
OCSP_REQ_CTX_set1_req(rctx, req) is equivalent to the following:
.PP
.Vb 2
\& OCSP_REQ_CTX_i2d(rctx, "application/ocsp\-request",
\&                        ASN1_ITEM_rptr(OCSP_REQUEST), (ASN1_VALUE *)req)
.Ve
.PP
\&\fBOCSP_REQ_CTX_i2d()\fR sets the request context \fBrctx\fR to have the request
\&\fBreq\fR, which has the ASN.1 type \fBit\fR.
The \fBcontent_type\fR, if not NULL, will be included in the HTTP request.
The function should be called after all other headers have already been added.
.PP
\&\fBOCSP_sendreq_bio()\fR performs an OCSP request using the responder \fBio\fR, the URL
path \fBpath\fR, and the OCSP request \fBreq\fR with a response header maximum line
length 4k. It waits indefinitely on a response.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOCSP_sendreq_new()\fR returns a valid \fBOCSP_REQ_CTX\fR structure or \fBNULL\fR if
an error occurred.
.PP
\&\fBOCSP_sendreq_nbio()\fR returns \fB1\fR if the operation was completed successfully,
\&\fB\-1\fR if the operation should be retried and \fB0\fR if an error occurred.
.PP
\&\fBOCSP_REQ_CTX_add1_header()\fR, \fBOCSP_REQ_CTX_set1_req()\fR, and \fBOCSP_REQ_CTX_i2d()\fR
return \fB1\fR for success and \fB0\fR for failure.
.PP
\&\fBOCSP_sendreq_bio()\fR returns the \fBOCSP_RESPONSE\fR structure sent by the
responder or \fBNULL\fR if an error occurred.
.PP
\&\fBOCSP_REQ_CTX_free()\fR and \fBOCSP_set_max_response_length()\fR do not return values.
.SH NOTES
.IX Header "NOTES"
These functions only perform a minimal HTTP query to a responder. If an
application wishes to support more advanced features it should use an
alternative more complete HTTP library.
.PP
Currently only HTTP POST queries to responders are supported.
.PP
The arguments to \fBOCSP_sendreq_new()\fR correspond to the components of the URL.
For example if the responder URL is \fBhttp://ocsp.com/ocspreq\fR the BIO
\&\fBio\fR should be connected to host \fBocsp.com\fR on port 80 and \fBpath\fR
should be set to \fB"/ocspreq"\fR
.PP
The headers added with \fBOCSP_REQ_CTX_add1_header()\fR are of the form
"\fBname\fR: \fBvalue\fR" or just "\fBname\fR" if \fBvalue\fR is \fBNULL\fR. So to add
a Host header for \fBocsp.com\fR you would call:
.PP
.Vb 1
\& OCSP_REQ_CTX_add1_header(ctx, "Host", "ocsp.com");
.Ve
.PP
If \fBOCSP_sendreq_nbio()\fR indicates an operation should be retried the
corresponding BIO can be examined to determine which operation (read or
write) should be retried and appropriate action taken (for example a \fBselect()\fR
call on the underlying socket).
.PP
\&\fBOCSP_sendreq_bio()\fR does not support retries and so cannot handle nonblocking
I/O efficiently. It is retained for compatibility and its use in new
applications is not recommended.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7),
\&\fBOCSP_cert_to_id\fR\|(3),
\&\fBOCSP_request_add1_nonce\fR\|(3),
\&\fBOCSP_REQUEST_new\fR\|(3),
\&\fBOCSP_resp_find_status\fR\|(3),
\&\fBOCSP_response_status\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
