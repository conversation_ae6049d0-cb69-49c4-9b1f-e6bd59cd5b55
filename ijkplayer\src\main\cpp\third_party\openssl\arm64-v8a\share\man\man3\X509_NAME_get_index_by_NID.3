.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_NAME_GET_INDEX_BY_NID 3"
.TH X509_NAME_GET_INDEX_BY_NID 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_NAME_get_index_by_NID, X509_NAME_get_index_by_OBJ, X509_NAME_get_entry,
X509_NAME_entry_count, X509_NAME_get_text_by_NID, X509_NAME_get_text_by_OBJ \-
X509_NAME lookup and enumeration functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_NAME_get_index_by_NID(X509_NAME *name, int nid, int lastpos);
\& int X509_NAME_get_index_by_OBJ(X509_NAME *name, const ASN1_OBJECT *obj, int lastpos);
\&
\& int X509_NAME_entry_count(const X509_NAME *name);
\& X509_NAME_ENTRY *X509_NAME_get_entry(const X509_NAME *name, int loc);
\&
\& int X509_NAME_get_text_by_NID(X509_NAME *name, int nid, char *buf, int len);
\& int X509_NAME_get_text_by_OBJ(X509_NAME *name, const ASN1_OBJECT *obj, char *buf, int len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions allow an \fBX509_NAME\fR structure to be examined. The
\&\fBX509_NAME\fR structure is the same as the \fBName\fR type defined in
RFC2459 (and elsewhere) and used for example in certificate subject
and issuer names.
.PP
\&\fBX509_NAME_get_index_by_NID()\fR and \fBX509_NAME_get_index_by_OBJ()\fR retrieve
the next index matching \fBnid\fR or \fBobj\fR after \fBlastpos\fR. \fBlastpos\fR
should initially be set to \-1. If there are no more entries \-1 is returned.
If \fBnid\fR is invalid (doesn't correspond to a valid OID) then \-2 is returned.
.PP
\&\fBX509_NAME_entry_count()\fR returns the total number of entries in \fBname\fR.
.PP
\&\fBX509_NAME_get_entry()\fR retrieves the \fBX509_NAME_ENTRY\fR from \fBname\fR
corresponding to index \fBloc\fR. Acceptable values for \fBloc\fR run from
0 to (X509_NAME_entry_count(name) \- 1). The value returned is an
internal pointer which must not be freed.
.PP
\&\fBX509_NAME_get_text_by_NID()\fR, \fBX509_NAME_get_text_by_OBJ()\fR retrieve
the "text" from the first entry in \fBname\fR which matches \fBnid\fR or
\&\fBobj\fR, if no such entry exists \-1 is returned. At most \fBlen\fR bytes
will be written and the text written to \fBbuf\fR will be null
terminated. The length of the output string written is returned
excluding the terminating null. If \fBbuf\fR is <NULL> then the amount
of space needed in \fBbuf\fR (excluding the final null) is returned.
.SH NOTES
.IX Header "NOTES"
\&\fBX509_NAME_get_text_by_NID()\fR and \fBX509_NAME_get_text_by_OBJ()\fR should be
considered deprecated because they
have various limitations which make them
of minimal use in practice. They can only find the first matching
entry and will copy the contents of the field verbatim: this can
be highly confusing if the target is a multicharacter string type
like a BMPString or a UTF8String.
.PP
For a more general solution \fBX509_NAME_get_index_by_NID()\fR or
\&\fBX509_NAME_get_index_by_OBJ()\fR should be used followed by
\&\fBX509_NAME_get_entry()\fR on any matching indices and then the
various \fBX509_NAME_ENTRY\fR utility functions on the result.
.PP
The list of all relevant \fBNID_*\fR and \fBOBJ_* codes\fR can be found in
the source code header files <openssl/obj_mac.h> and/or
<openssl/objects.h>.
.PP
Applications which could pass invalid NIDs to \fBX509_NAME_get_index_by_NID()\fR
should check for the return value of \-2. Alternatively the NID validity
can be determined first by checking OBJ_nid2obj(nid) is not NULL.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_NAME_get_index_by_NID()\fR and \fBX509_NAME_get_index_by_OBJ()\fR
return the index of the next matching entry or \-1 if not found.
\&\fBX509_NAME_get_index_by_NID()\fR can also return \-2 if the supplied
NID is invalid.
.PP
\&\fBX509_NAME_entry_count()\fR returns the total number of entries.
.PP
\&\fBX509_NAME_get_entry()\fR returns an \fBX509_NAME\fR pointer to the
requested entry or \fBNULL\fR if the index is invalid.
.SH EXAMPLES
.IX Header "EXAMPLES"
Process all entries:
.PP
.Vb 2
\& int i;
\& X509_NAME_ENTRY *e;
\&
\& for (i = 0; i < X509_NAME_entry_count(nm); i++) {
\&     e = X509_NAME_get_entry(nm, i);
\&     /* Do something with e */
\& }
.Ve
.PP
Process all commonName entries:
.PP
.Vb 2
\& int lastpos = \-1;
\& X509_NAME_ENTRY *e;
\&
\& for (;;) {
\&     lastpos = X509_NAME_get_index_by_NID(nm, NID_commonName, lastpos);
\&     if (lastpos == \-1)
\&         break;
\&     e = X509_NAME_get_entry(nm, lastpos);
\&     /* Do something with e */
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBd2i_X509_NAME\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
