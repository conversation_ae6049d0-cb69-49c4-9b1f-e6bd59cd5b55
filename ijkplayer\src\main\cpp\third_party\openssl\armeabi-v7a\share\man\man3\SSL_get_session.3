.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_SESSION 3"
.TH SSL_GET_SESSION 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_session, SSL_get0_session, SSL_get1_session \- retrieve TLS/SSL session data
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& SSL_SESSION *SSL_get_session(const SSL *ssl);
\& SSL_SESSION *SSL_get0_session(const SSL *ssl);
\& SSL_SESSION *SSL_get1_session(SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_session()\fR returns a pointer to the \fBSSL_SESSION\fR actually used in
\&\fBssl\fR. The reference count of the \fBSSL_SESSION\fR is not incremented, so
that the pointer can become invalid by other operations.
.PP
\&\fBSSL_get0_session()\fR is the same as \fBSSL_get_session()\fR.
.PP
\&\fBSSL_get1_session()\fR is the same as \fBSSL_get_session()\fR, but the reference
count of the \fBSSL_SESSION\fR is incremented by one.
.SH NOTES
.IX Header "NOTES"
The ssl session contains all information required to re-establish the
connection without a full handshake for SSL versions up to and including
TLSv1.2. In TLSv1.3 the same is true, but sessions are established after the
main handshake has occurred. The server will send the session information to the
client at a time of its choosing, which may be some while after the initial
connection is established (or never). Calling these functions on the client side
in TLSv1.3 before the session has been established will still return an
SSL_SESSION object but that object cannot be used for resuming the session. See
\&\fBSSL_SESSION_is_resumable\fR\|(3) for information on how to determine whether an
SSL_SESSION object can be used for resumption or not.
.PP
Additionally, in TLSv1.3, a server can send multiple messages that establish a
session for a single connection. In that case, on the client side, the above
functions will only return information on the last session that was received. On
the server side they will only return information on the last session that was
sent, or if no session tickets were sent then the session for the current
connection.
.PP
The preferred way for applications to obtain a resumable SSL_SESSION object is
to use a new session callback as described in \fBSSL_CTX_sess_set_new_cb\fR\|(3).
The new session callback is only invoked when a session is actually established,
so this avoids the problem described above where an application obtains an
SSL_SESSION object that cannot be used for resumption in TLSv1.3. It also
enables applications to obtain information about all sessions sent by the
server.
.PP
A session will be automatically removed from the session cache and marked as
non-resumable if the connection is not closed down cleanly, e.g. if a fatal
error occurs on the connection or \fBSSL_shutdown\fR\|(3) is not called prior to
\&\fBSSL_free\fR\|(3).
.PP
In TLSv1.3 it is recommended that each SSL_SESSION object is only used for
resumption once.
.PP
\&\fBSSL_get0_session()\fR returns a pointer to the actual session. As the
reference counter is not incremented, the pointer is only valid while
the connection is in use. If \fBSSL_clear\fR\|(3) or
\&\fBSSL_free\fR\|(3) is called, the session may be removed completely
(if considered bad), and the pointer obtained will become invalid. Even
if the session is valid, it can be removed at any time due to timeout
during \fBSSL_CTX_flush_sessions\fR\|(3).
.PP
If the data is to be kept, \fBSSL_get1_session()\fR will increment the reference
count, so that the session will not be implicitly removed by other operations
but stays in memory. In order to remove the session
\&\fBSSL_SESSION_free\fR\|(3) must be explicitly called once
to decrement the reference count again.
.PP
SSL_SESSION objects keep internal link information about the session cache
list, when being inserted into one SSL_CTX object's session cache.
One SSL_SESSION object, regardless of its reference count, must therefore
only be used with one SSL_CTX object (and the SSL objects created
from this SSL_CTX object).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur:
.IP NULL 4
.IX Item "NULL"
There is no session available in \fBssl\fR.
.IP "Pointer to an SSL_SESSION" 4
.IX Item "Pointer to an SSL_SESSION"
The return value points to the data of an SSL session.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_free\fR\|(3),
\&\fBSSL_clear\fR\|(3),
\&\fBSSL_SESSION_free\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
