.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_TMP_DH_CALLBACK 3"
.TH SSL_CTX_SET_TMP_DH_CALLBACK 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_tmp_dh_callback, SSL_CTX_set_tmp_dh, SSL_set_tmp_dh_callback, SSL_set_tmp_dh \- handle DH keys for ephemeral key exchange
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_tmp_dh_callback(SSL_CTX *ctx,
\&                                  DH *(*tmp_dh_callback)(SSL *ssl, int is_export,
\&                                                         int keylength));
\& long SSL_CTX_set_tmp_dh(SSL_CTX *ctx, DH *dh);
\&
\& void SSL_set_tmp_dh_callback(SSL *ctx,
\&                              DH *(*tmp_dh_callback)(SSL *ssl, int is_export,
\&                                                     int keylength));
\& long SSL_set_tmp_dh(SSL *ssl, DH *dh)
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_tmp_dh_callback()\fR sets the callback function for \fBctx\fR to be
used when a DH parameters are required to \fBtmp_dh_callback\fR.
The callback is inherited by all \fBssl\fR objects created from \fBctx\fR.
.PP
\&\fBSSL_CTX_set_tmp_dh()\fR sets DH parameters to be used to be \fBdh\fR.
The key is inherited by all \fBssl\fR objects created from \fBctx\fR.
.PP
\&\fBSSL_set_tmp_dh_callback()\fR sets the callback only for \fBssl\fR.
.PP
\&\fBSSL_set_tmp_dh()\fR sets the parameters only for \fBssl\fR.
.PP
These functions apply to SSL/TLS servers only.
.SH NOTES
.IX Header "NOTES"
When using a cipher with RSA authentication, an ephemeral DH key exchange
can take place. Ciphers with DSA keys always use ephemeral DH keys as well.
In these cases, the session data are negotiated using the
ephemeral/temporary DH key and the key supplied and certified
by the certificate chain is only used for signing.
Anonymous ciphers (without a permanent server key) also use ephemeral DH keys.
.PP
Using ephemeral DH key exchange yields forward secrecy, as the connection
can only be decrypted, when the DH key is known. By generating a temporary
DH key inside the server application that is lost when the application
is left, it becomes impossible for an attacker to decrypt past sessions,
even if he gets hold of the normal (certified) key, as this key was
only used for signing.
.PP
In order to perform a DH key exchange the server must use a DH group
(DH parameters) and generate a DH key. The server will always generate
a new DH key during the negotiation.
.PP
As generating DH parameters is extremely time consuming, an application
should not generate the parameters on the fly but supply the parameters.
DH parameters can be reused, as the actual key is newly generated during
the negotiation. The risk in reusing DH parameters is that an attacker
may specialize on a very often used DH group. Applications should therefore
generate their own DH parameters during the installation process using the
openssl \fBdhparam\fR\|(1) application. This application
guarantees that "strong" primes are used.
.PP
Files dh2048.pem, and dh4096.pem in the 'apps' directory of the current
version of the OpenSSL distribution contain the 'SKIP' DH parameters,
which use safe primes and were generated verifiably pseudo-randomly.
These files can be converted into C code using the \fB\-C\fR option of the
\&\fBdhparam\fR\|(1) application. Generation of custom DH
parameters during installation should still be preferred to stop an
attacker from specializing on a commonly used group. File dh1024.pem
contains old parameters that must not be used by applications.
.PP
An application may either directly specify the DH parameters or
can supply the DH parameters via a callback function.
.PP
Previous versions of the callback used \fBis_export\fR and \fBkeylength\fR
parameters to control parameter generation for export and non-export
cipher suites. Modern servers that do not support export cipher suites
are advised to either use \fBSSL_CTX_set_tmp_dh()\fR or alternatively, use
the callback but ignore \fBkeylength\fR and \fBis_export\fR and simply
supply at least 2048\-bit parameters in the callback.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_tmp_dh_callback()\fR and \fBSSL_set_tmp_dh_callback()\fR do not return
diagnostic output.
.PP
\&\fBSSL_CTX_set_tmp_dh()\fR and \fBSSL_set_tmp_dh()\fR do return 1 on success and 0
on failure. Check the error queue to find out the reason of failure.
.SH EXAMPLES
.IX Header "EXAMPLES"
Setup DH parameters with a key length of 2048 bits. (Error handling
partly left out.)
.PP
Command-line parameter generation:
.PP
.Vb 1
\& $ openssl dhparam \-out dh_param_2048.pem 2048
.Ve
.PP
Code for setting up parameters during server initialization:
.PP
.Vb 1
\& SSL_CTX ctx = SSL_CTX_new();
\&
\& DH *dh_2048 = NULL;
\& FILE *paramfile = fopen("dh_param_2048.pem", "r");
\&
\& if (paramfile) {
\&     dh_2048 = PEM_read_DHparams(paramfile, NULL, NULL, NULL);
\&     fclose(paramfile);
\& } else {
\&     /* Error. */
\& }
\& if (dh_2048 == NULL)
\&     /* Error. */
\& if (SSL_CTX_set_tmp_dh(ctx, dh_2048) != 1)
\&     /* Error. */
\& ...
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_set_cipher_list\fR\|(3),
\&\fBSSL_CTX_set_options\fR\|(3),
\&\fBciphers\fR\|(1), \fBdhparam\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
