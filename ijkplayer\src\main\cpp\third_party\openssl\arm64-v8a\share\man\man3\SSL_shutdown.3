.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SHUTDOWN 3"
.TH SSL_SHUTDOWN 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_shutdown \- shut down a TLS/SSL connection
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_shutdown(SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_shutdown()\fR shuts down an active TLS/SSL connection. It sends the
close_notify shutdown alert to the peer.
.SH NOTES
.IX Header "NOTES"
\&\fBSSL_shutdown()\fR tries to send the close_notify shutdown alert to the peer.
Whether the operation succeeds or not, the SSL_SENT_SHUTDOWN flag is set and
a currently open session is considered closed and good and will be kept in the
session cache for further reuse.
.PP
Note that \fBSSL_shutdown()\fR must not be called if a previous fatal error has
occurred on a connection i.e. if \fBSSL_get_error()\fR has returned SSL_ERROR_SYSCALL
or SSL_ERROR_SSL.
.PP
The shutdown procedure consists of two steps: sending of the close_notify
shutdown alert, and reception of the peer's close_notify shutdown alert.
The order of those two steps depends on the application.
.PP
It is acceptable for an application to only send its shutdown alert and
then close the underlying connection without waiting for the peer's response.
This way resources can be saved, as the process can already terminate or
serve another connection.
This should only be done when it is known that the other side will not send more
data, otherwise there is a risk of a truncation attack.
.PP
When a client only writes and never reads from the connection, and the server
has sent a session ticket to establish a session, the client might not be able
to resume the session because it did not received and process the session ticket
from the server.
In case the application wants to be able to resume the session, it is recommended to
do a complete shutdown procedure (bidirectional close_notify alerts).
.PP
When the underlying connection shall be used for more communications, the
complete shutdown procedure must be performed, so that the peers stay
synchronized.
.PP
\&\fBSSL_shutdown()\fR only closes the write direction.
It is not possible to call \fBSSL_write()\fR after calling \fBSSL_shutdown()\fR.
The read direction is closed by the peer.
.SS "First to close the connection"
.IX Subsection "First to close the connection"
When the application is the first party to send the close_notify
alert, \fBSSL_shutdown()\fR will only send the alert and then set the
SSL_SENT_SHUTDOWN flag (so that the session is considered good and will
be kept in the cache).
If successful, \fBSSL_shutdown()\fR will return 0.
.PP
If a unidirectional shutdown is enough (the underlying connection shall be
closed anyway), this first successful call to \fBSSL_shutdown()\fR is sufficient.
.PP
In order to complete the bidirectional shutdown handshake, the peer needs
to send back a close_notify alert.
The SSL_RECEIVED_SHUTDOWN flag will be set after receiving and processing
it.
.PP
The peer is still allowed to send data after receiving the close_notify
event.
When it is done sending data, it will send the close_notify alert.
\&\fBSSL_read()\fR should be called until all data is received.
\&\fBSSL_read()\fR will indicate the end of the peer data by returning <= 0
and \fBSSL_get_error()\fR returning SSL_ERROR_ZERO_RETURN.
.SS "Peer closes the connection"
.IX Subsection "Peer closes the connection"
If the peer already sent the close_notify alert \fBand\fR it was
already processed implicitly inside another function
(\fBSSL_read\fR\|(3)), the SSL_RECEIVED_SHUTDOWN flag is set.
\&\fBSSL_read()\fR will return <= 0 in that case, and \fBSSL_get_error()\fR will return
SSL_ERROR_ZERO_RETURN.
\&\fBSSL_shutdown()\fR will send the close_notify alert, set the SSL_SENT_SHUTDOWN
flag.
If successful, \fBSSL_shutdown()\fR will return 1.
.PP
Whether SSL_RECEIVED_SHUTDOWN is already set can be checked using the
\&\fBSSL_get_shutdown()\fR (see also \fBSSL_set_shutdown\fR\|(3) call.
.SH NOTES
.IX Header "NOTES"
The behaviour of \fBSSL_shutdown()\fR additionally depends on the underlying BIO.
If the underlying BIO is \fBblocking\fR, \fBSSL_shutdown()\fR will only return once the
handshake step has been finished or an error occurred.
.PP
If the underlying BIO is \fBnonblocking\fR, \fBSSL_shutdown()\fR will also return
when the underlying BIO could not satisfy the needs of \fBSSL_shutdown()\fR
to continue the handshake. In this case a call to \fBSSL_get_error()\fR with the
return value of \fBSSL_shutdown()\fR will yield \fBSSL_ERROR_WANT_READ\fR or
\&\fBSSL_ERROR_WANT_WRITE\fR. The calling process then must repeat the call after
taking appropriate action to satisfy the needs of \fBSSL_shutdown()\fR.
The action depends on the underlying BIO. When using a nonblocking socket,
nothing is to be done, but \fBselect()\fR can be used to check for the required
condition. When using a buffering BIO, like a BIO pair, data must be written
into or retrieved out of the BIO before being able to continue.
.PP
After \fBSSL_shutdown()\fR returned 0, it is possible to call \fBSSL_shutdown()\fR again
to wait for the peer's close_notify alert.
\&\fBSSL_shutdown()\fR will return 1 in that case.
However, it is recommended to wait for it using \fBSSL_read()\fR instead.
.PP
\&\fBSSL_shutdown()\fR can be modified to only set the connection to "shutdown"
state but not actually send the close_notify alert messages,
see \fBSSL_CTX_set_quiet_shutdown\fR\|(3).
When "quiet shutdown" is enabled, \fBSSL_shutdown()\fR will always succeed
and return 1.
Note that this is not standard compliant behaviour.
It should only be done when the peer has a way to make sure all
data has been received and doesn't wait for the close_notify alert
message, otherwise an unexpected EOF will be reported.
.PP
There are implementations that do not send the required close_notify alert.
If there is a need to communicate with such an implementation, and it's clear
that all data has been received, do not wait for the peer's close_notify alert.
Waiting for the close_notify alert when the peer just closes the connection will
result in an error being generated.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur:
.IP 0 4
The shutdown is not yet finished: the close_notify was sent but the peer
did not send it back yet.
Call \fBSSL_read()\fR to do a bidirectional shutdown.
.Sp
Unlike most other function, returning 0 does not indicate an error.
\&\fBSSL_get_error\fR\|(3) should not get called, it may misleadingly
indicate an error even though no error occurred.
.IP 1 4
.IX Item "1"
The shutdown was successfully completed. The close_notify alert was sent
and the peer's close_notify alert was received.
.IP <0 4
.IX Item "<0"
The shutdown was not successful.
Call \fBSSL_get_error\fR\|(3) with the return value \fBret\fR to find out the reason.
It can occur if an action is needed to continue the operation for nonblocking
BIOs.
.Sp
It can also occur when not all data was read using \fBSSL_read()\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_get_error\fR\|(3), \fBSSL_connect\fR\|(3),
\&\fBSSL_accept\fR\|(3), \fBSSL_set_shutdown\fR\|(3),
\&\fBSSL_CTX_set_quiet_shutdown\fR\|(3),
\&\fBSSL_clear\fR\|(3), \fBSSL_free\fR\|(3),
\&\fBssl\fR\|(7), \fBbio\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
