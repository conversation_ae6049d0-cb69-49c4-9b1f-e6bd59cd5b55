.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CRL 1"
.TH CRL 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-crl,
crl \- CRL utility
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBcrl\fR
[\fB\-help\fR]
[\fB\-inform PEM|DER\fR]
[\fB\-outform PEM|DER\fR]
[\fB\-text\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-nameopt option\fR]
[\fB\-noout\fR]
[\fB\-hash\fR]
[\fB\-issuer\fR]
[\fB\-lastupdate\fR]
[\fB\-nextupdate\fR]
[\fB\-CAfile file\fR]
[\fB\-CApath dir\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBcrl\fR command processes CRL files in DER or PEM format.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format. \fBDER\fR format is DER encoded CRL
structure. \fBPEM\fR (the default) is a base64 encoded version of
the DER form with header and footer lines.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format, the options have the same meaning and default
as the \fB\-inform\fR option.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read from or standard input if this
option is not specified.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
Specifies the output filename to write to or standard output by
default.
.IP \fB\-text\fR 4
.IX Item "-text"
Print out the CRL in text form.
.IP "\fB\-nameopt option\fR" 4
.IX Item "-nameopt option"
Option which determines how the subject or issuer names are displayed. See
the description of \fB\-nameopt\fR in \fBx509\fR\|(1).
.IP \fB\-noout\fR 4
.IX Item "-noout"
Don't output the encoded version of the CRL.
.IP \fB\-hash\fR 4
.IX Item "-hash"
Output a hash of the issuer name. This can be use to lookup CRLs in
a directory by issuer name.
.IP \fB\-hash_old\fR 4
.IX Item "-hash_old"
Outputs the "hash" of the CRL issuer name using the older algorithm
as used by OpenSSL before version 1.0.0.
.IP \fB\-issuer\fR 4
.IX Item "-issuer"
Output the issuer name.
.IP \fB\-lastupdate\fR 4
.IX Item "-lastupdate"
Output the lastUpdate field.
.IP \fB\-nextupdate\fR 4
.IX Item "-nextupdate"
Output the nextUpdate field.
.IP "\fB\-CAfile file\fR" 4
.IX Item "-CAfile file"
Verify the signature on a CRL by looking up the issuing certificate in
\&\fBfile\fR.
.IP "\fB\-CApath dir\fR" 4
.IX Item "-CApath dir"
Verify the signature on a CRL by looking up the issuing certificate in
\&\fBdir\fR. This directory must be a standard certificate directory: that
is a hash of each subject name (using \fBx509 \-hash\fR) should be linked
to each certificate.
.SH NOTES
.IX Header "NOTES"
The PEM CRL format uses the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN X509 CRL\-\-\-\-\-
\& \-\-\-\-\-END X509 CRL\-\-\-\-\-
.Ve
.SH EXAMPLES
.IX Header "EXAMPLES"
Convert a CRL file from PEM to DER:
.PP
.Vb 1
\& openssl crl \-in crl.pem \-outform DER \-out crl.der
.Ve
.PP
Output the text form of a DER encoded certificate:
.PP
.Vb 1
\& openssl crl \-in crl.der \-inform DER \-text \-noout
.Ve
.SH BUGS
.IX Header "BUGS"
Ideally it should be possible to create a CRL using appropriate options
and files too.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrl2pkcs7\fR\|(1), \fBca\fR\|(1), \fBx509\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
