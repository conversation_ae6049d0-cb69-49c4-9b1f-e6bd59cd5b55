<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ECDSA_SIG_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ECDSA_SIG_get0, ECDSA_SIG_get0_r, ECDSA_SIG_get0_s, ECDSA_SIG_set0, ECDSA_SIG_new, ECDSA_SIG_free, ECDSA_size, ECDSA_sign, ECDSA_do_sign, ECDSA_verify, ECDSA_do_verify, ECDSA_sign_setup, ECDSA_sign_ex, ECDSA_do_sign_ex - low-level elliptic curve digital signature algorithm (ECDSA) functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ecdsa.h&gt;

ECDSA_SIG *ECDSA_SIG_new(void);
void ECDSA_SIG_free(ECDSA_SIG *sig);
void ECDSA_SIG_get0(const ECDSA_SIG *sig, const BIGNUM **pr, const BIGNUM **ps);
const BIGNUM *ECDSA_SIG_get0_r(const ECDSA_SIG *sig);
const BIGNUM *ECDSA_SIG_get0_s(const ECDSA_SIG *sig);
int ECDSA_SIG_set0(ECDSA_SIG *sig, BIGNUM *r, BIGNUM *s);
int ECDSA_size(const EC_KEY *eckey);

int ECDSA_sign(int type, const unsigned char *dgst, int dgstlen,
               unsigned char *sig, unsigned int *siglen, EC_KEY *eckey);
ECDSA_SIG *ECDSA_do_sign(const unsigned char *dgst, int dgst_len,
                         EC_KEY *eckey);

int ECDSA_verify(int type, const unsigned char *dgst, int dgstlen,
                 const unsigned char *sig, int siglen, EC_KEY *eckey);
int ECDSA_do_verify(const unsigned char *dgst, int dgst_len,
                    const ECDSA_SIG *sig, EC_KEY* eckey);

ECDSA_SIG *ECDSA_do_sign_ex(const unsigned char *dgst, int dgstlen,
                            const BIGNUM *kinv, const BIGNUM *rp,
                            EC_KEY *eckey);
int ECDSA_sign_setup(EC_KEY *eckey, BN_CTX *ctx, BIGNUM **kinv, BIGNUM **rp);
int ECDSA_sign_ex(int type, const unsigned char *dgst, int dgstlen,
                  unsigned char *sig, unsigned int *siglen,
                  const BIGNUM *kinv, const BIGNUM *rp, EC_KEY *eckey);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Note: these functions provide a low-level interface to ECDSA. Most applications should use the higher level <b>EVP</b> interface such as <a href="../man3/EVP_DigestSignInit.html">EVP_DigestSignInit(3)</a> or <a href="../man3/EVP_DigestVerifyInit.html">EVP_DigestVerifyInit(3)</a> instead.</p>

<p><b>ECDSA_SIG</b> is an opaque structure consisting of two BIGNUMs for the <b>r</b> and <b>s</b> value of an ECDSA signature (see X9.62 or FIPS 186-2).</p>

<p>ECDSA_SIG_new() allocates an empty <b>ECDSA_SIG</b> structure. Note: before OpenSSL 1.1.0 the: the <b>r</b> and <b>s</b> components were initialised.</p>

<p>ECDSA_SIG_free() frees the <b>ECDSA_SIG</b> structure <b>sig</b>.</p>

<p>ECDSA_SIG_get0() returns internal pointers the <b>r</b> and <b>s</b> values contained in <b>sig</b> and stores them in <b>*pr</b> and <b>*ps</b>, respectively. The pointer <b>pr</b> or <b>ps</b> can be NULL, in which case the corresponding value is not returned.</p>

<p>The values <b>r</b>, <b>s</b> can also be retrieved separately by the corresponding function ECDSA_SIG_get0_r() and ECDSA_SIG_get0_s(), respectively.</p>

<p>The <b>r</b> and <b>s</b> values can be set by calling ECDSA_SIG_set0() and passing the new values for <b>r</b> and <b>s</b> as parameters to the function. Calling this function transfers the memory management of the values to the ECDSA_SIG object, and therefore the values that have been passed in should not be freed directly after this function has been called.</p>

<p>See <a href="../man3/i2d_ECDSA_SIG.html">i2d_ECDSA_SIG(3)</a> and <a href="../man3/d2i_ECDSA_SIG.html">d2i_ECDSA_SIG(3)</a> for information about encoding and decoding ECDSA signatures to/from DER.</p>

<p>ECDSA_size() returns the maximum length of a DER encoded ECDSA signature created with the private EC key <b>eckey</b>.</p>

<p>ECDSA_sign() computes a digital signature of the <b>dgstlen</b> bytes hash value <b>dgst</b> using the private EC key <b>eckey</b>. The DER encoded signatures is stored in <b>sig</b> and its length is returned in <b>sig_len</b>. Note: <b>sig</b> must point to ECDSA_size(eckey) bytes of memory. The parameter <b>type</b> is currently ignored. ECDSA_sign() is wrapper function for ECDSA_sign_ex() with <b>kinv</b> and <b>rp</b> set to NULL.</p>

<p>ECDSA_do_sign() is similar to ECDSA_sign() except the signature is returned as a newly allocated <b>ECDSA_SIG</b> structure (or NULL on error). ECDSA_do_sign() is a wrapper function for ECDSA_do_sign_ex() with <b>kinv</b> and <b>rp</b> set to NULL.</p>

<p>ECDSA_verify() verifies that the signature in <b>sig</b> of size <b>siglen</b> is a valid ECDSA signature of the hash value <b>dgst</b> of size <b>dgstlen</b> using the public key <b>eckey</b>. The parameter <b>type</b> is ignored.</p>

<p>ECDSA_do_verify() is similar to ECDSA_verify() except the signature is presented in the form of a pointer to an <b>ECDSA_SIG</b> structure.</p>

<p>The remaining functions utilise the internal <b>kinv</b> and <b>r</b> values used during signature computation. Most applications will never need to call these and some external ECDSA ENGINE implementations may not support them at all if either <b>kinv</b> or <b>r</b> is not <b>NULL</b>.</p>

<p>ECDSA_sign_setup() may be used to precompute parts of the signing operation. <b>eckey</b> is the private EC key and <b>ctx</b> is a pointer to <b>BN_CTX</b> structure (or NULL). The precomputed values or returned in <b>kinv</b> and <b>rp</b> and can be used in a later call to ECDSA_sign_ex() or ECDSA_do_sign_ex().</p>

<p>ECDSA_sign_ex() computes a digital signature of the <b>dgstlen</b> bytes hash value <b>dgst</b> using the private EC key <b>eckey</b> and the optional pre-computed values <b>kinv</b> and <b>rp</b>. The DER encoded signature is stored in <b>sig</b> and its length is returned in <b>sig_len</b>. Note: <b>sig</b> must point to ECDSA_size(eckey) bytes of memory. The parameter <b>type</b> is ignored.</p>

<p>ECDSA_do_sign_ex() is similar to ECDSA_sign_ex() except the signature is returned as a newly allocated <b>ECDSA_SIG</b> structure (or NULL on error).</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>ECDSA_SIG_new() returns NULL if the allocation fails.</p>

<p>ECDSA_SIG_set0() returns 1 on success or 0 on failure.</p>

<p>ECDSA_SIG_get0_r() and ECDSA_SIG_get0_s() return the corresponding value, or NULL if it is unset.</p>

<p>ECDSA_size() returns the maximum length signature or 0 on error.</p>

<p>ECDSA_sign(), ECDSA_sign_ex() and ECDSA_sign_setup() return 1 if successful or 0 on error.</p>

<p>ECDSA_do_sign() and ECDSA_do_sign_ex() return a pointer to an allocated <b>ECDSA_SIG</b> structure or NULL on error.</p>

<p>ECDSA_verify() and ECDSA_do_verify() return 1 for a valid signature, 0 for an invalid signature and -1 on error. The error codes can be obtained by <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Creating an ECDSA signature of a given SHA-256 hash value using the named curve prime256v1 (aka P-256).</p>

<p>First step: create an EC_KEY object (note: this part is <b>not</b> ECDSA specific)</p>

<pre><code>int ret;
ECDSA_SIG *sig;
EC_KEY *eckey;

eckey = EC_KEY_new_by_curve_name(NID_X9_62_prime256v1);
if (eckey == NULL)
    /* error */
if (EC_KEY_generate_key(eckey) == 0)
    /* error */</code></pre>

<p>Second step: compute the ECDSA signature of a SHA-256 hash value using ECDSA_do_sign():</p>

<pre><code>sig = ECDSA_do_sign(digest, 32, eckey);
if (sig == NULL)
    /* error */</code></pre>

<p>or using ECDSA_sign():</p>

<pre><code>unsigned char *buffer, *pp;
int buf_len;

buf_len = ECDSA_size(eckey);
buffer = OPENSSL_malloc(buf_len);
pp = buffer;
if (ECDSA_sign(0, dgst, dgstlen, pp, &amp;buf_len, eckey) == 0)
    /* error */</code></pre>

<p>Third step: verify the created ECDSA signature using ECDSA_do_verify():</p>

<pre><code>ret = ECDSA_do_verify(digest, 32, sig, eckey);</code></pre>

<p>or using ECDSA_verify():</p>

<pre><code>ret = ECDSA_verify(0, digest, 32, buffer, buf_len, eckey);</code></pre>

<p>and finally evaluate the return value:</p>

<pre><code>if (ret == 1)
    /* signature ok */
else if (ret == 0)
    /* incorrect signature */
else
    /* error */</code></pre>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>ANSI X9.62, US Federal Information Processing Standard FIPS 186-2 (Digital Signature Standard, DSS)</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EC_KEY_new.html">EC_KEY_new(3)</a>, <a href="../man3/EVP_DigestSignInit.html">EVP_DigestSignInit(3)</a>, <a href="../man3/EVP_DigestVerifyInit.html">EVP_DigestVerifyInit(3)</a>, <a href="../man3/i2d_ECDSA_SIG.html">i2d_ECDSA_SIG(3)</a>, <a href="../man3/d2i_ECDSA_SIG.html">d2i_ECDSA_SIG(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2004-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


