.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_GET0_PARAM 3"
.TH SSL_CTX_GET0_PARAM 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_get0_param, SSL_get0_param, SSL_CTX_set1_param, SSL_set1_param \-
get and set verification parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& X509_VERIFY_PARAM *SSL_CTX_get0_param(SSL_CTX *ctx)
\& X509_VERIFY_PARAM *SSL_get0_param(SSL *ssl)
\& int SSL_CTX_set1_param(SSL_CTX *ctx, X509_VERIFY_PARAM *vpm)
\& int SSL_set1_param(SSL *ssl, X509_VERIFY_PARAM *vpm)
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_get0_param()\fR and \fBSSL_get0_param()\fR retrieve an internal pointer to
the verification parameters for \fBctx\fR or \fBssl\fR respectively. The returned
pointer must not be freed by the calling application.
.PP
\&\fBSSL_CTX_set1_param()\fR and \fBSSL_set1_param()\fR set the verification parameters
to \fBvpm\fR for \fBctx\fR or \fBssl\fR.
.SH NOTES
.IX Header "NOTES"
Typically parameters are retrieved from an \fBSSL_CTX\fR or \fBSSL\fR structure
using \fBSSL_CTX_get0_param()\fR or \fBSSL_get0_param()\fR and an application modifies
them to suit its needs: for example to add a hostname check.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_get0_param()\fR and \fBSSL_get0_param()\fR return a pointer to an
\&\fBX509_VERIFY_PARAM\fR structure.
.PP
\&\fBSSL_CTX_set1_param()\fR and \fBSSL_set1_param()\fR return 1 for success and 0
for failure.
.SH EXAMPLES
.IX Header "EXAMPLES"
Check hostname matches "www.foo.com" in peer certificate:
.PP
.Vb 2
\& X509_VERIFY_PARAM *vpm = SSL_get0_param(ssl);
\& X509_VERIFY_PARAM_set1_host(vpm, "www.foo.com", 0);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_VERIFY_PARAM_set_flags\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.2.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
