<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ERR_get_error</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ERR_get_error, ERR_peek_error, ERR_peek_last_error, ERR_get_error_line, ERR_peek_error_line, ERR_peek_last_error_line, ERR_get_error_line_data, ERR_peek_error_line_data, ERR_peek_last_error_line_data - obtain error code and data</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/err.h&gt;

unsigned long ERR_get_error(void);
unsigned long ERR_peek_error(void);
unsigned long ERR_peek_last_error(void);

unsigned long ERR_get_error_line(const char **file, int *line);
unsigned long ERR_peek_error_line(const char **file, int *line);
unsigned long ERR_peek_last_error_line(const char **file, int *line);

unsigned long ERR_get_error_line_data(const char **file, int *line,
                                      const char **data, int *flags);
unsigned long ERR_peek_error_line_data(const char **file, int *line,
                                       const char **data, int *flags);
unsigned long ERR_peek_last_error_line_data(const char **file, int *line,
                                            const char **data, int *flags);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>ERR_get_error() returns the earliest error code from the thread&#39;s error queue and removes the entry. This function can be called repeatedly until there are no more error codes to return.</p>

<p>ERR_peek_error() returns the earliest error code from the thread&#39;s error queue without modifying it.</p>

<p>ERR_peek_last_error() returns the latest error code from the thread&#39;s error queue without modifying it.</p>

<p>See <a href="../man3/ERR_GET_LIB.html">ERR_GET_LIB(3)</a> for obtaining information about location and reason of the error, and <a href="../man3/ERR_error_string.html">ERR_error_string(3)</a> for human-readable error messages.</p>

<p>ERR_get_error_line(), ERR_peek_error_line() and ERR_peek_last_error_line() are the same as the above, but they additionally store the filename and line number where the error occurred in *<b>file</b> and *<b>line</b>, unless these are <b>NULL</b>.</p>

<p>ERR_get_error_line_data(), ERR_peek_error_line_data() and ERR_peek_last_error_line_data() store additional data and flags associated with the error code in *<b>data</b> and *<b>flags</b>, unless these are <b>NULL</b>. *<b>data</b> contains a string if *<b>flags</b>&amp;<b>ERR_TXT_STRING</b> is true.</p>

<p>An application <b>MUST NOT</b> free the *<b>data</b> pointer (or any other pointers returned by these functions) with OPENSSL_free() as freeing is handled automatically by the error library.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The error code, or 0 if there is no error in the queue.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_error_string.html">ERR_error_string(3)</a>, <a href="../man3/ERR_GET_LIB.html">ERR_GET_LIB(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


