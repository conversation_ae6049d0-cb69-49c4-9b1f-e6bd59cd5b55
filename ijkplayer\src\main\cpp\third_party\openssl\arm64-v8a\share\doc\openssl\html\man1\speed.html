<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>speed</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-speed, speed - test library performance</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl speed</b> [<b>-help</b>] [<b>-engine id</b>] [<b>-elapsed</b>] [<b>-evp algo</b>] [<b>-decrypt</b>] [<b>-rand file...</b>] [<b>-writerand file</b>] [<b>-primes num</b>] [<b>-seconds num</b>] [<b>-bytes num</b>] [<b>algorithm...</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command is used to test the performance of cryptographic algorithms. To see the list of supported algorithms, use the <i>list --digest-commands</i> or <i>list --cipher-commands</i> command. The global CSPRNG is denoted by the <i>rand</i> algorithm name.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Specifying an engine (by its unique <b>id</b> string) will cause <b>speed</b> to attempt to obtain a functional reference to the specified engine, thus initialising it if needed. The engine will then be set as the default for all available algorithms.</p>

</dd>
<dt id="elapsed"><b>-elapsed</b></dt>
<dd>

<p>When calculating operations- or bytes-per-second, use wall-clock time instead of CPU user time as divisor. It can be useful when testing speed of hardware engines.</p>

</dd>
<dt id="evp-algo"><b>-evp algo</b></dt>
<dd>

<p>Use the specified cipher or message digest algorithm via the EVP interface. If <b>algo</b> is an AEAD cipher, then you can pass &lt;-aead&gt; to benchmark a TLS-like sequence. And if <b>algo</b> is a multi-buffer capable cipher, e.g. aes-128-cbc-hmac-sha1, then <b>-mb</b> will time multi-buffer operation.</p>

</dd>
<dt id="decrypt"><b>-decrypt</b></dt>
<dd>

<p>Time the decryption instead of encryption. Affects only the EVP testing.</p>

</dd>
<dt id="rand-file"><b>-rand file...</b></dt>
<dd>

<p>A file or files containing random data used to seed the random number generator. Multiple files can be specified separated by an OS-dependent character. The separator is <b>;</b> for MS-Windows, <b>,</b> for OpenVMS, and <b>:</b> for all others.</p>

</dd>
<dt id="writerand-file">[<b>-writerand file</b>]</dt>
<dd>

<p>Writes random data to the specified <i>file</i> upon exit. This can be used with a subsequent <b>-rand</b> flag.</p>

</dd>
<dt id="primes-num"><b>-primes num</b></dt>
<dd>

<p>Generate a <b>num</b>-prime RSA key and use it to run the benchmarks. This option is only effective if RSA algorithm is specified to test.</p>

</dd>
<dt id="seconds-num"><b>-seconds num</b></dt>
<dd>

<p>Run benchmarks for <b>num</b> seconds.</p>

</dd>
<dt id="bytes-num"><b>-bytes num</b></dt>
<dd>

<p>Run benchmarks on <b>num</b>-byte buffers. Affects ciphers, digests and the CSPRNG.</p>

</dd>
<dt id="zero-or-more-test-algorithms"><b>[zero or more test algorithms]</b></dt>
<dd>

<p>If any options are given, <b>speed</b> tests those algorithms, otherwise a pre-compiled grand selection is tested.</p>

</dd>
</dl>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


