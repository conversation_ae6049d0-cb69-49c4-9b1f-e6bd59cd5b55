.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_FREE 3"
.TH SSL_FREE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_free \- free an allocated SSL structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_free(SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_free()\fR decrements the reference count of \fBssl\fR, and removes the SSL
structure pointed to by \fBssl\fR and frees up the allocated memory if the
reference count has reached 0.
If \fBssl\fR is NULL nothing is done.
.SH NOTES
.IX Header "NOTES"
\&\fBSSL_free()\fR also calls the \fBfree()\fRing procedures for indirectly affected items, if
applicable: the buffering BIO, the read and write BIOs,
cipher lists specially created for this \fBssl\fR, the \fBSSL_SESSION\fR.
Do not explicitly free these indirectly freed up items before or after
calling \fBSSL_free()\fR, as trying to free things twice may lead to program
failure.
.PP
The ssl session has reference counts from two users: the SSL object, for
which the reference count is removed by \fBSSL_free()\fR and the internal
session cache. If the session is considered bad, because
\&\fBSSL_shutdown\fR\|(3) was not called for the connection
and \fBSSL_set_shutdown\fR\|(3) was not used to set the
SSL_SENT_SHUTDOWN state, the session will also be removed
from the session cache as required by RFC2246.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_free()\fR does not provide diagnostic information.
.PP
\&\fBSSL_new\fR\|(3), \fBSSL_clear\fR\|(3),
\&\fBSSL_shutdown\fR\|(3), \fBSSL_set_shutdown\fR\|(3),
\&\fBssl\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
