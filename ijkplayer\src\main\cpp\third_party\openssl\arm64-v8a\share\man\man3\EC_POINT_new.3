.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EC_POINT_NEW 3"
.TH EC_POINT_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EC_POINT_set_Jprojective_coordinates_GFp,
EC_POINT_point2buf,
EC_POINT_new,
EC_POINT_free,
EC_POINT_clear_free,
EC_POINT_copy,
EC_POINT_dup,
EC_POINT_method_of,
EC_POINT_set_to_infinity,
EC_POINT_get_Jprojective_coordinates_GFp,
EC_POINT_set_affine_coordinates,
EC_POINT_get_affine_coordinates,
EC_POINT_set_compressed_coordinates,
EC_POINT_set_affine_coordinates_GFp,
EC_POINT_get_affine_coordinates_GFp,
EC_POINT_set_compressed_coordinates_GFp,
EC_POINT_set_affine_coordinates_GF2m,
EC_POINT_get_affine_coordinates_GF2m,
EC_POINT_set_compressed_coordinates_GF2m,
EC_POINT_point2oct,
EC_POINT_oct2point,
EC_POINT_point2bn,
EC_POINT_bn2point,
EC_POINT_point2hex,
EC_POINT_hex2point
\&\- Functions for creating, destroying and manipulating EC_POINT objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ec.h>
\&
\& EC_POINT *EC_POINT_new(const EC_GROUP *group);
\& void EC_POINT_free(EC_POINT *point);
\& void EC_POINT_clear_free(EC_POINT *point);
\& int EC_POINT_copy(EC_POINT *dst, const EC_POINT *src);
\& EC_POINT *EC_POINT_dup(const EC_POINT *src, const EC_GROUP *group);
\& const EC_METHOD *EC_POINT_method_of(const EC_POINT *point);
\& int EC_POINT_set_to_infinity(const EC_GROUP *group, EC_POINT *point);
\& int EC_POINT_set_Jprojective_coordinates_GFp(const EC_GROUP *group,
\&                                              EC_POINT *p,
\&                                              const BIGNUM *x, const BIGNUM *y,
\&                                              const BIGNUM *z, BN_CTX *ctx);
\& int EC_POINT_get_Jprojective_coordinates_GFp(const EC_GROUP *group,
\&                                              const EC_POINT *p,
\&                                              BIGNUM *x, BIGNUM *y, BIGNUM *z,
\&                                              BN_CTX *ctx);
\& int EC_POINT_set_affine_coordinates(const EC_GROUP *group, EC_POINT *p,
\&                                     const BIGNUM *x, const BIGNUM *y,
\&                                     BN_CTX *ctx);
\& int EC_POINT_get_affine_coordinates(const EC_GROUP *group, const EC_POINT *p,
\&                                     BIGNUM *x, BIGNUM *y, BN_CTX *ctx);
\& int EC_POINT_set_compressed_coordinates(const EC_GROUP *group, EC_POINT *p,
\&                                         const BIGNUM *x, int y_bit,
\&                                         BN_CTX *ctx);
\& int EC_POINT_set_affine_coordinates_GFp(const EC_GROUP *group, EC_POINT *p,
\&                                         const BIGNUM *x, const BIGNUM *y,
\&                                         BN_CTX *ctx);
\& int EC_POINT_get_affine_coordinates_GFp(const EC_GROUP *group,
\&                                         const EC_POINT *p,
\&                                         BIGNUM *x, BIGNUM *y, BN_CTX *ctx);
\& int EC_POINT_set_compressed_coordinates_GFp(const EC_GROUP *group,
\&                                             EC_POINT *p,
\&                                             const BIGNUM *x, int y_bit,
\&                                             BN_CTX *ctx);
\& int EC_POINT_set_affine_coordinates_GF2m(const EC_GROUP *group, EC_POINT *p,
\&                                          const BIGNUM *x, const BIGNUM *y,
\&                                          BN_CTX *ctx);
\& int EC_POINT_get_affine_coordinates_GF2m(const EC_GROUP *group,
\&                                          const EC_POINT *p,
\&                                          BIGNUM *x, BIGNUM *y, BN_CTX *ctx);
\& int EC_POINT_set_compressed_coordinates_GF2m(const EC_GROUP *group,
\&                                              EC_POINT *p,
\&                                              const BIGNUM *x, int y_bit,
\&                                              BN_CTX *ctx);
\& size_t EC_POINT_point2oct(const EC_GROUP *group, const EC_POINT *p,
\&                           point_conversion_form_t form,
\&                           unsigned char *buf, size_t len, BN_CTX *ctx);
\& size_t EC_POINT_point2buf(const EC_GROUP *group, const EC_POINT *point,
\&                           point_conversion_form_t form,
\&                           unsigned char **pbuf, BN_CTX *ctx);
\& int EC_POINT_oct2point(const EC_GROUP *group, EC_POINT *p,
\&                        const unsigned char *buf, size_t len, BN_CTX *ctx);
\& BIGNUM *EC_POINT_point2bn(const EC_GROUP *group, const EC_POINT *p,
\&                           point_conversion_form_t form, BIGNUM *bn,
\&                           BN_CTX *ctx);
\& EC_POINT *EC_POINT_bn2point(const EC_GROUP *group, const BIGNUM *bn,
\&                             EC_POINT *p, BN_CTX *ctx);
\& char *EC_POINT_point2hex(const EC_GROUP *group, const EC_POINT *p,
\&                          point_conversion_form_t form, BN_CTX *ctx);
\& EC_POINT *EC_POINT_hex2point(const EC_GROUP *group, const char *hex,
\&                              EC_POINT *p, BN_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
An \fBEC_POINT\fR structure represents a point on a curve. A new point is
constructed by calling the function \fBEC_POINT_new()\fR and providing the
\&\fBgroup\fR object that the point relates to.
.PP
\&\fBEC_POINT_free()\fR frees the memory associated with the \fBEC_POINT\fR.
if \fBpoint\fR is NULL nothing is done.
.PP
\&\fBEC_POINT_clear_free()\fR destroys any sensitive data held within the EC_POINT and
then frees its memory. If \fBpoint\fR is NULL nothing is done.
.PP
\&\fBEC_POINT_copy()\fR copies the point \fBsrc\fR into \fBdst\fR. Both \fBsrc\fR and \fBdst\fR
must use the same \fBEC_METHOD\fR.
.PP
\&\fBEC_POINT_dup()\fR creates a new \fBEC_POINT\fR object and copies the content from
\&\fBsrc\fR to the newly created \fBEC_POINT\fR object.
.PP
\&\fBEC_POINT_method_of()\fR obtains the \fBEC_METHOD\fR associated with \fBpoint\fR.
.PP
A valid point on a curve is the special point at infinity. A point is set to
be at infinity by calling \fBEC_POINT_set_to_infinity()\fR.
.PP
The affine co-ordinates for a point describe a point in terms of its x and y
position. The function \fBEC_POINT_set_affine_coordinates()\fR sets the \fBx\fR and \fBy\fR
co-ordinates for the point \fBp\fR defined over the curve given in \fBgroup\fR. The
function \fBEC_POINT_get_affine_coordinates()\fR sets \fBx\fR and \fBy\fR, either of which
may be NULL, to the corresponding coordinates of \fBp\fR.
.PP
The functions \fBEC_POINT_set_affine_coordinates_GFp()\fR and
\&\fBEC_POINT_set_affine_coordinates_GF2m()\fR are synonyms for
\&\fBEC_POINT_set_affine_coordinates()\fR. They are defined for backwards compatibility
only and should not be used.
.PP
The functions \fBEC_POINT_get_affine_coordinates_GFp()\fR and
\&\fBEC_POINT_get_affine_coordinates_GF2m()\fR are synonyms for
\&\fBEC_POINT_get_affine_coordinates()\fR. They are defined for backwards compatibility
only and should not be used.
.PP
As well as the affine co-ordinates, a point can alternatively be described in
terms of its Jacobian projective co-ordinates (for Fp curves only). Jacobian
projective co-ordinates are expressed as three values x, y and z. Working in
this co-ordinate system provides more efficient point multiplication
operations.  A mapping exists between Jacobian projective co-ordinates and
affine co-ordinates. A Jacobian projective co-ordinate (x, y, z) can be written
as an affine co-ordinate as (x/(z^2), y/(z^3)). Conversion to Jacobian
projective from affine co-ordinates is simple. The co-ordinate (x, y) is mapped
to (x, y, 1). To set or get the projective co-ordinates use
\&\fBEC_POINT_set_Jprojective_coordinates_GFp()\fR and
\&\fBEC_POINT_get_Jprojective_coordinates_GFp()\fR respectively.
.PP
Points can also be described in terms of their compressed co-ordinates. For a
point (x, y), for any given value for x such that the point is on the curve
there will only ever be two possible values for y. Therefore, a point can be set
using the \fBEC_POINT_set_compressed_coordinates()\fR function where \fBx\fR is the x
co-ordinate and \fBy_bit\fR is a value 0 or 1 to identify which of the two
possible values for y should be used.
.PP
The functions \fBEC_POINT_set_compressed_coordinates_GFp()\fR and
\&\fBEC_POINT_set_compressed_coordinates_GF2m()\fR are synonyms for
\&\fBEC_POINT_set_compressed_coordinates()\fR. They are defined for backwards
compatibility only and should not be used.
.PP
In addition \fBEC_POINT\fR can be converted to and from various external
representations. The octet form is the binary encoding of the \fBECPoint\fR
structure (as defined in RFC5480 and used in certificates and TLS records):
only the content octets are present, the \fBOCTET STRING\fR tag and length are
not included. \fBBIGNUM\fR form is the octet form interpreted as a big endian
integer converted to a \fBBIGNUM\fR structure. Hexadecimal form is the octet
form converted to a NULL terminated character string where each character
is one of the printable values 0\-9 or A\-F (or a\-f).
.PP
The functions \fBEC_POINT_point2oct()\fR, \fBEC_POINT_oct2point()\fR, \fBEC_POINT_point2bn()\fR,
\&\fBEC_POINT_bn2point()\fR, \fBEC_POINT_point2hex()\fR and \fBEC_POINT_hex2point()\fR convert from
and to EC_POINTs for the formats: octet, BIGNUM and hexadecimal respectively.
.PP
The function \fBEC_POINT_point2oct()\fR encodes the given curve point \fBp\fR as an
octet string into the buffer \fBbuf\fR of size \fBlen\fR, using the specified
conversion form \fBform\fR.
The encoding conforms with Sec. 2.3.3 of the SECG SEC 1 ("Elliptic Curve
Cryptography") standard.
Similarly the function \fBEC_POINT_oct2point()\fR decodes a curve point into \fBp\fR from
the octet string contained in the given buffer \fBbuf\fR of size \fBlen\fR, conforming
to Sec. 2.3.4 of the SECG SEC 1 ("Elliptic Curve Cryptography") standard.
.PP
The functions \fBEC_POINT_point2hex()\fR and \fBEC_POINT_point2bn()\fR convert a point \fBp\fR,
respectively, to the hexadecimal or BIGNUM representation of the same
encoding of the function \fBEC_POINT_point2oct()\fR.
Vice versa, similarly to the function \fBEC_POINT_oct2point()\fR, the functions
\&\fBEC_POINT_hex2point()\fR and \fBEC_POINT_point2bn()\fR decode the hexadecimal or
BIGNUM representation into the EC_POINT \fBp\fR.
.PP
Notice that, according to the standard, the octet string encoding of the point
at infinity for a given curve is fixed to a single octet of value zero and that,
vice versa, a single octet of size zero is decoded as the point at infinity.
.PP
The function \fBEC_POINT_point2oct()\fR must be supplied with a buffer long enough to
store the octet form. The return value provides the number of octets stored.
Calling the function with a NULL buffer will not perform the conversion but
will still return the required buffer length.
.PP
The function \fBEC_POINT_point2buf()\fR allocates a buffer of suitable length and
writes an EC_POINT to it in octet format. The allocated buffer is written to
\&\fB*pbuf\fR and its length is returned. The caller must free up the allocated
buffer with a call to \fBOPENSSL_free()\fR. Since the allocated buffer value is
written to \fB*pbuf\fR the \fBpbuf\fR parameter \fBMUST NOT\fR be \fBNULL\fR.
.PP
The function \fBEC_POINT_point2hex()\fR will allocate sufficient memory to store the
hexadecimal string. It is the caller's responsibility to free this memory with
a subsequent call to \fBOPENSSL_free()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEC_POINT_new()\fR and \fBEC_POINT_dup()\fR return the newly allocated EC_POINT or NULL
on error.
.PP
The following functions return 1 on success or 0 on error: \fBEC_POINT_copy()\fR,
\&\fBEC_POINT_set_to_infinity()\fR, \fBEC_POINT_set_Jprojective_coordinates_GFp()\fR,
\&\fBEC_POINT_get_Jprojective_coordinates_GFp()\fR,
\&\fBEC_POINT_set_affine_coordinates_GFp()\fR, \fBEC_POINT_get_affine_coordinates_GFp()\fR,
\&\fBEC_POINT_set_compressed_coordinates_GFp()\fR,
\&\fBEC_POINT_set_affine_coordinates_GF2m()\fR, \fBEC_POINT_get_affine_coordinates_GF2m()\fR,
\&\fBEC_POINT_set_compressed_coordinates_GF2m()\fR and \fBEC_POINT_oct2point()\fR.
.PP
EC_POINT_method_of returns the EC_METHOD associated with the supplied EC_POINT.
.PP
\&\fBEC_POINT_point2oct()\fR and \fBEC_POINT_point2buf()\fR return the length of the required
buffer or 0 on error.
.PP
\&\fBEC_POINT_point2bn()\fR returns the pointer to the BIGNUM supplied, or NULL on
error.
.PP
\&\fBEC_POINT_bn2point()\fR returns the pointer to the EC_POINT supplied, or NULL on
error.
.PP
\&\fBEC_POINT_point2hex()\fR returns a pointer to the hex string, or NULL on error.
.PP
\&\fBEC_POINT_hex2point()\fR returns the pointer to the EC_POINT supplied, or NULL on
error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBEC_GROUP_new\fR\|(3), \fBEC_GROUP_copy\fR\|(3),
\&\fBEC_POINT_add\fR\|(3), \fBEC_KEY_new\fR\|(3),
\&\fBEC_GFp_simple_method\fR\|(3), \fBd2i_ECPKParameters\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2013\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
