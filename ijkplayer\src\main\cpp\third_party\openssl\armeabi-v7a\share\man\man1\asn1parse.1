.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1PARSE 1"
.TH ASN1PARSE 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-asn1parse,
asn1parse \- ASN.1 parsing tool
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBasn1parse\fR
[\fB\-help\fR]
[\fB\-inform PEM|DER\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-noout\fR]
[\fB\-offset number\fR]
[\fB\-length number\fR]
[\fB\-i\fR]
[\fB\-oid filename\fR]
[\fB\-dump\fR]
[\fB\-dlimit num\fR]
[\fB\-strparse offset\fR]
[\fB\-genstr string\fR]
[\fB\-genconf file\fR]
[\fB\-strictpem\fR]
[\fB\-item name\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBasn1parse\fR command is a diagnostic utility that can parse ASN.1
structures. It can also be used to extract data from ASN.1 formatted data.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform\fR \fBDER|PEM\fR" 4
.IX Item "-inform DER|PEM"
The input format. \fBDER\fR is binary format and \fBPEM\fR (the default) is base64
encoded.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
The input file, default is standard input.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
Output file to place the DER encoded data into. If this
option is not present then no data will be output. This is most useful when
combined with the \fB\-strparse\fR option.
.IP \fB\-noout\fR 4
.IX Item "-noout"
Don't output the parsed version of the input file.
.IP "\fB\-offset number\fR" 4
.IX Item "-offset number"
Starting offset to begin parsing, default is start of file.
.IP "\fB\-length number\fR" 4
.IX Item "-length number"
Number of bytes to parse, default is until end of file.
.IP \fB\-i\fR 4
.IX Item "-i"
Indents the output according to the "depth" of the structures.
.IP "\fB\-oid filename\fR" 4
.IX Item "-oid filename"
A file containing additional OBJECT IDENTIFIERs (OIDs). The format of this
file is described in the NOTES section below.
.IP \fB\-dump\fR 4
.IX Item "-dump"
Dump unknown data in hex format.
.IP "\fB\-dlimit num\fR" 4
.IX Item "-dlimit num"
Like \fB\-dump\fR, but only the first \fBnum\fR bytes are output.
.IP "\fB\-strparse offset\fR" 4
.IX Item "-strparse offset"
Parse the contents octets of the ASN.1 object starting at \fBoffset\fR. This
option can be used multiple times to "drill down" into a nested structure.
.IP "\fB\-genstr string\fR, \fB\-genconf file\fR" 4
.IX Item "-genstr string, -genconf file"
Generate encoded data based on \fBstring\fR, \fBfile\fR or both using
\&\fBASN1_generate_nconf\fR\|(3) format. If \fBfile\fR only is
present then the string is obtained from the default section using the name
\&\fBasn1\fR. The encoded data is passed through the ASN1 parser and printed out as
though it came from a file, the contents can thus be examined and written to a
file using the \fBout\fR option.
.IP \fB\-strictpem\fR 4
.IX Item "-strictpem"
If this option is used then \fB\-inform\fR will be ignored. Without this option any
data in a PEM format input file will be treated as being base64 encoded and
processed whether it has the normal PEM BEGIN and END markers or not. This
option will ignore any data prior to the start of the BEGIN marker, or after an
END marker in a PEM file.
.IP "\fB\-item name\fR" 4
.IX Item "-item name"
Attempt to decode and print the data as \fBASN1_ITEM name\fR. This can be used to
print out the fields of any supported ASN.1 structure if the type is known.
.SS Output
.IX Subsection "Output"
The output will typically contain lines like this:
.PP
.Vb 1
\&  0:d=0  hl=4 l= 681 cons: SEQUENCE
.Ve
.PP
\&.....
.PP
.Vb 10
\&  229:d=3  hl=3 l= 141 prim: BIT STRING
\&  373:d=2  hl=3 l= 162 cons: cont [ 3 ]
\&  376:d=3  hl=3 l= 159 cons: SEQUENCE
\&  379:d=4  hl=2 l=  29 cons: SEQUENCE
\&  381:d=5  hl=2 l=   3 prim: OBJECT            :X509v3 Subject Key Identifier
\&  386:d=5  hl=2 l=  22 prim: OCTET STRING
\&  410:d=4  hl=2 l= 112 cons: SEQUENCE
\&  412:d=5  hl=2 l=   3 prim: OBJECT            :X509v3 Authority Key Identifier
\&  417:d=5  hl=2 l= 105 prim: OCTET STRING
\&  524:d=4  hl=2 l=  12 cons: SEQUENCE
.Ve
.PP
\&.....
.PP
This example is part of a self-signed certificate. Each line starts with the
offset in decimal. \fBd=XX\fR specifies the current depth. The depth is increased
within the scope of any SET or SEQUENCE. \fBhl=XX\fR gives the header length
(tag and length octets) of the current type. \fBl=XX\fR gives the length of
the contents octets.
.PP
The \fB\-i\fR option can be used to make the output more readable.
.PP
Some knowledge of the ASN.1 structure is needed to interpret the output.
.PP
In this example the BIT STRING at offset 229 is the certificate public key.
The contents octets of this will contain the public key information. This can
be examined using the option \fB\-strparse 229\fR to yield:
.PP
.Vb 3
\&    0:d=0  hl=3 l= 137 cons: SEQUENCE
\&    3:d=1  hl=3 l= 129 prim: INTEGER           :E5D21E1F5C8D208EA7A2166C7FAF9F6BDF2059669C60876DDB70840F1A5AAFA59699FE471F379F1DD6A487E7D5409AB6A88D4A9746E24B91D8CF55DB3521015460C8EDE44EE8A4189F7A7BE77D6CD3A9AF2696F486855CF58BF0EDF2B4068058C7A947F52548DDF7E15E96B385F86422BEA9064A3EE9E1158A56E4A6F47E5897
\&  135:d=1  hl=2 l=   3 prim: INTEGER           :010001
.Ve
.SH NOTES
.IX Header "NOTES"
If an OID is not part of OpenSSL's internal table it will be represented in
numerical form (for example 1.2.3.4). The file passed to the \fB\-oid\fR option
allows additional OIDs to be included. Each line consists of three columns,
the first column is the OID in numerical format and should be followed by white
space. The second column is the "short name" which is a single word followed
by white space. The final column is the rest of the line and is the
"long name". \fBasn1parse\fR displays the long name. Example:
.PP
\&\f(CW\*(C`1.2.3.4       shortName       A long name\*(C'\fR
.SH EXAMPLES
.IX Header "EXAMPLES"
Parse a file:
.PP
.Vb 1
\& openssl asn1parse \-in file.pem
.Ve
.PP
Parse a DER file:
.PP
.Vb 1
\& openssl asn1parse \-inform DER \-in file.der
.Ve
.PP
Generate a simple UTF8String:
.PP
.Vb 1
\& openssl asn1parse \-genstr \*(AqUTF8:Hello World\*(Aq
.Ve
.PP
Generate and write out a UTF8String, don't print parsed output:
.PP
.Vb 1
\& openssl asn1parse \-genstr \*(AqUTF8:Hello World\*(Aq \-noout \-out utf8.der
.Ve
.PP
Generate using a config file:
.PP
.Vb 1
\& openssl asn1parse \-genconf asn1.cnf \-noout \-out asn1.der
.Ve
.PP
Example config file:
.PP
.Vb 1
\& asn1=SEQUENCE:seq_sect
\&
\& [seq_sect]
\&
\& field1=BOOL:TRUE
\& field2=EXP:0, UTF8:some random string
.Ve
.SH BUGS
.IX Header "BUGS"
There should be options to change the format of output lines. The output of some
ASN.1 types is not well handled (if at all).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBASN1_generate_nconf\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
