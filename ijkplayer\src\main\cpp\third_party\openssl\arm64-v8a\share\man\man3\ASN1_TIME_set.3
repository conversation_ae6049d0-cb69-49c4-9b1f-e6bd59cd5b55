.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_TIME_SET 3"
.TH ASN1_TIME_SET 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_TIME_set, ASN1_UTCTIME_set, ASN1_GENERALIZEDTIME_set,
ASN1_TIME_adj, ASN1_UTCTIME_adj, ASN1_GENERALIZEDTIME_adj,
ASN1_TIME_check, ASN1_UTCTIME_check, ASN1_GENERALIZEDTIME_check,
ASN1_TIME_set_string, ASN1_UTCTIME_set_string, ASN1_GENERALIZEDTIME_set_string,
ASN1_TIME_set_string_X509,
ASN1_TIME_normalize,
ASN1_TIME_to_tm,
ASN1_TIME_print, ASN1_UTCTIME_print, ASN1_GENERALIZEDTIME_print,
ASN1_TIME_diff,
ASN1_TIME_cmp_time_t, ASN1_UTCTIME_cmp_time_t,
ASN1_TIME_compare,
ASN1_TIME_to_generalizedtime \- ASN.1 Time functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 4
\& ASN1_TIME *ASN1_TIME_set(ASN1_TIME *s, time_t t);
\& ASN1_UTCTIME *ASN1_UTCTIME_set(ASN1_UTCTIME *s, time_t t);
\& ASN1_GENERALIZEDTIME *ASN1_GENERALIZEDTIME_set(ASN1_GENERALIZEDTIME *s,
\&                                                time_t t);
\&
\& ASN1_TIME *ASN1_TIME_adj(ASN1_TIME *s, time_t t, int offset_day,
\&                          long offset_sec);
\& ASN1_UTCTIME *ASN1_UTCTIME_adj(ASN1_UTCTIME *s, time_t t,
\&                                int offset_day, long offset_sec);
\& ASN1_GENERALIZEDTIME *ASN1_GENERALIZEDTIME_adj(ASN1_GENERALIZEDTIME *s,
\&                                                time_t t, int offset_day,
\&                                                long offset_sec);
\&
\& int ASN1_TIME_set_string(ASN1_TIME *s, const char *str);
\& int ASN1_TIME_set_string_X509(ASN1_TIME *s, const char *str);
\& int ASN1_UTCTIME_set_string(ASN1_UTCTIME *s, const char *str);
\& int ASN1_GENERALIZEDTIME_set_string(ASN1_GENERALIZEDTIME *s,
\&                                     const char *str);
\&
\& int ASN1_TIME_normalize(ASN1_TIME *s);
\&
\& int ASN1_TIME_check(const ASN1_TIME *t);
\& int ASN1_UTCTIME_check(const ASN1_UTCTIME *t);
\& int ASN1_GENERALIZEDTIME_check(const ASN1_GENERALIZEDTIME *t);
\&
\& int ASN1_TIME_print(BIO *b, const ASN1_TIME *s);
\& int ASN1_UTCTIME_print(BIO *b, const ASN1_UTCTIME *s);
\& int ASN1_GENERALIZEDTIME_print(BIO *b, const ASN1_GENERALIZEDTIME *s);
\&
\& int ASN1_TIME_to_tm(const ASN1_TIME *s, struct tm *tm);
\& int ASN1_TIME_diff(int *pday, int *psec, const ASN1_TIME *from,
\&                    const ASN1_TIME *to);
\&
\& int ASN1_TIME_cmp_time_t(const ASN1_TIME *s, time_t t);
\& int ASN1_UTCTIME_cmp_time_t(const ASN1_UTCTIME *s, time_t t);
\&
\& int ASN1_TIME_compare(const ASN1_TIME *a, const ASN1_TIME *b);
\&
\& ASN1_GENERALIZEDTIME *ASN1_TIME_to_generalizedtime(ASN1_TIME *t,
\&                                                    ASN1_GENERALIZEDTIME **out);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBASN1_TIME_set()\fR, \fBASN1_UTCTIME_set()\fR and \fBASN1_GENERALIZEDTIME_set()\fR
functions set the structure \fBs\fR to the time represented by the time_t
value \fBt\fR. If \fBs\fR is NULL a new time structure is allocated and returned.
.PP
The \fBASN1_TIME_adj()\fR, \fBASN1_UTCTIME_adj()\fR and \fBASN1_GENERALIZEDTIME_adj()\fR
functions set the time structure \fBs\fR to the time represented
by the time \fBoffset_day\fR and \fBoffset_sec\fR after the time_t value \fBt\fR.
The values of \fBoffset_day\fR or \fBoffset_sec\fR can be negative to set a
time before \fBt\fR. The \fBoffset_sec\fR value can also exceed the number of
seconds in a day. If \fBs\fR is NULL a new structure is allocated
and returned.
.PP
The \fBASN1_TIME_set_string()\fR, \fBASN1_UTCTIME_set_string()\fR and
\&\fBASN1_GENERALIZEDTIME_set_string()\fR functions set the time structure \fBs\fR
to the time represented by string \fBstr\fR which must be in appropriate ASN.1
time format (for example YYMMDDHHMMSSZ or YYYYMMDDHHMMSSZ). If \fBs\fR is NULL
this function performs a format check on \fBstr\fR only. The string \fBstr\fR
is copied into \fBs\fR.
.PP
\&\fBASN1_TIME_set_string_X509()\fR sets ASN1_TIME structure \fBs\fR to the time
represented by string \fBstr\fR which must be in appropriate time format
that RFC 5280 requires, which means it only allows YYMMDDHHMMSSZ and
YYYYMMDDHHMMSSZ (leap second is rejected), all other ASN.1 time format
are not allowed. If \fBs\fR is NULL this function performs a format check
on \fBstr\fR only.
.PP
The \fBASN1_TIME_normalize()\fR function converts an ASN1_GENERALIZEDTIME or
ASN1_UTCTIME into a time value that can be used in a certificate. It
should be used after the \fBASN1_TIME_set_string()\fR functions and before
\&\fBASN1_TIME_print()\fR functions to get consistent (i.e. GMT) results.
.PP
The \fBASN1_TIME_check()\fR, \fBASN1_UTCTIME_check()\fR and \fBASN1_GENERALIZEDTIME_check()\fR
functions check the syntax of the time structure \fBs\fR.
.PP
The \fBASN1_TIME_print()\fR, \fBASN1_UTCTIME_print()\fR and \fBASN1_GENERALIZEDTIME_print()\fR
functions print the time structure \fBs\fR to BIO \fBb\fR in human readable
format. It will be of the format MMM DD HH:MM:SS YYYY [GMT], for example
"Feb  3 00:55:52 2015 GMT" it does not include a newline. If the time
structure has invalid format it prints out "Bad time value" and returns
an error. The output for generalized time may include a fractional part
following the second.
.PP
\&\fBASN1_TIME_to_tm()\fR converts the time \fBs\fR to the standard \fBtm\fR structure.
If \fBs\fR is NULL, then the current time is converted. The output time is GMT.
The \fBtm_sec\fR, \fBtm_min\fR, \fBtm_hour\fR, \fBtm_mday\fR, \fBtm_wday\fR, \fBtm_yday\fR,
\&\fBtm_mon\fR and \fBtm_year\fR fields of \fBtm\fR structure are set to proper values,
whereas all other fields are set to 0. If \fBtm\fR is NULL this function performs
a format check on \fBs\fR only. If \fBs\fR is in Generalized format with fractional
seconds, e.g. YYYYMMDDHHMMSS.SSSZ, the fractional seconds will be lost while
converting \fBs\fR to \fBtm\fR structure.
.PP
\&\fBASN1_TIME_diff()\fR sets \fB*pday\fR and \fB*psec\fR to the time difference between
\&\fBfrom\fR and \fBto\fR. If \fBto\fR represents a time later than \fBfrom\fR then
one or both (depending on the time difference) of \fB*pday\fR and \fB*psec\fR
will be positive. If \fBto\fR represents a time earlier than \fBfrom\fR then
one or both of \fB*pday\fR and \fB*psec\fR will be negative. If \fBto\fR and \fBfrom\fR
represent the same time then \fB*pday\fR and \fB*psec\fR will both be zero.
If both \fB*pday\fR and \fB*psec\fR are nonzero they will always have the same
sign. The value of \fB*psec\fR will always be less than the number of seconds
in a day. If \fBfrom\fR or \fBto\fR is NULL the current time is used.
.PP
The \fBASN1_TIME_cmp_time_t()\fR and \fBASN1_UTCTIME_cmp_time_t()\fR functions compare
the two times represented by the time structure \fBs\fR and the time_t \fBt\fR.
.PP
The \fBASN1_TIME_compare()\fR function compares the two times represented by the
time structures \fBa\fR and \fBb\fR.
.PP
The \fBASN1_TIME_to_generalizedtime()\fR function converts an ASN1_TIME to an
ASN1_GENERALIZEDTIME, regardless of year. If either \fBout\fR or
\&\fB*out\fR are NULL, then a new object is allocated and must be freed after use.
.SH NOTES
.IX Header "NOTES"
The ASN1_TIME structure corresponds to the ASN.1 structure \fBTime\fR
defined in RFC5280 et al. The time setting functions obey the rules outlined
in RFC5280: if the date can be represented by UTCTime it is used, else
GeneralizedTime is used.
.PP
The ASN1_TIME, ASN1_UTCTIME and ASN1_GENERALIZEDTIME structures are represented
as an ASN1_STRING internally and can be freed up using \fBASN1_STRING_free()\fR.
.PP
The ASN1_TIME structure can represent years from 0000 to 9999 but no attempt
is made to correct ancient calendar changes (for example from Julian to
Gregorian calendars).
.PP
ASN1_UTCTIME is limited to a year range of 1950 through 2049.
.PP
Some applications add offset times directly to a time_t value and pass the
results to \fBASN1_TIME_set()\fR (or equivalent). This can cause problems as the
time_t value can overflow on some systems resulting in unexpected results.
New applications should use \fBASN1_TIME_adj()\fR instead and pass the offset value
in the \fBoffset_sec\fR and \fBoffset_day\fR parameters instead of directly
manipulating a time_t value.
.PP
\&\fBASN1_TIME_adj()\fR may change the type from ASN1_GENERALIZEDTIME to ASN1_UTCTIME,
or vice versa, based on the resulting year. The \fBASN1_GENERALIZEDTIME_adj()\fR and
\&\fBASN1_UTCTIME_adj()\fR functions will not modify the type of the return structure.
.PP
It is recommended that functions starting with ASN1_TIME be used instead of
those starting with ASN1_UTCTIME or ASN1_GENERALIZEDTIME. The functions
starting with ASN1_UTCTIME and ASN1_GENERALIZEDTIME act only on that specific
time format. The functions starting with ASN1_TIME will operate on either
format.
.SH BUGS
.IX Header "BUGS"
\&\fBASN1_TIME_print()\fR, \fBASN1_UTCTIME_print()\fR and \fBASN1_GENERALIZEDTIME_print()\fR
do not print out the timezone: it either prints out "GMT" or nothing. But all
certificates complying with RFC5280 et al use GMT anyway.
.PP
Use the \fBASN1_TIME_normalize()\fR function to normalize the time value before
printing to get GMT results.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASN1_TIME_set()\fR, \fBASN1_UTCTIME_set()\fR, \fBASN1_GENERALIZEDTIME_set()\fR, \fBASN1_TIME_adj()\fR,
ASN1_UTCTIME_adj and ASN1_GENERALIZEDTIME_set return a pointer to a time structure
or NULL if an error occurred.
.PP
\&\fBASN1_TIME_set_string()\fR, \fBASN1_UTCTIME_set_string()\fR, \fBASN1_GENERALIZEDTIME_set_string()\fR
\&\fBASN1_TIME_set_string_X509()\fR return 1 if the time value is successfully set and 0 otherwise.
.PP
\&\fBASN1_TIME_normalize()\fR returns 1 on success, and 0 on error.
.PP
\&\fBASN1_TIME_check()\fR, ASN1_UTCTIME_check and \fBASN1_GENERALIZEDTIME_check()\fR return 1
if the structure is syntactically correct and 0 otherwise.
.PP
\&\fBASN1_TIME_print()\fR, \fBASN1_UTCTIME_print()\fR and \fBASN1_GENERALIZEDTIME_print()\fR return 1
if the time is successfully printed out and 0 if an error occurred (I/O error or
invalid time format).
.PP
\&\fBASN1_TIME_to_tm()\fR returns 1 if the time is successfully parsed and 0 if an
error occurred (invalid time format).
.PP
\&\fBASN1_TIME_diff()\fR returns 1 for success and 0 for failure. It can fail if the
passed-in time structure has invalid syntax, for example.
.PP
\&\fBASN1_TIME_cmp_time_t()\fR and \fBASN1_UTCTIME_cmp_time_t()\fR return \-1 if \fBs\fR is
before \fBt\fR, 0 if \fBs\fR equals \fBt\fR, or 1 if \fBs\fR is after \fBt\fR. \-2 is returned
on error.
.PP
\&\fBASN1_TIME_compare()\fR returns \-1 if \fBa\fR is before \fBb\fR, 0 if \fBa\fR equals \fBb\fR, or 1 if \fBa\fR is after \fBb\fR. \-2 is returned on error.
.PP
\&\fBASN1_TIME_to_generalizedtime()\fR returns a pointer to
the appropriate time structure on success or NULL if an error occurred.
.SH EXAMPLES
.IX Header "EXAMPLES"
Set a time structure to one hour after the current time and print it out:
.PP
.Vb 2
\& #include <time.h>
\& #include <openssl/asn1.h>
\&
\& ASN1_TIME *tm;
\& time_t t;
\& BIO *b;
\&
\& t = time(NULL);
\& tm = ASN1_TIME_adj(NULL, t, 0, 60 * 60);
\& b = BIO_new_fp(stdout, BIO_NOCLOSE);
\& ASN1_TIME_print(b, tm);
\& ASN1_STRING_free(tm);
\& BIO_free(b);
.Ve
.PP
Determine if one time is later or sooner than the current time:
.PP
.Vb 1
\& int day, sec;
\&
\& if (!ASN1_TIME_diff(&day, &sec, NULL, to))
\&     /* Invalid time format */
\&
\& if (day > 0 || sec > 0)
\&     printf("Later\en");
\& else if (day < 0 || sec < 0)
\&     printf("Sooner\en");
\& else
\&     printf("Same\en");
.Ve
.SH HISTORY
.IX Header "HISTORY"
The \fBASN1_TIME_to_tm()\fR function was added in OpenSSL 1.1.1.
The \fBASN1_TIME_set_string_X509()\fR function was added in OpenSSL 1.1.1.
The \fBASN1_TIME_normalize()\fR function was added in OpenSSL 1.1.1.
The \fBASN1_TIME_cmp_time_t()\fR function was added in OpenSSL 1.1.1.
The \fBASN1_TIME_compare()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
