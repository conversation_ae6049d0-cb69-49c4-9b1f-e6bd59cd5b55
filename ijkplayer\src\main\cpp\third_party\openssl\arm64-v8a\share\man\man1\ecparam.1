.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ECPARAM 1"
.TH ECPARAM 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-ecparam,
ecparam \- EC parameter manipulation and generation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl ecparam\fR
[\fB\-help\fR]
[\fB\-inform DER|PEM\fR]
[\fB\-outform DER|PEM\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-noout\fR]
[\fB\-text\fR]
[\fB\-C\fR]
[\fB\-check\fR]
[\fB\-name arg\fR]
[\fB\-list_curves\fR]
[\fB\-conv_form arg\fR]
[\fB\-param_enc arg\fR]
[\fB\-no_seed\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-genkey\fR]
[\fB\-engine id\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to manipulate or generate EC parameter files.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format. The \fBDER\fR option uses an ASN.1 DER encoded
form compatible with RFC 3279 EcpkParameters. The PEM form is the default
format: it consists of the \fBDER\fR format base64 encoded with additional
header and footer lines.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format, the options have the same meaning and default
as the \fB\-inform\fR option.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read parameters from or standard input if
this option is not specified.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
This specifies the output filename parameters to. Standard output is used
if this option is not present. The output filename should \fBnot\fR be the same
as the input filename.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option inhibits the output of the encoded version of the parameters.
.IP \fB\-text\fR 4
.IX Item "-text"
This option prints out the EC parameters in human readable form.
.IP \fB\-C\fR 4
.IX Item "-C"
This option converts the EC parameters into C code. The parameters can then
be loaded by calling the \fBget_ec_group_XXX()\fR function.
.IP \fB\-check\fR 4
.IX Item "-check"
Validate the elliptic curve parameters.
.IP "\fB\-name arg\fR" 4
.IX Item "-name arg"
Use the EC parameters with the specified 'short' name. Use \fB\-list_curves\fR
to get a list of all currently implemented EC parameters.
.IP \fB\-list_curves\fR 4
.IX Item "-list_curves"
If this options is specified \fBecparam\fR will print out a list of all
currently implemented EC parameters names and exit.
.IP \fB\-conv_form\fR 4
.IX Item "-conv_form"
This specifies how the points on the elliptic curve are converted
into octet strings. Possible values are: \fBcompressed\fR, \fBuncompressed\fR (the
default value) and \fBhybrid\fR. For more information regarding
the point conversion forms please read the X9.62 standard.
\&\fBNote\fR Due to patent issues the \fBcompressed\fR option is disabled
by default for binary curves and can be enabled by defining
the preprocessor macro \fBOPENSSL_EC_BIN_PT_COMP\fR at compile time.
.IP "\fB\-param_enc arg\fR" 4
.IX Item "-param_enc arg"
This specifies how the elliptic curve parameters are encoded.
Possible value are: \fBnamed_curve\fR, i.e. the ec parameters are
specified by an OID, or \fBexplicit\fR where the ec parameters are
explicitly given (see RFC 3279 for the definition of the
EC parameters structures). The default value is \fBnamed_curve\fR.
\&\fBNote\fR the \fBimplicitlyCA\fR alternative, as specified in RFC 3279,
is currently not implemented in OpenSSL.
.IP \fB\-no_seed\fR 4
.IX Item "-no_seed"
This option inhibits that the 'seed' for the parameter generation
is included in the ECParameters structure (see RFC 3279).
.IP \fB\-genkey\fR 4
.IX Item "-genkey"
This option will generate an EC private key using the specified parameters.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBecparam\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.SH NOTES
.IX Header "NOTES"
PEM format EC parameters use the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN EC PARAMETERS\-\-\-\-\-
\& \-\-\-\-\-END EC PARAMETERS\-\-\-\-\-
.Ve
.PP
OpenSSL is currently not able to generate new groups and therefore
\&\fBecparam\fR can only create EC parameters from known (named) curves.
.SH EXAMPLES
.IX Header "EXAMPLES"
To create EC parameters with the group 'prime192v1':
.PP
.Vb 1
\&  openssl ecparam \-out ec_param.pem \-name prime192v1
.Ve
.PP
To create EC parameters with explicit parameters:
.PP
.Vb 1
\&  openssl ecparam \-out ec_param.pem \-name prime192v1 \-param_enc explicit
.Ve
.PP
To validate given EC parameters:
.PP
.Vb 1
\&  openssl ecparam \-in ec_param.pem \-check
.Ve
.PP
To create EC parameters and a private key:
.PP
.Vb 1
\&  openssl ecparam \-out ec_key.pem \-name prime192v1 \-genkey
.Ve
.PP
To change the point encoding to 'compressed':
.PP
.Vb 1
\&  openssl ecparam \-in ec_in.pem \-out ec_out.pem \-conv_form compressed
.Ve
.PP
To print out the EC parameters to standard output:
.PP
.Vb 1
\&  openssl ecparam \-in ec_param.pem \-noout \-text
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBec\fR\|(1), \fBdsaparam\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2003\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
