.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_VERIFY_RESULT 3"
.TH SSL_GET_VERIFY_RESULT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_verify_result \- get result of peer certificate verification
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_get_verify_result(const SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_verify_result()\fR returns the result of the verification of the
X509 certificate presented by the peer, if any.
.SH NOTES
.IX Header "NOTES"
\&\fBSSL_get_verify_result()\fR can only return one error code while the verification
of a certificate can fail because of many reasons at the same time. Only
the last verification error that occurred during the processing is available
from \fBSSL_get_verify_result()\fR.
.PP
The verification result is part of the established session and is restored
when a session is reused.
.SH BUGS
.IX Header "BUGS"
If no peer certificate was presented, the returned result code is
X509_V_OK. This is because no verification error occurred, it does however
not indicate success. \fBSSL_get_verify_result()\fR is only useful in connection
with \fBSSL_get_peer_certificate\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can currently occur:
.IP X509_V_OK 4
.IX Item "X509_V_OK"
The verification succeeded or no peer certificate was presented.
.IP "Any other value" 4
.IX Item "Any other value"
Documented in \fBverify\fR\|(1).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_set_verify_result\fR\|(3),
\&\fBSSL_get_peer_certificate\fR\|(3),
\&\fBverify\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
