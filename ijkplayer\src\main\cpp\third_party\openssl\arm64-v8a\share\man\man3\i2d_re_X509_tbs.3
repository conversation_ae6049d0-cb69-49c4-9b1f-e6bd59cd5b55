.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "I2D_RE_X509_TBS 3"
.TH I2D_RE_X509_TBS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
d2i_X509_AUX, i2d_X509_AUX,
i2d_re_X509_tbs, i2d_re_X509_CRL_tbs, i2d_re_X509_REQ_tbs
\&\- X509 encode and decode functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& X509 *d2i_X509_AUX(X509 **px, const unsigned char **in, long len);
\& int i2d_X509_AUX(X509 *x, unsigned char **out);
\& int i2d_re_X509_tbs(X509 *x, unsigned char **out);
\& int i2d_re_X509_CRL_tbs(X509_CRL *crl, unsigned char **pp);
\& int i2d_re_X509_REQ_tbs(X509_REQ *req, unsigned char **pp);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The X509 encode and decode routines encode and parse an
\&\fBX509\fR structure, which represents an X509 certificate.
.PP
\&\fBd2i_X509_AUX()\fR is similar to \fBd2i_X509\fR\|(3) but the input is expected to
consist of an X509 certificate followed by auxiliary trust information.
This is used by the PEM routines to read "TRUSTED CERTIFICATE" objects.
This function should not be called on untrusted input.
.PP
\&\fBi2d_X509_AUX()\fR is similar to \fBi2d_X509\fR\|(3), but the encoded output
contains both the certificate and any auxiliary trust information.
This is used by the PEM routines to write "TRUSTED CERTIFICATE" objects.
Note that this is a non-standard OpenSSL-specific data format.
.PP
\&\fBi2d_re_X509_tbs()\fR is similar to \fBi2d_X509\fR\|(3) except it encodes only
the TBSCertificate portion of the certificate.  \fBi2d_re_X509_CRL_tbs()\fR
and \fBi2d_re_X509_REQ_tbs()\fR are analogous for CRL and certificate request,
respectively.  The "re" in \fBi2d_re_X509_tbs\fR stands for "re-encode",
and ensures that a fresh encoding is generated in case the object has been
modified after creation (see the BUGS section).
.PP
The encoding of the TBSCertificate portion of a certificate is cached
in the \fBX509\fR structure internally to improve encoding performance
and to ensure certificate signatures are verified correctly in some
certificates with broken (non-DER) encodings.
.PP
If, after modification, the \fBX509\fR object is re-signed with \fBX509_sign()\fR,
the encoding is automatically renewed. Otherwise, the encoding of the
TBSCertificate portion of the \fBX509\fR can be manually renewed by calling
\&\fBi2d_re_X509_tbs()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBd2i_X509_AUX()\fR returns a valid \fBX509\fR structure or NULL if an error occurred.
.PP
\&\fBi2d_X509_AUX()\fR returns the length of encoded data or \-1 on error.
.PP
\&\fBi2d_re_X509_tbs()\fR, \fBi2d_re_X509_CRL_tbs()\fR and \fBi2d_re_X509_REQ_tbs()\fR return the
length of encoded data or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3)
\&\fBX509_CRL_get0_by_serial\fR\|(3),
\&\fBX509_get0_signature\fR\|(3),
\&\fBX509_get_ext_d2i\fR\|(3),
\&\fBX509_get_extension_flags\fR\|(3),
\&\fBX509_get_pubkey\fR\|(3),
\&\fBX509_get_subject_name\fR\|(3),
\&\fBX509_get_version\fR\|(3),
\&\fBX509_NAME_add_entry_by_txt\fR\|(3),
\&\fBX509_NAME_ENTRY_get_object\fR\|(3),
\&\fBX509_NAME_get_index_by_NID\fR\|(3),
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBX509_new\fR\|(3),
\&\fBX509_sign\fR\|(3),
\&\fBX509V3_get_d2i\fR\|(3),
\&\fBX509_verify_cert\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
