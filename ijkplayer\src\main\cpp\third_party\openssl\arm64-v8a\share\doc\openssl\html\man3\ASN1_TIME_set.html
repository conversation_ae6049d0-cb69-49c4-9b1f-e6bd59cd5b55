<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ASN1_TIME_set</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ASN1_TIME_set, ASN1_UTCTIME_set, ASN1_GENERALIZEDTIME_set, ASN1_TIME_adj, ASN1_UTCTIME_adj, ASN1_GENERALIZEDTIME_adj, ASN1_TIME_check, ASN1_UTCTIME_check, ASN1_GENERALIZEDTIME_check, ASN1_TIME_set_string, ASN1_UTCTIME_set_string, ASN1_GENERALIZEDTIME_set_string, ASN1_TIME_set_string_X509, ASN1_TIME_normalize, ASN1_TIME_to_tm, ASN1_TIME_print, ASN1_UTCTIME_print, ASN1_GENERALIZEDTIME_print, ASN1_TIME_diff, ASN1_TIME_cmp_time_t, ASN1_UTCTIME_cmp_time_t, ASN1_TIME_compare, ASN1_TIME_to_generalizedtime - ASN.1 Time functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>ASN1_TIME *ASN1_TIME_set(ASN1_TIME *s, time_t t);
ASN1_UTCTIME *ASN1_UTCTIME_set(ASN1_UTCTIME *s, time_t t);
ASN1_GENERALIZEDTIME *ASN1_GENERALIZEDTIME_set(ASN1_GENERALIZEDTIME *s,
                                               time_t t);

ASN1_TIME *ASN1_TIME_adj(ASN1_TIME *s, time_t t, int offset_day,
                         long offset_sec);
ASN1_UTCTIME *ASN1_UTCTIME_adj(ASN1_UTCTIME *s, time_t t,
                               int offset_day, long offset_sec);
ASN1_GENERALIZEDTIME *ASN1_GENERALIZEDTIME_adj(ASN1_GENERALIZEDTIME *s,
                                               time_t t, int offset_day,
                                               long offset_sec);

int ASN1_TIME_set_string(ASN1_TIME *s, const char *str);
int ASN1_TIME_set_string_X509(ASN1_TIME *s, const char *str);
int ASN1_UTCTIME_set_string(ASN1_UTCTIME *s, const char *str);
int ASN1_GENERALIZEDTIME_set_string(ASN1_GENERALIZEDTIME *s,
                                    const char *str);

int ASN1_TIME_normalize(ASN1_TIME *s);

int ASN1_TIME_check(const ASN1_TIME *t);
int ASN1_UTCTIME_check(const ASN1_UTCTIME *t);
int ASN1_GENERALIZEDTIME_check(const ASN1_GENERALIZEDTIME *t);

int ASN1_TIME_print(BIO *b, const ASN1_TIME *s);
int ASN1_UTCTIME_print(BIO *b, const ASN1_UTCTIME *s);
int ASN1_GENERALIZEDTIME_print(BIO *b, const ASN1_GENERALIZEDTIME *s);

int ASN1_TIME_to_tm(const ASN1_TIME *s, struct tm *tm);
int ASN1_TIME_diff(int *pday, int *psec, const ASN1_TIME *from,
                   const ASN1_TIME *to);

int ASN1_TIME_cmp_time_t(const ASN1_TIME *s, time_t t);
int ASN1_UTCTIME_cmp_time_t(const ASN1_UTCTIME *s, time_t t);

int ASN1_TIME_compare(const ASN1_TIME *a, const ASN1_TIME *b);

ASN1_GENERALIZEDTIME *ASN1_TIME_to_generalizedtime(ASN1_TIME *t,
                                                   ASN1_GENERALIZEDTIME **out);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The ASN1_TIME_set(), ASN1_UTCTIME_set() and ASN1_GENERALIZEDTIME_set() functions set the structure <b>s</b> to the time represented by the time_t value <b>t</b>. If <b>s</b> is NULL a new time structure is allocated and returned.</p>

<p>The ASN1_TIME_adj(), ASN1_UTCTIME_adj() and ASN1_GENERALIZEDTIME_adj() functions set the time structure <b>s</b> to the time represented by the time <b>offset_day</b> and <b>offset_sec</b> after the time_t value <b>t</b>. The values of <b>offset_day</b> or <b>offset_sec</b> can be negative to set a time before <b>t</b>. The <b>offset_sec</b> value can also exceed the number of seconds in a day. If <b>s</b> is NULL a new structure is allocated and returned.</p>

<p>The ASN1_TIME_set_string(), ASN1_UTCTIME_set_string() and ASN1_GENERALIZEDTIME_set_string() functions set the time structure <b>s</b> to the time represented by string <b>str</b> which must be in appropriate ASN.1 time format (for example YYMMDDHHMMSSZ or YYYYMMDDHHMMSSZ). If <b>s</b> is NULL this function performs a format check on <b>str</b> only. The string <b>str</b> is copied into <b>s</b>.</p>

<p>ASN1_TIME_set_string_X509() sets ASN1_TIME structure <b>s</b> to the time represented by string <b>str</b> which must be in appropriate time format that RFC 5280 requires, which means it only allows YYMMDDHHMMSSZ and YYYYMMDDHHMMSSZ (leap second is rejected), all other ASN.1 time format are not allowed. If <b>s</b> is NULL this function performs a format check on <b>str</b> only.</p>

<p>The ASN1_TIME_normalize() function converts an ASN1_GENERALIZEDTIME or ASN1_UTCTIME into a time value that can be used in a certificate. It should be used after the ASN1_TIME_set_string() functions and before ASN1_TIME_print() functions to get consistent (i.e. GMT) results.</p>

<p>The ASN1_TIME_check(), ASN1_UTCTIME_check() and ASN1_GENERALIZEDTIME_check() functions check the syntax of the time structure <b>s</b>.</p>

<p>The ASN1_TIME_print(), ASN1_UTCTIME_print() and ASN1_GENERALIZEDTIME_print() functions print the time structure <b>s</b> to BIO <b>b</b> in human readable format. It will be of the format MMM DD HH:MM:SS YYYY [GMT], for example &quot;Feb 3 00:55:52 2015 GMT&quot; it does not include a newline. If the time structure has invalid format it prints out &quot;Bad time value&quot; and returns an error. The output for generalized time may include a fractional part following the second.</p>

<p>ASN1_TIME_to_tm() converts the time <b>s</b> to the standard <b>tm</b> structure. If <b>s</b> is NULL, then the current time is converted. The output time is GMT. The <b>tm_sec</b>, <b>tm_min</b>, <b>tm_hour</b>, <b>tm_mday</b>, <b>tm_wday</b>, <b>tm_yday</b>, <b>tm_mon</b> and <b>tm_year</b> fields of <b>tm</b> structure are set to proper values, whereas all other fields are set to 0. If <b>tm</b> is NULL this function performs a format check on <b>s</b> only. If <b>s</b> is in Generalized format with fractional seconds, e.g. YYYYMMDDHHMMSS.SSSZ, the fractional seconds will be lost while converting <b>s</b> to <b>tm</b> structure.</p>

<p>ASN1_TIME_diff() sets <b>*pday</b> and <b>*psec</b> to the time difference between <b>from</b> and <b>to</b>. If <b>to</b> represents a time later than <b>from</b> then one or both (depending on the time difference) of <b>*pday</b> and <b>*psec</b> will be positive. If <b>to</b> represents a time earlier than <b>from</b> then one or both of <b>*pday</b> and <b>*psec</b> will be negative. If <b>to</b> and <b>from</b> represent the same time then <b>*pday</b> and <b>*psec</b> will both be zero. If both <b>*pday</b> and <b>*psec</b> are nonzero they will always have the same sign. The value of <b>*psec</b> will always be less than the number of seconds in a day. If <b>from</b> or <b>to</b> is NULL the current time is used.</p>

<p>The ASN1_TIME_cmp_time_t() and ASN1_UTCTIME_cmp_time_t() functions compare the two times represented by the time structure <b>s</b> and the time_t <b>t</b>.</p>

<p>The ASN1_TIME_compare() function compares the two times represented by the time structures <b>a</b> and <b>b</b>.</p>

<p>The ASN1_TIME_to_generalizedtime() function converts an ASN1_TIME to an ASN1_GENERALIZEDTIME, regardless of year. If either <b>out</b> or <b>*out</b> are NULL, then a new object is allocated and must be freed after use.</p>

<h1 id="NOTES">NOTES</h1>

<p>The ASN1_TIME structure corresponds to the ASN.1 structure <b>Time</b> defined in RFC5280 et al. The time setting functions obey the rules outlined in RFC5280: if the date can be represented by UTCTime it is used, else GeneralizedTime is used.</p>

<p>The ASN1_TIME, ASN1_UTCTIME and ASN1_GENERALIZEDTIME structures are represented as an ASN1_STRING internally and can be freed up using ASN1_STRING_free().</p>

<p>The ASN1_TIME structure can represent years from 0000 to 9999 but no attempt is made to correct ancient calendar changes (for example from Julian to Gregorian calendars).</p>

<p>ASN1_UTCTIME is limited to a year range of 1950 through 2049.</p>

<p>Some applications add offset times directly to a time_t value and pass the results to ASN1_TIME_set() (or equivalent). This can cause problems as the time_t value can overflow on some systems resulting in unexpected results. New applications should use ASN1_TIME_adj() instead and pass the offset value in the <b>offset_sec</b> and <b>offset_day</b> parameters instead of directly manipulating a time_t value.</p>

<p>ASN1_TIME_adj() may change the type from ASN1_GENERALIZEDTIME to ASN1_UTCTIME, or vice versa, based on the resulting year. The ASN1_GENERALIZEDTIME_adj() and ASN1_UTCTIME_adj() functions will not modify the type of the return structure.</p>

<p>It is recommended that functions starting with ASN1_TIME be used instead of those starting with ASN1_UTCTIME or ASN1_GENERALIZEDTIME. The functions starting with ASN1_UTCTIME and ASN1_GENERALIZEDTIME act only on that specific time format. The functions starting with ASN1_TIME will operate on either format.</p>

<h1 id="BUGS">BUGS</h1>

<p>ASN1_TIME_print(), ASN1_UTCTIME_print() and ASN1_GENERALIZEDTIME_print() do not print out the timezone: it either prints out &quot;GMT&quot; or nothing. But all certificates complying with RFC5280 et al use GMT anyway.</p>

<p>Use the ASN1_TIME_normalize() function to normalize the time value before printing to get GMT results.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>ASN1_TIME_set(), ASN1_UTCTIME_set(), ASN1_GENERALIZEDTIME_set(), ASN1_TIME_adj(), ASN1_UTCTIME_adj and ASN1_GENERALIZEDTIME_set return a pointer to a time structure or NULL if an error occurred.</p>

<p>ASN1_TIME_set_string(), ASN1_UTCTIME_set_string(), ASN1_GENERALIZEDTIME_set_string() ASN1_TIME_set_string_X509() return 1 if the time value is successfully set and 0 otherwise.</p>

<p>ASN1_TIME_normalize() returns 1 on success, and 0 on error.</p>

<p>ASN1_TIME_check(), ASN1_UTCTIME_check and ASN1_GENERALIZEDTIME_check() return 1 if the structure is syntactically correct and 0 otherwise.</p>

<p>ASN1_TIME_print(), ASN1_UTCTIME_print() and ASN1_GENERALIZEDTIME_print() return 1 if the time is successfully printed out and 0 if an error occurred (I/O error or invalid time format).</p>

<p>ASN1_TIME_to_tm() returns 1 if the time is successfully parsed and 0 if an error occurred (invalid time format).</p>

<p>ASN1_TIME_diff() returns 1 for success and 0 for failure. It can fail if the passed-in time structure has invalid syntax, for example.</p>

<p>ASN1_TIME_cmp_time_t() and ASN1_UTCTIME_cmp_time_t() return -1 if <b>s</b> is before <b>t</b>, 0 if <b>s</b> equals <b>t</b>, or 1 if <b>s</b> is after <b>t</b>. -2 is returned on error.</p>

<p>ASN1_TIME_compare() returns -1 if <b>a</b> is before <b>b</b>, 0 if <b>a</b> equals <b>b</b>, or 1 if <b>a</b> is after <b>b</b>. -2 is returned on error.</p>

<p>ASN1_TIME_to_generalizedtime() returns a pointer to the appropriate time structure on success or NULL if an error occurred.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Set a time structure to one hour after the current time and print it out:</p>

<pre><code>#include &lt;time.h&gt;
#include &lt;openssl/asn1.h&gt;

ASN1_TIME *tm;
time_t t;
BIO *b;

t = time(NULL);
tm = ASN1_TIME_adj(NULL, t, 0, 60 * 60);
b = BIO_new_fp(stdout, BIO_NOCLOSE);
ASN1_TIME_print(b, tm);
ASN1_STRING_free(tm);
BIO_free(b);</code></pre>

<p>Determine if one time is later or sooner than the current time:</p>

<pre><code>int day, sec;

if (!ASN1_TIME_diff(&amp;day, &amp;sec, NULL, to))
    /* Invalid time format */

if (day &gt; 0 || sec &gt; 0)
    printf(&quot;Later\n&quot;);
else if (day &lt; 0 || sec &lt; 0)
    printf(&quot;Sooner\n&quot;);
else
    printf(&quot;Same\n&quot;);</code></pre>

<h1 id="HISTORY">HISTORY</h1>

<p>The ASN1_TIME_to_tm() function was added in OpenSSL 1.1.1. The ASN1_TIME_set_string_X509() function was added in OpenSSL 1.1.1. The ASN1_TIME_normalize() function was added in OpenSSL 1.1.1. The ASN1_TIME_cmp_time_t() function was added in OpenSSL 1.1.1. The ASN1_TIME_compare() function was added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


