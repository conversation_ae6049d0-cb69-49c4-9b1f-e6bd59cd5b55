<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>RSA_size</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>RSA_size, RSA_bits, RSA_security_bits - get RSA modulus size or security bits</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/rsa.h&gt;

int RSA_size(const RSA *rsa);

int RSA_bits(const RSA *rsa);

int RSA_security_bits(const RSA *rsa)</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>RSA_size() returns the RSA modulus size in bytes. It can be used to determine how much memory must be allocated for an RSA encrypted value.</p>

<p>RSA_bits() returns the number of significant bits.</p>

<p><b>rsa</b> and <b>rsa-&gt;n</b> must not be <b>NULL</b>.</p>

<p>RSA_security_bits() returns the number of security bits of the given <b>rsa</b> key. See <a href="../man3/BN_security_bits.html">BN_security_bits(3)</a>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>RSA_size() returns the size of modulus in bytes.</p>

<p>DSA_bits() returns the number of bits in the key.</p>

<p>RSA_security_bits() returns the number of security bits.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/BN_num_bits.html">BN_num_bits(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The RSA_bits() function was added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


