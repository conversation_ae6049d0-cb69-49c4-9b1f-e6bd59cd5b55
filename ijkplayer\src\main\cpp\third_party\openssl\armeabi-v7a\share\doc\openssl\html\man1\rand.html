<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>rand</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-rand, rand - generate pseudo-random bytes</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl rand</b> [<b>-help</b>] [<b>-out</b> <i>file</i>] [<b>-rand file...</b>] [<b>-writerand file</b>] [<b>-base64</b>] [<b>-hex</b>] <i>num</i></p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command generates <i>num</i> random bytes using a cryptographically secure pseudo random number generator (CSPRNG).</p>

<p>The random bytes are generated using the <a href="../man3/RAND_bytes.html">RAND_bytes(3)</a> function, which provides a security level of 256 bits, provided it managed to seed itself successfully from a trusted operating system entropy source. Otherwise, the command will fail with a nonzero error code. For more details, see <a href="../man3/RAND_bytes.html">RAND_bytes(3)</a>, <a href="../man7/RAND.html">RAND(7)</a>, and <a href="../man7/RAND_DRBG.html">RAND_DRBG(7)</a>.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="out-file"><b>-out file</b></dt>
<dd>

<p>Write to <i>file</i> instead of standard output.</p>

</dd>
<dt id="rand-file"><b>-rand file...</b></dt>
<dd>

<p>A file or files containing random data used to seed the random number generator. Multiple files can be specified separated by an OS-dependent character. The separator is <b>;</b> for MS-Windows, <b>,</b> for OpenVMS, and <b>:</b> for all others. Explicitly specifying a seed file is in general not necessary, see the <a href="#NOTES">&quot;NOTES&quot;</a> section for more information.</p>

</dd>
<dt id="writerand-file">[<b>-writerand file</b>]</dt>
<dd>

<p>Writes random data to the specified <i>file</i> upon exit. This can be used with a subsequent <b>-rand</b> flag.</p>

</dd>
<dt id="base64"><b>-base64</b></dt>
<dd>

<p>Perform base64 encoding on the output.</p>

</dd>
<dt id="hex"><b>-hex</b></dt>
<dd>

<p>Show the output as a hex string.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>Prior to OpenSSL 1.1.1, it was common for applications to store information about the state of the random-number generator in a file that was loaded at startup and rewritten upon exit. On modern operating systems, this is generally no longer necessary as OpenSSL will seed itself from a trusted entropy source provided by the operating system. The <b>-rand</b> and <b>-writerand</b> flags are still supported for special platforms or circumstances that might require them.</p>

<p>It is generally an error to use the same seed file more than once and every use of <b>-rand</b> should be paired with <b>-writerand</b>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/RAND_bytes.html">RAND_bytes(3)</a>, <a href="../man7/RAND.html">RAND(7)</a>, <a href="../man7/RAND_DRBG.html">RAND_DRBG(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


