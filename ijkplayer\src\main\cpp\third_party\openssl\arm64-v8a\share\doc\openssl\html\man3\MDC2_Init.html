<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>MDC2_Init</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>MDC2, MDC2_Init, MDC2_Update, MDC2_Final - MDC2 hash function</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/mdc2.h&gt;

unsigned char *MDC2(const unsigned char *d, unsigned long n,
                    unsigned char *md);

int MDC2_Init(MDC2_CTX *c);
int MDC2_Update(MDC2_CTX *c, const unsigned char *data,
                unsigned long len);
int MDC2_Final(unsigned char *md, MDC2_CTX *c);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>MDC2 is a method to construct hash functions with 128 bit output from block ciphers. These functions are an implementation of MDC2 with DES.</p>

<p>MDC2() computes the MDC2 message digest of the <b>n</b> bytes at <b>d</b> and places it in <b>md</b> (which must have space for MDC2_DIGEST_LENGTH == 16 bytes of output). If <b>md</b> is NULL, the digest is placed in a static array.</p>

<p>The following functions may be used if the message is not completely stored in memory:</p>

<p>MDC2_Init() initializes a <b>MDC2_CTX</b> structure.</p>

<p>MDC2_Update() can be called repeatedly with chunks of the message to be hashed (<b>len</b> bytes at <b>data</b>).</p>

<p>MDC2_Final() places the message digest in <b>md</b>, which must have space for MDC2_DIGEST_LENGTH == 16 bytes of output, and erases the <b>MDC2_CTX</b>.</p>

<p>Applications should use the higher level functions <a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a> etc. instead of calling the hash functions directly.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>MDC2() returns a pointer to the hash value.</p>

<p>MDC2_Init(), MDC2_Update() and MDC2_Final() return 1 for success, 0 otherwise.</p>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>ISO/IEC 10118-2:2000 Hash-Function 2, with DES as the underlying block cipher.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2017 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


