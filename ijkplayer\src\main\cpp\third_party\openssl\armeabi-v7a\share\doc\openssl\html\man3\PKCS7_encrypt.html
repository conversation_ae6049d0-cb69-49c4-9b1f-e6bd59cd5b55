<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>PKCS7_encrypt</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#NOTES1">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>PKCS7_encrypt - create a PKCS#7 envelopedData structure</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/pkcs7.h&gt;

PKCS7 *PKCS7_encrypt(STACK_OF(X509) *certs, BIO *in, const EVP_CIPHER *cipher,
                     int flags);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>PKCS7_encrypt() creates and returns a PKCS#7 envelopedData structure. <b>certs</b> is a list of recipient certificates. <b>in</b> is the content to be encrypted. <b>cipher</b> is the symmetric cipher to use. <b>flags</b> is an optional set of flags.</p>

<h1 id="NOTES">NOTES</h1>

<p>Only RSA keys are supported in PKCS#7 and envelopedData so the recipient certificates supplied to this function must all contain RSA public keys, though they do not have to be signed using the RSA algorithm.</p>

<p>EVP_des_ede3_cbc() (triple DES) is the algorithm of choice for S/MIME use because most clients will support it.</p>

<p>Some old &quot;export grade&quot; clients may only support weak encryption using 40 or 64 bit RC2. These can be used by passing EVP_rc2_40_cbc() and EVP_rc2_64_cbc() respectively.</p>

<p>The algorithm passed in the <b>cipher</b> parameter must support ASN1 encoding of its parameters.</p>

<p>Many browsers implement a &quot;sign and encrypt&quot; option which is simply an S/MIME envelopedData containing an S/MIME signed message. This can be readily produced by storing the S/MIME signed message in a memory BIO and passing it to PKCS7_encrypt().</p>

<p>The following flags can be passed in the <b>flags</b> parameter.</p>

<p>If the <b>PKCS7_TEXT</b> flag is set MIME headers for type <b>text/plain</b> are prepended to the data.</p>

<p>Normally the supplied content is translated into MIME canonical format (as required by the S/MIME specifications) if <b>PKCS7_BINARY</b> is set no translation occurs. This option should be used if the supplied data is in binary format otherwise the translation will corrupt it. If <b>PKCS7_BINARY</b> is set then <b>PKCS7_TEXT</b> is ignored.</p>

<p>If the <b>PKCS7_STREAM</b> flag is set a partial <b>PKCS7</b> structure is output suitable for streaming I/O: no data is read from the BIO <b>in</b>.</p>

<h1 id="NOTES1">NOTES</h1>

<p>If the flag <b>PKCS7_STREAM</b> is set the returned <b>PKCS7</b> structure is <b>not</b> complete and outputting its contents via a function that does not properly finalize the <b>PKCS7</b> structure will give unpredictable results.</p>

<p>Several functions including SMIME_write_PKCS7(), i2d_PKCS7_bio_stream(), PEM_write_bio_PKCS7_stream() finalize the structure. Alternatively finalization can be performed by obtaining the streaming ASN1 <b>BIO</b> directly using BIO_new_PKCS7().</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>PKCS7_encrypt() returns either a PKCS7 structure or NULL if an error occurred. The error can be obtained from ERR_get_error(3).</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/PKCS7_decrypt.html">PKCS7_decrypt(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The <b>PKCS7_STREAM</b> flag was added in OpenSSL 1.0.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2002-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


