<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>d2i_X509</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#WARNINGS">WARNINGS</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>d2i_ACCESS_DESCRIPTION, d2i_ADMISSIONS, d2i_ADMISSION_SYNTAX, d2i_ASIdOrRange, d2i_ASIdentifierChoice, d2i_ASIdentifiers, d2i_ASN1_BIT_STRING, d2i_ASN1_BMPSTRING, d2i_ASN1_ENUMERATED, d2i_ASN1_GENERALIZEDTIME, d2i_ASN1_GENERALSTRING, d2i_ASN1_IA5STRING, d2i_ASN1_INTEGER, d2i_ASN1_NULL, d2i_ASN1_OBJECT, d2i_ASN1_OCTET_STRING, d2i_ASN1_PRINTABLE, d2i_ASN1_PRINTABLESTRING, d2i_ASN1_SEQUENCE_ANY, d2i_ASN1_SET_ANY, d2i_ASN1_T61STRING, d2i_ASN1_TIME, d2i_ASN1_TYPE, d2i_ASN1_UINTEGER, d2i_ASN1_UNIVERSALSTRING, d2i_ASN1_UTCTIME, d2i_ASN1_UTF8STRING, d2i_ASN1_VISIBLESTRING, d2i_ASRange, d2i_AUTHORITY_INFO_ACCESS, d2i_AUTHORITY_KEYID, d2i_BASIC_CONSTRAINTS, d2i_CERTIFICATEPOLICIES, d2i_CMS_ContentInfo, d2i_CMS_ReceiptRequest, d2i_CMS_bio, d2i_CRL_DIST_POINTS, d2i_DHxparams, d2i_DIRECTORYSTRING, d2i_DISPLAYTEXT, d2i_DIST_POINT, d2i_DIST_POINT_NAME, d2i_DSAPrivateKey, d2i_DSAPrivateKey_bio, d2i_DSAPrivateKey_fp, d2i_DSAPublicKey, d2i_DSA_PUBKEY, d2i_DSA_PUBKEY_bio, d2i_DSA_PUBKEY_fp, d2i_DSA_SIG, d2i_DSAparams, d2i_ECDSA_SIG, d2i_ECPKParameters, d2i_ECParameters, d2i_ECPrivateKey, d2i_ECPrivateKey_bio, d2i_ECPrivateKey_fp, d2i_EC_PUBKEY, d2i_EC_PUBKEY_bio, d2i_EC_PUBKEY_fp, d2i_EDIPARTYNAME, d2i_ESS_CERT_ID, d2i_ESS_ISSUER_SERIAL, d2i_ESS_SIGNING_CERT, d2i_EXTENDED_KEY_USAGE, d2i_GENERAL_NAME, d2i_GENERAL_NAMES, d2i_IPAddressChoice, d2i_IPAddressFamily, d2i_IPAddressOrRange, d2i_IPAddressRange, d2i_ISSUING_DIST_POINT, d2i_NAMING_AUTHORITY, d2i_NETSCAPE_CERT_SEQUENCE, d2i_NETSCAPE_SPKAC, d2i_NETSCAPE_SPKI, d2i_NOTICEREF, d2i_OCSP_BASICRESP, d2i_OCSP_CERTID, d2i_OCSP_CERTSTATUS, d2i_OCSP_CRLID, d2i_OCSP_ONEREQ, d2i_OCSP_REQINFO, d2i_OCSP_REQUEST, d2i_OCSP_RESPBYTES, d2i_OCSP_RESPDATA, d2i_OCSP_RESPID, d2i_OCSP_RESPONSE, d2i_OCSP_REVOKEDINFO, d2i_OCSP_SERVICELOC, d2i_OCSP_SIGNATURE, d2i_OCSP_SINGLERESP, d2i_OTHERNAME, d2i_PBE2PARAM, d2i_PBEPARAM, d2i_PBKDF2PARAM, d2i_PKCS12, d2i_PKCS12_BAGS, d2i_PKCS12_MAC_DATA, d2i_PKCS12_SAFEBAG, d2i_PKCS12_bio, d2i_PKCS12_fp, d2i_PKCS7, d2i_PKCS7_DIGEST, d2i_PKCS7_ENCRYPT, d2i_PKCS7_ENC_CONTENT, d2i_PKCS7_ENVELOPE, d2i_PKCS7_ISSUER_AND_SERIAL, d2i_PKCS7_RECIP_INFO, d2i_PKCS7_SIGNED, d2i_PKCS7_SIGNER_INFO, d2i_PKCS7_SIGN_ENVELOPE, d2i_PKCS7_bio, d2i_PKCS7_fp, d2i_PKCS8_PRIV_KEY_INFO, d2i_PKCS8_PRIV_KEY_INFO_bio, d2i_PKCS8_PRIV_KEY_INFO_fp, d2i_PKCS8_bio, d2i_PKCS8_fp, d2i_PKEY_USAGE_PERIOD, d2i_POLICYINFO, d2i_POLICYQUALINFO, d2i_PROFESSION_INFO, d2i_PROXY_CERT_INFO_EXTENSION, d2i_PROXY_POLICY, d2i_RSAPrivateKey, d2i_RSAPrivateKey_bio, d2i_RSAPrivateKey_fp, d2i_RSAPublicKey, d2i_RSAPublicKey_bio, d2i_RSAPublicKey_fp, d2i_RSA_OAEP_PARAMS, d2i_RSA_PSS_PARAMS, d2i_RSA_PUBKEY, d2i_RSA_PUBKEY_bio, d2i_RSA_PUBKEY_fp, d2i_SCRYPT_PARAMS, d2i_SCT_LIST, d2i_SXNET, d2i_SXNETID, d2i_TS_ACCURACY, d2i_TS_MSG_IMPRINT, d2i_TS_MSG_IMPRINT_bio, d2i_TS_MSG_IMPRINT_fp, d2i_TS_REQ, d2i_TS_REQ_bio, d2i_TS_REQ_fp, d2i_TS_RESP, d2i_TS_RESP_bio, d2i_TS_RESP_fp, d2i_TS_STATUS_INFO, d2i_TS_TST_INFO, d2i_TS_TST_INFO_bio, d2i_TS_TST_INFO_fp, d2i_USERNOTICE, d2i_X509, d2i_X509_bio, d2i_X509_fp, d2i_X509_ALGOR, d2i_X509_ALGORS, d2i_X509_ATTRIBUTE, d2i_X509_CERT_AUX, d2i_X509_CINF, d2i_X509_CRL, d2i_X509_CRL_INFO, d2i_X509_CRL_bio, d2i_X509_CRL_fp, d2i_X509_EXTENSION, d2i_X509_EXTENSIONS, d2i_X509_NAME, d2i_X509_NAME_ENTRY, d2i_X509_PUBKEY, d2i_X509_REQ, d2i_X509_REQ_INFO, d2i_X509_REQ_bio, d2i_X509_REQ_fp, d2i_X509_REVOKED, d2i_X509_SIG, d2i_X509_VAL, i2d_ACCESS_DESCRIPTION, i2d_ADMISSIONS, i2d_ADMISSION_SYNTAX, i2d_ASIdOrRange, i2d_ASIdentifierChoice, i2d_ASIdentifiers, i2d_ASN1_BIT_STRING, i2d_ASN1_BMPSTRING, i2d_ASN1_ENUMERATED, i2d_ASN1_GENERALIZEDTIME, i2d_ASN1_GENERALSTRING, i2d_ASN1_IA5STRING, i2d_ASN1_INTEGER, i2d_ASN1_NULL, i2d_ASN1_OBJECT, i2d_ASN1_OCTET_STRING, i2d_ASN1_PRINTABLE, i2d_ASN1_PRINTABLESTRING, i2d_ASN1_SEQUENCE_ANY, i2d_ASN1_SET_ANY, i2d_ASN1_T61STRING, i2d_ASN1_TIME, i2d_ASN1_TYPE, i2d_ASN1_UNIVERSALSTRING, i2d_ASN1_UTCTIME, i2d_ASN1_UTF8STRING, i2d_ASN1_VISIBLESTRING, i2d_ASN1_bio_stream, i2d_ASRange, i2d_AUTHORITY_INFO_ACCESS, i2d_AUTHORITY_KEYID, i2d_BASIC_CONSTRAINTS, i2d_CERTIFICATEPOLICIES, i2d_CMS_ContentInfo, i2d_CMS_ReceiptRequest, i2d_CMS_bio, i2d_CRL_DIST_POINTS, i2d_DHxparams, i2d_DIRECTORYSTRING, i2d_DISPLAYTEXT, i2d_DIST_POINT, i2d_DIST_POINT_NAME, i2d_DSAPrivateKey, i2d_DSAPrivateKey_bio, i2d_DSAPrivateKey_fp, i2d_DSAPublicKey, i2d_DSA_PUBKEY, i2d_DSA_PUBKEY_bio, i2d_DSA_PUBKEY_fp, i2d_DSA_SIG, i2d_DSAparams, i2d_ECDSA_SIG, i2d_ECPKParameters, i2d_ECParameters, i2d_ECPrivateKey, i2d_ECPrivateKey_bio, i2d_ECPrivateKey_fp, i2d_EC_PUBKEY, i2d_EC_PUBKEY_bio, i2d_EC_PUBKEY_fp, i2d_EDIPARTYNAME, i2d_ESS_CERT_ID, i2d_ESS_ISSUER_SERIAL, i2d_ESS_SIGNING_CERT, i2d_EXTENDED_KEY_USAGE, i2d_GENERAL_NAME, i2d_GENERAL_NAMES, i2d_IPAddressChoice, i2d_IPAddressFamily, i2d_IPAddressOrRange, i2d_IPAddressRange, i2d_ISSUING_DIST_POINT, i2d_NAMING_AUTHORITY, i2d_NETSCAPE_CERT_SEQUENCE, i2d_NETSCAPE_SPKAC, i2d_NETSCAPE_SPKI, i2d_NOTICEREF, i2d_OCSP_BASICRESP, i2d_OCSP_CERTID, i2d_OCSP_CERTSTATUS, i2d_OCSP_CRLID, i2d_OCSP_ONEREQ, i2d_OCSP_REQINFO, i2d_OCSP_REQUEST, i2d_OCSP_RESPBYTES, i2d_OCSP_RESPDATA, i2d_OCSP_RESPID, i2d_OCSP_RESPONSE, i2d_OCSP_REVOKEDINFO, i2d_OCSP_SERVICELOC, i2d_OCSP_SIGNATURE, i2d_OCSP_SINGLERESP, i2d_OTHERNAME, i2d_PBE2PARAM, i2d_PBEPARAM, i2d_PBKDF2PARAM, i2d_PKCS12, i2d_PKCS12_BAGS, i2d_PKCS12_MAC_DATA, i2d_PKCS12_SAFEBAG, i2d_PKCS12_bio, i2d_PKCS12_fp, i2d_PKCS7, i2d_PKCS7_DIGEST, i2d_PKCS7_ENCRYPT, i2d_PKCS7_ENC_CONTENT, i2d_PKCS7_ENVELOPE, i2d_PKCS7_ISSUER_AND_SERIAL, i2d_PKCS7_NDEF, i2d_PKCS7_RECIP_INFO, i2d_PKCS7_SIGNED, i2d_PKCS7_SIGNER_INFO, i2d_PKCS7_SIGN_ENVELOPE, i2d_PKCS7_bio, i2d_PKCS7_fp, i2d_PKCS8PrivateKeyInfo_bio, i2d_PKCS8PrivateKeyInfo_fp, i2d_PKCS8_PRIV_KEY_INFO, i2d_PKCS8_PRIV_KEY_INFO_bio, i2d_PKCS8_PRIV_KEY_INFO_fp, i2d_PKCS8_bio, i2d_PKCS8_fp, i2d_PKEY_USAGE_PERIOD, i2d_POLICYINFO, i2d_POLICYQUALINFO, i2d_PROFESSION_INFO, i2d_PROXY_CERT_INFO_EXTENSION, i2d_PROXY_POLICY, i2d_RSAPrivateKey, i2d_RSAPrivateKey_bio, i2d_RSAPrivateKey_fp, i2d_RSAPublicKey, i2d_RSAPublicKey_bio, i2d_RSAPublicKey_fp, i2d_RSA_OAEP_PARAMS, i2d_RSA_PSS_PARAMS, i2d_RSA_PUBKEY, i2d_RSA_PUBKEY_bio, i2d_RSA_PUBKEY_fp, i2d_SCRYPT_PARAMS, i2d_SCT_LIST, i2d_SXNET, i2d_SXNETID, i2d_TS_ACCURACY, i2d_TS_MSG_IMPRINT, i2d_TS_MSG_IMPRINT_bio, i2d_TS_MSG_IMPRINT_fp, i2d_TS_REQ, i2d_TS_REQ_bio, i2d_TS_REQ_fp, i2d_TS_RESP, i2d_TS_RESP_bio, i2d_TS_RESP_fp, i2d_TS_STATUS_INFO, i2d_TS_TST_INFO, i2d_TS_TST_INFO_bio, i2d_TS_TST_INFO_fp, i2d_USERNOTICE, i2d_X509, i2d_X509_bio, i2d_X509_fp, i2d_X509_ALGOR, i2d_X509_ALGORS, i2d_X509_ATTRIBUTE, i2d_X509_CERT_AUX, i2d_X509_CINF, i2d_X509_CRL, i2d_X509_CRL_INFO, i2d_X509_CRL_bio, i2d_X509_CRL_fp, i2d_X509_EXTENSION, i2d_X509_EXTENSIONS, i2d_X509_NAME, i2d_X509_NAME_ENTRY, i2d_X509_PUBKEY, i2d_X509_REQ, i2d_X509_REQ_INFO, i2d_X509_REQ_bio, i2d_X509_REQ_fp, i2d_X509_REVOKED, i2d_X509_SIG, i2d_X509_VAL, - convert objects from/to ASN.1/DER representation</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>TYPE *d2i_TYPE(TYPE **a, const unsigned char **ppin, long length);
TYPE *d2i_TYPE_bio(BIO *bp, TYPE **a);
TYPE *d2i_TYPE_fp(FILE *fp, TYPE **a);

int i2d_TYPE(TYPE *a, unsigned char **ppout);
int i2d_TYPE_fp(FILE *fp, TYPE *a);
int i2d_TYPE_bio(BIO *bp, TYPE *a);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>In the description here, <i>TYPE</i> is used a placeholder for any of the OpenSSL datatypes, such as <i>X509_CRL</i>. The function parameters <i>ppin</i> and <i>ppout</i> are generally either both named <i>pp</i> in the headers, or <i>in</i> and <i>out</i>.</p>

<p>These functions convert OpenSSL objects to and from their ASN.1/DER encoding. Unlike the C structures which can have pointers to sub-objects within, the DER is a serialized encoding, suitable for sending over the network, writing to a file, and so on.</p>

<p>d2i_TYPE() attempts to decode <b>len</b> bytes at <b>*ppin</b>. If successful a pointer to the <b>TYPE</b> structure is returned and <b>*ppin</b> is incremented to the byte following the parsed data. If <b>a</b> is not <b>NULL</b> then a pointer to the returned structure is also written to <b>*a</b>. If an error occurred then <b>NULL</b> is returned.</p>

<p>On a successful return, if <b>*a</b> is not <b>NULL</b> then it is assumed that <b>*a</b> contains a valid <b>TYPE</b> structure and an attempt is made to reuse it. This &quot;reuse&quot; capability is present for historical compatibility but its use is <b>strongly discouraged</b> (see BUGS below, and the discussion in the RETURN VALUES section).</p>

<p>d2i_TYPE_bio() is similar to d2i_TYPE() except it attempts to parse data from BIO <b>bp</b>.</p>

<p>d2i_TYPE_fp() is similar to d2i_TYPE() except it attempts to parse data from FILE pointer <b>fp</b>.</p>

<p>i2d_TYPE() encodes the structure pointed to by <b>a</b> into DER format. If <b>ppout</b> is not <b>NULL</b>, it writes the DER encoded data to the buffer at <b>*ppout</b>, and increments it to point after the data just written. If the return value is negative an error occurred, otherwise it returns the length of the encoded data.</p>

<p>If <b>*ppout</b> is <b>NULL</b> memory will be allocated for a buffer and the encoded data written to it. In this case <b>*ppout</b> is not incremented and it points to the start of the data just written.</p>

<p>i2d_TYPE_bio() is similar to i2d_TYPE() except it writes the encoding of the structure <b>a</b> to BIO <b>bp</b> and it returns 1 for success and 0 for failure.</p>

<p>i2d_TYPE_fp() is similar to i2d_TYPE() except it writes the encoding of the structure <b>a</b> to BIO <b>bp</b> and it returns 1 for success and 0 for failure.</p>

<p>These routines do not encrypt private keys and therefore offer no security; use <a href="../man3/PEM_write_PrivateKey.html">PEM_write_PrivateKey(3)</a> or similar for writing to files.</p>

<h1 id="NOTES">NOTES</h1>

<p>The letters <b>i</b> and <b>d</b> in <b>i2d_TYPE</b> stand for &quot;internal&quot; (that is, an internal C structure) and &quot;DER&quot; respectively. So <b>i2d_TYPE</b> converts from internal to DER.</p>

<p>The functions can also understand <b>BER</b> forms.</p>

<p>The actual TYPE structure passed to i2d_TYPE() must be a valid populated <b>TYPE</b> structure -- it <b>cannot</b> simply be fed with an empty structure such as that returned by TYPE_new().</p>

<p>The encoded data is in binary form and may contain embedded zeros. Therefore, any FILE pointers or BIOs should be opened in binary mode. Functions such as strlen() will <b>not</b> return the correct length of the encoded structure.</p>

<p>The ways that <b>*ppin</b> and <b>*ppout</b> are incremented after the operation can trap the unwary. See the <b>WARNINGS</b> section for some common errors. The reason for this-auto increment behaviour is to reflect a typical usage of ASN1 functions: after one structure is encoded or decoded another will be processed after it.</p>

<p>The following points about the data types might be useful:</p>

<dl>

<dt id="ASN1_OBJECT"><b>ASN1_OBJECT</b></dt>
<dd>

<p>Represents an ASN1 OBJECT IDENTIFIER.</p>

</dd>
<dt id="DHparams"><b>DHparams</b></dt>
<dd>

<p>Represents a PKCS#3 DH parameters structure.</p>

</dd>
<dt id="DHxparams"><b>DHxparams</b></dt>
<dd>

<p>Represents an ANSI X9.42 DH parameters structure.</p>

</dd>
<dt id="DSA_PUBKEY"><b>DSA_PUBKEY</b></dt>
<dd>

<p>Represents a DSA public key using a <b>SubjectPublicKeyInfo</b> structure.</p>

</dd>
<dt id="DSAPublicKey-DSAPrivateKey"><b>DSAPublicKey, DSAPrivateKey</b></dt>
<dd>

<p>Use a non-standard OpenSSL format and should be avoided; use <b>DSA_PUBKEY</b>, <b>PEM_write_PrivateKey(3)</b>, or similar instead.</p>

</dd>
<dt id="ECDSA_SIG"><b>ECDSA_SIG</b></dt>
<dd>

<p>Represents an ECDSA signature.</p>

</dd>
<dt id="RSAPublicKey"><b>RSAPublicKey</b></dt>
<dd>

<p>Represents a PKCS#1 RSA public key structure.</p>

</dd>
<dt id="X509_ALGOR"><b>X509_ALGOR</b></dt>
<dd>

<p>Represents an <b>AlgorithmIdentifier</b> structure as used in IETF RFC 6960 and elsewhere.</p>

</dd>
<dt id="X509_Name"><b>X509_Name</b></dt>
<dd>

<p>Represents a <b>Name</b> type as used for subject and issuer names in IETF RFC 6960 and elsewhere.</p>

</dd>
<dt id="X509_REQ"><b>X509_REQ</b></dt>
<dd>

<p>Represents a PKCS#10 certificate request.</p>

</dd>
<dt id="X509_SIG"><b>X509_SIG</b></dt>
<dd>

<p>Represents the <b>DigestInfo</b> structure defined in PKCS#1 and PKCS#7.</p>

</dd>
</dl>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>d2i_TYPE(), d2i_TYPE_bio() and d2i_TYPE_fp() return a valid <b>TYPE</b> structure or <b>NULL</b> if an error occurs. If the &quot;reuse&quot; capability has been used with a valid structure being passed in via <b>a</b>, then the object is freed in the event of error and <b>*a</b> is set to NULL.</p>

<p>i2d_TYPE() returns the number of bytes successfully encoded or a negative value if an error occurs.</p>

<p>i2d_TYPE_bio() and i2d_TYPE_fp() return 1 for success and 0 if an error occurs.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Allocate and encode the DER encoding of an X509 structure:</p>

<pre><code>int len;
unsigned char *buf;

buf = NULL;
len = i2d_X509(x, &amp;buf);
if (len &lt; 0)
    /* error */</code></pre>

<p>Attempt to decode a buffer:</p>

<pre><code>X509 *x;
unsigned char *buf;
const unsigned char *p;
int len;

/* Set up buf and len to point to the input buffer. */
p = buf;
x = d2i_X509(NULL, &amp;p, len);
if (x == NULL)
    /* error */</code></pre>

<p>Alternative technique:</p>

<pre><code>X509 *x;
unsigned char *buf;
const unsigned char *p;
int len;

/* Set up buf and len to point to the input buffer. */
p = buf;
x = NULL;

if (d2i_X509(&amp;x, &amp;p, len) == NULL)
    /* error */</code></pre>

<h1 id="WARNINGS">WARNINGS</h1>

<p>Using a temporary variable is mandatory. A common mistake is to attempt to use a buffer directly as follows:</p>

<pre><code>int len;
unsigned char *buf;

len = i2d_X509(x, NULL);
buf = OPENSSL_malloc(len);
...
i2d_X509(x, &amp;buf);
...
OPENSSL_free(buf);</code></pre>

<p>This code will result in <b>buf</b> apparently containing garbage because it was incremented after the call to point after the data just written. Also <b>buf</b> will no longer contain the pointer allocated by OPENSSL_malloc() and the subsequent call to OPENSSL_free() is likely to crash.</p>

<p>Another trap to avoid is misuse of the <b>a</b> argument to d2i_TYPE():</p>

<pre><code>X509 *x;

if (d2i_X509(&amp;x, &amp;p, len) == NULL)
    /* error */</code></pre>

<p>This will probably crash somewhere in d2i_X509(). The reason for this is that the variable <b>x</b> is uninitialized and an attempt will be made to interpret its (invalid) value as an <b>X509</b> structure, typically causing a segmentation violation. If <b>x</b> is set to NULL first then this will not happen.</p>

<h1 id="BUGS">BUGS</h1>

<p>In some versions of OpenSSL the &quot;reuse&quot; behaviour of d2i_TYPE() when <b>*a</b> is valid is broken and some parts of the reused structure may persist if they are not present in the new one. Additionally, in versions of OpenSSL prior to 1.1.0, when the &quot;reuse&quot; behaviour is used and an error occurs the behaviour is inconsistent. Some functions behaved as described here, while some did not free <b>*a</b> on error and did not set <b>*a</b> to NULL.</p>

<p>As a result of the above issues the &quot;reuse&quot; behaviour is strongly discouraged.</p>

<p>i2d_TYPE() will not return an error in many versions of OpenSSL, if mandatory fields are not initialized due to a programming error then the encoded structure may contain invalid data or omit the fields entirely and will not be parsed by d2i_TYPE(). This may be fixed in future so code should not assume that i2d_TYPE() will always succeed.</p>

<p>Any function which encodes a structure (i2d_TYPE(), i2d_TYPE() or i2d_TYPE()) may return a stale encoding if the structure has been modified after deserialization or previous serialization. This is because some objects cache the encoding for efficiency reasons.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 1998-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


