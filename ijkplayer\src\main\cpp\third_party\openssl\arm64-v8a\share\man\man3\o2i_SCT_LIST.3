.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "O2I_SCT_LIST 3"
.TH O2I_SCT_LIST 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
o2i_SCT_LIST, i2o_SCT_LIST, o2i_SCT, i2o_SCT \-
decode and encode Signed Certificate Timestamp lists in TLS wire format
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ct.h>
\&
\& STACK_OF(SCT) *o2i_SCT_LIST(STACK_OF(SCT) **a, const unsigned char **pp,
\&                             size_t len);
\& int i2o_SCT_LIST(const STACK_OF(SCT) *a, unsigned char **pp);
\& SCT *o2i_SCT(SCT **psct, const unsigned char **in, size_t len);
\& int i2o_SCT(const SCT *sct, unsigned char **out);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The SCT_LIST and SCT functions are very similar to the i2d and d2i family of
functions, except that they convert to and from TLS wire format, as described in
RFC 6962. See d2i_SCT_LIST for more information about how the parameters are
treated and the return values.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All of the functions have return values consistent with those stated for
d2i_SCT_LIST and i2d_SCT_LIST.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBct\fR\|(7),
\&\fBd2i_SCT_LIST\fR\|(3),
\&\fBi2d_SCT_LIST\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
