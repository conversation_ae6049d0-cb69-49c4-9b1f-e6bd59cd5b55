<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>RIPEMD160_Init</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTE">NOTE</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>RIPEMD160, RIPEMD160_Init, RIPEMD160_Update, RIPEMD160_Final - RIPEMD-160 hash function</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ripemd.h&gt;

unsigned char *RIPEMD160(const unsigned char *d, unsigned long n,
                         unsigned char *md);

int RIPEMD160_Init(RIPEMD160_CTX *c);
int RIPEMD160_Update(RIPEMD160_CTX *c, const void *data, unsigned long len);
int RIPEMD160_Final(unsigned char *md, RIPEMD160_CTX *c);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>RIPEMD-160 is a cryptographic hash function with a 160 bit output.</p>

<p>RIPEMD160() computes the RIPEMD-160 message digest of the <b>n</b> bytes at <b>d</b> and places it in <b>md</b> (which must have space for RIPEMD160_DIGEST_LENGTH == 20 bytes of output). If <b>md</b> is NULL, the digest is placed in a static array.</p>

<p>The following functions may be used if the message is not completely stored in memory:</p>

<p>RIPEMD160_Init() initializes a <b>RIPEMD160_CTX</b> structure.</p>

<p>RIPEMD160_Update() can be called repeatedly with chunks of the message to be hashed (<b>len</b> bytes at <b>data</b>).</p>

<p>RIPEMD160_Final() places the message digest in <b>md</b>, which must have space for RIPEMD160_DIGEST_LENGTH == 20 bytes of output, and erases the <b>RIPEMD160_CTX</b>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>RIPEMD160() returns a pointer to the hash value.</p>

<p>RIPEMD160_Init(), RIPEMD160_Update() and RIPEMD160_Final() return 1 for success, 0 otherwise.</p>

<h1 id="NOTE">NOTE</h1>

<p>Applications should use the higher level functions <a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a> etc. instead of calling these functions directly.</p>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>ISO/IEC 10118-3:2016 Dedicated Hash-Function 1 (RIPEMD-160).</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2019 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


