<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>config</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPENSSL-LIBRARY-CONFIGURATION">OPENSSL LIBRARY CONFIGURATION</a>
    <ul>
      <li><a href="#ASN1-Object-Configuration-Module">ASN1 Object Configuration Module</a></li>
      <li><a href="#Engine-Configuration-Module">Engine Configuration Module</a></li>
      <li><a href="#EVP-Configuration-Module">EVP Configuration Module</a></li>
      <li><a href="#SSL-Configuration-Module">SSL Configuration Module</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#ENVIRONMENT">ENVIRONMENT</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>config - OpenSSL CONF library configuration files</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The OpenSSL CONF library can be used to read configuration files. It is used for the OpenSSL master configuration file <b>openssl.cnf</b> and in a few other places like <b>SPKAC</b> files and certificate extension files for the <b>x509</b> utility. OpenSSL applications can also use the CONF library for their own purposes.</p>

<p>A configuration file is divided into a number of sections. Each section starts with a line <b>[ section_name ]</b> and ends when a new section is started or end of file is reached. A section name can consist of alphanumeric characters and underscores.</p>

<p>The first section of a configuration file is special and is referred to as the <b>default</b> section. This section is usually unnamed and spans from the start of file until the first named section. When a name is being looked up it is first looked up in a named section (if any) and then the default section.</p>

<p>The environment is mapped onto a section called <b>ENV</b>.</p>

<p>Comments can be included by preceding them with the <b>#</b> character</p>

<p>Other files can be included using the <b>.include</b> directive followed by a path. If the path points to a directory all files with names ending with <b>.cnf</b> or <b>.conf</b> are included from the directory. Recursive inclusion of directories from files in such directory is not supported. That means the files in the included directory can also contain <b>.include</b> directives but only inclusion of regular files is supported there. The inclusion of directories is not supported on systems without POSIX IO support.</p>

<p>It is strongly recommended to use absolute paths with the <b>.include</b> directive. Relative paths are evaluated based on the application current working directory so unless the configuration file containing the <b>.include</b> directive is application specific the inclusion will not work as expected.</p>

<p>There can be optional <b>=</b> character and whitespace characters between <b>.include</b> directive and the path which can be useful in cases the configuration file needs to be loaded by old OpenSSL versions which do not support the <b>.include</b> syntax. They would bail out with error if the <b>=</b> character is not present but with it they just ignore the include.</p>

<p>Each section in a configuration file consists of a number of name and value pairs of the form <b>name=value</b></p>

<p>The <b>name</b> string can contain any alphanumeric characters as well as a few punctuation symbols such as <b>.</b> <b>,</b> <b>;</b> and <b>_</b>.</p>

<p>The <b>value</b> string consists of the string following the <b>=</b> character until end of line with any leading and trailing white space removed.</p>

<p>The value string undergoes variable expansion. This can be done by including the form <b>$var</b> or <b>${var}</b>: this will substitute the value of the named variable in the current section. It is also possible to substitute a value from another section using the syntax <b>$section::name</b> or <b>${section::name}</b>. By using the form <b>$ENV::name</b> environment variables can be substituted. It is also possible to assign values to environment variables by using the name <b>ENV::name</b>, this will work if the program looks up environment variables using the <b>CONF</b> library instead of calling getenv() directly. The value string must not exceed 64k in length after variable expansion. Otherwise an error will occur.</p>

<p>It is possible to escape certain characters by using any kind of quote or the <b>\</b> character. By making the last character of a line a <b>\</b> a <b>value</b> string can be spread across multiple lines. In addition the sequences <b>\n</b>, <b>\r</b>, <b>\b</b> and <b>\t</b> are recognized.</p>

<p>All expansion and escape rules as described above that apply to <b>value</b> also apply to the path of the <b>.include</b> directive.</p>

<h1 id="OPENSSL-LIBRARY-CONFIGURATION">OPENSSL LIBRARY CONFIGURATION</h1>

<p>Applications can automatically configure certain aspects of OpenSSL using the master OpenSSL configuration file, or optionally an alternative configuration file. The <b>openssl</b> utility includes this functionality: any sub command uses the master OpenSSL configuration file unless an option is used in the sub command to use an alternative configuration file.</p>

<p>To enable library configuration the default section needs to contain an appropriate line which points to the main configuration section. The default name is <b>openssl_conf</b> which is used by the <b>openssl</b> utility. Other applications may use an alternative name such as <b>myapplication_conf</b>. All library configuration lines appear in the default section at the start of the configuration file.</p>

<p>The configuration section should consist of a set of name value pairs which contain specific module configuration information. The <b>name</b> represents the name of the <i>configuration module</i>. The meaning of the <b>value</b> is module specific: it may, for example, represent a further configuration section containing configuration module specific information. E.g.:</p>

<pre><code># This must be in the default section
openssl_conf = openssl_init

[openssl_init]

oid_section = new_oids
engines = engine_section

[new_oids]

... new oids here ...

[engine_section]

... engine stuff here ...</code></pre>

<p>The features of each configuration module are described below.</p>

<h2 id="ASN1-Object-Configuration-Module">ASN1 Object Configuration Module</h2>

<p>This module has the name <b>oid_section</b>. The value of this variable points to a section containing name value pairs of OIDs: the name is the OID short and long name, the value is the numerical form of the OID. Although some of the <b>openssl</b> utility sub commands already have their own ASN1 OBJECT section functionality not all do. By using the ASN1 OBJECT configuration module <b>all</b> the <b>openssl</b> utility sub commands can see the new objects as well as any compliant applications. For example:</p>

<pre><code>[new_oids]

some_new_oid = *******
some_other_oid = 1.2.3.5</code></pre>

<p>It is also possible to set the value to the long name followed by a comma and the numerical OID form. For example:</p>

<pre><code>shortName = some object long name, *******</code></pre>

<h2 id="Engine-Configuration-Module">Engine Configuration Module</h2>

<p>This ENGINE configuration module has the name <b>engines</b>. The value of this variable points to a section containing further ENGINE configuration information.</p>

<p>The section pointed to by <b>engines</b> is a table of engine names (though see <b>engine_id</b> below) and further sections containing configuration information specific to each ENGINE.</p>

<p>Each ENGINE specific section is used to set default algorithms, load dynamic, perform initialization and send ctrls. The actual operation performed depends on the <i>command</i> name which is the name of the name value pair. The currently supported commands are listed below.</p>

<p>For example:</p>

<pre><code>[engine_section]

# Configure ENGINE named &quot;foo&quot;
foo = foo_section
# Configure ENGINE named &quot;bar&quot;
bar = bar_section

[foo_section]
... foo ENGINE specific commands ...

[bar_section]
... &quot;bar&quot; ENGINE specific commands ...</code></pre>

<p>The command <b>engine_id</b> is used to give the ENGINE name. If used this command must be first. For example:</p>

<pre><code>[engine_section]
# This would normally handle an ENGINE named &quot;foo&quot;
foo = foo_section

[foo_section]
# Override default name and use &quot;myfoo&quot; instead.
engine_id = myfoo</code></pre>

<p>The command <b>dynamic_path</b> loads and adds an ENGINE from the given path. It is equivalent to sending the ctrls <b>SO_PATH</b> with the path argument followed by <b>LIST_ADD</b> with value 2 and <b>LOAD</b> to the dynamic ENGINE. If this is not the required behaviour then alternative ctrls can be sent directly to the dynamic ENGINE using ctrl commands.</p>

<p>The command <b>init</b> determines whether to initialize the ENGINE. If the value is <b>0</b> the ENGINE will not be initialized, if <b>1</b> and attempt it made to initialized the ENGINE immediately. If the <b>init</b> command is not present then an attempt will be made to initialize the ENGINE after all commands in its section have been processed.</p>

<p>The command <b>default_algorithms</b> sets the default algorithms an ENGINE will supply using the functions ENGINE_set_default_string().</p>

<p>If the name matches none of the above command names it is assumed to be a ctrl command which is sent to the ENGINE. The value of the command is the argument to the ctrl command. If the value is the string <b>EMPTY</b> then no value is sent to the command.</p>

<p>For example:</p>

<pre><code>[engine_section]

# Configure ENGINE named &quot;foo&quot;
foo = foo_section

[foo_section]
# Load engine from DSO
dynamic_path = /some/path/fooengine.so
# A foo specific ctrl.
some_ctrl = some_value
# Another ctrl that doesn&#39;t take a value.
other_ctrl = EMPTY
# Supply all default algorithms
default_algorithms = ALL</code></pre>

<h2 id="EVP-Configuration-Module">EVP Configuration Module</h2>

<p>This modules has the name <b>alg_section</b> which points to a section containing algorithm commands.</p>

<p>Currently the only algorithm command supported is <b>fips_mode</b> whose value can only be the boolean string <b>off</b>. If <b>fips_mode</b> is set to <b>on</b>, an error occurs as this library version is not FIPS capable.</p>

<h2 id="SSL-Configuration-Module">SSL Configuration Module</h2>

<p>This module has the name <b>ssl_conf</b> which points to a section containing SSL configurations.</p>

<p>Each line in the SSL configuration section contains the name of the configuration and the section containing it.</p>

<p>Each configuration section consists of command value pairs for <b>SSL_CONF</b>. Each pair will be passed to a <b>SSL_CTX</b> or <b>SSL</b> structure if it calls SSL_CTX_config() or SSL_config() with the appropriate configuration name.</p>

<p>Note: any characters before an initial dot in the configuration section are ignored so the same command can be used multiple times.</p>

<p>For example:</p>

<pre><code>ssl_conf = ssl_sect

[ssl_sect]

server = server_section

[server_section]

RSA.Certificate = server-rsa.pem
ECDSA.Certificate = server-ecdsa.pem
Ciphers = ALL:!RC4</code></pre>

<p>The system default configuration with name <b>system_default</b> if present will be applied during any creation of the <b>SSL_CTX</b> structure.</p>

<p>Example of a configuration with the system default:</p>

<pre><code>ssl_conf = ssl_sect

[ssl_sect]
system_default = system_default_sect

[system_default_sect]
MinProtocol = TLSv1.2
MinProtocol = DTLSv1.2</code></pre>

<h1 id="NOTES">NOTES</h1>

<p>If a configuration file attempts to expand a variable that doesn&#39;t exist then an error is flagged and the file will not load. This can happen if an attempt is made to expand an environment variable that doesn&#39;t exist. For example in a previous version of OpenSSL the default OpenSSL master configuration file used the value of <b>HOME</b> which may not be defined on non Unix systems and would cause an error.</p>

<p>This can be worked around by including a <b>default</b> section to provide a default value: then if the environment lookup fails the default value will be used instead. For this to work properly the default value must be defined earlier in the configuration file than the expansion. See the <b>EXAMPLES</b> section for an example of how to do this.</p>

<p>If the same variable exists in the same section then all but the last value will be silently ignored. In certain circumstances such as with DNs the same field may occur multiple times. This is usually worked around by ignoring any characters before an initial <b>.</b> e.g.</p>

<pre><code>1.OU=&quot;My first OU&quot;
2.OU=&quot;My Second OU&quot;</code></pre>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Here is a sample configuration file using some of the features mentioned above.</p>

<pre><code># This is the default section.

HOME=/temp
RANDFILE= ${ENV::HOME}/.rnd
configdir=$ENV::HOME/config

[ section_one ]

# We are now in section one.

# Quotes permit leading and trailing whitespace
any = &quot; any variable name &quot;

other = A string that can \
cover several lines \
by including \\ characters

message = Hello World\n

[ section_two ]

greeting = $section_one::message</code></pre>

<p>This next example shows how to expand environment variables safely.</p>

<p>Suppose you want a variable called <b>tmpfile</b> to refer to a temporary filename. The directory it is placed in can determined by the <b>TEMP</b> or <b>TMP</b> environment variables but they may not be set to any value at all. If you just include the environment variable names and the variable doesn&#39;t exist then this will cause an error when an attempt is made to load the configuration file. By making use of the default section both values can be looked up with <b>TEMP</b> taking priority and <b>/tmp</b> used if neither is defined:</p>

<pre><code>TMP=/tmp
# The above value is used if TMP isn&#39;t in the environment
TEMP=$ENV::TMP
# The above value is used if TEMP isn&#39;t in the environment
tmpfile=${ENV::TEMP}/tmp.filename</code></pre>

<p>Simple OpenSSL library configuration example to enter FIPS mode:</p>

<pre><code># Default appname: should match &quot;appname&quot; parameter (if any)
# supplied to CONF_modules_load_file et al.
openssl_conf = openssl_conf_section

[openssl_conf_section]
# Configuration module list
alg_section = evp_sect

[evp_sect]
# Set to &quot;yes&quot; to enter FIPS mode if supported
fips_mode = yes</code></pre>

<p>Note: in the above example you will get an error in non FIPS capable versions of OpenSSL.</p>

<p>Simple OpenSSL library configuration to make TLS 1.2 and DTLS 1.2 the system-default minimum TLS and DTLS versions, respectively:</p>

<pre><code># Toplevel section for openssl (including libssl)
openssl_conf = default_conf_section

[default_conf_section]
# We only specify configuration for the &quot;ssl module&quot;
ssl_conf = ssl_section

[ssl_section]
system_default = system_default_section

[system_default_section]
MinProtocol = TLSv1.2
MinProtocol = DTLSv1.2</code></pre>

<p>The minimum TLS protocol is applied to <b>SSL_CTX</b> objects that are TLS-based, and the minimum DTLS protocol to those are DTLS-based. The same applies also to maximum versions set with <b>MaxProtocol</b>.</p>

<p>More complex OpenSSL library configuration. Add OID and don&#39;t enter FIPS mode:</p>

<pre><code># Default appname: should match &quot;appname&quot; parameter (if any)
# supplied to CONF_modules_load_file et al.
openssl_conf = openssl_conf_section

[openssl_conf_section]
# Configuration module list
alg_section = evp_sect
oid_section = new_oids

[evp_sect]
# This will have no effect as FIPS mode is off by default.
# Set to &quot;yes&quot; to enter FIPS mode, if supported
fips_mode = no

[new_oids]
# New OID, just short name
newoid1 = *******.1
# New OID shortname and long name
newoid2 = New OID 2 long name, *******.2</code></pre>

<p>The above examples can be used with any application supporting library configuration if &quot;openssl_conf&quot; is modified to match the appropriate &quot;appname&quot;.</p>

<p>For example if the second sample file above is saved to &quot;example.cnf&quot; then the command line:</p>

<pre><code>OPENSSL_CONF=example.cnf openssl asn1parse -genstr OID:*******.1</code></pre>

<p>will output:</p>

<pre><code>0:d=0  hl=2 l=   4 prim: OBJECT            :newoid1</code></pre>

<p>showing that the OID &quot;newoid1&quot; has been added as &quot;*******.1&quot;.</p>

<h1 id="ENVIRONMENT">ENVIRONMENT</h1>

<dl>

<dt id="OPENSSL_CONF"><b>OPENSSL_CONF</b></dt>
<dd>

<p>The path to the config file. Ignored in set-user-ID and set-group-ID programs.</p>

</dd>
<dt id="OPENSSL_ENGINES"><b>OPENSSL_ENGINES</b></dt>
<dd>

<p>The path to the engines directory. Ignored in set-user-ID and set-group-ID programs.</p>

</dd>
</dl>

<h1 id="BUGS">BUGS</h1>

<p>Currently there is no way to include characters using the octal <b>\nnn</b> form. Strings are all null terminated so nulls cannot form part of the value.</p>

<p>The escaping isn&#39;t quite right: if you want to use sequences like <b>\n</b> you can&#39;t use any quote escaping on the same line.</p>

<p>Files are loaded in a single pass. This means that a variable expansion will only work if the variables referenced are defined earlier in the file.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/x509.html">x509(1)</a>, <a href="../man1/req.html">req(1)</a>, <a href="../man1/ca.html">ca(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


