.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_CLEANUP 3"
.TH RAND_CLEANUP 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_cleanup \- erase the PRNG state
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand.h>
\&
\& #if OPENSSL_API_COMPAT < 0x10100000L
\& void RAND_cleanup(void)
\& #endif
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Prior to OpenSSL 1.1.0, \fBRAND_cleanup()\fR released all resources used by
the PRNG.  As of version 1.1.0, it does nothing and should not be called,
since no explicit initialisation or de-initialisation is necessary. See
\&\fBOPENSSL_init_crypto\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_cleanup()\fR returns no value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBRAND_cleanup()\fR was deprecated in OpenSSL 1.1.0; do not use it.
See \fBOPENSSL_init_crypto\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
