<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>genrsa</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-genrsa, genrsa - generate an RSA private key</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>genrsa</b> [<b>-help</b>] [<b>-out filename</b>] [<b>-passout arg</b>] [<b>-aes128</b>] [<b>-aes192</b>] [<b>-aes256</b>] [<b>-aria128</b>] [<b>-aria192</b>] [<b>-aria256</b>] [<b>-camellia128</b>] [<b>-camellia192</b>] [<b>-camellia256</b>] [<b>-des</b>] [<b>-des3</b>] [<b>-idea</b>] [<b>-f4</b>] [<b>-3</b>] [<b>-rand file...</b>] [<b>-writerand file</b>] [<b>-engine id</b>] [<b>-primes num</b>] [<b>numbits</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>genrsa</b> command generates an RSA private key.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="out-filename"><b>-out filename</b></dt>
<dd>

<p>Output the key to the specified file. If this argument is not specified then standard output is used.</p>

</dd>
<dt id="passout-arg"><b>-passout arg</b></dt>
<dd>

<p>The output file password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="aes128--aes192--aes256--aria128--aria192--aria256--camellia128--camellia192--camellia256--des--des3--idea"><b>-aes128</b>, <b>-aes192</b>, <b>-aes256</b>, <b>-aria128</b>, <b>-aria192</b>, <b>-aria256</b>, <b>-camellia128</b>, <b>-camellia192</b>, <b>-camellia256</b>, <b>-des</b>, <b>-des3</b>, <b>-idea</b></dt>
<dd>

<p>These options encrypt the private key with specified cipher before outputting it. If none of these options is specified no encryption is used. If encryption is used a pass phrase is prompted for if it is not supplied via the <b>-passout</b> argument.</p>

</dd>
<dt id="F4--3"><b>-F4|-3</b></dt>
<dd>

<p>The public exponent to use, either 65537 or 3. The default is 65537.</p>

</dd>
<dt id="rand-file"><b>-rand file...</b></dt>
<dd>

<p>A file or files containing random data used to seed the random number generator. Multiple files can be specified separated by an OS-dependent character. The separator is <b>;</b> for MS-Windows, <b>,</b> for OpenVMS, and <b>:</b> for all others.</p>

</dd>
<dt id="writerand-file">[<b>-writerand file</b>]</dt>
<dd>

<p>Writes random data to the specified <i>file</i> upon exit. This can be used with a subsequent <b>-rand</b> flag.</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Specifying an engine (by its unique <b>id</b> string) will cause <b>genrsa</b> to attempt to obtain a functional reference to the specified engine, thus initialising it if needed. The engine will then be set as the default for all available algorithms.</p>

</dd>
<dt id="primes-num"><b>-primes num</b></dt>
<dd>

<p>Specify the number of primes to use while generating the RSA key. The <b>num</b> parameter must be a positive integer that is greater than 1 and less than 16. If <b>num</b> is greater than 2, then the generated key is called a &#39;multi-prime&#39; RSA key, which is defined in RFC 8017.</p>

</dd>
<dt id="numbits"><b>numbits</b></dt>
<dd>

<p>The size of the private key to generate in bits. This must be the last option specified. The default is 2048 and values less than 512 are not allowed.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>RSA private key generation essentially involves the generation of two or more prime numbers. When generating a private key various symbols will be output to indicate the progress of the generation. A <b>.</b> represents each number which has passed an initial sieve test, <b>+</b> means a number has passed a single round of the Miller-Rabin primality test, <b>*</b> means the current prime starts a regenerating progress due to some failed tests. A newline means that the number has passed all the prime tests (the actual number depends on the key size).</p>

<p>Because key generation is a random process the time taken to generate a key may vary somewhat. But in general, more primes lead to less generation time of a key.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/gendsa.html">gendsa(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


