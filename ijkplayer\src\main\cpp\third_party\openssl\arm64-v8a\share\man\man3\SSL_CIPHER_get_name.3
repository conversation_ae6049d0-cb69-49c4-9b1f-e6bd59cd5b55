.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CIPHER_GET_NAME 3"
.TH SSL_CIPHER_GET_NAME 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CIPHER_get_name,
SSL_CIPHER_standard_name,
OPENSSL_cipher_name,
SSL_CIPHER_get_bits,
SSL_CIPHER_get_version,
SSL_CIPHER_description,
SSL_CIPHER_get_cipher_nid,
SSL_CIPHER_get_digest_nid,
SSL_CIPHER_get_handshake_digest,
SSL_CIPHER_get_kx_nid,
SSL_CIPHER_get_auth_nid,
SSL_CIPHER_is_aead,
SSL_CIPHER_find,
SSL_CIPHER_get_id,
SSL_CIPHER_get_protocol_id
\&\- get SSL_CIPHER properties
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& const char *SSL_CIPHER_get_name(const SSL_CIPHER *cipher);
\& const char *SSL_CIPHER_standard_name(const SSL_CIPHER *cipher);
\& const char *OPENSSL_cipher_name(const char *stdname);
\& int SSL_CIPHER_get_bits(const SSL_CIPHER *cipher, int *alg_bits);
\& char *SSL_CIPHER_get_version(const SSL_CIPHER *cipher);
\& char *SSL_CIPHER_description(const SSL_CIPHER *cipher, char *buf, int size);
\& int SSL_CIPHER_get_cipher_nid(const SSL_CIPHER *c);
\& int SSL_CIPHER_get_digest_nid(const SSL_CIPHER *c);
\& const EVP_MD *SSL_CIPHER_get_handshake_digest(const SSL_CIPHER *c);
\& int SSL_CIPHER_get_kx_nid(const SSL_CIPHER *c);
\& int SSL_CIPHER_get_auth_nid(const SSL_CIPHER *c);
\& int SSL_CIPHER_is_aead(const SSL_CIPHER *c);
\& const SSL_CIPHER *SSL_CIPHER_find(SSL *ssl, const unsigned char *ptr);
\& uint32_t SSL_CIPHER_get_id(const SSL_CIPHER *c);
\& uint32_t SSL_CIPHER_get_protocol_id(const SSL_CIPHER *c);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CIPHER_get_name()\fR returns a pointer to the name of \fBcipher\fR. If the
\&\fBcipher\fR is NULL, it returns "(NONE)".
.PP
\&\fBSSL_CIPHER_standard_name()\fR returns a pointer to the standard RFC name of
\&\fBcipher\fR. If the \fBcipher\fR is NULL, it returns "(NONE)". If the \fBcipher\fR
has no standard name, it returns \fBNULL\fR. If \fBcipher\fR was defined in both
SSLv3 and TLS, it returns the TLS name.
.PP
\&\fBOPENSSL_cipher_name()\fR returns a pointer to the OpenSSL name of \fBstdname\fR.
If the \fBstdname\fR is NULL, or \fBstdname\fR has no corresponding OpenSSL name,
it returns "(NONE)". Where both exist, \fBstdname\fR should be the TLS name rather
than the SSLv3 name.
.PP
\&\fBSSL_CIPHER_get_bits()\fR returns the number of secret bits used for \fBcipher\fR.
If \fBcipher\fR is NULL, 0 is returned.
.PP
\&\fBSSL_CIPHER_get_version()\fR returns string which indicates the SSL/TLS protocol
version that first defined the cipher.  It returns "(NONE)" if \fBcipher\fR is NULL.
.PP
\&\fBSSL_CIPHER_get_cipher_nid()\fR returns the cipher NID corresponding to \fBc\fR.
If there is no cipher (e.g. for cipher suites with no encryption) then
\&\fBNID_undef\fR is returned.
.PP
\&\fBSSL_CIPHER_get_digest_nid()\fR returns the digest NID corresponding to the MAC
used by \fBc\fR during record encryption/decryption. If there is no digest (e.g.
for AEAD cipher suites) then \fBNID_undef\fR is returned.
.PP
\&\fBSSL_CIPHER_get_handshake_digest()\fR returns an EVP_MD for the digest used during
the SSL/TLS handshake when using the SSL_CIPHER \fBc\fR. Note that this may be
different to the digest used to calculate the MAC for encrypted records.
.PP
\&\fBSSL_CIPHER_get_kx_nid()\fR returns the key exchange NID corresponding to the method
used by \fBc\fR. If there is no key exchange, then \fBNID_undef\fR is returned.
If any appropriate key exchange algorithm can be used (as in the case of TLS 1.3
cipher suites) \fBNID_kx_any\fR is returned. Examples (not comprehensive):
.PP
.Vb 4
\& NID_kx_rsa
\& NID_kx_ecdhe
\& NID_kx_dhe
\& NID_kx_psk
.Ve
.PP
\&\fBSSL_CIPHER_get_auth_nid()\fR returns the authentication NID corresponding to the method
used by \fBc\fR. If there is no authentication, then \fBNID_undef\fR is returned.
If any appropriate authentication algorithm can be used (as in the case of
TLS 1.3 cipher suites) \fBNID_auth_any\fR is returned. Examples (not comprehensive):
.PP
.Vb 3
\& NID_auth_rsa
\& NID_auth_ecdsa
\& NID_auth_psk
.Ve
.PP
\&\fBSSL_CIPHER_is_aead()\fR returns 1 if the cipher \fBc\fR is AEAD (e.g. GCM or
ChaCha20/Poly1305), and 0 if it is not AEAD.
.PP
\&\fBSSL_CIPHER_find()\fR returns a \fBSSL_CIPHER\fR structure which has the cipher ID stored
in \fBptr\fR. The \fBptr\fR parameter is a two element array of \fBchar\fR, which stores the
two-byte TLS cipher ID (as allocated by IANA) in network byte order. This parameter
is usually retrieved from a TLS packet by using functions like
\&\fBSSL_client_hello_get0_ciphers\fR\|(3).  \fBSSL_CIPHER_find()\fR returns NULL if an
error occurs or the indicated cipher is not found.
.PP
\&\fBSSL_CIPHER_get_id()\fR returns the OpenSSL-specific ID of the given cipher \fBc\fR. That ID is
not the same as the IANA-specific ID.
.PP
\&\fBSSL_CIPHER_get_protocol_id()\fR returns the two-byte ID used in the TLS protocol of the given
cipher \fBc\fR.
.PP
\&\fBSSL_CIPHER_description()\fR returns a textual description of the cipher used
into the buffer \fBbuf\fR of length \fBlen\fR provided.  If \fBbuf\fR is provided, it
must be at least 128 bytes, otherwise a buffer will be allocated using
\&\fBOPENSSL_malloc()\fR.  If the provided buffer is too small, or the allocation fails,
\&\fBNULL\fR is returned.
.PP
The string returned by \fBSSL_CIPHER_description()\fR consists of several fields
separated by whitespace:
.IP <ciphername> 4
.IX Item "<ciphername>"
Textual representation of the cipher name.
.IP "<protocol version>" 4
.IX Item "<protocol version>"
The minimum protocol version that the ciphersuite supports, such as \fBTLSv1.2\fR.
Note that this is not always the same as the protocol version in which the
ciphersuite was first defined because some ciphersuites are backwards compatible
with earlier protocol versions.
.IP "Kx=<key exchange>" 4
.IX Item "Kx=<key exchange>"
Key exchange method such as \fBRSA\fR, \fBECDHE\fR, etc.
.IP Au=<authentication> 4
.IX Item "Au=<authentication>"
Authentication method such as \fBRSA\fR, \fBNone\fR, etc.. None is the
representation of anonymous ciphers.
.IP "Enc=<symmetric encryption method>" 4
.IX Item "Enc=<symmetric encryption method>"
Encryption method, with number of secret bits, such as \fBAESGCM(128)\fR.
.IP "Mac=<message authentication code>" 4
.IX Item "Mac=<message authentication code>"
Message digest, such as \fBSHA256\fR.
.PP
Some examples for the output of \fBSSL_CIPHER_description()\fR:
.PP
.Vb 2
\& ECDHE\-RSA\-AES256\-GCM\-SHA256 TLSv1.2 Kx=ECDH     Au=RSA  Enc=AESGCM(256) Mac=AEAD
\& RSA\-PSK\-AES256\-CBC\-SHA384 TLSv1.0 Kx=RSAPSK   Au=RSA  Enc=AES(256)  Mac=SHA384
.Ve
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CIPHER_get_name()\fR, \fBSSL_CIPHER_standard_name()\fR, \fBOPENSSL_cipher_name()\fR,
\&\fBSSL_CIPHER_get_version()\fR and \fBSSL_CIPHER_description()\fR return the corresponding
value in a null-terminated string for a specific cipher or "(NONE)"
if the cipher is not found.
.PP
\&\fBSSL_CIPHER_get_bits()\fR returns a positive integer representing the number of
secret bits or 0 if an error occurred.
.PP
\&\fBSSL_CIPHER_get_cipher_nid()\fR, \fBSSL_CIPHER_get_digest_nid()\fR,
\&\fBSSL_CIPHER_get_kx_nid()\fR and \fBSSL_CIPHER_get_auth_nid()\fR return the NID value or
\&\fBNID_undef\fR if an error occurred.
.PP
\&\fBSSL_CIPHER_get_handshake_digest()\fR returns a valid \fBEVP_MD\fR structure or NULL
if an error occurred.
.PP
\&\fBSSL_CIPHER_is_aead()\fR returns 1 if the cipher is AEAD or 0 otherwise.
.PP
\&\fBSSL_CIPHER_find()\fR returns a valid \fBSSL_CIPHER\fR structure or NULL if an error
occurred.
.PP
\&\fBSSL_CIPHER_get_id()\fR returns a 4\-byte integer representing the OpenSSL-specific ID.
.PP
\&\fBSSL_CIPHER_get_protocol_id()\fR returns a 2\-byte integer representing the TLS
protocol-specific ID.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_get_current_cipher\fR\|(3),
\&\fBSSL_get_ciphers\fR\|(3), \fBciphers\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_CIPHER_get_version()\fR function was updated to always return the
correct protocol string in OpenSSL 1.1.0.
.PP
The \fBSSL_CIPHER_description()\fR function was changed to return \fBNULL\fR on error,
rather than a fixed string, in OpenSSL 1.1.0.
.PP
The \fBSSL_CIPHER_get_handshake_digest()\fR function was added in OpenSSL 1.1.1.
.PP
The \fBSSL_CIPHER_standard_name()\fR function was globally available in OpenSSL 1.1.1.
 Before OpenSSL 1.1.1, tracing (\fBenable-ssl-trace\fR argument to Configure) was
required to enable this function.
.PP
The \fBOPENSSL_cipher_name()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
