prefix=/home/<USER>/work/ijkaction/ijkaction/tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/x86_64
exec_prefix=${prefix}
libdir=/home/<USER>/work/ijkaction/ijkaction/tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/x86_64/lib
includedir=/home/<USER>/work/ijkaction/ijkaction/tpc_c_cplusplus/lycium/usr/FFmpeg-ff4.0/x86_64/include

Name: libavfilter
Description: FFmpeg audio/video filtering library
Version: 7.16.100
Requires: libswscale >= 5.1.100, libavformat >= 58.12.100, libavcodec >= 58.18.100, libswresample >= 3.1.100, libavutil >= 56.14.100
Requires.private: 
Conflicts:
Libs: -L${libdir}  -lavfilter -pthread -lm
Libs.private: 
Cflags: -I${includedir}
