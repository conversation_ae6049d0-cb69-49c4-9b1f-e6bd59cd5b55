<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>RAND_DRBG_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>RAND_DRBG_new, RAND_DRBG_secure_new, RAND_DRBG_set, RAND_DRBG_set_defaults, RAND_DRBG_instantiate, RAND_DRBG_uninstantiate, RAND_DRBG_free - initialize and cleanup a RAND_DRBG instance</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/rand_drbg.h&gt;


RAND_DRBG *RAND_DRBG_new(int type,
                         unsigned int flags,
                         RAND_DRBG *parent);

RAND_DRBG *RAND_DRBG_secure_new(int type,
                                unsigned int flags,
                                RAND_DRBG *parent);

int RAND_DRBG_set(RAND_DRBG *drbg,
                  int type, unsigned int flags);

int RAND_DRBG_set_defaults(int type, unsigned int flags);

int RAND_DRBG_instantiate(RAND_DRBG *drbg,
                          const unsigned char *pers, size_t perslen);

int RAND_DRBG_uninstantiate(RAND_DRBG *drbg);

void RAND_DRBG_free(RAND_DRBG *drbg);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>RAND_DRBG_new() and RAND_DRBG_secure_new() create a new DRBG instance of the given <b>type</b>, allocated from the heap resp. the secure heap (using OPENSSL_zalloc() resp. OPENSSL_secure_zalloc()).</p>

<p>RAND_DRBG_set() initializes the <b>drbg</b> with the given <b>type</b> and <b>flags</b>.</p>

<p>RAND_DRBG_set_defaults() sets the default <b>type</b> and <b>flags</b> for new DRBG instances.</p>

<p>Currently, all DRBG types are based on AES-CTR, so <b>type</b> can be one of the following values: NID_aes_128_ctr, NID_aes_192_ctr, NID_aes_256_ctr. Before the DRBG can be used to generate random bits, it is necessary to set its type and to instantiate it.</p>

<p>The optional <b>flags</b> argument specifies a set of bit flags which can be joined using the | operator. Currently, the only flag is RAND_DRBG_FLAG_CTR_NO_DF, which disables the use of the derivation function ctr_df. For an explanation, see [NIST SP 800-90A Rev. 1].</p>

<p>If a <b>parent</b> instance is specified then this will be used instead of the default entropy source for reseeding the <b>drbg</b>. It is said that the <b>drbg</b> is <i>chained</i> to its <b>parent</b>. For more information, see the NOTES section.</p>

<p>RAND_DRBG_instantiate() seeds the <b>drbg</b> instance using random input from trusted entropy sources. Optionally, a personalization string <b>pers</b> of length <b>perslen</b> can be specified. To omit the personalization string, set <b>pers</b>=NULL and <b>perslen</b>=0;</p>

<p>RAND_DRBG_uninstantiate() clears the internal state of the <b>drbg</b> and puts it back in the uninstantiated state.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>RAND_DRBG_new() and RAND_DRBG_secure_new() return a pointer to a DRBG instance allocated on the heap, resp. secure heap.</p>

<p>RAND_DRBG_set(), RAND_DRBG_instantiate(), and RAND_DRBG_uninstantiate() return 1 on success, and 0 on failure.</p>

<p>RAND_DRBG_free() does not return a value.</p>

<h1 id="NOTES">NOTES</h1>

<p>The DRBG design supports <i>chaining</i>, which means that a DRBG instance can use another <b>parent</b> DRBG instance instead of the default entropy source to obtain fresh random input for reseeding, provided that <b>parent</b> DRBG instance was properly instantiated, either from a trusted entropy source, or from yet another parent DRBG instance. For a detailed description of the reseeding process, see <a href="../man7/RAND_DRBG.html">RAND_DRBG(7)</a>.</p>

<p>The default DRBG type and flags are applied only during creation of a DRBG instance. To ensure that they are applied to the global and thread-local DRBG instances (&lt;master&gt;, resp. &lt;public&gt; and &lt;private&gt;), it is necessary to call RAND_DRBG_set_defaults() before creating any thread and before calling any cryptographic routines that obtain random data directly or indirectly.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/OPENSSL_zalloc.html">OPENSSL_zalloc(3)</a>, <a href="../man3/OPENSSL_secure_zalloc.html">OPENSSL_secure_zalloc(3)</a>, <a href="../man3/RAND_DRBG_generate.html">RAND_DRBG_generate(3)</a>, <a href="../man7/RAND_DRBG.html">RAND_DRBG(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The RAND_DRBG functions were added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


