<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SMIME_write_PKCS7</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SMIME_write_PKCS7 - convert PKCS#7 structure to S/MIME format</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/pkcs7.h&gt;

int SMIME_write_PKCS7(BIO *out, PKCS7 *p7, BIO *data, int flags);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SMIME_write_PKCS7() adds the appropriate MIME headers to a PKCS#7 structure to produce an S/MIME message.</p>

<p><b>out</b> is the BIO to write the data to. <b>p7</b> is the appropriate <b>PKCS7</b> structure. If streaming is enabled then the content must be supplied in the <b>data</b> argument. <b>flags</b> is an optional set of flags.</p>

<h1 id="NOTES">NOTES</h1>

<p>The following flags can be passed in the <b>flags</b> parameter.</p>

<p>If <b>PKCS7_DETACHED</b> is set then cleartext signing will be used, this option only makes sense for signedData where <b>PKCS7_DETACHED</b> is also set when PKCS7_sign() is also called.</p>

<p>If the <b>PKCS7_TEXT</b> flag is set MIME headers for type <b>text/plain</b> are added to the content, this only makes sense if <b>PKCS7_DETACHED</b> is also set.</p>

<p>If the <b>PKCS7_STREAM</b> flag is set streaming is performed. This flag should only be set if <b>PKCS7_STREAM</b> was also set in the previous call to PKCS7_sign() or PKCS7_encrypt().</p>

<p>If cleartext signing is being used and <b>PKCS7_STREAM</b> not set then the data must be read twice: once to compute the signature in PKCS7_sign() and once to output the S/MIME message.</p>

<p>If streaming is performed the content is output in BER format using indefinite length constructed encoding except in the case of signed data with detached content where the content is absent and DER format is used.</p>

<h1 id="BUGS">BUGS</h1>

<p>SMIME_write_PKCS7() always base64 encodes PKCS#7 structures, there should be an option to disable this.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SMIME_write_PKCS7() returns 1 for success or 0 for failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/PKCS7_sign.html">PKCS7_sign(3)</a>, <a href="../man3/PKCS7_verify.html">PKCS7_verify(3)</a>, <a href="../man3/PKCS7_encrypt.html">PKCS7_encrypt(3)</a> <a href="../man3/PKCS7_decrypt.html">PKCS7_decrypt(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2002-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


