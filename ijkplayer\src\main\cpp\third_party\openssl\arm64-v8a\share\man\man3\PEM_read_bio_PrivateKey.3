.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PEM_READ_BIO_PRIVATEKEY 3"
.TH PEM_READ_BIO_PRIVATEKEY 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
pem_password_cb,
PEM_read_bio_PrivateKey, PEM_read_PrivateKey, PEM_write_bio_PrivateKey,
PEM_write_bio_PrivateKey_traditional, PEM_write_PrivateKey,
PEM_write_bio_PKCS8PrivateKey, PEM_write_PKCS8PrivateKey,
PEM_write_bio_PKCS8PrivateKey_nid, PEM_write_PKCS8PrivateKey_nid,
PEM_read_bio_PUBKEY, PEM_read_PUBKEY, PEM_write_bio_PUBKEY, PEM_write_PUBKEY,
PEM_read_bio_RSAPrivateKey, PEM_read_RSAPrivateKey,
PEM_write_bio_RSAPrivateKey, PEM_write_RSAPrivateKey,
PEM_read_bio_RSAPublicKey, PEM_read_RSAPublicKey, PEM_write_bio_RSAPublicKey,
PEM_write_RSAPublicKey, PEM_read_bio_RSA_PUBKEY, PEM_read_RSA_PUBKEY,
PEM_write_bio_RSA_PUBKEY, PEM_write_RSA_PUBKEY, PEM_read_bio_DSAPrivateKey,
PEM_read_DSAPrivateKey, PEM_write_bio_DSAPrivateKey, PEM_write_DSAPrivateKey,
PEM_read_bio_DSA_PUBKEY, PEM_read_DSA_PUBKEY, PEM_write_bio_DSA_PUBKEY,
PEM_write_DSA_PUBKEY, PEM_read_bio_Parameters, PEM_write_bio_Parameters,
PEM_read_bio_DSAparams, PEM_read_DSAparams,
PEM_write_bio_DSAparams, PEM_write_DSAparams, PEM_read_bio_DHparams,
PEM_read_DHparams, PEM_write_bio_DHparams, PEM_write_DHparams,
PEM_read_bio_X509, PEM_read_X509, PEM_write_bio_X509, PEM_write_X509,
PEM_read_bio_X509_AUX, PEM_read_X509_AUX, PEM_write_bio_X509_AUX,
PEM_write_X509_AUX, PEM_read_bio_X509_REQ, PEM_read_X509_REQ,
PEM_write_bio_X509_REQ, PEM_write_X509_REQ, PEM_write_bio_X509_REQ_NEW,
PEM_write_X509_REQ_NEW, PEM_read_bio_X509_CRL, PEM_read_X509_CRL,
PEM_write_bio_X509_CRL, PEM_write_X509_CRL, PEM_read_bio_PKCS7, PEM_read_PKCS7,
PEM_write_bio_PKCS7, PEM_write_PKCS7 \- PEM routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pem.h>
\&
\& typedef int pem_password_cb(char *buf, int size, int rwflag, void *u);
\&
\& EVP_PKEY *PEM_read_bio_PrivateKey(BIO *bp, EVP_PKEY **x,
\&                                   pem_password_cb *cb, void *u);
\& EVP_PKEY *PEM_read_PrivateKey(FILE *fp, EVP_PKEY **x,
\&                               pem_password_cb *cb, void *u);
\& int PEM_write_bio_PrivateKey(BIO *bp, EVP_PKEY *x, const EVP_CIPHER *enc,
\&                              unsigned char *kstr, int klen,
\&                              pem_password_cb *cb, void *u);
\& int PEM_write_bio_PrivateKey_traditional(BIO *bp, EVP_PKEY *x,
\&                                          const EVP_CIPHER *enc,
\&                                          unsigned char *kstr, int klen,
\&                                          pem_password_cb *cb, void *u);
\& int PEM_write_PrivateKey(FILE *fp, EVP_PKEY *x, const EVP_CIPHER *enc,
\&                          unsigned char *kstr, int klen,
\&                          pem_password_cb *cb, void *u);
\&
\& int PEM_write_bio_PKCS8PrivateKey(BIO *bp, EVP_PKEY *x, const EVP_CIPHER *enc,
\&                                   char *kstr, int klen,
\&                                   pem_password_cb *cb, void *u);
\& int PEM_write_PKCS8PrivateKey(FILE *fp, EVP_PKEY *x, const EVP_CIPHER *enc,
\&                               char *kstr, int klen,
\&                               pem_password_cb *cb, void *u);
\& int PEM_write_bio_PKCS8PrivateKey_nid(BIO *bp, EVP_PKEY *x, int nid,
\&                                       char *kstr, int klen,
\&                                       pem_password_cb *cb, void *u);
\& int PEM_write_PKCS8PrivateKey_nid(FILE *fp, EVP_PKEY *x, int nid,
\&                                   char *kstr, int klen,
\&                                   pem_password_cb *cb, void *u);
\&
\& EVP_PKEY *PEM_read_bio_PUBKEY(BIO *bp, EVP_PKEY **x,
\&                               pem_password_cb *cb, void *u);
\& EVP_PKEY *PEM_read_PUBKEY(FILE *fp, EVP_PKEY **x,
\&                           pem_password_cb *cb, void *u);
\& int PEM_write_bio_PUBKEY(BIO *bp, EVP_PKEY *x);
\& int PEM_write_PUBKEY(FILE *fp, EVP_PKEY *x);
\&
\& RSA *PEM_read_bio_RSAPrivateKey(BIO *bp, RSA **x,
\&                                 pem_password_cb *cb, void *u);
\& RSA *PEM_read_RSAPrivateKey(FILE *fp, RSA **x,
\&                             pem_password_cb *cb, void *u);
\& int PEM_write_bio_RSAPrivateKey(BIO *bp, RSA *x, const EVP_CIPHER *enc,
\&                                 unsigned char *kstr, int klen,
\&                                 pem_password_cb *cb, void *u);
\& int PEM_write_RSAPrivateKey(FILE *fp, RSA *x, const EVP_CIPHER *enc,
\&                             unsigned char *kstr, int klen,
\&                             pem_password_cb *cb, void *u);
\&
\& RSA *PEM_read_bio_RSAPublicKey(BIO *bp, RSA **x,
\&                                pem_password_cb *cb, void *u);
\& RSA *PEM_read_RSAPublicKey(FILE *fp, RSA **x,
\&                            pem_password_cb *cb, void *u);
\& int PEM_write_bio_RSAPublicKey(BIO *bp, RSA *x);
\& int PEM_write_RSAPublicKey(FILE *fp, RSA *x);
\&
\& RSA *PEM_read_bio_RSA_PUBKEY(BIO *bp, RSA **x,
\&                              pem_password_cb *cb, void *u);
\& RSA *PEM_read_RSA_PUBKEY(FILE *fp, RSA **x,
\&                          pem_password_cb *cb, void *u);
\& int PEM_write_bio_RSA_PUBKEY(BIO *bp, RSA *x);
\& int PEM_write_RSA_PUBKEY(FILE *fp, RSA *x);
\&
\& DSA *PEM_read_bio_DSAPrivateKey(BIO *bp, DSA **x,
\&                                 pem_password_cb *cb, void *u);
\& DSA *PEM_read_DSAPrivateKey(FILE *fp, DSA **x,
\&                             pem_password_cb *cb, void *u);
\& int PEM_write_bio_DSAPrivateKey(BIO *bp, DSA *x, const EVP_CIPHER *enc,
\&                                 unsigned char *kstr, int klen,
\&                                 pem_password_cb *cb, void *u);
\& int PEM_write_DSAPrivateKey(FILE *fp, DSA *x, const EVP_CIPHER *enc,
\&                             unsigned char *kstr, int klen,
\&                             pem_password_cb *cb, void *u);
\&
\& DSA *PEM_read_bio_DSA_PUBKEY(BIO *bp, DSA **x,
\&                              pem_password_cb *cb, void *u);
\& DSA *PEM_read_DSA_PUBKEY(FILE *fp, DSA **x,
\&                          pem_password_cb *cb, void *u);
\& int PEM_write_bio_DSA_PUBKEY(BIO *bp, DSA *x);
\& int PEM_write_DSA_PUBKEY(FILE *fp, DSA *x);
\&
\& EVP_PKEY *PEM_read_bio_Parameters(BIO *bp, EVP_PKEY **x);
\& int PEM_write_bio_Parameters(BIO *bp, const EVP_PKEY *x);
\&
\& DSA *PEM_read_bio_DSAparams(BIO *bp, DSA **x, pem_password_cb *cb, void *u);
\& DSA *PEM_read_DSAparams(FILE *fp, DSA **x, pem_password_cb *cb, void *u);
\& int PEM_write_bio_DSAparams(BIO *bp, DSA *x);
\& int PEM_write_DSAparams(FILE *fp, DSA *x);
\&
\& DH *PEM_read_bio_DHparams(BIO *bp, DH **x, pem_password_cb *cb, void *u);
\& DH *PEM_read_DHparams(FILE *fp, DH **x, pem_password_cb *cb, void *u);
\& int PEM_write_bio_DHparams(BIO *bp, DH *x);
\& int PEM_write_DHparams(FILE *fp, DH *x);
\&
\& X509 *PEM_read_bio_X509(BIO *bp, X509 **x, pem_password_cb *cb, void *u);
\& X509 *PEM_read_X509(FILE *fp, X509 **x, pem_password_cb *cb, void *u);
\& int PEM_write_bio_X509(BIO *bp, X509 *x);
\& int PEM_write_X509(FILE *fp, X509 *x);
\&
\& X509 *PEM_read_bio_X509_AUX(BIO *bp, X509 **x, pem_password_cb *cb, void *u);
\& X509 *PEM_read_X509_AUX(FILE *fp, X509 **x, pem_password_cb *cb, void *u);
\& int PEM_write_bio_X509_AUX(BIO *bp, X509 *x);
\& int PEM_write_X509_AUX(FILE *fp, X509 *x);
\&
\& X509_REQ *PEM_read_bio_X509_REQ(BIO *bp, X509_REQ **x,
\&                                 pem_password_cb *cb, void *u);
\& X509_REQ *PEM_read_X509_REQ(FILE *fp, X509_REQ **x,
\&                             pem_password_cb *cb, void *u);
\& int PEM_write_bio_X509_REQ(BIO *bp, X509_REQ *x);
\& int PEM_write_X509_REQ(FILE *fp, X509_REQ *x);
\& int PEM_write_bio_X509_REQ_NEW(BIO *bp, X509_REQ *x);
\& int PEM_write_X509_REQ_NEW(FILE *fp, X509_REQ *x);
\&
\& X509_CRL *PEM_read_bio_X509_CRL(BIO *bp, X509_CRL **x,
\&                                 pem_password_cb *cb, void *u);
\& X509_CRL *PEM_read_X509_CRL(FILE *fp, X509_CRL **x,
\&                             pem_password_cb *cb, void *u);
\& int PEM_write_bio_X509_CRL(BIO *bp, X509_CRL *x);
\& int PEM_write_X509_CRL(FILE *fp, X509_CRL *x);
\&
\& PKCS7 *PEM_read_bio_PKCS7(BIO *bp, PKCS7 **x, pem_password_cb *cb, void *u);
\& PKCS7 *PEM_read_PKCS7(FILE *fp, PKCS7 **x, pem_password_cb *cb, void *u);
\& int PEM_write_bio_PKCS7(BIO *bp, PKCS7 *x);
\& int PEM_write_PKCS7(FILE *fp, PKCS7 *x);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The PEM functions read or write structures in PEM format. In
this sense PEM format is simply base64 encoded data surrounded
by header lines.
.PP
For more details about the meaning of arguments see the
\&\fBPEM FUNCTION ARGUMENTS\fR section.
.PP
Each operation has four functions associated with it. For
brevity the term "\fBTYPE\fR functions" will be used below to collectively
refer to the \fBPEM_read_bio_TYPE()\fR, \fBPEM_read_TYPE()\fR,
\&\fBPEM_write_bio_TYPE()\fR, and \fBPEM_write_TYPE()\fR functions.
.PP
The \fBPrivateKey\fR functions read or write a private key in PEM format using an
EVP_PKEY structure. The write routines use PKCS#8 private key format and are
equivalent to \fBPEM_write_bio_PKCS8PrivateKey()\fR.The read functions transparently
handle traditional and PKCS#8 format encrypted and unencrypted keys.
.PP
\&\fBPEM_write_bio_PrivateKey_traditional()\fR writes out a private key in the
"traditional" format with a simple private key marker and should only
be used for compatibility with legacy programs.
.PP
\&\fBPEM_write_bio_PKCS8PrivateKey()\fR and \fBPEM_write_PKCS8PrivateKey()\fR write a private
key in an EVP_PKEY structure in PKCS#8 EncryptedPrivateKeyInfo format using
PKCS#5 v2.0 password based encryption algorithms. The \fBcipher\fR argument
specifies the encryption algorithm to use: unlike some other PEM routines the
encryption is applied at the PKCS#8 level and not in the PEM headers. If
\&\fBcipher\fR is NULL then no encryption is used and a PKCS#8 PrivateKeyInfo
structure is used instead.
.PP
\&\fBPEM_write_bio_PKCS8PrivateKey_nid()\fR and \fBPEM_write_PKCS8PrivateKey_nid()\fR
also write out a private key as a PKCS#8 EncryptedPrivateKeyInfo however
it uses PKCS#5 v1.5 or PKCS#12 encryption algorithms instead. The algorithm
to use is specified in the \fBnid\fR parameter and should be the NID of the
corresponding OBJECT IDENTIFIER (see NOTES section).
.PP
The \fBPUBKEY\fR functions process a public key using an EVP_PKEY
structure. The public key is encoded as a SubjectPublicKeyInfo
structure.
.PP
The \fBRSAPrivateKey\fR functions process an RSA private key using an
RSA structure. The write routines uses traditional format. The read
routines handles the same formats as the \fBPrivateKey\fR
functions but an error occurs if the private key is not RSA.
.PP
The \fBRSAPublicKey\fR functions process an RSA public key using an
RSA structure. The public key is encoded using a PKCS#1 RSAPublicKey
structure.
.PP
The \fBRSA_PUBKEY\fR functions also process an RSA public key using
an RSA structure. However, the public key is encoded using a
SubjectPublicKeyInfo structure and an error occurs if the public
key is not RSA.
.PP
The \fBDSAPrivateKey\fR functions process a DSA private key using a
DSA structure. The write routines uses traditional format. The read
routines handles the same formats as the \fBPrivateKey\fR
functions but an error occurs if the private key is not DSA.
.PP
The \fBDSA_PUBKEY\fR functions process a DSA public key using
a DSA structure. The public key is encoded using a
SubjectPublicKeyInfo structure and an error occurs if the public
key is not DSA.
.PP
The \fBParameters\fR functions read or write key parameters in PEM format using
an EVP_PKEY structure.  The encoding depends on the type of key; for DSA key
parameters, it will be a Dss-Parms structure as defined in RFC2459, and for DH
key parameters, it will be a PKCS#3 DHparameter structure.  \fIThese functions
only exist for the \fR\f(BIBIO\fR\fI type\fR.
.PP
The \fBDSAparams\fR functions process DSA parameters using a DSA
structure. The parameters are encoded using a Dss-Parms structure
as defined in RFC2459.
.PP
The \fBDHparams\fR functions process DH parameters using a DH
structure. The parameters are encoded using a PKCS#3 DHparameter
structure.
.PP
The \fBX509\fR functions process an X509 certificate using an X509
structure. They will also process a trusted X509 certificate but
any trust settings are discarded.
.PP
The \fBX509_AUX\fR functions process a trusted X509 certificate using
an X509 structure.
.PP
The \fBX509_REQ\fR and \fBX509_REQ_NEW\fR functions process a PKCS#10
certificate request using an X509_REQ structure. The \fBX509_REQ\fR
write functions use \fBCERTIFICATE REQUEST\fR in the header whereas
the \fBX509_REQ_NEW\fR functions use \fBNEW CERTIFICATE REQUEST\fR
(as required by some CAs). The \fBX509_REQ\fR read functions will
handle either form so there are no \fBX509_REQ_NEW\fR read functions.
.PP
The \fBX509_CRL\fR functions process an X509 CRL using an X509_CRL
structure.
.PP
The \fBPKCS7\fR functions process a PKCS#7 ContentInfo using a PKCS7
structure.
.SH "PEM FUNCTION ARGUMENTS"
.IX Header "PEM FUNCTION ARGUMENTS"
The PEM functions have many common arguments.
.PP
The \fBbp\fR BIO parameter (if present) specifies the BIO to read from
or write to.
.PP
The \fBfp\fR FILE parameter (if present) specifies the FILE pointer to
read from or write to.
.PP
The PEM read functions all take an argument \fBTYPE **x\fR and return
a \fBTYPE *\fR pointer. Where \fBTYPE\fR is whatever structure the function
uses. If \fBx\fR is NULL then the parameter is ignored. If \fBx\fR is not
NULL but \fB*x\fR is NULL then the structure returned will be written
to \fB*x\fR. If neither \fBx\fR nor \fB*x\fR is NULL then an attempt is made
to reuse the structure at \fB*x\fR (but see BUGS and EXAMPLES sections).
Irrespective of the value of \fBx\fR a pointer to the structure is always
returned (or NULL if an error occurred).
.PP
The PEM functions which write private keys take an \fBenc\fR parameter
which specifies the encryption algorithm to use, encryption is done
at the PEM level. If this parameter is set to NULL then the private
key is written in unencrypted form.
.PP
The \fBcb\fR argument is the callback to use when querying for the pass
phrase used for encrypted PEM structures (normally only private keys).
.PP
For the PEM write routines if the \fBkstr\fR parameter is not NULL then
\&\fBklen\fR bytes at \fBkstr\fR are used as the passphrase and \fBcb\fR is
ignored.
.PP
If the \fBcb\fR parameters is set to NULL and the \fBu\fR parameter is not
NULL then the \fBu\fR parameter is interpreted as a null terminated string
to use as the passphrase. If both \fBcb\fR and \fBu\fR are NULL then the
default callback routine is used which will typically prompt for the
passphrase on the current terminal with echoing turned off.
.PP
The default passphrase callback is sometimes inappropriate (for example
in a GUI application) so an alternative can be supplied. The callback
routine has the following form:
.PP
.Vb 1
\& int cb(char *buf, int size, int rwflag, void *u);
.Ve
.PP
\&\fBbuf\fR is the buffer to write the passphrase to. \fBsize\fR is the maximum
length of the passphrase (i.e. the size of buf). \fBrwflag\fR is a flag
which is set to 0 when reading and 1 when writing. A typical routine
will ask the user to verify the passphrase (for example by prompting
for it twice) if \fBrwflag\fR is 1. The \fBu\fR parameter has the same
value as the \fBu\fR parameter passed to the PEM routine. It allows
arbitrary data to be passed to the callback by the application
(for example a window handle in a GUI application). The callback
\&\fBmust\fR return the number of characters in the passphrase or \-1 if
an error occurred.
.SH NOTES
.IX Header "NOTES"
The old \fBPrivateKey\fR write routines are retained for compatibility.
New applications should write private keys using the
\&\fBPEM_write_bio_PKCS8PrivateKey()\fR or \fBPEM_write_PKCS8PrivateKey()\fR routines
because they are more secure (they use an iteration count of 2048 whereas
the traditional routines use a count of 1) unless compatibility with older
versions of OpenSSL is important.
.PP
The \fBPrivateKey\fR read routines can be used in all applications because
they handle all formats transparently.
.PP
A frequent cause of problems is attempting to use the PEM routines like
this:
.PP
.Vb 1
\& X509 *x;
\&
\& PEM_read_bio_X509(bp, &x, 0, NULL);
.Ve
.PP
this is a bug because an attempt will be made to reuse the data at \fBx\fR
which is an uninitialised pointer.
.PP
These functions make no assumption regarding the pass phrase received from the
password callback.
It will simply be treated as a byte sequence.
.SH "PEM ENCRYPTION FORMAT"
.IX Header "PEM ENCRYPTION FORMAT"
These old \fBPrivateKey\fR routines use a non standard technique for encryption.
.PP
The private key (or other data) takes the following form:
.PP
.Vb 3
\& \-\-\-\-\-BEGIN RSA PRIVATE KEY\-\-\-\-\-
\& Proc\-Type: 4,ENCRYPTED
\& DEK\-Info: DES\-EDE3\-CBC,3F17F5316E2BAC89
\&
\& ...base64 encoded data...
\& \-\-\-\-\-END RSA PRIVATE KEY\-\-\-\-\-
.Ve
.PP
The line beginning with \fIProc-Type\fR contains the version and the
protection on the encapsulated data. The line beginning \fIDEK-Info\fR
contains two comma separated values: the encryption algorithm name as
used by \fBEVP_get_cipherbyname()\fR and an initialization vector used by the
cipher encoded as a set of hexadecimal digits. After those two lines is
the base64\-encoded encrypted data.
.PP
The encryption key is derived using \fBEVP_BytesToKey()\fR. The cipher's
initialization vector is passed to \fBEVP_BytesToKey()\fR as the \fBsalt\fR
parameter. Internally, \fBPKCS5_SALT_LEN\fR bytes of the salt are used
(regardless of the size of the initialization vector). The user's
password is passed to \fBEVP_BytesToKey()\fR using the \fBdata\fR and \fBdatal\fR
parameters. Finally, the library uses an iteration count of 1 for
\&\fBEVP_BytesToKey()\fR.
.PP
The \fBkey\fR derived by \fBEVP_BytesToKey()\fR along with the original initialization
vector is then used to decrypt the encrypted data. The \fBiv\fR produced by
\&\fBEVP_BytesToKey()\fR is not utilized or needed, and NULL should be passed to
the function.
.PP
The pseudo code to derive the key would look similar to:
.PP
.Vb 2
\& EVP_CIPHER* cipher = EVP_des_ede3_cbc();
\& EVP_MD* md = EVP_md5();
\&
\& unsigned int nkey = EVP_CIPHER_key_length(cipher);
\& unsigned int niv = EVP_CIPHER_iv_length(cipher);
\& unsigned char key[nkey];
\& unsigned char iv[niv];
\&
\& memcpy(iv, HexToBin("3F17F5316E2BAC89"), niv);
\& rc = EVP_BytesToKey(cipher, md, iv /*salt*/, pword, plen, 1, key, NULL /*iv*/);
\& if (rc != nkey)
\&     /* Error */
\&
\& /* On success, use key and iv to initialize the cipher */
.Ve
.SH BUGS
.IX Header "BUGS"
The PEM read routines in some versions of OpenSSL will not correctly reuse
an existing structure. Therefore, the following:
.PP
.Vb 1
\& PEM_read_bio_X509(bp, &x, 0, NULL);
.Ve
.PP
where \fBx\fR already contains a valid certificate, may not work, whereas:
.PP
.Vb 2
\& X509_free(x);
\& x = PEM_read_bio_X509(bp, NULL, 0, NULL);
.Ve
.PP
is guaranteed to work.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The read routines return either a pointer to the structure read or NULL
if an error occurred.
.PP
The write routines return 1 for success or 0 for failure.
.SH EXAMPLES
.IX Header "EXAMPLES"
Although the PEM routines take several arguments in almost all applications
most of them are set to 0 or NULL.
.PP
Read a certificate in PEM format from a BIO:
.PP
.Vb 1
\& X509 *x;
\&
\& x = PEM_read_bio_X509(bp, NULL, 0, NULL);
\& if (x == NULL)
\&     /* Error */
.Ve
.PP
Alternative method:
.PP
.Vb 1
\& X509 *x = NULL;
\&
\& if (!PEM_read_bio_X509(bp, &x, 0, NULL))
\&     /* Error */
.Ve
.PP
Write a certificate to a BIO:
.PP
.Vb 2
\& if (!PEM_write_bio_X509(bp, x))
\&     /* Error */
.Ve
.PP
Write a private key (using traditional format) to a BIO using
triple DES encryption, the pass phrase is prompted for:
.PP
.Vb 2
\& if (!PEM_write_bio_PrivateKey(bp, key, EVP_des_ede3_cbc(), NULL, 0, 0, NULL))
\&     /* Error */
.Ve
.PP
Write a private key (using PKCS#8 format) to a BIO using triple
DES encryption, using the pass phrase "hello":
.PP
.Vb 3
\& if (!PEM_write_bio_PKCS8PrivateKey(bp, key, EVP_des_ede3_cbc(),
\&                                    NULL, 0, 0, "hello"))
\&     /* Error */
.Ve
.PP
Read a private key from a BIO using a pass phrase callback:
.PP
.Vb 3
\& key = PEM_read_bio_PrivateKey(bp, NULL, pass_cb, "My Private Key");
\& if (key == NULL)
\&     /* Error */
.Ve
.PP
Skeleton pass phrase callback:
.PP
.Vb 2
\& int pass_cb(char *buf, int size, int rwflag, void *u)
\& {
\&
\&     /* We\*(Aqd probably do something else if \*(Aqrwflag\*(Aq is 1 */
\&     printf("Enter pass phrase for \e"%s\e"\en", (char *)u);
\&
\&     /* get pass phrase, length \*(Aqlen\*(Aq into \*(Aqtmp\*(Aq */
\&     char *tmp = "hello";
\&     if (tmp == NULL) /* An error occurred */
\&         return \-1;
\&
\&     size_t len = strlen(tmp);
\&
\&     if (len > size)
\&         len = size;
\&     memcpy(buf, tmp, len);
\&     return len;
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_EncryptInit\fR\|(3), \fBEVP_BytesToKey\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The old Netscape certificate sequences were no longer documented
in OpenSSL 1.1.0; applications should use the PKCS7 standard instead
as they will be formally deprecated in a future releases.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
