<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_DigestSignInit</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_DigestSignInit, EVP_DigestSignUpdate, EVP_DigestSignFinal, EVP_DigestSign - EVP signing functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

int EVP_DigestSignInit(EVP_MD_CTX *ctx, EVP_PKEY_CTX **pctx,
                       const EVP_MD *type, ENGINE *e, EVP_PKEY *pkey);
int EVP_DigestSignUpdate(EVP_MD_CTX *ctx, const void *d, size_t cnt);
int EVP_DigestSignFinal(EVP_MD_CTX *ctx, unsigned char *sig, size_t *siglen);

int EVP_DigestSign(EVP_MD_CTX *ctx, unsigned char *sigret,
                   size_t *siglen, const unsigned char *tbs,
                   size_t tbslen);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The EVP signature routines are a high-level interface to digital signatures.</p>

<p>EVP_DigestSignInit() sets up signing context <b>ctx</b> to use digest <b>type</b> from ENGINE <b>e</b> and private key <b>pkey</b>. <b>ctx</b> must be created with EVP_MD_CTX_new() before calling this function. If <b>pctx</b> is not NULL, the EVP_PKEY_CTX of the signing operation will be written to <b>*pctx</b>: this can be used to set alternative signing options. Note that any existing value in <b>*pctx</b> is overwritten. The EVP_PKEY_CTX value returned must not be freed directly by the application if <b>ctx</b> is not assigned an EVP_PKEY_CTX value before being passed to EVP_DigestSignInit() (which means the EVP_PKEY_CTX is created inside EVP_DigestSignInit() and it will be freed automatically when the EVP_MD_CTX is freed).</p>

<p>The digest <b>type</b> may be NULL if the signing algorithm supports it.</p>

<p>No <b>EVP_PKEY_CTX</b> will be created by EVP_DigestSignInit() if the passed <b>ctx</b> has already been assigned one via <a href="../man3/EVP_MD_CTX_set_pkey_ctx.html">EVP_MD_CTX_set_pkey_ctx(3)</a>. See also <a href="../man7/SM2.html">SM2(7)</a>.</p>

<p>Only EVP_PKEY types that support signing can be used with these functions. This includes MAC algorithms where the MAC generation is considered as a form of &quot;signing&quot;. Built-in EVP_PKEY types supported by these functions are CMAC, Poly1305, DSA, ECDSA, HMAC, RSA, SipHash, Ed25519 and Ed448.</p>

<p>Not all digests can be used for all key types. The following combinations apply.</p>

<dl>

<dt id="DSA">DSA</dt>
<dd>

<p>Supports SHA1, SHA224, SHA256, SHA384 and SHA512</p>

</dd>
<dt id="ECDSA">ECDSA</dt>
<dd>

<p>Supports SHA1, SHA224, SHA256, SHA384, SHA512 and SM3</p>

</dd>
<dt id="RSA-with-no-padding">RSA with no padding</dt>
<dd>

<p>Supports no digests (the digest <b>type</b> must be NULL)</p>

</dd>
<dt id="RSA-with-X931-padding">RSA with X931 padding</dt>
<dd>

<p>Supports SHA1, SHA256, SHA384 and SHA512</p>

</dd>
<dt id="All-other-RSA-padding-types">All other RSA padding types</dt>
<dd>

<p>Support SHA1, SHA224, SHA256, SHA384, SHA512, MD5, MD5_SHA1, MD2, MD4, MDC2, SHA3-224, SHA3-256, SHA3-384, SHA3-512</p>

</dd>
<dt id="Ed25519-and-Ed448">Ed25519 and Ed448</dt>
<dd>

<p>Support no digests (the digest <b>type</b> must be NULL)</p>

</dd>
<dt id="HMAC">HMAC</dt>
<dd>

<p>Supports any digest</p>

</dd>
<dt id="CMAC-Poly1305-and-SipHash">CMAC, Poly1305 and SipHash</dt>
<dd>

<p>Will ignore any digest provided.</p>

</dd>
</dl>

<p>If RSA-PSS is used and restrictions apply then the digest must match.</p>

<p>EVP_DigestSignUpdate() hashes <b>cnt</b> bytes of data at <b>d</b> into the signature context <b>ctx</b>. This function can be called several times on the same <b>ctx</b> to include additional data. This function is currently implemented using a macro.</p>

<p>EVP_DigestSignFinal() signs the data in <b>ctx</b> and places the signature in <b>sig</b>. If <b>sig</b> is <b>NULL</b> then the maximum size of the output buffer is written to the <b>siglen</b> parameter. If <b>sig</b> is not <b>NULL</b> then before the call the <b>siglen</b> parameter should contain the length of the <b>sig</b> buffer. If the call is successful the signature is written to <b>sig</b> and the amount of data written to <b>siglen</b>.</p>

<p>EVP_DigestSign() signs <b>tbslen</b> bytes of data at <b>tbs</b> and places the signature in <b>sig</b> and its length in <b>siglen</b> in a similar way to EVP_DigestSignFinal().</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_DigestSignInit(), EVP_DigestSignUpdate(), EVP_DigestSignFinal() and EVP_DigestSign() return 1 for success and 0 for failure.</p>

<p>The error codes can be obtained from <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>.</p>

<h1 id="NOTES">NOTES</h1>

<p>The <b>EVP</b> interface to digital signatures should almost always be used in preference to the low-level interfaces. This is because the code then becomes transparent to the algorithm used and much more flexible.</p>

<p>EVP_DigestSign() is a one shot operation which signs a single block of data in one function. For algorithms that support streaming it is equivalent to calling EVP_DigestSignUpdate() and EVP_DigestSignFinal(). For algorithms which do not support streaming (e.g. PureEdDSA) it is the only way to sign data.</p>

<p>In previous versions of OpenSSL there was a link between message digest types and public key algorithms. This meant that &quot;clone&quot; digests such as EVP_dss1() needed to be used to sign using SHA1 and DSA. This is no longer necessary and the use of clone digest is now discouraged.</p>

<p>For some key types and parameters the random number generator must be seeded. If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to external circumstances (see <a href="../man7/RAND.html">RAND(7)</a>), the operation will fail.</p>

<p>The call to EVP_DigestSignFinal() internally finalizes a copy of the digest context. This means that calls to EVP_DigestSignUpdate() and EVP_DigestSignFinal() can be called later to digest and sign additional data.</p>

<p>Since only a copy of the digest context is ever finalized, the context must be cleaned up after use by calling EVP_MD_CTX_free() or a memory leak will occur.</p>

<p>The use of EVP_PKEY_size() with these functions is discouraged because some signature operations may have a signature length which depends on the parameters set. As a result EVP_PKEY_size() would have to return a value which indicates the maximum possible signature for any set of parameters.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_DigestVerifyInit.html">EVP_DigestVerifyInit(3)</a>, <a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a>, <a href="../man7/evp.html">evp(7)</a>, <a href="../man3/HMAC.html">HMAC(3)</a>, <a href="../man3/MD2.html">MD2(3)</a>, <a href="../man3/MD5.html">MD5(3)</a>, <a href="../man3/MDC2.html">MDC2(3)</a>, <a href="../man3/RIPEMD160.html">RIPEMD160(3)</a>, <a href="../man3/SHA1.html">SHA1(3)</a>, <a href="../man1/dgst.html">dgst(1)</a>, <a href="../man7/RAND.html">RAND(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>EVP_DigestSignInit(), EVP_DigestSignUpdate() and EVP_DigestSignFinal() were added in OpenSSL 1.0.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2006-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


