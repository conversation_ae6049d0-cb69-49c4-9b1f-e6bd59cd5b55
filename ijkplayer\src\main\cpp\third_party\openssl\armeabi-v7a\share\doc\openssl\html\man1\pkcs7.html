<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>pkcs7</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RESTRICTIONS">RESTRICTIONS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-pkcs7, pkcs7 - PKCS#7 utility</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>pkcs7</b> [<b>-help</b>] [<b>-inform PEM|DER</b>] [<b>-outform PEM|DER</b>] [<b>-in filename</b>] [<b>-out filename</b>] [<b>-print_certs</b>] [<b>-text</b>] [<b>-noout</b>] [<b>-engine id</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>pkcs7</b> command processes PKCS#7 files in DER or PEM format.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="inform-DER-PEM"><b>-inform DER|PEM</b></dt>
<dd>

<p>This specifies the input format. <b>DER</b> format is DER encoded PKCS#7 v1.5 structure.<b>PEM</b> (the default) is a base64 encoded version of the DER form with header and footer lines.</p>

</dd>
<dt id="outform-DER-PEM"><b>-outform DER|PEM</b></dt>
<dd>

<p>This specifies the output format, the options have the same meaning and default as the <b>-inform</b> option.</p>

</dd>
<dt id="in-filename"><b>-in filename</b></dt>
<dd>

<p>This specifies the input filename to read from or standard input if this option is not specified.</p>

</dd>
<dt id="out-filename"><b>-out filename</b></dt>
<dd>

<p>Specifies the output filename to write to or standard output by default.</p>

</dd>
<dt id="print_certs"><b>-print_certs</b></dt>
<dd>

<p>Prints out any certificates or CRLs contained in the file. They are preceded by their subject and issuer names in one line format.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>Prints out certificates details in full rather than just subject and issuer names.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>Don&#39;t output the encoded version of the PKCS#7 structure (or certificates is <b>-print_certs</b> is set).</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Specifying an engine (by its unique <b>id</b> string) will cause <b>pkcs7</b> to attempt to obtain a functional reference to the specified engine, thus initialising it if needed. The engine will then be set as the default for all available algorithms.</p>

</dd>
</dl>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Convert a PKCS#7 file from PEM to DER:</p>

<pre><code>openssl pkcs7 -in file.pem -outform DER -out file.der</code></pre>

<p>Output all certificates in a file:</p>

<pre><code>openssl pkcs7 -in file.pem -print_certs -out certs.pem</code></pre>

<h1 id="NOTES">NOTES</h1>

<p>The PEM PKCS#7 format uses the header and footer lines:</p>

<pre><code>-----BEGIN PKCS7-----
-----END PKCS7-----</code></pre>

<p>For compatibility with some CAs it will also accept:</p>

<pre><code>-----BEGIN CERTIFICATE-----
-----END CERTIFICATE-----</code></pre>

<h1 id="RESTRICTIONS">RESTRICTIONS</h1>

<p>There is no option to print out all the fields of a PKCS#7 file.</p>

<p>This PKCS#7 routines only understand PKCS#7 v 1.5 as specified in RFC2315 they cannot currently parse, for example, the new CMS as described in RFC2630.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/crl2pkcs7.html">crl2pkcs7(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2017 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


