.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_ENCRYPTINIT 3"
.TH EVP_ENCRYPTINIT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_CIPHER_CTX_new,
EVP_CIPHER_CTX_reset,
EVP_CIPHER_CTX_free,
EVP_EncryptInit_ex,
EVP_EncryptUpdate,
EVP_EncryptFinal_ex,
EVP_DecryptInit_ex,
EVP_DecryptUpdate,
EVP_DecryptFinal_ex,
EVP_CipherInit_ex,
EVP_CipherUpdate,
EVP_CipherFinal_ex,
EVP_CIPHER_CTX_set_key_length,
EVP_CIPHER_CTX_ctrl,
EVP_EncryptInit,
EVP_EncryptFinal,
EVP_DecryptInit,
EVP_DecryptFinal,
EVP_CipherInit,
EVP_CipherFinal,
EVP_get_cipherbyname,
EVP_get_cipherbynid,
EVP_get_cipherbyobj,
EVP_CIPHER_nid,
EVP_CIPHER_block_size,
EVP_CIPHER_key_length,
EVP_CIPHER_iv_length,
EVP_CIPHER_flags,
EVP_CIPHER_mode,
EVP_CIPHER_type,
EVP_CIPHER_CTX_cipher,
EVP_CIPHER_CTX_nid,
EVP_CIPHER_CTX_block_size,
EVP_CIPHER_CTX_key_length,
EVP_CIPHER_CTX_iv_length,
EVP_CIPHER_CTX_get_app_data,
EVP_CIPHER_CTX_set_app_data,
EVP_CIPHER_CTX_type,
EVP_CIPHER_CTX_flags,
EVP_CIPHER_CTX_mode,
EVP_CIPHER_param_to_asn1,
EVP_CIPHER_asn1_to_param,
EVP_CIPHER_CTX_set_padding,
EVP_enc_null
\&\- EVP cipher routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_CIPHER_CTX *EVP_CIPHER_CTX_new(void);
\& int EVP_CIPHER_CTX_reset(EVP_CIPHER_CTX *ctx);
\& void EVP_CIPHER_CTX_free(EVP_CIPHER_CTX *ctx);
\&
\& int EVP_EncryptInit_ex(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                        ENGINE *impl, const unsigned char *key, const unsigned char *iv);
\& int EVP_EncryptUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
\&                       int *outl, const unsigned char *in, int inl);
\& int EVP_EncryptFinal_ex(EVP_CIPHER_CTX *ctx, unsigned char *out, int *outl);
\&
\& int EVP_DecryptInit_ex(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                        ENGINE *impl, const unsigned char *key, const unsigned char *iv);
\& int EVP_DecryptUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
\&                       int *outl, const unsigned char *in, int inl);
\& int EVP_DecryptFinal_ex(EVP_CIPHER_CTX *ctx, unsigned char *outm, int *outl);
\&
\& int EVP_CipherInit_ex(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                       ENGINE *impl, const unsigned char *key, const unsigned char *iv, int enc);
\& int EVP_CipherUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
\&                      int *outl, const unsigned char *in, int inl);
\& int EVP_CipherFinal_ex(EVP_CIPHER_CTX *ctx, unsigned char *outm, int *outl);
\&
\& int EVP_EncryptInit(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                     const unsigned char *key, const unsigned char *iv);
\& int EVP_EncryptFinal(EVP_CIPHER_CTX *ctx, unsigned char *out, int *outl);
\&
\& int EVP_DecryptInit(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                     const unsigned char *key, const unsigned char *iv);
\& int EVP_DecryptFinal(EVP_CIPHER_CTX *ctx, unsigned char *outm, int *outl);
\&
\& int EVP_CipherInit(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                    const unsigned char *key, const unsigned char *iv, int enc);
\& int EVP_CipherFinal(EVP_CIPHER_CTX *ctx, unsigned char *outm, int *outl);
\&
\& int EVP_CIPHER_CTX_set_padding(EVP_CIPHER_CTX *x, int padding);
\& int EVP_CIPHER_CTX_set_key_length(EVP_CIPHER_CTX *x, int keylen);
\& int EVP_CIPHER_CTX_ctrl(EVP_CIPHER_CTX *ctx, int type, int arg, void *ptr);
\& int EVP_CIPHER_CTX_rand_key(EVP_CIPHER_CTX *ctx, unsigned char *key);
\&
\& const EVP_CIPHER *EVP_get_cipherbyname(const char *name);
\& const EVP_CIPHER *EVP_get_cipherbynid(int nid);
\& const EVP_CIPHER *EVP_get_cipherbyobj(const ASN1_OBJECT *a);
\&
\& int EVP_CIPHER_nid(const EVP_CIPHER *e);
\& int EVP_CIPHER_block_size(const EVP_CIPHER *e);
\& int EVP_CIPHER_key_length(const EVP_CIPHER *e);
\& int EVP_CIPHER_iv_length(const EVP_CIPHER *e);
\& unsigned long EVP_CIPHER_flags(const EVP_CIPHER *e);
\& unsigned long EVP_CIPHER_mode(const EVP_CIPHER *e);
\& int EVP_CIPHER_type(const EVP_CIPHER *ctx);
\&
\& const EVP_CIPHER *EVP_CIPHER_CTX_cipher(const EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_nid(const EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_block_size(const EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_key_length(const EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_iv_length(const EVP_CIPHER_CTX *ctx);
\& void *EVP_CIPHER_CTX_get_app_data(const EVP_CIPHER_CTX *ctx);
\& void EVP_CIPHER_CTX_set_app_data(const EVP_CIPHER_CTX *ctx, void *data);
\& int EVP_CIPHER_CTX_type(const EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_mode(const EVP_CIPHER_CTX *ctx);
\&
\& int EVP_CIPHER_param_to_asn1(EVP_CIPHER_CTX *c, ASN1_TYPE *type);
\& int EVP_CIPHER_asn1_to_param(EVP_CIPHER_CTX *c, ASN1_TYPE *type);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP cipher routines are a high-level interface to certain
symmetric ciphers.
.PP
\&\fBEVP_CIPHER_CTX_new()\fR creates a cipher context.
.PP
\&\fBEVP_CIPHER_CTX_free()\fR clears all information from a cipher context
and free up any allocated memory associate with it, including \fBctx\fR
itself. This function should be called after all operations using a
cipher are complete so sensitive information does not remain in
memory.
.PP
\&\fBEVP_EncryptInit_ex()\fR sets up cipher context \fBctx\fR for encryption
with cipher \fBtype\fR from ENGINE \fBimpl\fR. \fBctx\fR must be created
before calling this function. \fBtype\fR is normally supplied
by a function such as \fBEVP_aes_256_cbc()\fR. If \fBimpl\fR is NULL then the
default implementation is used. \fBkey\fR is the symmetric key to use
and \fBiv\fR is the IV to use (if necessary), the actual number of bytes
used for the key and IV depends on the cipher. It is possible to set
all parameters to NULL except \fBtype\fR in an initial call and supply
the remaining parameters in subsequent calls, all of which have \fBtype\fR
set to NULL. This is done when the default cipher parameters are not
appropriate.
.PP
\&\fBEVP_EncryptUpdate()\fR encrypts \fBinl\fR bytes from the buffer \fBin\fR and
writes the encrypted version to \fBout\fR. This function can be called
multiple times to encrypt successive blocks of data. The amount
of data written depends on the block alignment of the encrypted data.
For most ciphers and modes, the amount of data written can be anything
from zero bytes to (inl + cipher_block_size \- 1) bytes.
For wrap cipher modes, the amount of data written can be anything
from zero bytes to (inl + cipher_block_size) bytes.
For stream ciphers, the amount of data written can be anything from zero
bytes to inl bytes.
Thus, \fBout\fR should contain sufficient room for the operation being performed.
The actual number of bytes written is placed in \fBoutl\fR. It also
checks if \fBin\fR and \fBout\fR are partially overlapping, and if they are
0 is returned to indicate failure.
.PP
If padding is enabled (the default) then \fBEVP_EncryptFinal_ex()\fR encrypts
the "final" data, that is any data that remains in a partial block.
It uses standard block padding (aka PKCS padding) as described in
the NOTES section, below. The encrypted
final data is written to \fBout\fR which should have sufficient space for
one cipher block. The number of bytes written is placed in \fBoutl\fR. After
this function is called the encryption operation is finished and no further
calls to \fBEVP_EncryptUpdate()\fR should be made.
.PP
If padding is disabled then \fBEVP_EncryptFinal_ex()\fR will not encrypt any more
data and it will return an error if any data remains in a partial block:
that is if the total data length is not a multiple of the block size.
.PP
\&\fBEVP_DecryptInit_ex()\fR, \fBEVP_DecryptUpdate()\fR and \fBEVP_DecryptFinal_ex()\fR are the
corresponding decryption operations. \fBEVP_DecryptFinal()\fR will return an
error code if padding is enabled and the final block is not correctly
formatted. The parameters and restrictions are identical to the encryption
operations except that if padding is enabled the decrypted data buffer \fBout\fR
passed to \fBEVP_DecryptUpdate()\fR should have sufficient room for
(\fBinl\fR + cipher_block_size) bytes unless the cipher block size is 1 in
which case \fBinl\fR bytes is sufficient.
.PP
\&\fBEVP_CipherInit_ex()\fR, \fBEVP_CipherUpdate()\fR and \fBEVP_CipherFinal_ex()\fR are
functions that can be used for decryption or encryption. The operation
performed depends on the value of the \fBenc\fR parameter. It should be set
to 1 for encryption, 0 for decryption and \-1 to leave the value unchanged
(the actual value of 'enc' being supplied in a previous call).
.PP
\&\fBEVP_CIPHER_CTX_reset()\fR clears all information from a cipher context
and free up any allocated memory associate with it, except the \fBctx\fR
itself. This function should be called anytime \fBctx\fR is to be reused
for another \fBEVP_CipherInit()\fR / \fBEVP_CipherUpdate()\fR / \fBEVP_CipherFinal()\fR
series of calls.
.PP
\&\fBEVP_EncryptInit()\fR, \fBEVP_DecryptInit()\fR and \fBEVP_CipherInit()\fR behave in a
similar way to \fBEVP_EncryptInit_ex()\fR, \fBEVP_DecryptInit_ex()\fR and
\&\fBEVP_CipherInit_ex()\fR except they always use the default cipher implementation.
.PP
\&\fBEVP_EncryptFinal()\fR, \fBEVP_DecryptFinal()\fR and \fBEVP_CipherFinal()\fR are
identical to \fBEVP_EncryptFinal_ex()\fR, \fBEVP_DecryptFinal_ex()\fR and
\&\fBEVP_CipherFinal_ex()\fR. In previous releases they also cleaned up
the \fBctx\fR, but this is no longer done and \fBEVP_CIPHER_CTX_clean()\fR
must be called to free any context resources.
.PP
\&\fBEVP_get_cipherbyname()\fR, \fBEVP_get_cipherbynid()\fR and \fBEVP_get_cipherbyobj()\fR
return an EVP_CIPHER structure when passed a cipher name, a NID or an
ASN1_OBJECT structure.
.PP
\&\fBEVP_CIPHER_nid()\fR and \fBEVP_CIPHER_CTX_nid()\fR return the NID of a cipher when
passed an \fBEVP_CIPHER\fR or \fBEVP_CIPHER_CTX\fR structure.  The actual NID
value is an internal value which may not have a corresponding OBJECT
IDENTIFIER.
.PP
\&\fBEVP_CIPHER_CTX_set_padding()\fR enables or disables padding. This
function should be called after the context is set up for encryption
or decryption with \fBEVP_EncryptInit_ex()\fR, \fBEVP_DecryptInit_ex()\fR or
\&\fBEVP_CipherInit_ex()\fR. By default encryption operations are padded using
standard block padding and the padding is checked and removed when
decrypting. If the \fBpad\fR parameter is zero then no padding is
performed, the total amount of data encrypted or decrypted must then
be a multiple of the block size or an error will occur.
.PP
\&\fBEVP_CIPHER_key_length()\fR and \fBEVP_CIPHER_CTX_key_length()\fR return the key
length of a cipher when passed an \fBEVP_CIPHER\fR or \fBEVP_CIPHER_CTX\fR
structure. The constant \fBEVP_MAX_KEY_LENGTH\fR is the maximum key length
for all ciphers. Note: although \fBEVP_CIPHER_key_length()\fR is fixed for a
given cipher, the value of \fBEVP_CIPHER_CTX_key_length()\fR may be different
for variable key length ciphers.
.PP
\&\fBEVP_CIPHER_CTX_set_key_length()\fR sets the key length of the cipher ctx.
If the cipher is a fixed length cipher then attempting to set the key
length to any value other than the fixed value is an error.
.PP
\&\fBEVP_CIPHER_iv_length()\fR and \fBEVP_CIPHER_CTX_iv_length()\fR return the IV
length of a cipher when passed an \fBEVP_CIPHER\fR or \fBEVP_CIPHER_CTX\fR.
It will return zero if the cipher does not use an IV.  The constant
\&\fBEVP_MAX_IV_LENGTH\fR is the maximum IV length for all ciphers.
.PP
\&\fBEVP_CIPHER_block_size()\fR and \fBEVP_CIPHER_CTX_block_size()\fR return the block
size of a cipher when passed an \fBEVP_CIPHER\fR or \fBEVP_CIPHER_CTX\fR
structure. The constant \fBEVP_MAX_BLOCK_LENGTH\fR is also the maximum block
length for all ciphers.
.PP
\&\fBEVP_CIPHER_type()\fR and \fBEVP_CIPHER_CTX_type()\fR return the type of the passed
cipher or context. This "type" is the actual NID of the cipher OBJECT
IDENTIFIER as such it ignores the cipher parameters and 40 bit RC2 and
128 bit RC2 have the same NID. If the cipher does not have an object
identifier or does not have ASN1 support this function will return
\&\fBNID_undef\fR.
.PP
\&\fBEVP_CIPHER_CTX_cipher()\fR returns the \fBEVP_CIPHER\fR structure when passed
an \fBEVP_CIPHER_CTX\fR structure.
.PP
\&\fBEVP_CIPHER_mode()\fR and \fBEVP_CIPHER_CTX_mode()\fR return the block cipher mode:
EVP_CIPH_ECB_MODE, EVP_CIPH_CBC_MODE, EVP_CIPH_CFB_MODE, EVP_CIPH_OFB_MODE,
EVP_CIPH_CTR_MODE, EVP_CIPH_GCM_MODE, EVP_CIPH_CCM_MODE, EVP_CIPH_XTS_MODE,
EVP_CIPH_WRAP_MODE or EVP_CIPH_OCB_MODE. If the cipher is a stream cipher then
EVP_CIPH_STREAM_CIPHER is returned.
.PP
\&\fBEVP_CIPHER_param_to_asn1()\fR sets the AlgorithmIdentifier "parameter" based
on the passed cipher. This will typically include any parameters and an
IV. The cipher IV (if any) must be set when this call is made. This call
should be made before the cipher is actually "used" (before any
\&\fBEVP_EncryptUpdate()\fR, \fBEVP_DecryptUpdate()\fR calls for example). This function
may fail if the cipher does not have any ASN1 support.
.PP
\&\fBEVP_CIPHER_asn1_to_param()\fR sets the cipher parameters based on an ASN1
AlgorithmIdentifier "parameter". The precise effect depends on the cipher
In the case of RC2, for example, it will set the IV and effective key length.
This function should be called after the base cipher type is set but before
the key is set. For example \fBEVP_CipherInit()\fR will be called with the IV and
key set to NULL, \fBEVP_CIPHER_asn1_to_param()\fR will be called and finally
\&\fBEVP_CipherInit()\fR again with all parameters except the key set to NULL. It is
possible for this function to fail if the cipher does not have any ASN1 support
or the parameters cannot be set (for example the RC2 effective key length
is not supported.
.PP
\&\fBEVP_CIPHER_CTX_ctrl()\fR allows various cipher specific parameters to be determined
and set.
.PP
\&\fBEVP_CIPHER_CTX_rand_key()\fR generates a random key of the appropriate length
based on the cipher context. The EVP_CIPHER can provide its own random key
generation routine to support keys of a specific form. \fBKey\fR must point to a
buffer at least as big as the value returned by \fBEVP_CIPHER_CTX_key_length()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_CIPHER_CTX_new()\fR returns a pointer to a newly created
\&\fBEVP_CIPHER_CTX\fR for success and \fBNULL\fR for failure.
.PP
\&\fBEVP_EncryptInit_ex()\fR, \fBEVP_EncryptUpdate()\fR and \fBEVP_EncryptFinal_ex()\fR
return 1 for success and 0 for failure.
.PP
\&\fBEVP_DecryptInit_ex()\fR and \fBEVP_DecryptUpdate()\fR return 1 for success and 0 for failure.
\&\fBEVP_DecryptFinal_ex()\fR returns 0 if the decrypt failed or 1 for success.
.PP
\&\fBEVP_CipherInit_ex()\fR and \fBEVP_CipherUpdate()\fR return 1 for success and 0 for failure.
\&\fBEVP_CipherFinal_ex()\fR returns 0 for a decryption failure or 1 for success.
.PP
\&\fBEVP_CIPHER_CTX_reset()\fR returns 1 for success and 0 for failure.
.PP
\&\fBEVP_get_cipherbyname()\fR, \fBEVP_get_cipherbynid()\fR and \fBEVP_get_cipherbyobj()\fR
return an \fBEVP_CIPHER\fR structure or NULL on error.
.PP
\&\fBEVP_CIPHER_nid()\fR and \fBEVP_CIPHER_CTX_nid()\fR return a NID.
.PP
\&\fBEVP_CIPHER_block_size()\fR and \fBEVP_CIPHER_CTX_block_size()\fR return the block
size.
.PP
\&\fBEVP_CIPHER_key_length()\fR and \fBEVP_CIPHER_CTX_key_length()\fR return the key
length.
.PP
\&\fBEVP_CIPHER_CTX_set_padding()\fR always returns 1.
.PP
\&\fBEVP_CIPHER_iv_length()\fR and \fBEVP_CIPHER_CTX_iv_length()\fR return the IV
length, zero if the cipher does not use an IV and a negative value on error.
.PP
\&\fBEVP_CIPHER_type()\fR and \fBEVP_CIPHER_CTX_type()\fR return the NID of the cipher's
OBJECT IDENTIFIER or NID_undef if it has no defined OBJECT IDENTIFIER.
.PP
\&\fBEVP_CIPHER_CTX_cipher()\fR returns an \fBEVP_CIPHER\fR structure.
.PP
\&\fBEVP_CIPHER_param_to_asn1()\fR and \fBEVP_CIPHER_asn1_to_param()\fR return greater
than zero for success and zero or a negative number on failure.
.PP
\&\fBEVP_CIPHER_CTX_rand_key()\fR returns 1 for success.
.SH "CIPHER LISTING"
.IX Header "CIPHER LISTING"
All algorithms have a fixed key length unless otherwise stated.
.PP
Refer to "SEE ALSO" for the full list of ciphers available through the EVP
interface.
.IP \fBEVP_enc_null()\fR 4
.IX Item "EVP_enc_null()"
Null cipher: does nothing.
.SH "AEAD Interface"
.IX Header "AEAD Interface"
The EVP interface for Authenticated Encryption with Associated Data (AEAD)
modes are subtly altered and several additional \fIctrl\fR operations are supported
depending on the mode specified.
.PP
To specify additional authenticated data (AAD), a call to \fBEVP_CipherUpdate()\fR,
\&\fBEVP_EncryptUpdate()\fR or \fBEVP_DecryptUpdate()\fR should be made with the output
parameter \fBout\fR set to \fBNULL\fR.
.PP
When decrypting, the return value of \fBEVP_DecryptFinal()\fR or \fBEVP_CipherFinal()\fR
indicates whether the operation was successful. If it does not indicate success,
the authentication operation has failed and any output data \fBMUST NOT\fR be used
as it is corrupted.
.SS "GCM and OCB Modes"
.IX Subsection "GCM and OCB Modes"
The following \fIctrl\fRs are supported in GCM and OCB modes.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)"
Sets the IV length. This call can only be made before specifying an IV. If
not called a default IV length is used.
.Sp
For GCM AES and OCB AES the default is 12 (i.e. 96 bits). For OCB mode the
maximum is 15.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, taglen, tag)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, taglen, tag)"
Writes \f(CW\*(C`taglen\*(C'\fR bytes of the tag value to the buffer indicated by \f(CW\*(C`tag\*(C'\fR.
This call can only be made when encrypting data and \fBafter\fR all data has been
processed (e.g. after an \fBEVP_EncryptFinal()\fR call).
.Sp
For OCB, \f(CW\*(C`taglen\*(C'\fR must either be 16 or the value previously set via
\&\fBEVP_CTRL_AEAD_SET_TAG\fR.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)"
When decrypting, this call sets the expected tag to \f(CW\*(C`taglen\*(C'\fR bytes from \f(CW\*(C`tag\*(C'\fR.
\&\f(CW\*(C`taglen\*(C'\fR must be between 1 and 16 inclusive.
The tag must be set prior to any call to \fBEVP_DecryptFinal()\fR or
\&\fBEVP_DecryptFinal_ex()\fR.
.Sp
For GCM, this call is only valid when decrypting data.
.Sp
For OCB, this call is valid when decrypting data to set the expected tag,
and when encrypting to set the desired tag length.
.Sp
In OCB mode, calling this when encrypting with \f(CW\*(C`tag\*(C'\fR set to \f(CW\*(C`NULL\*(C'\fR sets the
tag length. The tag length can only be set before specifying an IV. If this is
not called prior to setting the IV during encryption, then a default tag length
is used.
.Sp
For OCB AES, the default tag length is 16 (i.e. 128 bits).  It is also the
maximum tag length for OCB.
.SS "CCM Mode"
.IX Subsection "CCM Mode"
The EVP interface for CCM mode is similar to that of the GCM mode but with a
few additional requirements and different \fIctrl\fR values.
.PP
For CCM mode, the total plaintext or ciphertext length \fBMUST\fR be passed to
\&\fBEVP_CipherUpdate()\fR, \fBEVP_EncryptUpdate()\fR or \fBEVP_DecryptUpdate()\fR with the output
and input parameters (\fBin\fR and \fBout\fR) set to \fBNULL\fR and the length passed in
the \fBinl\fR parameter.
.PP
The following \fIctrl\fRs are supported in CCM mode.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)"
This call is made to set the expected \fBCCM\fR tag value when decrypting or
the length of the tag (with the \f(CW\*(C`tag\*(C'\fR parameter set to NULL) when encrypting.
The tag length is often referred to as \fBM\fR. If not set a default value is
used (12 for AES). When decrypting, the tag needs to be set before passing
in data to be decrypted, but as in GCM and OCB mode, it can be set after
passing additional authenticated data (see "AEAD Interface").
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_CCM_SET_L, ivlen, NULL)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_CCM_SET_L, ivlen, NULL)"
Sets the CCM \fBL\fR value. If not set a default is used (8 for AES).
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)"
Sets the CCM nonce (IV) length. This call can only be made before specifying 
a nonce value. The nonce length is given by \fB15 \- L\fR so it is 7 by default for
AES.
.SS ChaCha20\-Poly1305
.IX Subsection "ChaCha20-Poly1305"
The following \fIctrl\fRs are supported for the ChaCha20\-Poly1305 AEAD algorithm.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)"
Sets the nonce length. This call can only be made before specifying the nonce.
If not called a default nonce length of 12 (i.e. 96 bits) is used. The maximum
nonce length is 12 bytes (i.e. 96\-bits). If a nonce of less than 12 bytes is set
then the nonce is automatically padded with leading 0 bytes to make it 12 bytes
in length.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, taglen, tag)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, taglen, tag)"
Writes \f(CW\*(C`taglen\*(C'\fR bytes of the tag value to the buffer indicated by \f(CW\*(C`tag\*(C'\fR.
This call can only be made when encrypting data and \fBafter\fR all data has been
processed (e.g. after an \fBEVP_EncryptFinal()\fR call).
.Sp
\&\f(CW\*(C`taglen\*(C'\fR specified here must be 16 (\fBPOLY1305_BLOCK_SIZE\fR, i.e. 128\-bits) or
less.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)"
Sets the expected tag to \f(CW\*(C`taglen\*(C'\fR bytes from \f(CW\*(C`tag\*(C'\fR.
The tag length can only be set before specifying an IV.
\&\f(CW\*(C`taglen\*(C'\fR must be between 1 and 16 (\fBPOLY1305_BLOCK_SIZE\fR) inclusive.
This call is only valid when decrypting data.
.SH NOTES
.IX Header "NOTES"
Where possible the \fBEVP\fR interface to symmetric ciphers should be used in
preference to the low-level interfaces. This is because the code then becomes
transparent to the cipher used and much more flexible. Additionally, the
\&\fBEVP\fR interface will ensure the use of platform specific cryptographic
acceleration such as AES-NI (the low-level interfaces do not provide the
guarantee).
.PP
PKCS padding works by adding \fBn\fR padding bytes of value \fBn\fR to make the total
length of the encrypted data a multiple of the block size. Padding is always
added so if the data is already a multiple of the block size \fBn\fR will equal
the block size. For example if the block size is 8 and 11 bytes are to be
encrypted then 5 padding bytes of value 5 will be added.
.PP
When decrypting the final block is checked to see if it has the correct form.
.PP
Although the decryption operation can produce an error if padding is enabled,
it is not a strong test that the input data or key is correct. A random block
has better than 1 in 256 chance of being of the correct format and problems with
the input data earlier on will not produce a final decrypt error.
.PP
If padding is disabled then the decryption operation will always succeed if
the total amount of data decrypted is a multiple of the block size.
.PP
The functions \fBEVP_EncryptInit()\fR, \fBEVP_EncryptFinal()\fR, \fBEVP_DecryptInit()\fR,
\&\fBEVP_CipherInit()\fR and \fBEVP_CipherFinal()\fR are obsolete but are retained for
compatibility with existing code. New code should use \fBEVP_EncryptInit_ex()\fR,
\&\fBEVP_EncryptFinal_ex()\fR, \fBEVP_DecryptInit_ex()\fR, \fBEVP_DecryptFinal_ex()\fR,
\&\fBEVP_CipherInit_ex()\fR and \fBEVP_CipherFinal_ex()\fR because they can reuse an
existing context without allocating and freeing it up on each call.
.PP
There are some differences between functions \fBEVP_CipherInit()\fR and
\&\fBEVP_CipherInit_ex()\fR, significant in some circumstances. \fBEVP_CipherInit()\fR fills
the passed context object with zeros.  As a consequence, \fBEVP_CipherInit()\fR does
not allow step-by-step initialization of the ctx when the \fIkey\fR and \fIiv\fR are
passed in separate calls. It also means that the flags set for the CTX are
removed, and it is especially important for the
\&\fBEVP_CIPHER_CTX_FLAG_WRAP_ALLOW\fR flag treated specially in
\&\fBEVP_CipherInit_ex()\fR.
.PP
\&\fBEVP_get_cipherbynid()\fR, and \fBEVP_get_cipherbyobj()\fR are implemented as macros.
.SH BUGS
.IX Header "BUGS"
\&\fBEVP_MAX_KEY_LENGTH\fR and \fBEVP_MAX_IV_LENGTH\fR only refer to the internal
ciphers with default key lengths. If custom ciphers exceed these values the
results are unpredictable. This is because it has become standard practice to
define a generic key as a fixed unsigned char array containing
\&\fBEVP_MAX_KEY_LENGTH\fR bytes.
.PP
The ASN1 code is incomplete (and sometimes inaccurate) it has only been tested
for certain common S/MIME ciphers (RC2, DES, triple DES) in CBC mode.
.SH EXAMPLES
.IX Header "EXAMPLES"
Encrypt a string using IDEA:
.PP
.Vb 10
\& int do_crypt(char *outfile)
\& {
\&     unsigned char outbuf[1024];
\&     int outlen, tmplen;
\&     /*
\&      * Bogus key and IV: we\*(Aqd normally set these from
\&      * another source.
\&      */
\&     unsigned char key[] = {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15};
\&     unsigned char iv[] = {1,2,3,4,5,6,7,8};
\&     char intext[] = "Some Crypto Text";
\&     EVP_CIPHER_CTX *ctx;
\&     FILE *out;
\&
\&     ctx = EVP_CIPHER_CTX_new();
\&     EVP_EncryptInit_ex(ctx, EVP_idea_cbc(), NULL, key, iv);
\&
\&     if (!EVP_EncryptUpdate(ctx, outbuf, &outlen, intext, strlen(intext))) {
\&         /* Error */
\&         EVP_CIPHER_CTX_free(ctx);
\&         return 0;
\&     }
\&     /*
\&      * Buffer passed to EVP_EncryptFinal() must be after data just
\&      * encrypted to avoid overwriting it.
\&      */
\&     if (!EVP_EncryptFinal_ex(ctx, outbuf + outlen, &tmplen)) {
\&         /* Error */
\&         EVP_CIPHER_CTX_free(ctx);
\&         return 0;
\&     }
\&     outlen += tmplen;
\&     EVP_CIPHER_CTX_free(ctx);
\&     /*
\&      * Need binary mode for fopen because encrypted data is
\&      * binary data. Also cannot use strlen() on it because
\&      * it won\*(Aqt be NUL terminated and may contain embedded
\&      * NULs.
\&      */
\&     out = fopen(outfile, "wb");
\&     if (out == NULL) {
\&         /* Error */
\&         return 0;
\&     }
\&     fwrite(outbuf, 1, outlen, out);
\&     fclose(out);
\&     return 1;
\& }
.Ve
.PP
The ciphertext from the above example can be decrypted using the \fBopenssl\fR
utility with the command line (shown on two lines for clarity):
.PP
.Vb 2
\& openssl idea \-d \e
\&     \-K 000102030405060708090A0B0C0D0E0F \-iv 0102030405060708 <filename
.Ve
.PP
General encryption and decryption function example using FILE I/O and AES128
with a 128\-bit key:
.PP
.Vb 12
\& int do_crypt(FILE *in, FILE *out, int do_encrypt)
\& {
\&     /* Allow enough space in output buffer for additional block */
\&     unsigned char inbuf[1024], outbuf[1024 + EVP_MAX_BLOCK_LENGTH];
\&     int inlen, outlen;
\&     EVP_CIPHER_CTX *ctx;
\&     /*
\&      * Bogus key and IV: we\*(Aqd normally set these from
\&      * another source.
\&      */
\&     unsigned char key[] = "0123456789abcdeF";
\&     unsigned char iv[] = "1234567887654321";
\&
\&     /* Don\*(Aqt set key or IV right away; we want to check lengths */
\&     ctx = EVP_CIPHER_CTX_new();
\&     EVP_CipherInit_ex(ctx, EVP_aes_128_cbc(), NULL, NULL, NULL,
\&                       do_encrypt);
\&     OPENSSL_assert(EVP_CIPHER_CTX_key_length(ctx) == 16);
\&     OPENSSL_assert(EVP_CIPHER_CTX_iv_length(ctx) == 16);
\&
\&     /* Now we can set key and IV */
\&     EVP_CipherInit_ex(ctx, NULL, NULL, key, iv, do_encrypt);
\&
\&     for (;;) {
\&         inlen = fread(inbuf, 1, 1024, in);
\&         if (inlen <= 0)
\&             break;
\&         if (!EVP_CipherUpdate(ctx, outbuf, &outlen, inbuf, inlen)) {
\&             /* Error */
\&             EVP_CIPHER_CTX_free(ctx);
\&             return 0;
\&         }
\&         fwrite(outbuf, 1, outlen, out);
\&     }
\&     if (!EVP_CipherFinal_ex(ctx, outbuf, &outlen)) {
\&         /* Error */
\&         EVP_CIPHER_CTX_free(ctx);
\&         return 0;
\&     }
\&     fwrite(outbuf, 1, outlen, out);
\&
\&     EVP_CIPHER_CTX_free(ctx);
\&     return 1;
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7)
.PP
Supported ciphers are listed in:
.PP
\&\fBEVP_aes\fR\|(3),
\&\fBEVP_aria\fR\|(3),
\&\fBEVP_bf\fR\|(3),
\&\fBEVP_camellia\fR\|(3),
\&\fBEVP_cast5\fR\|(3),
\&\fBEVP_chacha20\fR\|(3),
\&\fBEVP_des\fR\|(3),
\&\fBEVP_desx\fR\|(3),
\&\fBEVP_idea\fR\|(3),
\&\fBEVP_rc2\fR\|(3),
\&\fBEVP_rc4\fR\|(3),
\&\fBEVP_rc5\fR\|(3),
\&\fBEVP_seed\fR\|(3),
\&\fBEVP_sm4\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
Support for OCB mode was added in OpenSSL 1.1.0.
.PP
\&\fBEVP_CIPHER_CTX\fR was made opaque in OpenSSL 1.1.0.  As a result,
\&\fBEVP_CIPHER_CTX_reset()\fR appeared and \fBEVP_CIPHER_CTX_cleanup()\fR
disappeared.  \fBEVP_CIPHER_CTX_init()\fR remains as an alias for
\&\fBEVP_CIPHER_CTX_reset()\fR.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
