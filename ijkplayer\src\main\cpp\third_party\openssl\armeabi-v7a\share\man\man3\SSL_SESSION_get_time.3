.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SESSION_GET_TIME 3"
.TH SSL_SESSION_GET_TIME 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_SESSION_get_time, SSL_SESSION_set_time, SSL_SESSION_get_timeout,
SSL_SESSION_set_timeout,
SSL_get_time, SSL_set_time, SSL_get_timeout, SSL_set_timeout
\&\- retrieve and manipulate session time and timeout settings
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_SESSION_get_time(const SSL_SESSION *s);
\& long SSL_SESSION_set_time(SSL_SESSION *s, long tm);
\& long SSL_SESSION_get_timeout(const SSL_SESSION *s);
\& long SSL_SESSION_set_timeout(SSL_SESSION *s, long tm);
\&
\& long SSL_get_time(const SSL_SESSION *s);
\& long SSL_set_time(SSL_SESSION *s, long tm);
\& long SSL_get_timeout(const SSL_SESSION *s);
\& long SSL_set_timeout(SSL_SESSION *s, long tm);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_SESSION_get_time()\fR returns the time at which the session \fBs\fR was
established. The time is given in seconds since the Epoch and therefore
compatible to the time delivered by the \fBtime()\fR call.
.PP
\&\fBSSL_SESSION_set_time()\fR replaces the creation time of the session \fBs\fR with
the chosen value \fBtm\fR.
.PP
\&\fBSSL_SESSION_get_timeout()\fR returns the timeout value set for session \fBs\fR
in seconds.
.PP
\&\fBSSL_SESSION_set_timeout()\fR sets the timeout value for session \fBs\fR in seconds
to \fBtm\fR.
.PP
The \fBSSL_get_time()\fR, \fBSSL_set_time()\fR, \fBSSL_get_timeout()\fR, and \fBSSL_set_timeout()\fR
functions are synonyms for the SSL_SESSION_*() counterparts.
.SH NOTES
.IX Header "NOTES"
Sessions are expired by examining the creation time and the timeout value.
Both are set at creation time of the session to the actual time and the
default timeout value at creation, respectively, as set by
\&\fBSSL_CTX_set_timeout\fR\|(3).
Using these functions it is possible to extend or shorten the lifetime
of the session.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_SESSION_get_time()\fR and \fBSSL_SESSION_get_timeout()\fR return the currently
valid values.
.PP
\&\fBSSL_SESSION_set_time()\fR and \fBSSL_SESSION_set_timeout()\fR return 1 on success.
.PP
If any of the function is passed the NULL pointer for the session \fBs\fR,
0 is returned.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_set_timeout\fR\|(3),
\&\fBSSL_get_default_timeout\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
