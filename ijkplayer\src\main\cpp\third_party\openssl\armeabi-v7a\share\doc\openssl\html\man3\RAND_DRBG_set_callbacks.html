<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>RAND_DRBG_set_callbacks</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a>
    <ul>
      <li><a href="#Callback-Functions">Callback Functions</a></li>
    </ul>
  </li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>RAND_DRBG_set_callbacks, RAND_DRBG_get_entropy_fn, RAND_DRBG_cleanup_entropy_fn, RAND_DRBG_get_nonce_fn, RAND_DRBG_cleanup_nonce_fn - set callbacks for reseeding</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/rand_drbg.h&gt;


int RAND_DRBG_set_callbacks(RAND_DRBG *drbg,
                            RAND_DRBG_get_entropy_fn get_entropy,
                            RAND_DRBG_cleanup_entropy_fn cleanup_entropy,
                            RAND_DRBG_get_nonce_fn get_nonce,
                            RAND_DRBG_cleanup_nonce_fn cleanup_nonce);</code></pre>

<h2 id="Callback-Functions">Callback Functions</h2>

<pre><code>typedef size_t (*RAND_DRBG_get_entropy_fn)(
                      RAND_DRBG *drbg,
                      unsigned char **pout,
                      int entropy,
                      size_t min_len, size_t max_len,
                      int prediction_resistance);

typedef void (*RAND_DRBG_cleanup_entropy_fn)(
                    RAND_DRBG *drbg,
                    unsigned char *out, size_t outlen);

typedef size_t (*RAND_DRBG_get_nonce_fn)(
                      RAND_DRBG *drbg,
                      unsigned char **pout,
                      int entropy,
                      size_t min_len, size_t max_len);

typedef void (*RAND_DRBG_cleanup_nonce_fn)(
                    RAND_DRBG *drbg,
                    unsigned char *out, size_t outlen);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>RAND_DRBG_set_callbacks() sets the callbacks for obtaining fresh entropy and the nonce when reseeding the given <b>drbg</b>. The callback functions are implemented and provided by the caller. Their parameter lists need to match the function prototypes above.</p>

<p>Setting the callbacks is allowed only if the DRBG has not been initialized yet. Otherwise, the operation will fail. To change the settings for one of the three shared DRBGs it is necessary to call RAND_DRBG_uninstantiate() first.</p>

<p>The <b>get_entropy</b>() callback is called by the <b>drbg</b> when it requests fresh random input. It is expected that the callback allocates and fills a random buffer of size <b>min_len</b> &lt;= size &lt;= <b>max_len</b> (in bytes) which contains at least <b>entropy</b> bits of randomness. The <b>prediction_resistance</b> flag indicates whether the reseeding was triggered by a prediction resistance request.</p>

<p>The buffer&#39;s address is to be returned in *<b>pout</b> and the number of collected randomness bytes as return value.</p>

<p>If the callback fails to acquire at least <b>entropy</b> bits of randomness, it must indicate an error by returning a buffer length of 0.</p>

<p>If <b>prediction_resistance</b> was requested and the random source of the DRBG does not satisfy the conditions requested by [NIST SP 800-90C], then it must also indicate an error by returning a buffer length of 0. See NOTES section for more details.</p>

<p>The <b>cleanup_entropy</b>() callback is called from the <b>drbg</b> to clear and free the buffer allocated previously by get_entropy(). The values <b>out</b> and <b>outlen</b> are the random buffer&#39;s address and length, as returned by the get_entropy() callback.</p>

<p>The <b>get_nonce</b>() and <b>cleanup_nonce</b>() callbacks are used to obtain a nonce and free it again. A nonce is only required for instantiation (not for reseeding) and only in the case where the DRBG uses a derivation function. The callbacks are analogous to get_entropy() and cleanup_entropy(), except for the missing prediction_resistance flag.</p>

<p>If the derivation function is disabled, then no nonce is used for instantiation, and the <b>get_nonce</b>() and <b>cleanup_nonce</b>() callbacks can be omitted by setting them to NULL.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>RAND_DRBG_set_callbacks() return 1 on success, and 0 on failure</p>

<h1 id="NOTES">NOTES</h1>

<p>It is important that <b>cleanup_entropy</b>() and <b>cleanup_nonce</b>() clear the buffer contents safely before freeing it, in order not to leave sensitive information about the DRBG&#39;s state in memory.</p>

<p>A request for prediction resistance can only be satisfied by pulling fresh entropy from one of the approved entropy sources listed in section 5.5.2 of [NIST SP 800-90C]. Since the default implementation of the get_entropy callback does not have access to such an approved entropy source, a request for prediction resistance will always fail. In other words, prediction resistance is currently not supported yet by the DRBG.</p>

<p>The derivation function is disabled during initialization by calling the RAND_DRBG_set() function with the RAND_DRBG_FLAG_CTR_NO_DF flag. For more information on the derivation function and when it can be omitted, see [NIST SP 800-90A Rev. 1]. Roughly speaking it can be omitted if the random source has &quot;full entropy&quot;, i.e., contains 8 bits of entropy per byte.</p>

<p>Even if a nonce is required, the <b>get_nonce</b>() and <b>cleanup_nonce</b>() callbacks can be omitted by setting them to NULL. In this case the DRBG will automatically request an extra amount of entropy (using the <b>get_entropy</b>() and <b>cleanup_entropy</b>() callbacks) which it will utilize for the nonce, following the recommendations of [NIST SP 800-90A Rev. 1], section 8.6.7.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/RAND_DRBG_new.html">RAND_DRBG_new(3)</a>, <a href="../man3/RAND_DRBG_reseed.html">RAND_DRBG_reseed(3)</a>, <a href="../man7/RAND_DRBG.html">RAND_DRBG(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The RAND_DRBG functions were added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


