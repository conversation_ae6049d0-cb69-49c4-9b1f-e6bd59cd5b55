.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_CIPHER_LIST 3"
.TH SSL_CTX_SET_CIPHER_LIST 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_cipher_list,
SSL_set_cipher_list,
SSL_CTX_set_ciphersuites,
SSL_set_ciphersuites
\&\- choose list of available SSL_CIPHERs
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_set_cipher_list(SSL_CTX *ctx, const char *str);
\& int SSL_set_cipher_list(SSL *ssl, const char *str);
\&
\& int SSL_CTX_set_ciphersuites(SSL_CTX *ctx, const char *str);
\& int SSL_set_ciphersuites(SSL *s, const char *str);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_cipher_list()\fR sets the list of available ciphers (TLSv1.2 and below)
for \fBctx\fR using the control string \fBstr\fR. The format of the string is described
in \fBciphers\fR\|(1). The list of ciphers is inherited by all
\&\fBssl\fR objects created from \fBctx\fR. This function does not impact TLSv1.3
ciphersuites. Use \fBSSL_CTX_set_ciphersuites()\fR to configure those.
.PP
\&\fBSSL_set_cipher_list()\fR sets the list of ciphers (TLSv1.2 and below) only for
\&\fBssl\fR.
.PP
\&\fBSSL_CTX_set_ciphersuites()\fR is used to configure the available TLSv1.3
ciphersuites for \fBctx\fR. This is a simple colon (":") separated list of TLSv1.3
ciphersuite names in order of preference. Valid TLSv1.3 ciphersuite names are:
.IP TLS_AES_128_GCM_SHA256 4
.IX Item "TLS_AES_128_GCM_SHA256"
.PD 0
.IP TLS_AES_256_GCM_SHA384 4
.IX Item "TLS_AES_256_GCM_SHA384"
.IP TLS_CHACHA20_POLY1305_SHA256 4
.IX Item "TLS_CHACHA20_POLY1305_SHA256"
.IP TLS_AES_128_CCM_SHA256 4
.IX Item "TLS_AES_128_CCM_SHA256"
.IP TLS_AES_128_CCM_8_SHA256 4
.IX Item "TLS_AES_128_CCM_8_SHA256"
.PD
.PP
An empty list is permissible. The default value for the this setting is:
.PP
"TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256"
.PP
\&\fBSSL_set_ciphersuites()\fR is the same as \fBSSL_CTX_set_ciphersuites()\fR except it
configures the ciphersuites for \fBssl\fR.
.SH NOTES
.IX Header "NOTES"
The control string \fBstr\fR for \fBSSL_CTX_set_cipher_list()\fR and
\&\fBSSL_set_cipher_list()\fR should be universally usable and not depend
on details of the library configuration (ciphers compiled in). Thus no
syntax checking takes place. Items that are not recognized, because the
corresponding ciphers are not compiled in or because they are mistyped,
are simply ignored. Failure is only flagged if no ciphers could be collected
at all.
.PP
It should be noted, that inclusion of a cipher to be used into the list is
a necessary condition. On the client side, the inclusion into the list is
also sufficient unless the security level excludes it. On the server side,
additional restrictions apply. All ciphers have additional requirements.
ADH ciphers don't need a certificate, but DH-parameters must have been set.
All other ciphers need a corresponding certificate and key.
.PP
A RSA cipher can only be chosen, when a RSA certificate is available.
RSA ciphers using DHE need a certificate and key and additional DH-parameters
(see \fBSSL_CTX_set_tmp_dh_callback\fR\|(3)).
.PP
A DSA cipher can only be chosen, when a DSA certificate is available.
DSA ciphers always use DH key exchange and therefore need DH-parameters
(see \fBSSL_CTX_set_tmp_dh_callback\fR\|(3)).
.PP
When these conditions are not met for any cipher in the list (e.g. a
client only supports export RSA ciphers with an asymmetric key length
of 512 bits and the server is not configured to use temporary RSA
keys), the "no shared cipher" (SSL_R_NO_SHARED_CIPHER) error is generated
and the handshake will fail.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_cipher_list()\fR and \fBSSL_set_cipher_list()\fR return 1 if any cipher
could be selected and 0 on complete failure.
.PP
\&\fBSSL_CTX_set_ciphersuites()\fR and \fBSSL_set_ciphersuites()\fR return 1 if the requested
ciphersuite list was configured, and 0 otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_get_ciphers\fR\|(3),
\&\fBSSL_CTX_use_certificate\fR\|(3),
\&\fBSSL_CTX_set_tmp_dh_callback\fR\|(3),
\&\fBciphers\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
