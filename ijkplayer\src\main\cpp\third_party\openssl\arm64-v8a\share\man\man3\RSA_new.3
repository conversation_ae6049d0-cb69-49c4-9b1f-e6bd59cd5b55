.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_NEW 3"
.TH RSA_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_new, RSA_free \- allocate and free RSA objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
\&
\& RSA *RSA_new(void);
\&
\& void RSA_free(RSA *rsa);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBRSA_new()\fR allocates and initializes an \fBRSA\fR structure. It is equivalent to
calling RSA_new_method(NULL).
.PP
\&\fBRSA_free()\fR frees the \fBRSA\fR structure and its components. The key is
erased before the memory is returned to the system.
If \fBrsa\fR is NULL nothing is done.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If the allocation fails, \fBRSA_new()\fR returns \fBNULL\fR and sets an error
code that can be obtained by \fBERR_get_error\fR\|(3). Otherwise it returns
a pointer to the newly allocated structure.
.PP
\&\fBRSA_free()\fR returns no value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBRSA_generate_key\fR\|(3),
\&\fBRSA_new_method\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
