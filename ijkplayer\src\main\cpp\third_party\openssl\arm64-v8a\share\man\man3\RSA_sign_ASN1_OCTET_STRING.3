.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_SIGN_ASN1_OCTET_STRING 3"
.TH RSA_SIGN_ASN1_OCTET_STRING 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_sign_ASN1_OCTET_STRING, RSA_verify_ASN1_OCTET_STRING \- RSA signatures
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
\&
\& int RSA_sign_ASN1_OCTET_STRING(int dummy, unsigned char *m,
\&                                unsigned int m_len, unsigned char *sigret,
\&                                unsigned int *siglen, RSA *rsa);
\&
\& int RSA_verify_ASN1_OCTET_STRING(int dummy, unsigned char *m,
\&                                  unsigned int m_len, unsigned char *sigbuf,
\&                                  unsigned int siglen, RSA *rsa);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBRSA_sign_ASN1_OCTET_STRING()\fR signs the octet string \fBm\fR of size
\&\fBm_len\fR using the private key \fBrsa\fR represented in DER using PKCS #1
padding. It stores the signature in \fBsigret\fR and the signature size
in \fBsiglen\fR. \fBsigret\fR must point to \fBRSA_size(rsa)\fR bytes of
memory.
.PP
\&\fBdummy\fR is ignored.
.PP
The random number generator must be seeded when calling
\&\fBRSA_sign_ASN1_OCTET_STRING()\fR.
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see \fBRAND\fR\|(7)), the operation will fail.
.PP
\&\fBRSA_verify_ASN1_OCTET_STRING()\fR verifies that the signature \fBsigbuf\fR
of size \fBsiglen\fR is the DER representation of a given octet string
\&\fBm\fR of size \fBm_len\fR. \fBdummy\fR is ignored. \fBrsa\fR is the signer's
public key.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRSA_sign_ASN1_OCTET_STRING()\fR returns 1 on success, 0 otherwise.
\&\fBRSA_verify_ASN1_OCTET_STRING()\fR returns 1 on successful verification, 0
otherwise.
.PP
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH BUGS
.IX Header "BUGS"
These functions serve no recognizable purpose.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBRAND_bytes\fR\|(3), \fBRSA_sign\fR\|(3),
\&\fBRSA_verify\fR\|(3),
\&\fBRAND\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
