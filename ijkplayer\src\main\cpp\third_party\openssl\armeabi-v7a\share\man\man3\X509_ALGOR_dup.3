.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_ALGOR_DUP 3"
.TH X509_ALGOR_DUP 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_ALGOR_dup, X509_ALGOR_set0, X509_ALGOR_get0, X509_ALGOR_set_md, X509_ALGOR_cmp, X509_ALGOR_copy \- AlgorithmIdentifier functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& X509_ALGOR *X509_ALGOR_dup(X509_ALGOR *alg);
\& int X509_ALGOR_set0(X509_ALGOR *alg, ASN1_OBJECT *aobj, int ptype, void *pval);
\& void X509_ALGOR_get0(const ASN1_OBJECT **paobj, int *pptype,
\&                      const void **ppval, const X509_ALGOR *alg);
\& void X509_ALGOR_set_md(X509_ALGOR *alg, const EVP_MD *md);
\& int X509_ALGOR_cmp(const X509_ALGOR *a, const X509_ALGOR *b);
\& int X509_ALGOR_copy(X509_ALGOR *dest, const X509_ALGOR *src);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_ALGOR_dup()\fR returns a copy of \fBalg\fR.
.PP
\&\fBX509_ALGOR_set0()\fR sets the algorithm OID of \fBalg\fR to \fBaobj\fR and the
associated parameter type to \fBptype\fR with value \fBpval\fR. If \fBptype\fR is
\&\fBV_ASN1_UNDEF\fR the parameter is omitted, otherwise \fBptype\fR and \fBpval\fR have
the same meaning as the \fBtype\fR and \fBvalue\fR parameters to \fBASN1_TYPE_set()\fR.
All the supplied parameters are used internally so must \fBNOT\fR be freed after
this call.
.PP
\&\fBX509_ALGOR_get0()\fR is the inverse of \fBX509_ALGOR_set0()\fR: it returns the
algorithm OID in \fB*paobj\fR and the associated parameter in \fB*pptype\fR
and \fB*ppval\fR from the \fBAlgorithmIdentifier\fR \fBalg\fR.
.PP
\&\fBX509_ALGOR_set_md()\fR sets the \fBAlgorithmIdentifier\fR \fBalg\fR to appropriate
values for the message digest \fBmd\fR.
.PP
\&\fBX509_ALGOR_cmp()\fR compares \fBa\fR and \fBb\fR and returns 0 if they have identical
encodings and nonzero otherwise.
.PP
\&\fBX509_ALGOR_copy()\fR copies the source values into the dest structs; making
a duplicate of each (and free any thing pointed to from within *dest).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_ALGOR_dup()\fR returns a valid \fBX509_ALGOR\fR structure or NULL if an error
occurred.
.PP
\&\fBX509_ALGOR_set0()\fR and \fBX509_ALGOR_copy()\fR return 1 on success or 0 on error.
.PP
\&\fBX509_ALGOR_get0()\fR and \fBX509_ALGOR_set_md()\fR return no values.
.PP
\&\fBX509_ALGOR_cmp()\fR returns 0 if the two parameters have identical encodings and
nonzero otherwise.
.SH HISTORY
.IX Header "HISTORY"
The \fBX509_ALGOR_copy()\fR was added in 1.1.1e.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
