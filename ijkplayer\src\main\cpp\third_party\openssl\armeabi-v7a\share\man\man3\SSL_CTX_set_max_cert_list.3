.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_MAX_CERT_LIST 3"
.TH SSL_CTX_SET_MAX_CERT_LIST 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_max_cert_list, SSL_CTX_get_max_cert_list, SSL_set_max_cert_list, SSL_get_max_cert_list \- manipulate allowed size for the peer's certificate chain
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_set_max_cert_list(SSL_CTX *ctx, long size);
\& long SSL_CTX_get_max_cert_list(SSL_CTX *ctx);
\&
\& long SSL_set_max_cert_list(SSL *ssl, long size);
\& long SSL_get_max_cert_list(SSL *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_max_cert_list()\fR sets the maximum size allowed for the peer's
certificate chain for all SSL objects created from \fBctx\fR to be <size> bytes.
The SSL objects inherit the setting valid for \fBctx\fR at the time
\&\fBSSL_new\fR\|(3) is being called.
.PP
\&\fBSSL_CTX_get_max_cert_list()\fR returns the currently set maximum size for \fBctx\fR.
.PP
\&\fBSSL_set_max_cert_list()\fR sets the maximum size allowed for the peer's
certificate chain for \fBssl\fR to be <size> bytes. This setting stays valid
until a new value is set.
.PP
\&\fBSSL_get_max_cert_list()\fR returns the currently set maximum size for \fBssl\fR.
.SH NOTES
.IX Header "NOTES"
During the handshake process, the peer may send a certificate chain.
The TLS/SSL standard does not give any maximum size of the certificate chain.
The OpenSSL library handles incoming data by a dynamically allocated buffer.
In order to prevent this buffer from growing without bounds due to data
received from a faulty or malicious peer, a maximum size for the certificate
chain is set.
.PP
The default value for the maximum certificate chain size is 100kB (30kB
on the 16\-bit DOS platform). This should be sufficient for usual certificate
chains (OpenSSL's default maximum chain length is 10, see
\&\fBSSL_CTX_set_verify\fR\|(3), and certificates
without special extensions have a typical size of 1\-2kB).
.PP
For special applications it can be necessary to extend the maximum certificate
chain size allowed to be sent by the peer, see e.g. the work on
"Internet X.509 Public Key Infrastructure Proxy Certificate Profile"
and "TLS Delegation Protocol" at http://www.ietf.org/ and
http://www.globus.org/ .
.PP
Under normal conditions it should never be necessary to set a value smaller
than the default, as the buffer is handled dynamically and only uses the
memory actually required by the data sent by the peer.
.PP
If the maximum certificate chain size allowed is exceeded, the handshake will
fail with a SSL_R_EXCESSIVE_MESSAGE_SIZE error.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_max_cert_list()\fR and \fBSSL_set_max_cert_list()\fR return the previously
set value.
.PP
\&\fBSSL_CTX_get_max_cert_list()\fR and \fBSSL_get_max_cert_list()\fR return the currently
set value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_new\fR\|(3),
\&\fBSSL_CTX_set_verify\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
