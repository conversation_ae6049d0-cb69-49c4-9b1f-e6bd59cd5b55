<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CTX_set_cert_verify_callback</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#WARNINGS">WARNINGS</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CTX_set_cert_verify_callback - set peer certificate verification procedure</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

void SSL_CTX_set_cert_verify_callback(SSL_CTX *ctx,
                                      int (*callback)(X509_STORE_CTX *, void *),
                                      void *arg);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_CTX_set_cert_verify_callback() sets the verification callback function for <i>ctx</i>. SSL objects that are created from <i>ctx</i> inherit the setting valid at the time when <a href="../man3/SSL_new.html">SSL_new(3)</a> is called.</p>

<h1 id="NOTES">NOTES</h1>

<p>Whenever a certificate is verified during a SSL/TLS handshake, a verification function is called. If the application does not explicitly specify a verification callback function, the built-in verification function is used. If a verification callback <i>callback</i> is specified via SSL_CTX_set_cert_verify_callback(), the supplied callback function is called instead. By setting <i>callback</i> to NULL, the default behaviour is restored.</p>

<p>When the verification must be performed, <i>callback</i> will be called with the arguments callback(X509_STORE_CTX *x509_store_ctx, void *arg). The argument <i>arg</i> is specified by the application when setting <i>callback</i>.</p>

<p><i>callback</i> should return 1 to indicate verification success and 0 to indicate verification failure. If SSL_VERIFY_PEER is set and <i>callback</i> returns 0, the handshake will fail. As the verification procedure may allow the connection to continue in the case of failure (by always returning 1) the verification result must be set in any case using the <b>error</b> member of <i>x509_store_ctx</i> so that the calling application will be informed about the detailed result of the verification procedure!</p>

<p>Within <i>x509_store_ctx</i>, <i>callback</i> has access to the <i>verify_callback</i> function set using <a href="../man3/SSL_CTX_set_verify.html">SSL_CTX_set_verify(3)</a>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_CTX_set_cert_verify_callback() does not return a value.</p>

<h1 id="WARNINGS">WARNINGS</h1>

<p>Do not mix the verification callback described in this function with the <b>verify_callback</b> function called during the verification process. The latter is set using the <a href="../man3/SSL_CTX_set_verify.html">SSL_CTX_set_verify(3)</a> family of functions.</p>

<p>Providing a complete verification procedure including certificate purpose settings etc is a complex task. The built-in procedure is quite powerful and in most cases it should be sufficient to modify its behaviour using the <b>verify_callback</b> function.</p>

<h1 id="BUGS">BUGS</h1>

<p>SSL_CTX_set_cert_verify_callback() does not provide diagnostic information.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CTX_set_verify.html">SSL_CTX_set_verify(3)</a>, <a href="../man3/SSL_get_verify_result.html">SSL_get_verify_result(3)</a>, <a href="../man3/SSL_CTX_load_verify_locations.html">SSL_CTX_load_verify_locations(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2001-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


