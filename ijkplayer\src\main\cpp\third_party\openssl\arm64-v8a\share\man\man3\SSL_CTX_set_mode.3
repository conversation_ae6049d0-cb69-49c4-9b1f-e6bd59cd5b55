.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_MODE 3"
.TH SSL_CTX_SET_MODE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_mode, SSL_CTX_clear_mode, SSL_set_mode, SSL_clear_mode, SSL_CTX_get_mode, SSL_get_mode \- manipulate SSL engine mode
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_set_mode(SSL_CTX *ctx, long mode);
\& long SSL_CTX_clear_mode(SSL_CTX *ctx, long mode);
\& long SSL_set_mode(SSL *ssl, long mode);
\& long SSL_clear_mode(SSL *ssl, long mode);
\&
\& long SSL_CTX_get_mode(SSL_CTX *ctx);
\& long SSL_get_mode(SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_mode()\fR adds the mode set via bit mask in \fBmode\fR to \fBctx\fR.
Options already set before are not cleared.
\&\fBSSL_CTX_clear_mode()\fR removes the mode set via bit mask in \fBmode\fR from \fBctx\fR.
.PP
\&\fBSSL_set_mode()\fR adds the mode set via bit mask in \fBmode\fR to \fBssl\fR.
Options already set before are not cleared.
\&\fBSSL_clear_mode()\fR removes the mode set via bit mask in \fBmode\fR from \fBssl\fR.
.PP
\&\fBSSL_CTX_get_mode()\fR returns the mode set for \fBctx\fR.
.PP
\&\fBSSL_get_mode()\fR returns the mode set for \fBssl\fR.
.SH NOTES
.IX Header "NOTES"
The following mode changes are available:
.IP SSL_MODE_ENABLE_PARTIAL_WRITE 4
.IX Item "SSL_MODE_ENABLE_PARTIAL_WRITE"
Allow SSL_write_ex(..., n, &r) to return with 0 < r < n (i.e. report success
when just a single record has been written). This works in a similar way for
\&\fBSSL_write()\fR. When not set (the default), \fBSSL_write_ex()\fR or \fBSSL_write()\fR will only
report success once the complete chunk was written. Once \fBSSL_write_ex()\fR or
\&\fBSSL_write()\fR returns successful, \fBr\fR bytes have been written and the next call
to \fBSSL_write_ex()\fR or \fBSSL_write()\fR must only send the n\-r bytes left, imitating
the behaviour of \fBwrite()\fR.
.IP SSL_MODE_ACCEPT_MOVING_WRITE_BUFFER 4
.IX Item "SSL_MODE_ACCEPT_MOVING_WRITE_BUFFER"
Make it possible to retry \fBSSL_write_ex()\fR or \fBSSL_write()\fR with changed buffer
location (the buffer contents must stay the same). This is not the default to
avoid the misconception that nonblocking \fBSSL_write()\fR behaves like
nonblocking \fBwrite()\fR.
.IP SSL_MODE_AUTO_RETRY 4
.IX Item "SSL_MODE_AUTO_RETRY"
During normal operations, non-application data records might need to be sent or
received that the application is not aware of.
If a non-application data record was processed,
\&\fBSSL_read_ex\fR\|(3) and \fBSSL_read\fR\|(3) can return with a failure and indicate the
need to retry with \fBSSL_ERROR_WANT_READ\fR.
If such a non-application data record was processed, the flag
\&\fBSSL_MODE_AUTO_RETRY\fR causes it to try to process the next record instead of
returning.
.Sp
In a nonblocking environment applications must be prepared to handle
incomplete read/write operations.
Setting \fBSSL_MODE_AUTO_RETRY\fR for a nonblocking \fBBIO\fR will process
non-application data records until either no more data is available or
an application data record has been processed.
.Sp
In a blocking environment, applications are not always prepared to
deal with the functions returning intermediate reports such as retry
requests, and setting the \fBSSL_MODE_AUTO_RETRY\fR flag will cause the functions
to only return after successfully processing an application data record or a
failure.
.Sp
Turning off \fBSSL_MODE_AUTO_RETRY\fR can be useful with blocking \fBBIO\fRs in case
they are used in combination with something like \fBselect()\fR or \fBpoll()\fR.
Otherwise the call to \fBSSL_read()\fR or \fBSSL_read_ex()\fR might hang when a
non-application record was sent and no application data was sent.
.IP SSL_MODE_RELEASE_BUFFERS 4
.IX Item "SSL_MODE_RELEASE_BUFFERS"
When we no longer need a read buffer or a write buffer for a given SSL,
then release the memory we were using to hold it.
Using this flag can
save around 34k per idle SSL connection.
This flag has no effect on SSL v2 connections, or on DTLS connections.
.IP SSL_MODE_SEND_FALLBACK_SCSV 4
.IX Item "SSL_MODE_SEND_FALLBACK_SCSV"
Send TLS_FALLBACK_SCSV in the ClientHello.
To be set only by applications that reconnect with a downgraded protocol
version; see draft\-ietf\-tls\-downgrade\-scsv\-00 for details.
.Sp
DO NOT ENABLE THIS if your application attempts a normal handshake.
Only use this in explicit fallback retries, following the guidance
in draft\-ietf\-tls\-downgrade\-scsv\-00.
.IP SSL_MODE_ASYNC 4
.IX Item "SSL_MODE_ASYNC"
Enable asynchronous processing. TLS I/O operations may indicate a retry with
SSL_ERROR_WANT_ASYNC with this mode set if an asynchronous capable engine is
used to perform cryptographic operations. See \fBSSL_get_error\fR\|(3).
.IP SSL_MODE_DTLS_SCTP_LABEL_LENGTH_BUG 4
.IX Item "SSL_MODE_DTLS_SCTP_LABEL_LENGTH_BUG"
Older versions of OpenSSL had a bug in the computation of the label length
used for computing the endpoint-pair shared secret. The bug was that the
terminating zero was included in the length of the label. Setting this option
enables this behaviour to allow interoperability with such broken
implementations. Please note that setting this option breaks interoperability
with correct implementations. This option only applies to DTLS over SCTP.
.PP
All modes are off by default except for SSL_MODE_AUTO_RETRY which is on by
default since 1.1.1.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_mode()\fR and \fBSSL_set_mode()\fR return the new mode bit mask
after adding \fBmode\fR.
.PP
\&\fBSSL_CTX_get_mode()\fR and \fBSSL_get_mode()\fR return the current bit mask.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_read_ex\fR\|(3), \fBSSL_read\fR\|(3), \fBSSL_write_ex\fR\|(3) or
\&\fBSSL_write\fR\|(3), \fBSSL_get_error\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
SSL_MODE_ASYNC was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
