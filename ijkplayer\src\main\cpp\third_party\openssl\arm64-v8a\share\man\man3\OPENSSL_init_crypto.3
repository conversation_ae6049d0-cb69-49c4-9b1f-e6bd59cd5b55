.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_INIT_CRYPTO 3"
.TH OPENSSL_INIT_CRYPTO 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_INIT_new, OPENSSL_INIT_set_config_filename,
OPENSSL_INIT_set_config_appname, OPENSSL_INIT_set_config_file_flags,
OPENSSL_INIT_free, OPENSSL_init_crypto, OPENSSL_cleanup, OPENSSL_atexit,
OPENSSL_thread_stop \- OpenSSL initialisation
and deinitialisation functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crypto.h>
\&
\& void OPENSSL_cleanup(void);
\& int OPENSSL_init_crypto(uint64_t opts, const OPENSSL_INIT_SETTINGS *settings);
\& int OPENSSL_atexit(void (*handler)(void));
\& void OPENSSL_thread_stop(void);
\&
\& OPENSSL_INIT_SETTINGS *OPENSSL_INIT_new(void);
\& int OPENSSL_INIT_set_config_filename(OPENSSL_INIT_SETTINGS *init,
\&                                      const char* filename);
\& int OPENSSL_INIT_set_config_file_flags(OPENSSL_INIT_SETTINGS *init,
\&                                        unsigned long flags);
\& int OPENSSL_INIT_set_config_appname(OPENSSL_INIT_SETTINGS *init,
\&                                     const char* name);
\& void OPENSSL_INIT_free(OPENSSL_INIT_SETTINGS *init);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
During normal operation OpenSSL (libcrypto) will allocate various resources at
start up that must, subsequently, be freed on close down of the library.
Additionally some resources are allocated on a per thread basis (if the
application is multi-threaded), and these resources must be freed prior to the
thread closing.
.PP
As of version 1.1.0 OpenSSL will automatically allocate all resources that it
needs so no explicit initialisation is required. Similarly it will also
automatically deinitialise as required.
.PP
However, there may be situations when explicit initialisation is desirable or
needed, for example when some nondefault initialisation is required. The
function \fBOPENSSL_init_crypto()\fR can be used for this purpose for
libcrypto (see also \fBOPENSSL_init_ssl\fR\|(3) for the libssl
equivalent).
.PP
Numerous internal OpenSSL functions call \fBOPENSSL_init_crypto()\fR.
Therefore, in order to perform nondefault initialisation,
\&\fBOPENSSL_init_crypto()\fR MUST be called by application code prior to
any other OpenSSL function calls.
.PP
The \fBopts\fR parameter specifies which aspects of libcrypto should be
initialised. Valid options are:
.IP OPENSSL_INIT_NO_LOAD_CRYPTO_STRINGS 4
.IX Item "OPENSSL_INIT_NO_LOAD_CRYPTO_STRINGS"
Suppress automatic loading of the libcrypto error strings. This option is
not a default option. Once selected subsequent calls to
\&\fBOPENSSL_init_crypto()\fR with the option
\&\fBOPENSSL_INIT_LOAD_CRYPTO_STRINGS\fR will be ignored.
.IP OPENSSL_INIT_LOAD_CRYPTO_STRINGS 4
.IX Item "OPENSSL_INIT_LOAD_CRYPTO_STRINGS"
Automatic loading of the libcrypto error strings. With this option the
library will automatically load the libcrypto error strings.
This option is a default option. Once selected subsequent calls to
\&\fBOPENSSL_init_crypto()\fR with the option
\&\fBOPENSSL_INIT_NO_LOAD_CRYPTO_STRINGS\fR will be ignored.
.IP OPENSSL_INIT_ADD_ALL_CIPHERS 4
.IX Item "OPENSSL_INIT_ADD_ALL_CIPHERS"
With this option the library will automatically load and make available all
libcrypto ciphers. This option is a default option. Once selected subsequent
calls to \fBOPENSSL_init_crypto()\fR with the option
\&\fBOPENSSL_INIT_NO_ADD_ALL_CIPHERS\fR will be ignored.
.IP OPENSSL_INIT_ADD_ALL_DIGESTS 4
.IX Item "OPENSSL_INIT_ADD_ALL_DIGESTS"
With this option the library will automatically load and make available all
libcrypto digests. This option is a default option. Once selected subsequent
calls to \fBOPENSSL_init_crypto()\fR with the option
\&\fBOPENSSL_INIT_NO_ADD_ALL_DIGESTS\fR will be ignored.
.IP OPENSSL_INIT_NO_ADD_ALL_CIPHERS 4
.IX Item "OPENSSL_INIT_NO_ADD_ALL_CIPHERS"
With this option the library will suppress automatic loading of libcrypto
ciphers. This option is not a default option. Once selected subsequent
calls to \fBOPENSSL_init_crypto()\fR with the option
\&\fBOPENSSL_INIT_ADD_ALL_CIPHERS\fR will be ignored.
.IP OPENSSL_INIT_NO_ADD_ALL_DIGESTS 4
.IX Item "OPENSSL_INIT_NO_ADD_ALL_DIGESTS"
With this option the library will suppress automatic loading of libcrypto
digests. This option is not a default option. Once selected subsequent
calls to \fBOPENSSL_init_crypto()\fR with the option
\&\fBOPENSSL_INIT_ADD_ALL_DIGESTS\fR will be ignored.
.IP OPENSSL_INIT_LOAD_CONFIG 4
.IX Item "OPENSSL_INIT_LOAD_CONFIG"
With this option an OpenSSL configuration file will be automatically loaded and
used by calling \fBOPENSSL_config()\fR. This is not a default option for libcrypto.
As of OpenSSL 1.1.1 this is a default option for libssl (see
\&\fBOPENSSL_init_ssl\fR\|(3) for further details about libssl initialisation). See the
description of \fBOPENSSL_INIT_new()\fR, below.
.IP OPENSSL_INIT_NO_LOAD_CONFIG 4
.IX Item "OPENSSL_INIT_NO_LOAD_CONFIG"
With this option the loading of OpenSSL configuration files will be suppressed.
It is the equivalent of calling \fBOPENSSL_no_config()\fR. This is not a default
option.
.IP OPENSSL_INIT_ASYNC 4
.IX Item "OPENSSL_INIT_ASYNC"
With this option the library with automatically initialise the libcrypto async
sub-library (see \fBASYNC_start_job\fR\|(3)). This is a default option.
.IP OPENSSL_INIT_ENGINE_RDRAND 4
.IX Item "OPENSSL_INIT_ENGINE_RDRAND"
With this option the library will automatically load and initialise the
RDRAND engine (if available). This not a default option.
.IP OPENSSL_INIT_ENGINE_DYNAMIC 4
.IX Item "OPENSSL_INIT_ENGINE_DYNAMIC"
With this option the library will automatically load and initialise the
dynamic engine. This not a default option.
.IP OPENSSL_INIT_ENGINE_OPENSSL 4
.IX Item "OPENSSL_INIT_ENGINE_OPENSSL"
With this option the library will automatically load and initialise the
openssl engine. This not a default option.
.IP OPENSSL_INIT_ENGINE_CRYPTODEV 4
.IX Item "OPENSSL_INIT_ENGINE_CRYPTODEV"
With this option the library will automatically load and initialise the
cryptodev engine (if available). This not a default option.
.IP OPENSSL_INIT_ENGINE_CAPI 4
.IX Item "OPENSSL_INIT_ENGINE_CAPI"
With this option the library will automatically load and initialise the
CAPI engine (if available). This not a default option.
.IP OPENSSL_INIT_ENGINE_PADLOCK 4
.IX Item "OPENSSL_INIT_ENGINE_PADLOCK"
With this option the library will automatically load and initialise the
padlock engine (if available). This not a default option.
.IP OPENSSL_INIT_ENGINE_AFALG 4
.IX Item "OPENSSL_INIT_ENGINE_AFALG"
With this option the library will automatically load and initialise the
AFALG engine. This not a default option.
.IP OPENSSL_INIT_ENGINE_ALL_BUILTIN 4
.IX Item "OPENSSL_INIT_ENGINE_ALL_BUILTIN"
With this option the library will automatically load and initialise all the
built in engines listed above with the exception of the openssl and afalg
engines. This not a default option.
.IP OPENSSL_INIT_ATFORK 4
.IX Item "OPENSSL_INIT_ATFORK"
With this option the library will register its fork handlers.
See \fBOPENSSL_fork_prepare\fR\|(3) for details.
.IP OPENSSL_INIT_NO_ATEXIT 4
.IX Item "OPENSSL_INIT_NO_ATEXIT"
By default OpenSSL will attempt to clean itself up when the process exits via an
"atexit" handler. Using this option suppresses that behaviour. This means that
the application will have to clean up OpenSSL explicitly using
\&\fBOPENSSL_cleanup()\fR.
.PP
Multiple options may be combined together in a single call to
\&\fBOPENSSL_init_crypto()\fR. For example:
.PP
.Vb 2
\& OPENSSL_init_crypto(OPENSSL_INIT_NO_ADD_ALL_CIPHERS
\&                     | OPENSSL_INIT_NO_ADD_ALL_DIGESTS, NULL);
.Ve
.PP
The \fBOPENSSL_cleanup()\fR function deinitialises OpenSSL (both libcrypto
and libssl). All resources allocated by OpenSSL are freed. Typically there
should be no need to call this function directly as it is initiated
automatically on application exit. This is done via the standard C library
\&\fBatexit()\fR function. In the event that the application will close in a manner
that will not call the registered \fBatexit()\fR handlers then the application should
call \fBOPENSSL_cleanup()\fR directly. Developers of libraries using OpenSSL
are discouraged from calling this function and should instead, typically, rely
on auto-deinitialisation. This is to avoid error conditions where both an
application and a library it depends on both use OpenSSL, and the library
deinitialises it before the application has finished using it.
.PP
Once \fBOPENSSL_cleanup()\fR has been called the library cannot be reinitialised.
Attempts to call \fBOPENSSL_init_crypto()\fR will fail and an ERR_R_INIT_FAIL error
will be added to the error stack. Note that because initialisation has failed
OpenSSL error strings will not be available, only an error code. This code can
be put through the openssl errstr command line application to produce a human
readable error (see \fBerrstr\fR\|(1)).
.PP
The \fBOPENSSL_atexit()\fR function enables the registration of a
function to be called during \fBOPENSSL_cleanup()\fR. Stop handlers are
called after deinitialisation of resources local to a thread, but before other
process wide resources are freed. In the event that multiple stop handlers are
registered, no guarantees are made about the order of execution.
.PP
The \fBOPENSSL_thread_stop()\fR function deallocates resources associated
with the current thread. Typically this function will be called automatically by
the library when the thread exits. This should only be called directly if
resources should be freed at an earlier time, or under the circumstances
described in the NOTES section below.
.PP
The \fBOPENSSL_INIT_LOAD_CONFIG\fR flag will load a configuration file, as with
\&\fBCONF_modules_load_file\fR\|(3) with NULL filename and application name and the
\&\fBCONF_MFLAGS_IGNORE_MISSING_FILE\fR, \fBCONF_MFLAGS_IGNORE_RETURN_CODES\fR  and
\&\fBCONF_MFLAGS_DEFAULT_SECTION\fR flags.
The filename, application name, and flags can be customized by providing a
non-null \fBOPENSSL_INIT_SETTINGS\fR object.
The object can be allocated via \fBOPENSSL_init_new()\fR.
The \fBOPENSSL_INIT_set_config_filename()\fR function can be used to specify a
nondefault filename, which is copied and need not refer to persistent storage.
Similarly, \fBOPENSSL_INIT_set_config_appname()\fR can be used to specify a
nondefault application name.
Finally, OPENSSL_INIT_set_file_flags can be used to specify nondefault flags.
If the \fBCONF_MFLAGS_IGNORE_RETURN_CODES\fR flag is not included, any errors in
the configuration file will cause an error return from \fBOPENSSL_init_crypto\fR
or indirectly \fBOPENSSL_init_ssl\fR\|(3).
The object can be released with \fBOPENSSL_INIT_free()\fR when done.
.SH NOTES
.IX Header "NOTES"
Resources local to a thread are deallocated automatically when the thread exits
(e.g. in a pthreads environment, when \fBpthread_exit()\fR is called). On Windows
platforms this is done in response to a DLL_THREAD_DETACH message being sent to
the libcrypto32.dll entry point. Some windows functions may cause threads to exit
without sending this message (for example \fBExitProcess()\fR). If the application
uses such functions, then the application must free up OpenSSL resources
directly via a call to \fBOPENSSL_thread_stop()\fR on each thread. Similarly this
message will also not be sent if OpenSSL is linked statically, and therefore
applications using static linking should also call \fBOPENSSL_thread_stop()\fR on each
thread. Additionally if OpenSSL is loaded dynamically via \fBLoadLibrary()\fR and the
threads are not destroyed until after \fBFreeLibrary()\fR is called then each thread
should call \fBOPENSSL_thread_stop()\fR prior to the \fBFreeLibrary()\fR call.
.PP
On Linux/Unix where OpenSSL has been loaded via \fBdlopen()\fR and the application is
multi-threaded and if \fBdlclose()\fR is subsequently called prior to the threads
being destroyed then OpenSSL will not be able to deallocate resources associated
with those threads. The application should either call \fBOPENSSL_thread_stop()\fR on
each thread prior to the \fBdlclose()\fR call, or alternatively the original \fBdlopen()\fR
call should use the RTLD_NODELETE flag (where available on the platform).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The functions OPENSSL_init_crypto, \fBOPENSSL_atexit()\fR and
\&\fBOPENSSL_INIT_set_config_appname()\fR return 1 on success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOPENSSL_init_ssl\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBOPENSSL_init_crypto()\fR, \fBOPENSSL_cleanup()\fR, \fBOPENSSL_atexit()\fR,
\&\fBOPENSSL_thread_stop()\fR, \fBOPENSSL_INIT_new()\fR, \fBOPENSSL_INIT_set_config_appname()\fR
and \fBOPENSSL_INIT_free()\fR functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
