.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_CT_VALIDATION_CALLBACK 3"
.TH SSL_CTX_SET_CT_VALIDATION_CALLBACK 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ssl_ct_validation_cb,
SSL_enable_ct, SSL_CTX_enable_ct, SSL_disable_ct, SSL_CTX_disable_ct,
SSL_set_ct_validation_callback, SSL_CTX_set_ct_validation_callback,
SSL_ct_is_enabled, SSL_CTX_ct_is_enabled \-
control Certificate Transparency policy
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& typedef int (*ssl_ct_validation_cb)(const CT_POLICY_EVAL_CTX *ctx,
\&                                    const STACK_OF(SCT) *scts, void *arg);
\&
\& int SSL_enable_ct(SSL *s, int validation_mode);
\& int SSL_CTX_enable_ct(SSL_CTX *ctx, int validation_mode);
\& int SSL_set_ct_validation_callback(SSL *s, ssl_ct_validation_cb callback,
\&                                    void *arg);
\& int SSL_CTX_set_ct_validation_callback(SSL_CTX *ctx,
\&                                        ssl_ct_validation_cb callback,
\&                                        void *arg);
\& void SSL_disable_ct(SSL *s);
\& void SSL_CTX_disable_ct(SSL_CTX *ctx);
\& int SSL_ct_is_enabled(const SSL *s);
\& int SSL_CTX_ct_is_enabled(const SSL_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_enable_ct()\fR and \fBSSL_CTX_enable_ct()\fR enable the processing of signed
certificate timestamps (SCTs) either for a given SSL connection or for all
connections that share the given SSL context, respectively.
This is accomplished by setting a built-in CT validation callback.
The behaviour of the callback is determined by the \fBvalidation_mode\fR argument,
which can be either of \fBSSL_CT_VALIDATION_PERMISSIVE\fR or
\&\fBSSL_CT_VALIDATION_STRICT\fR as described below.
.PP
If \fBvalidation_mode\fR is equal to \fBSSL_CT_VALIDATION_STRICT\fR, then in a full
TLS handshake with the verification mode set to \fBSSL_VERIFY_PEER\fR, if the peer
presents no valid SCTs the handshake will be aborted.
If the verification mode is \fBSSL_VERIFY_NONE\fR, the handshake will continue
despite lack of valid SCTs.
However, in that case if the verification status before the built-in callback
was \fBX509_V_OK\fR it will be set to \fBX509_V_ERR_NO_VALID_SCTS\fR after the
callback.
Applications can call \fBSSL_get_verify_result\fR\|(3) to check the status at
handshake completion, even after session resumption since the verification
status is part of the saved session state.
See \fBSSL_set_verify\fR\|(3), <\fBSSL_get_verify_result\fR\|(3)>, \fBSSL_session_reused\fR\|(3).
.PP
If \fBvalidation_mode\fR is equal to \fBSSL_CT_VALIDATION_PERMISSIVE\fR, then the
handshake continues, and the verification status is not modified, regardless of
the validation status of any SCTs.
The application can still inspect the validation status of the SCTs at
handshake completion.
Note that with session resumption there will not be any SCTs presented during
the handshake.
Therefore, in applications that delay SCT policy enforcement until after
handshake completion, such delayed SCT checks should only be performed when the
session is not resumed.
.PP
\&\fBSSL_set_ct_validation_callback()\fR and \fBSSL_CTX_set_ct_validation_callback()\fR
register a custom callback that may implement a different policy than either of
the above.
This callback can examine the peer's SCTs and determine whether they are
sufficient to allow the connection to continue.
The TLS handshake is aborted if the verification mode is not \fBSSL_VERIFY_NONE\fR
and the callback returns a non-positive result.
.PP
An arbitrary callback context argument, \fBarg\fR, can be passed in when setting
the callback.
This will be passed to the callback whenever it is invoked.
Ownership of this context remains with the caller.
.PP
If no callback is set, SCTs will not be requested and Certificate Transparency
validation will not occur.
.PP
No callback will be invoked when the peer presents no certificate, e.g. by
employing an anonymous (aNULL) cipher suite.
In that case the handshake continues as it would had no callback been
requested.
Callbacks are also not invoked when the peer certificate chain is invalid or
validated via \fBDANE\-TA\fR\|(2) or \fBDANE\-EE\fR\|(3) TLSA records which use a private X.509
PKI, or no X.509 PKI at all, respectively.
Clients that require SCTs are expected to not have enabled any aNULL ciphers
nor to have specified server verification via \fBDANE\-TA\fR\|(2) or \fBDANE\-EE\fR\|(3) TLSA
records.
.PP
\&\fBSSL_disable_ct()\fR and \fBSSL_CTX_disable_ct()\fR turn off CT processing, whether
enabled via the built-in or the custom callbacks, by setting a NULL callback.
These may be implemented as macros.
.PP
\&\fBSSL_ct_is_enabled()\fR and \fBSSL_CTX_ct_is_enabled()\fR return 1 if CT processing is
enabled via either \fBSSL_enable_ct()\fR or a non-null custom callback, and 0
otherwise.
.SH NOTES
.IX Header "NOTES"
When SCT processing is enabled, OCSP stapling will be enabled. This is because
one possible source of SCTs is the OCSP response from a server.
.PP
The time returned by \fBSSL_SESSION_get_time()\fR will be used to evaluate whether any
presented SCTs have timestamps that are in the future (and therefore invalid).
.SH RESTRICTIONS
.IX Header "RESTRICTIONS"
Certificate Transparency validation cannot be enabled and so a callback cannot
be set if a custom client extension handler has been registered to handle SCT
extensions (\fBTLSEXT_TYPE_signed_certificate_timestamp\fR).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_enable_ct()\fR, \fBSSL_CTX_enable_ct()\fR, \fBSSL_CTX_set_ct_validation_callback()\fR and
\&\fBSSL_set_ct_validation_callback()\fR return 1 if the \fBcallback\fR is successfully
set.
They return 0 if an error occurs, e.g. a custom client extension handler has
been setup to handle SCTs.
.PP
\&\fBSSL_disable_ct()\fR and \fBSSL_CTX_disable_ct()\fR do not return a result.
.PP
\&\fBSSL_CTX_ct_is_enabled()\fR and \fBSSL_ct_is_enabled()\fR return a 1 if a non-null CT
validation callback is set, or 0 if no callback (or equivalently a NULL
callback) is set.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
<\fBSSL_get_verify_result\fR\|(3)>,
\&\fBSSL_session_reused\fR\|(3),
\&\fBSSL_set_verify\fR\|(3),
\&\fBSSL_CTX_set_verify\fR\|(3),
\&\fBSSL_SESSION_get_time\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
