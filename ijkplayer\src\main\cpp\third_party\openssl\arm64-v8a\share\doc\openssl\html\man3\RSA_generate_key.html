<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>RSA_generate_key</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>RSA_generate_key_ex, RSA_generate_key, RSA_generate_multi_prime_key - generate RSA key pair</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/rsa.h&gt;

int RSA_generate_key_ex(RSA *rsa, int bits, BIGNUM *e, BN_GENCB *cb);
int RSA_generate_multi_prime_key(RSA *rsa, int bits, int primes, BIGNUM *e, BN_GENCB *cb);</code></pre>

<p>Deprecated:</p>

<pre><code>#if OPENSSL_API_COMPAT &lt; 0x00908000L
RSA *RSA_generate_key(int bits, unsigned long e,
                      void (*callback)(int, int, void *), void *cb_arg);
#endif</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>RSA_generate_key_ex() generates a 2-prime RSA key pair and stores it in the <b>RSA</b> structure provided in <b>rsa</b>. The pseudo-random number generator must be seeded prior to calling RSA_generate_key_ex().</p>

<p>RSA_generate_multi_prime_key() generates a multi-prime RSA key pair and stores it in the <b>RSA</b> structure provided in <b>rsa</b>. The number of primes is given by the <b>primes</b> parameter. The random number generator must be seeded when calling RSA_generate_multi_prime_key(). If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to external circumstances (see <a href="../man7/RAND.html">RAND(7)</a>), the operation will fail.</p>

<p>The modulus size will be of length <b>bits</b>, the number of primes to form the modulus will be <b>primes</b>, and the public exponent will be <b>e</b>. Key sizes with <b>num</b> &lt; 1024 should be considered insecure. The exponent is an odd number, typically 3, 17 or 65537.</p>

<p>In order to maintain adequate security level, the maximum number of permitted <b>primes</b> depends on modulus bit length:</p>

<pre><code>&lt;1024 | &gt;=1024 | &gt;=4096 | &gt;=8192
------+--------+--------+-------
  2   |   3    |   4    |   5</code></pre>

<p>A callback function may be used to provide feedback about the progress of the key generation. If <b>cb</b> is not <b>NULL</b>, it will be called as follows using the BN_GENCB_call() function described on the <a href="../man3/BN_generate_prime.html">BN_generate_prime(3)</a> page.</p>

<p>RSA_generate_key() is similar to RSA_generate_key_ex() but expects an old-style callback function; see <a href="../man3/BN_generate_prime.html">BN_generate_prime(3)</a> for information on the old-style callback.</p>

<ul>

<li><p>While a random prime number is generated, it is called as described in <a href="../man3/BN_generate_prime.html">BN_generate_prime(3)</a>.</p>

</li>
<li><p>When the n-th randomly generated prime is rejected as not suitable for the key, <b>BN_GENCB_call(cb, 2, n)</b> is called.</p>

</li>
<li><p>When a random p has been found with p-1 relatively prime to <b>e</b>, it is called as <b>BN_GENCB_call(cb, 3, 0)</b>.</p>

</li>
</ul>

<p>The process is then repeated for prime q and other primes (if any) with <b>BN_GENCB_call(cb, 3, i)</b> where <b>i</b> indicates the i-th prime.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>RSA_generate_multi_prime_key() returns 1 on success or 0 on error. RSA_generate_key_ex() returns 1 on success or 0 on error. The error codes can be obtained by <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>.</p>

<p>RSA_generate_key() returns a pointer to the RSA structure or <b>NULL</b> if the key generation fails.</p>

<h1 id="BUGS">BUGS</h1>

<p><b>BN_GENCB_call(cb, 2, x)</b> is used with two different meanings.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/RAND_bytes.html">RAND_bytes(3)</a>, <a href="../man3/BN_generate_prime.html">BN_generate_prime(3)</a>, <a href="../man7/RAND.html">RAND(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>RSA_generate_key() was deprecated in OpenSSL 0.9.8; use RSA_generate_key_ex() instead.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2019 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


