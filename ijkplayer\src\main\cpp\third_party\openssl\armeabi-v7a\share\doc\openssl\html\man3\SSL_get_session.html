<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_get_session</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_get_session, SSL_get0_session, SSL_get1_session - retrieve TLS/SSL session data</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

SSL_SESSION *SSL_get_session(const SSL *ssl);
SSL_SESSION *SSL_get0_session(const SSL *ssl);
SSL_SESSION *SSL_get1_session(SSL *ssl);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_get_session() returns a pointer to the <b>SSL_SESSION</b> actually used in <b>ssl</b>. The reference count of the <b>SSL_SESSION</b> is not incremented, so that the pointer can become invalid by other operations.</p>

<p>SSL_get0_session() is the same as SSL_get_session().</p>

<p>SSL_get1_session() is the same as SSL_get_session(), but the reference count of the <b>SSL_SESSION</b> is incremented by one.</p>

<h1 id="NOTES">NOTES</h1>

<p>The ssl session contains all information required to re-establish the connection without a full handshake for SSL versions up to and including TLSv1.2. In TLSv1.3 the same is true, but sessions are established after the main handshake has occurred. The server will send the session information to the client at a time of its choosing, which may be some while after the initial connection is established (or never). Calling these functions on the client side in TLSv1.3 before the session has been established will still return an SSL_SESSION object but that object cannot be used for resuming the session. See <a href="../man3/SSL_SESSION_is_resumable.html">SSL_SESSION_is_resumable(3)</a> for information on how to determine whether an SSL_SESSION object can be used for resumption or not.</p>

<p>Additionally, in TLSv1.3, a server can send multiple messages that establish a session for a single connection. In that case, on the client side, the above functions will only return information on the last session that was received. On the server side they will only return information on the last session that was sent, or if no session tickets were sent then the session for the current connection.</p>

<p>The preferred way for applications to obtain a resumable SSL_SESSION object is to use a new session callback as described in <a href="../man3/SSL_CTX_sess_set_new_cb.html">SSL_CTX_sess_set_new_cb(3)</a>. The new session callback is only invoked when a session is actually established, so this avoids the problem described above where an application obtains an SSL_SESSION object that cannot be used for resumption in TLSv1.3. It also enables applications to obtain information about all sessions sent by the server.</p>

<p>A session will be automatically removed from the session cache and marked as non-resumable if the connection is not closed down cleanly, e.g. if a fatal error occurs on the connection or <a href="../man3/SSL_shutdown.html">SSL_shutdown(3)</a> is not called prior to <a href="../man3/SSL_free.html">SSL_free(3)</a>.</p>

<p>In TLSv1.3 it is recommended that each SSL_SESSION object is only used for resumption once.</p>

<p>SSL_get0_session() returns a pointer to the actual session. As the reference counter is not incremented, the pointer is only valid while the connection is in use. If <a href="../man3/SSL_clear.html">SSL_clear(3)</a> or <a href="../man3/SSL_free.html">SSL_free(3)</a> is called, the session may be removed completely (if considered bad), and the pointer obtained will become invalid. Even if the session is valid, it can be removed at any time due to timeout during <a href="../man3/SSL_CTX_flush_sessions.html">SSL_CTX_flush_sessions(3)</a>.</p>

<p>If the data is to be kept, SSL_get1_session() will increment the reference count, so that the session will not be implicitly removed by other operations but stays in memory. In order to remove the session <a href="../man3/SSL_SESSION_free.html">SSL_SESSION_free(3)</a> must be explicitly called once to decrement the reference count again.</p>

<p>SSL_SESSION objects keep internal link information about the session cache list, when being inserted into one SSL_CTX object&#39;s session cache. One SSL_SESSION object, regardless of its reference count, must therefore only be used with one SSL_CTX object (and the SSL objects created from this SSL_CTX object).</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The following return values can occur:</p>

<dl>

<dt id="NULL">NULL</dt>
<dd>

<p>There is no session available in <b>ssl</b>.</p>

</dd>
<dt id="Pointer-to-an-SSL_SESSION">Pointer to an SSL_SESSION</dt>
<dd>

<p>The return value points to the data of an SSL session.</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_free.html">SSL_free(3)</a>, <a href="../man3/SSL_clear.html">SSL_clear(3)</a>, <a href="../man3/SSL_SESSION_free.html">SSL_SESSION_free(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


