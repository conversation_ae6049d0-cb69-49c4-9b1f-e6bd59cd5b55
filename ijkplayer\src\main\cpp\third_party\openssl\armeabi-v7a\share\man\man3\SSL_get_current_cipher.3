.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_CURRENT_CIPHER 3"
.TH SSL_GET_CURRENT_CIPHER 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_current_cipher, SSL_get_cipher_name, SSL_get_cipher,
SSL_get_cipher_bits, SSL_get_cipher_version,
SSL_get_pending_cipher \- get SSL_CIPHER of a connection
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& const SSL_CIPHER *SSL_get_current_cipher(const SSL *ssl);
\& const SSL_CIPHER *SSL_get_pending_cipher(const SSL *ssl);
\&
\& const char *SSL_get_cipher_name(const SSL *s);
\& const char *SSL_get_cipher(const SSL *s);
\& int SSL_get_cipher_bits(const SSL *s, int *np);
\& const char *SSL_get_cipher_version(const SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_current_cipher()\fR returns a pointer to an SSL_CIPHER object containing
the description of the actually used cipher of a connection established with
the \fBssl\fR object.
See \fBSSL_CIPHER_get_name\fR\|(3) for more details.
.PP
\&\fBSSL_get_cipher_name()\fR obtains the
name of the currently used cipher.
\&\fBSSL_get_cipher()\fR is identical to \fBSSL_get_cipher_name()\fR.
\&\fBSSL_get_cipher_bits()\fR is a
macro to obtain the number of secret/algorithm bits used and
\&\fBSSL_get_cipher_version()\fR returns the protocol name.
.PP
\&\fBSSL_get_pending_cipher()\fR returns a pointer to an SSL_CIPHER object containing
the description of the cipher (if any) that has been negotiated for future use
on the connection established with the \fBssl\fR object, but is not yet in use.
This may be the case during handshake processing, when control flow can be
returned to the application via any of several callback methods.  The internal
sequencing of handshake processing and callback invocation is not guaranteed
to be stable from release to release, and at present only the callback set
by \fBSSL_CTX_set_alpn_select_cb()\fR is guaranteed to have a non-NULL return value.
Other callbacks may be added to this list over time.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_get_current_cipher()\fR returns the cipher actually used, or NULL if
no session has been established.
.PP
\&\fBSSL_get_pending_cipher()\fR returns the cipher to be used at the next change
of cipher suite, or NULL if no such cipher is known.
.SH NOTES
.IX Header "NOTES"
SSL_get_cipher, SSL_get_cipher_bits, SSL_get_cipher_version, and
SSL_get_cipher_name are implemented as macros.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CIPHER_get_name\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
