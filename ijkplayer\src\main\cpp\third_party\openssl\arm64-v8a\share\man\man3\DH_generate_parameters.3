.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DH_GENERATE_PARAMETERS 3"
.TH DH_GENERATE_PARAMETERS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DH_generate_parameters_ex, DH_generate_parameters,
DH_check, DH_check_params,
DH_check_ex, DH_check_params_ex, DH_check_pub_key_ex
\&\- generate and check Diffie\-Hellman
parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dh.h>
\&
\& int DH_generate_parameters_ex(DH *dh, int prime_len, int generator, BN_GENCB *cb);
\&
\& int DH_check(DH *dh, int *codes);
\& int DH_check_params(DH *dh, int *codes);
\&
\& int DH_check_ex(const DH *dh);
\& int DH_check_params_ex(const DH *dh);
\& int DH_check_pub_key_ex(const DH *dh, const BIGNUM *pub_key);
.Ve
.PP
Deprecated:
.PP
.Vb 4
\& #if OPENSSL_API_COMPAT < 0x00908000L
\& DH *DH_generate_parameters(int prime_len, int generator,
\&                            void (*callback)(int, int, void *), void *cb_arg);
\& #endif
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBDH_generate_parameters_ex()\fR generates Diffie-Hellman parameters that can
be shared among a group of users, and stores them in the provided \fBDH\fR
structure. The pseudo-random number generator must be
seeded before calling it.
The parameters generated by \fBDH_generate_parameters_ex()\fR should not be used in
signature schemes.
.PP
\&\fBprime_len\fR is the length in bits of the safe prime to be generated.
\&\fBgenerator\fR is a small number > 1, typically 2 or 5.
.PP
A callback function may be used to provide feedback about the progress
of the key generation. If \fBcb\fR is not \fBNULL\fR, it will be
called as described in \fBBN_generate_prime\fR\|(3) while a random prime
number is generated, and when a prime has been found, \fBBN_GENCB_call(cb, 3, 0)\fR
is called. See \fBBN_generate_prime_ex\fR\|(3) for information on
the \fBBN_GENCB_call()\fR function.
.PP
\&\fBDH_generate_parameters()\fR is similar to \fBDH_generate_prime_ex()\fR but
expects an old-style callback function; see
\&\fBBN_generate_prime\fR\|(3) for information on the old-style callback.
.PP
\&\fBDH_check_params()\fR confirms that the \fBp\fR and \fBg\fR are likely enough to
be valid.
This is a lightweight check, if a more thorough check is needed, use
\&\fBDH_check()\fR.
The value of \fB*codes\fR is updated with any problems found.
If \fB*codes\fR is zero then no problems were found, otherwise the
following bits may be set:
.IP DH_CHECK_P_NOT_PRIME 4
.IX Item "DH_CHECK_P_NOT_PRIME"
The parameter \fBp\fR has been determined to not being an odd prime.
Note that the lack of this bit doesn't guarantee that \fBp\fR is a
prime.
.IP DH_NOT_SUITABLE_GENERATOR 4
.IX Item "DH_NOT_SUITABLE_GENERATOR"
The generator \fBg\fR is not suitable.
Note that the lack of this bit doesn't guarantee that \fBg\fR is
suitable, unless \fBp\fR is known to be a strong prime.
.PP
\&\fBDH_check()\fR confirms that the Diffie-Hellman parameters \fBdh\fR are valid. The
value of \fB*codes\fR is updated with any problems found. If \fB*codes\fR is zero then
no problems were found, otherwise the following bits may be set:
.IP DH_CHECK_P_NOT_PRIME 4
.IX Item "DH_CHECK_P_NOT_PRIME"
The parameter \fBp\fR is not prime.
.IP DH_CHECK_P_NOT_SAFE_PRIME 4
.IX Item "DH_CHECK_P_NOT_SAFE_PRIME"
The parameter \fBp\fR is not a safe prime and no \fBq\fR value is present.
.IP DH_UNABLE_TO_CHECK_GENERATOR 4
.IX Item "DH_UNABLE_TO_CHECK_GENERATOR"
The generator \fBg\fR cannot be checked for suitability.
.IP DH_NOT_SUITABLE_GENERATOR 4
.IX Item "DH_NOT_SUITABLE_GENERATOR"
The generator \fBg\fR is not suitable.
.IP DH_CHECK_Q_NOT_PRIME 4
.IX Item "DH_CHECK_Q_NOT_PRIME"
The parameter \fBq\fR is not prime.
.IP DH_CHECK_INVALID_Q_VALUE 4
.IX Item "DH_CHECK_INVALID_Q_VALUE"
The parameter \fBq\fR is invalid.
.IP DH_CHECK_INVALID_J_VALUE 4
.IX Item "DH_CHECK_INVALID_J_VALUE"
The parameter \fBj\fR is invalid.
.PP
\&\fBDH_check_ex()\fR, \fBDH_check_params()\fR and \fBDH_check_pub_key_ex()\fR are similar to
\&\fBDH_check()\fR and \fBDH_check_params()\fR respectively, but the error reasons are added
to the thread's error queue instead of provided as return values from the
function.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDH_generate_parameters_ex()\fR, \fBDH_check()\fR and \fBDH_check_params()\fR return 1
if the check could be performed, 0 otherwise.
.PP
\&\fBDH_generate_parameters()\fR returns a pointer to the DH structure or NULL if
the parameter generation fails.
.PP
\&\fBDH_check_ex()\fR, \fBDH_check_params()\fR and \fBDH_check_pub_key_ex()\fR return 1 if the
check is successful, 0 for failed.
.PP
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDH_new\fR\|(3), \fBERR_get_error\fR\|(3), \fBRAND_bytes\fR\|(3),
\&\fBDH_free\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBDH_generate_parameters()\fR was deprecated in OpenSSL 0.9.8; use
\&\fBDH_generate_parameters_ex()\fR instead.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
