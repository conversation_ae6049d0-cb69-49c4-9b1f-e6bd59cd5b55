.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "VERSION 1"
.TH VERSION 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-version,
version \- print OpenSSL version information
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl version\fR
[\fB\-help\fR]
[\fB\-a\fR]
[\fB\-v\fR]
[\fB\-b\fR]
[\fB\-o\fR]
[\fB\-f\fR]
[\fB\-p\fR]
[\fB\-d\fR]
[\fB\-e\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to print out version information about OpenSSL.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP \fB\-a\fR 4
.IX Item "-a"
All information, this is the same as setting all the other flags.
.IP \fB\-v\fR 4
.IX Item "-v"
The current OpenSSL version.
.IP \fB\-b\fR 4
.IX Item "-b"
The date the current version of OpenSSL was built.
.IP \fB\-o\fR 4
.IX Item "-o"
Option information: various options set when the library was built.
.IP \fB\-f\fR 4
.IX Item "-f"
Compilation flags.
.IP \fB\-p\fR 4
.IX Item "-p"
Platform setting.
.IP \fB\-d\fR 4
.IX Item "-d"
OPENSSLDIR setting.
.IP \fB\-e\fR 4
.IX Item "-e"
ENGINESDIR setting.
.SH NOTES
.IX Header "NOTES"
The output of \fBopenssl version \-a\fR would typically be used when sending
in a bug report.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
