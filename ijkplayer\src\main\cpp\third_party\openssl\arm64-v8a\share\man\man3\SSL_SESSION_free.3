.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SESSION_FREE 3"
.TH SSL_SESSION_FREE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_SESSION_new,
SSL_SESSION_dup,
SSL_SESSION_up_ref,
SSL_SESSION_free \- create, free and manage SSL_SESSION structures
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& SSL_SESSION *SSL_SESSION_new(void);
\& SSL_SESSION *SSL_SESSION_dup(SSL_SESSION *src);
\& int SSL_SESSION_up_ref(SSL_SESSION *ses);
\& void SSL_SESSION_free(SSL_SESSION *session);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_SESSION_new()\fR creates a new SSL_SESSION structure and returns a pointer to
it.
.PP
\&\fBSSL_SESSION_dup()\fR copies the contents of the SSL_SESSION structure in \fBsrc\fR
and returns a pointer to it.
.PP
\&\fBSSL_SESSION_up_ref()\fR increments the reference count on the given SSL_SESSION
structure.
.PP
\&\fBSSL_SESSION_free()\fR decrements the reference count of \fBsession\fR and removes
the \fBSSL_SESSION\fR structure pointed to by \fBsession\fR and frees up the allocated
memory, if the reference count has reached 0.
If \fBsession\fR is NULL nothing is done.
.SH NOTES
.IX Header "NOTES"
SSL_SESSION objects are allocated, when a TLS/SSL handshake operation
is successfully completed. Depending on the settings, see
\&\fBSSL_CTX_set_session_cache_mode\fR\|(3),
the SSL_SESSION objects are internally referenced by the SSL_CTX and
linked into its session cache. SSL objects may be using the SSL_SESSION object;
as a session may be reused, several SSL objects may be using one SSL_SESSION
object at the same time. It is therefore crucial to keep the reference
count (usage information) correct and not delete a SSL_SESSION object
that is still used, as this may lead to program failures due to
dangling pointers. These failures may also appear delayed, e.g.
when an SSL_SESSION object was completely freed as the reference count
incorrectly became 0, but it is still referenced in the internal
session cache and the cache list is processed during a
\&\fBSSL_CTX_flush_sessions\fR\|(3) operation.
.PP
\&\fBSSL_SESSION_free()\fR must only be called for SSL_SESSION objects, for
which the reference count was explicitly incremented (e.g.
by calling \fBSSL_get1_session()\fR, see \fBSSL_get_session\fR\|(3))
or when the SSL_SESSION object was generated outside a TLS handshake
operation, e.g. by using \fBd2i_SSL_SESSION\fR\|(3).
It must not be called on other SSL_SESSION objects, as this would cause
incorrect reference counts and therefore program failures.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
SSL_SESSION_new returns a pointer to the newly allocated SSL_SESSION structure
or NULL on error.
.PP
SSL_SESSION_up_ref returns 1 on success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_get_session\fR\|(3),
\&\fBSSL_CTX_set_session_cache_mode\fR\|(3),
\&\fBSSL_CTX_flush_sessions\fR\|(3),
\&\fBd2i_SSL_SESSION\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_SESSION_dup()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
