.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_LH_COMPFUNC 3"
.TH OPENSSL_LH_COMPFUNC 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
LHASH, DECLARE_LHASH_OF,
OPENSSL_LH_COMPFUNC, OPENSSL_LH_HASHFUNC, OPENSSL_LH_DOALL_FUNC,
LHASH_DOALL_ARG_FN_TYPE,
IMPLEMENT_LHASH_HASH_FN, IMPLEMENT_LHASH_COMP_FN,
lh_TYPE_new, lh_TYPE_free,
lh_TYPE_insert, lh_TYPE_delete, lh_TYPE_retrieve,
lh_TYPE_doall, lh_TYPE_doall_arg, lh_TYPE_error \- dynamic hash table
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/lhash.h>
\&
\& DECLARE_LHASH_OF(TYPE);
\&
\& LHASH *lh_TYPE_new(OPENSSL_LH_HASHFUNC hash, OPENSSL_LH_COMPFUNC compare);
\& void lh_TYPE_free(LHASH_OF(TYPE) *table);
\&
\& TYPE *lh_TYPE_insert(LHASH_OF(TYPE) *table, TYPE *data);
\& TYPE *lh_TYPE_delete(LHASH_OF(TYPE) *table, TYPE *data);
\& TYPE *lh_TYPE_retrieve(LHASH_OF(TYPE) *table, TYPE *data);
\&
\& void lh_TYPE_doall(LHASH_OF(TYPE) *table, OPENSSL_LH_DOALL_FUNC func);
\& void lh_TYPE_doall_arg(LHASH_OF(TYPE) *table, OPENSSL_LH_DOALL_FUNCARG func,
\&                        TYPE *arg);
\&
\& int lh_TYPE_error(LHASH_OF(TYPE) *table);
\&
\& typedef int (*OPENSSL_LH_COMPFUNC)(const void *, const void *);
\& typedef unsigned long (*OPENSSL_LH_HASHFUNC)(const void *);
\& typedef void (*OPENSSL_LH_DOALL_FUNC)(const void *);
\& typedef void (*LHASH_DOALL_ARG_FN_TYPE)(const void *, const void *);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This library implements type-checked dynamic hash tables. The hash
table entries can be arbitrary structures. Usually they consist of key
and value fields.  In the description here, \fITYPE\fR is used a placeholder
for any of the OpenSSL datatypes, such as \fISSL_SESSION\fR.
.PP
\&\fBlh_TYPE_new()\fR creates a new \fBLHASH_OF(TYPE)\fR structure to store
arbitrary data entries, and specifies the 'hash' and 'compare'
callbacks to be used in organising the table's entries.  The \fBhash\fR
callback takes a pointer to a table entry as its argument and returns
an unsigned long hash value for its key field.  The hash value is
normally truncated to a power of 2, so make sure that your hash
function returns well mixed low order bits.  The \fBcompare\fR callback
takes two arguments (pointers to two hash table entries), and returns
0 if their keys are equal, nonzero otherwise.
.PP
If your hash table
will contain items of some particular type and the \fBhash\fR and
\&\fBcompare\fR callbacks hash/compare these types, then the
\&\fBIMPLEMENT_LHASH_HASH_FN\fR and \fBIMPLEMENT_LHASH_COMP_FN\fR macros can be
used to create callback wrappers of the prototypes required by
\&\fBlh_TYPE_new()\fR as shown in this example:
.PP
.Vb 11
\& /*
\&  * Implement the hash and compare functions; "stuff" can be any word.
\&  */
\& static unsigned long stuff_hash(const TYPE *a)
\& {
\&     ...
\& }
\& static int stuff_cmp(const TYPE *a, const TYPE *b)
\& {
\&     ...
\& }
\&
\& /*
\&  * Implement the wrapper functions.
\&  */
\& static IMPLEMENT_LHASH_HASH_FN(stuff, TYPE)
\& static IMPLEMENT_LHASH_COMP_FN(stuff, TYPE)
.Ve
.PP
If the type is going to be used in several places, the following macros
can be used in a common header file to declare the function wrappers:
.PP
.Vb 2
\& DECLARE_LHASH_HASH_FN(stuff, TYPE)
\& DECLARE_LHASH_COMP_FN(stuff, TYPE)
.Ve
.PP
Then a hash table of TYPE objects can be created using this:
.PP
.Vb 1
\& LHASH_OF(TYPE) *htable;
\&
\& htable = lh_TYPE_new(LHASH_HASH_FN(stuff), LHASH_COMP_FN(stuff));
.Ve
.PP
\&\fBlh_TYPE_free()\fR frees the \fBLHASH_OF(TYPE)\fR structure
\&\fBtable\fR. Allocated hash table entries will not be freed; consider
using \fBlh_TYPE_doall()\fR to deallocate any remaining entries in the
hash table (see below).
.PP
\&\fBlh_TYPE_insert()\fR inserts the structure pointed to by \fBdata\fR into
\&\fBtable\fR.  If there already is an entry with the same key, the old
value is replaced. Note that \fBlh_TYPE_insert()\fR stores pointers, the
data are not copied.
.PP
\&\fBlh_TYPE_delete()\fR deletes an entry from \fBtable\fR.
.PP
\&\fBlh_TYPE_retrieve()\fR looks up an entry in \fBtable\fR. Normally, \fBdata\fR
is a structure with the key field(s) set; the function will return a
pointer to a fully populated structure.
.PP
\&\fBlh_TYPE_doall()\fR will, for every entry in the hash table, call
\&\fBfunc\fR with the data item as its parameter.
For example:
.PP
.Vb 2
\& /* Cleans up resources belonging to \*(Aqa\*(Aq (this is implemented elsewhere) */
\& void TYPE_cleanup_doall(TYPE *a);
\&
\& /* Implement a prototype\-compatible wrapper for "TYPE_cleanup" */
\& IMPLEMENT_LHASH_DOALL_FN(TYPE_cleanup, TYPE)
\&
\& /* Call "TYPE_cleanup" against all items in a hash table. */
\& lh_TYPE_doall(hashtable, LHASH_DOALL_FN(TYPE_cleanup));
\&
\& /* Then the hash table itself can be deallocated */
\& lh_TYPE_free(hashtable);
.Ve
.PP
When doing this, be careful if you delete entries from the hash table
in your callbacks: the table may decrease in size, moving the item
that you are currently on down lower in the hash table \- this could
cause some entries to be skipped during the iteration.  The second
best solution to this problem is to set hash\->down_load=0 before
you start (which will stop the hash table ever decreasing in size).
The best solution is probably to avoid deleting items from the hash
table inside a "doall" callback!
.PP
\&\fBlh_TYPE_doall_arg()\fR is the same as \fBlh_TYPE_doall()\fR except that
\&\fBfunc\fR will be called with \fBarg\fR as the second argument and \fBfunc\fR
should be of type \fBLHASH_DOALL_ARG_FN_TYPE\fR (a callback prototype
that is passed both the table entry and an extra argument).  As with
\&\fBlh_doall()\fR, you can instead choose to declare your callback with a
prototype matching the types you are dealing with and use the
declare/implement macros to create compatible wrappers that cast
variables before calling your type-specific callbacks.  An example of
this is demonstrated here (printing all hash table entries to a BIO
that is provided by the caller):
.PP
.Vb 2
\& /* Prints item \*(Aqa\*(Aq to \*(Aqoutput_bio\*(Aq (this is implemented elsewhere) */
\& void TYPE_print_doall_arg(const TYPE *a, BIO *output_bio);
\&
\& /* Implement a prototype\-compatible wrapper for "TYPE_print" */
\& static IMPLEMENT_LHASH_DOALL_ARG_FN(TYPE, const TYPE, BIO)
\&
\& /* Print out the entire hashtable to a particular BIO */
\& lh_TYPE_doall_arg(hashtable, LHASH_DOALL_ARG_FN(TYPE_print), BIO,
\&                   logging_bio);
.Ve
.PP
\&\fBlh_TYPE_error()\fR can be used to determine if an error occurred in the last
operation.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBlh_TYPE_new()\fR returns \fBNULL\fR on error, otherwise a pointer to the new
\&\fBLHASH\fR structure.
.PP
When a hash table entry is replaced, \fBlh_TYPE_insert()\fR returns the value
being replaced. \fBNULL\fR is returned on normal operation and on error.
.PP
\&\fBlh_TYPE_delete()\fR returns the entry being deleted.  \fBNULL\fR is returned if
there is no such value in the hash table.
.PP
\&\fBlh_TYPE_retrieve()\fR returns the hash table entry if it has been found,
\&\fBNULL\fR otherwise.
.PP
\&\fBlh_TYPE_error()\fR returns 1 if an error occurred in the last operation, 0
otherwise. It's meaningful only after non-retrieve operations.
.PP
\&\fBlh_TYPE_free()\fR, \fBlh_TYPE_doall()\fR and \fBlh_TYPE_doall_arg()\fR return no values.
.SH NOTE
.IX Header "NOTE"
The LHASH code is not thread safe. All updating operations, as well as
lh_TYPE_error call must be performed under a write lock. All retrieve
operations should be performed under a read lock, \fIunless\fR accurate
usage statistics are desired. In which case, a write lock should be used
for retrieve operations as well. For output of the usage statistics,
using the functions from \fBOPENSSL_LH_stats\fR\|(3), a read lock suffices.
.PP
The LHASH code regards table entries as constant data.  As such, it
internally represents \fBlh_insert()\fR'd items with a "const void *"
pointer type.  This is why callbacks such as those used by \fBlh_doall()\fR
and \fBlh_doall_arg()\fR declare their prototypes with "const", even for the
parameters that pass back the table items' data pointers \- for
consistency, user-provided data is "const" at all times as far as the
LHASH code is concerned.  However, as callers are themselves providing
these pointers, they can choose whether they too should be treating
all such parameters as constant.
.PP
As an example, a hash table may be maintained by code that, for
reasons of encapsulation, has only "const" access to the data being
indexed in the hash table (i.e. it is returned as "const" from
elsewhere in their code) \- in this case the LHASH prototypes are
appropriate as-is.  Conversely, if the caller is responsible for the
life-time of the data in question, then they may well wish to make
modifications to table item passed back in the \fBlh_doall()\fR or
\&\fBlh_doall_arg()\fR callbacks (see the "TYPE_cleanup" example above).  If
so, the caller can either cast the "const" away (if they're providing
the raw callbacks themselves) or use the macros to declare/implement
the wrapper functions without "const" types.
.PP
Callers that only have "const" access to data they're indexing in a
table, yet declare callbacks without constant types (or cast the
"const" away themselves), are therefore creating their own risks/bugs
without being encouraged to do so by the API.  On a related note,
those auditing code should pay special attention to any instances of
DECLARE/IMPLEMENT_LHASH_DOALL_[ARG_]_FN macros that provide types
without any "const" qualifiers.
.SH BUGS
.IX Header "BUGS"
\&\fBlh_TYPE_insert()\fR returns \fBNULL\fR both for success and error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOPENSSL_LH_stats\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
In OpenSSL 1.0.0, the lhash interface was revamped for better
type checking.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
