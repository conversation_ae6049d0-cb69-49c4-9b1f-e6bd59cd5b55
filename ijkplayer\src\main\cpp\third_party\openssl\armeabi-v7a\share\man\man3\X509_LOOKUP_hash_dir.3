.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_LOOKUP_HASH_DIR 3"
.TH X509_LOOKUP_HASH_DIR 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_LOOKUP_hash_dir, X509_LOOKUP_file,
X509_load_cert_file,
X509_load_crl_file,
X509_load_cert_crl_file \- Default OpenSSL certificate
lookup methods
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509_vfy.h>
\&
\& X509_LOOKUP_METHOD *X509_LOOKUP_hash_dir(void);
\& X509_LOOKUP_METHOD *X509_LOOKUP_file(void);
\&
\& int X509_load_cert_file(X509_LOOKUP *ctx, const char *file, int type);
\& int X509_load_crl_file(X509_LOOKUP *ctx, const char *file, int type);
\& int X509_load_cert_crl_file(X509_LOOKUP *ctx, const char *file, int type);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_LOOKUP_hash_dir\fR and \fBX509_LOOKUP_file\fR are two certificate
lookup methods to use with \fBX509_STORE\fR, provided by OpenSSL library.
.PP
Users of the library typically do not need to create instances of these
methods manually, they would be created automatically by
\&\fBX509_STORE_load_locations\fR\|(3) or
\&\fBSSL_CTX_load_verify_locations\fR\|(3)
functions.
.PP
Internally loading of certificates and CRLs is implemented via functions
\&\fBX509_load_cert_crl_file\fR, \fBX509_load_cert_file\fR and
\&\fBX509_load_crl_file\fR. These functions support parameter \fItype\fR, which
can be one of constants \fBFILETYPE_PEM\fR, \fBFILETYPE_ASN1\fR and
\&\fBFILETYPE_DEFAULT\fR. They load certificates and/or CRLs from specified
file into memory cache of \fBX509_STORE\fR objects which given \fBctx\fR
parameter is associated with.
.PP
Functions \fBX509_load_cert_file\fR and
\&\fBX509_load_crl_file\fR can load both PEM and DER formats depending of
type value. Because DER format cannot contain more than one certificate
or CRL object (while PEM can contain several concatenated PEM objects)
\&\fBX509_load_cert_crl_file\fR with \fBFILETYPE_ASN1\fR is equivalent to
\&\fBX509_load_cert_file\fR.
.PP
Constant \fBFILETYPE_DEFAULT\fR with NULL filename causes these functions
to load default certificate store file (see
\&\fBX509_STORE_set_default_paths\fR\|(3).
.PP
Functions return number of objects loaded from file or 0 in case of
error.
.PP
Both methods support adding several certificate locations into one
\&\fBX509_STORE\fR.
.PP
This page documents certificate store formats used by these methods and
caching policy.
.SS "File Method"
.IX Subsection "File Method"
The \fBX509_LOOKUP_file\fR method loads all the certificates or CRLs
present in a file into memory at the time the file is added as a
lookup source.
.PP
File format is ASCII text which contains concatenated PEM certificates
and CRLs.
.PP
This method should be used by applications which work with a small
set of CAs.
.SS "Hashed Directory Method"
.IX Subsection "Hashed Directory Method"
\&\fBX509_LOOKUP_hash_dir\fR is a more advanced method, which loads
certificates and CRLs on demand, and caches them in memory once
they are loaded. As of OpenSSL 1.0.0, it also checks for newer CRLs
upon each lookup, so that newer CRLs are as soon as they appear in
the directory.
.PP
The directory should contain one certificate or CRL per file in PEM format,
with a filename of the form \fIhash\fR.\fIN\fR for a certificate, or
\&\fIhash\fR.\fBr\fR\fIN\fR for a CRL.
The \fIhash\fR is the value returned by the \fBX509_NAME_hash\fR\|(3) function applied
to the subject name for certificates or issuer name for CRLs.
The hash can also be obtained via the \fB\-hash\fR option of the \fBx509\fR\|(1) or
\&\fBcrl\fR\|(1) commands.
.PP
The .\fIN\fR or .\fBr\fR\fIN\fR suffix is a sequence number that starts at zero, and is
incremented consecutively for each certificate or CRL with the same \fIhash\fR
value.
Gaps in the sequence numbers are not supported, it is assumed that there are no
more objects with the same hash beyond the first missing number in the
sequence.
.PP
Sequence numbers make it possible for the directory to contain multiple
certificates with same subject name hash value.
For example, it is possible to have in the store several certificates with same
subject or several CRLs with same issuer (and, for example, different validity
period).
.PP
When checking for new CRLs once one CRL for given hash value is
loaded, hash_dir lookup method checks only for certificates with
sequence number greater than that of the already cached CRL.
.PP
Note that the hash algorithm used for subject name hashing changed in OpenSSL
1.0.0, and all certificate stores have to be rehashed when moving from OpenSSL
0.9.8 to 1.0.0.
.PP
OpenSSL includes a \fBrehash\fR\|(1) utility which creates symlinks with correct
hashed names for all files with .pem suffix in a given directory.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_LOOKUP_hash_dir()\fR and \fBX509_LOOKUP_file()\fR always return a valid
\&\fBX509_LOOKUP_METHOD\fR structure.
.PP
\&\fBX509_load_cert_file()\fR, \fBX509_load_crl_file()\fR and \fBX509_load_cert_crl_file()\fR return
the number of loaded objects or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPEM_read_PrivateKey\fR\|(3),
\&\fBX509_STORE_load_locations\fR\|(3),
\&\fBX509_store_add_lookup\fR\|(3),
\&\fBSSL_CTX_load_verify_locations\fR\|(3),
\&\fBX509_LOOKUP_meth_new\fR\|(3),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
