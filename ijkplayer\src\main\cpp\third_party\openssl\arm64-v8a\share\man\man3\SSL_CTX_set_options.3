.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_OPTIONS 3"
.TH SSL_CTX_SET_OPTIONS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_options, SSL_set_options, SSL_CTX_clear_options,
SSL_clear_options, SSL_CTX_get_options, SSL_get_options,
SSL_get_secure_renegotiation_support \- manipulate SSL options
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_set_options(SSL_CTX *ctx, long options);
\& long SSL_set_options(SSL *ssl, long options);
\&
\& long SSL_CTX_clear_options(SSL_CTX *ctx, long options);
\& long SSL_clear_options(SSL *ssl, long options);
\&
\& long SSL_CTX_get_options(SSL_CTX *ctx);
\& long SSL_get_options(SSL *ssl);
\&
\& long SSL_get_secure_renegotiation_support(SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_options()\fR adds the options set via bit mask in \fBoptions\fR to \fBctx\fR.
Options already set before are not cleared!
.PP
\&\fBSSL_set_options()\fR adds the options set via bit mask in \fBoptions\fR to \fBssl\fR.
Options already set before are not cleared!
.PP
\&\fBSSL_CTX_clear_options()\fR clears the options set via bit mask in \fBoptions\fR
to \fBctx\fR.
.PP
\&\fBSSL_clear_options()\fR clears the options set via bit mask in \fBoptions\fR to \fBssl\fR.
.PP
\&\fBSSL_CTX_get_options()\fR returns the options set for \fBctx\fR.
.PP
\&\fBSSL_get_options()\fR returns the options set for \fBssl\fR.
.PP
\&\fBSSL_get_secure_renegotiation_support()\fR indicates whether the peer supports
secure renegotiation.
Note, this is implemented via a macro.
.SH NOTES
.IX Header "NOTES"
The behaviour of the SSL library can be changed by setting several options.
The options are coded as bit masks and can be combined by a bitwise \fBor\fR
operation (|).
.PP
\&\fBSSL_CTX_set_options()\fR and \fBSSL_set_options()\fR affect the (external)
protocol behaviour of the SSL library. The (internal) behaviour of
the API can be changed by using the similar
\&\fBSSL_CTX_set_mode\fR\|(3) and \fBSSL_set_mode()\fR functions.
.PP
During a handshake, the option settings of the SSL object are used. When
a new SSL object is created from a context using \fBSSL_new()\fR, the current
option setting is copied. Changes to \fBctx\fR do not affect already created
SSL objects. \fBSSL_clear()\fR does not affect the settings.
.PP
The following \fBbug workaround\fR options are available:
.IP SSL_OP_SAFARI_ECDHE_ECDSA_BUG 4
.IX Item "SSL_OP_SAFARI_ECDHE_ECDSA_BUG"
Don't prefer ECDHE-ECDSA ciphers when the client appears to be Safari on OS X.
OS X 10.8..10.8.3 has broken support for ECDHE-ECDSA ciphers.
.IP SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS 4
.IX Item "SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS"
Disables a countermeasure against a SSL 3.0/TLS 1.0 protocol
vulnerability affecting CBC ciphers, which cannot be handled by some
broken SSL implementations.  This option has no effect for connections
using other ciphers.
.IP SSL_OP_TLSEXT_PADDING 4
.IX Item "SSL_OP_TLSEXT_PADDING"
Adds a padding extension to ensure the ClientHello size is never between
256 and 511 bytes in length. This is needed as a workaround for some
implementations.
.IP SSL_OP_ALL 4
.IX Item "SSL_OP_ALL"
All of the above bug workarounds plus \fBSSL_OP_LEGACY_SERVER_CONNECT\fR as
mentioned below.
.PP
It is usually safe to use \fBSSL_OP_ALL\fR to enable the bug workaround
options if compatibility with somewhat broken implementations is
desired.
.PP
The following \fBmodifying\fR options are available:
.IP SSL_OP_TLS_ROLLBACK_BUG 4
.IX Item "SSL_OP_TLS_ROLLBACK_BUG"
Disable version rollback attack detection.
.Sp
During the client key exchange, the client must send the same information
about acceptable SSL/TLS protocol levels as during the first hello. Some
clients violate this rule by adapting to the server's answer. (Example:
the client sends a SSLv2 hello and accepts up to SSLv3.1=TLSv1, the server
only understands up to SSLv3. In this case the client must still use the
same SSLv3.1=TLSv1 announcement. Some clients step down to SSLv3 with respect
to the server's answer and violate the version rollback protection.)
.IP SSL_OP_CIPHER_SERVER_PREFERENCE 4
.IX Item "SSL_OP_CIPHER_SERVER_PREFERENCE"
When choosing a cipher, use the server's preferences instead of the client
preferences. When not set, the SSL server will always follow the clients
preferences. When set, the SSL/TLS server will choose following its
own preferences.
.IP "SSL_OP_NO_SSLv3, SSL_OP_NO_TLSv1, SSL_OP_NO_TLSv1_1, SSL_OP_NO_TLSv1_2, SSL_OP_NO_TLSv1_3, SSL_OP_NO_DTLSv1, SSL_OP_NO_DTLSv1_2" 4
.IX Item "SSL_OP_NO_SSLv3, SSL_OP_NO_TLSv1, SSL_OP_NO_TLSv1_1, SSL_OP_NO_TLSv1_2, SSL_OP_NO_TLSv1_3, SSL_OP_NO_DTLSv1, SSL_OP_NO_DTLSv1_2"
These options turn off the SSLv3, TLSv1, TLSv1.1, TLSv1.2 or TLSv1.3 protocol
versions with TLS or the DTLSv1, DTLSv1.2 versions with DTLS,
respectively.
As of OpenSSL 1.1.0, these options are deprecated, use
\&\fBSSL_CTX_set_min_proto_version\fR\|(3) and
\&\fBSSL_CTX_set_max_proto_version\fR\|(3) instead.
.IP SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION 4
.IX Item "SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION"
When performing renegotiation as a server, always start a new session
(i.e., session resumption requests are only accepted in the initial
handshake). This option is not needed for clients.
.IP SSL_OP_NO_COMPRESSION 4
.IX Item "SSL_OP_NO_COMPRESSION"
Do not use compression even if it is supported.
.IP SSL_OP_NO_QUERY_MTU 4
.IX Item "SSL_OP_NO_QUERY_MTU"
Do not query the MTU. Only affects DTLS connections.
.IP SSL_OP_COOKIE_EXCHANGE 4
.IX Item "SSL_OP_COOKIE_EXCHANGE"
Turn on Cookie Exchange as described in RFC4347 Section 4.2.1. Only affects
DTLS connections.
.IP SSL_OP_NO_TICKET 4
.IX Item "SSL_OP_NO_TICKET"
SSL/TLS supports two mechanisms for resuming sessions: session ids and stateless
session tickets.
.Sp
When using session ids a copy of the session information is
cached on the server and a unique id is sent to the client. When the client
wishes to resume it provides the unique id so that the server can retrieve the
session information from its cache.
.Sp
When using stateless session tickets the server uses a session ticket encryption
key to encrypt the session information. This encrypted data is sent to the
client as a "ticket". When the client wishes to resume it sends the encrypted
data back to the server. The server uses its key to decrypt the data and resume
the session. In this way the server can operate statelessly \- no session
information needs to be cached locally.
.Sp
The TLSv1.3 protocol only supports tickets and does not directly support session
ids. However, OpenSSL allows two modes of ticket operation in TLSv1.3: stateful
and stateless. Stateless tickets work the same way as in TLSv1.2 and below.
Stateful tickets mimic the session id behaviour available in TLSv1.2 and below.
The session information is cached on the server and the session id is wrapped up
in a ticket and sent back to the client. When the client wishes to resume, it
presents a ticket in the same way as for stateless tickets. The server can then
extract the session id from the ticket and retrieve the session information from
its cache.
.Sp
By default OpenSSL will use stateless tickets. The SSL_OP_NO_TICKET option will
cause stateless tickets to not be issued. In TLSv1.2 and below this means no
ticket gets sent to the client at all. In TLSv1.3 a stateful ticket will be
sent. This is a server-side option only.
.Sp
In TLSv1.3 it is possible to suppress all tickets (stateful and stateless) from
being sent by calling \fBSSL_CTX_set_num_tickets\fR\|(3) or
\&\fBSSL_set_num_tickets\fR\|(3).
.IP SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION 4
.IX Item "SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION"
Allow legacy insecure renegotiation between OpenSSL and unpatched clients or
servers. See the \fBSECURE RENEGOTIATION\fR section for more details.
.IP SSL_OP_LEGACY_SERVER_CONNECT 4
.IX Item "SSL_OP_LEGACY_SERVER_CONNECT"
Allow legacy insecure renegotiation between OpenSSL and unpatched servers
\&\fBonly\fR: this option is currently set by default. See the
\&\fBSECURE RENEGOTIATION\fR section for more details.
.IP SSL_OP_NO_ENCRYPT_THEN_MAC 4
.IX Item "SSL_OP_NO_ENCRYPT_THEN_MAC"
Normally clients and servers will transparently attempt to negotiate the
RFC7366 Encrypt-then-MAC option on TLS and DTLS connection.
.Sp
If this option is set, Encrypt-then-MAC is disabled. Clients will not
propose, and servers will not accept the extension.
.IP SSL_OP_NO_RENEGOTIATION 4
.IX Item "SSL_OP_NO_RENEGOTIATION"
Disable all renegotiation in TLSv1.2 and earlier. Do not send HelloRequest
messages, and ignore renegotiation requests via ClientHello.
.IP SSL_OP_ALLOW_NO_DHE_KEX 4
.IX Item "SSL_OP_ALLOW_NO_DHE_KEX"
In TLSv1.3 allow a non\-(ec)dhe based key exchange mode on resumption. This means
that there will be no forward secrecy for the resumed session.
.IP SSL_OP_PRIORITIZE_CHACHA 4
.IX Item "SSL_OP_PRIORITIZE_CHACHA"
When SSL_OP_CIPHER_SERVER_PREFERENCE is set, temporarily reprioritize
ChaCha20\-Poly1305 ciphers to the top of the server cipher list if a
ChaCha20\-Poly1305 cipher is at the top of the client cipher list. This helps
those clients (e.g. mobile) use ChaCha20\-Poly1305 if that cipher is anywhere
in the server cipher list; but still allows other clients to use AES and other
ciphers. Requires \fBSSL_OP_CIPHER_SERVER_PREFERENCE\fR.
.IP SSL_OP_ENABLE_MIDDLEBOX_COMPAT 4
.IX Item "SSL_OP_ENABLE_MIDDLEBOX_COMPAT"
If set then dummy Change Cipher Spec (CCS) messages are sent in TLSv1.3. This
has the effect of making TLSv1.3 look more like TLSv1.2 so that middleboxes that
do not understand TLSv1.3 will not drop the connection. Regardless of whether
this option is set or not CCS messages received from the peer will always be
ignored in TLSv1.3. This option is set by default. To switch it off use
\&\fBSSL_clear_options()\fR. A future version of OpenSSL may not set this by default.
.IP SSL_OP_NO_ANTI_REPLAY 4
.IX Item "SSL_OP_NO_ANTI_REPLAY"
By default, when a server is configured for early data (i.e., max_early_data > 0),
OpenSSL will switch on replay protection. See \fBSSL_read_early_data\fR\|(3) for a
description of the replay protection feature. Anti-replay measures are required
to comply with the TLSv1.3 specification. Some applications may be able to
mitigate the replay risks in other ways and in such cases the built in OpenSSL
functionality is not required. Those applications can turn this feature off by
setting this option. This is a server-side opton only. It is ignored by
clients.
.PP
The following options no longer have any effect but their identifiers are
retained for compatibility purposes:
.IP SSL_OP_NETSCAPE_REUSE_CIPHER_CHANGE_BUG 4
.IX Item "SSL_OP_NETSCAPE_REUSE_CIPHER_CHANGE_BUG"
.PD 0
.IP SSL_OP_MICROSOFT_BIG_SSLV3_BUFFER 4
.IX Item "SSL_OP_MICROSOFT_BIG_SSLV3_BUFFER"
.IP SSL_OP_SSLEAY_080_CLIENT_DH_BUG 4
.IX Item "SSL_OP_SSLEAY_080_CLIENT_DH_BUG"
.IP SSL_OP_TLS_D5_BUG 4
.IX Item "SSL_OP_TLS_D5_BUG"
.IP SSL_OP_TLS_BLOCK_PADDING_BUG 4
.IX Item "SSL_OP_TLS_BLOCK_PADDING_BUG"
.IP SSL_OP_MSIE_SSLV2_RSA_PADDING 4
.IX Item "SSL_OP_MSIE_SSLV2_RSA_PADDING"
.IP SSL_OP_SSLREF2_REUSE_CERT_TYPE_BUG 4
.IX Item "SSL_OP_SSLREF2_REUSE_CERT_TYPE_BUG"
.IP SSL_OP_MICROSOFT_SESS_ID_BUG 4
.IX Item "SSL_OP_MICROSOFT_SESS_ID_BUG"
.IP SSL_OP_NETSCAPE_CHALLENGE_BUG 4
.IX Item "SSL_OP_NETSCAPE_CHALLENGE_BUG"
.IP SSL_OP_PKCS1_CHECK_1 4
.IX Item "SSL_OP_PKCS1_CHECK_1"
.IP SSL_OP_PKCS1_CHECK_2 4
.IX Item "SSL_OP_PKCS1_CHECK_2"
.IP SSL_OP_SINGLE_DH_USE 4
.IX Item "SSL_OP_SINGLE_DH_USE"
.IP SSL_OP_SINGLE_ECDH_USE 4
.IX Item "SSL_OP_SINGLE_ECDH_USE"
.IP SSL_OP_EPHEMERAL_RSA 4
.IX Item "SSL_OP_EPHEMERAL_RSA"
.PD
.SH "SECURE RENEGOTIATION"
.IX Header "SECURE RENEGOTIATION"
OpenSSL always attempts to use secure renegotiation as
described in RFC5746. This counters the prefix attack described in
CVE\-2009\-3555 and elsewhere.
.PP
This attack has far reaching consequences which application writers should be
aware of. In the description below an implementation supporting secure
renegotiation is referred to as \fIpatched\fR. A server not supporting secure
renegotiation is referred to as \fIunpatched\fR.
.PP
The following sections describe the operations permitted by OpenSSL's secure
renegotiation implementation.
.SS "Patched client and server"
.IX Subsection "Patched client and server"
Connections and renegotiation are always permitted by OpenSSL implementations.
.SS "Unpatched client and patched OpenSSL server"
.IX Subsection "Unpatched client and patched OpenSSL server"
The initial connection succeeds but client renegotiation is denied by the
server with a \fBno_renegotiation\fR warning alert if TLS v1.0 is used or a fatal
\&\fBhandshake_failure\fR alert in SSL v3.0.
.PP
If the patched OpenSSL server attempts to renegotiate a fatal
\&\fBhandshake_failure\fR alert is sent. This is because the server code may be
unaware of the unpatched nature of the client.
.PP
If the option \fBSSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION\fR is set then
renegotiation \fBalways\fR succeeds.
.SS "Patched OpenSSL client and unpatched server."
.IX Subsection "Patched OpenSSL client and unpatched server."
If the option \fBSSL_OP_LEGACY_SERVER_CONNECT\fR or
\&\fBSSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION\fR is set then initial connections
and renegotiation between patched OpenSSL clients and unpatched servers
succeeds. If neither option is set then initial connections to unpatched
servers will fail.
.PP
The option \fBSSL_OP_LEGACY_SERVER_CONNECT\fR is currently set by default even
though it has security implications: otherwise it would be impossible to
connect to unpatched servers (i.e. all of them initially) and this is clearly
not acceptable. Renegotiation is permitted because this does not add any
additional security issues: during an attack clients do not see any
renegotiations anyway.
.PP
As more servers become patched the option \fBSSL_OP_LEGACY_SERVER_CONNECT\fR will
\&\fBnot\fR be set by default in a future version of OpenSSL.
.PP
OpenSSL client applications wishing to ensure they can connect to unpatched
servers should always \fBset\fR \fBSSL_OP_LEGACY_SERVER_CONNECT\fR
.PP
OpenSSL client applications that want to ensure they can \fBnot\fR connect to
unpatched servers (and thus avoid any security issues) should always \fBclear\fR
\&\fBSSL_OP_LEGACY_SERVER_CONNECT\fR using \fBSSL_CTX_clear_options()\fR or
\&\fBSSL_clear_options()\fR.
.PP
The difference between the \fBSSL_OP_LEGACY_SERVER_CONNECT\fR and
\&\fBSSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION\fR options is that
\&\fBSSL_OP_LEGACY_SERVER_CONNECT\fR enables initial connections and secure
renegotiation between OpenSSL clients and unpatched servers \fBonly\fR, while
\&\fBSSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION\fR allows initial connections
and renegotiation between OpenSSL and unpatched clients or servers.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_options()\fR and \fBSSL_set_options()\fR return the new options bit mask
after adding \fBoptions\fR.
.PP
\&\fBSSL_CTX_clear_options()\fR and \fBSSL_clear_options()\fR return the new options bit mask
after clearing \fBoptions\fR.
.PP
\&\fBSSL_CTX_get_options()\fR and \fBSSL_get_options()\fR return the current bit mask.
.PP
\&\fBSSL_get_secure_renegotiation_support()\fR returns 1 is the peer supports
secure renegotiation and 0 if it does not.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_new\fR\|(3), \fBSSL_clear\fR\|(3),
\&\fBSSL_CTX_set_tmp_dh_callback\fR\|(3),
\&\fBSSL_CTX_set_min_proto_version\fR\|(3),
\&\fBdhparam\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The attempt to always try to use secure renegotiation was added in
OpenSSL 0.9.8m.
.PP
The \fBSSL_OP_PRIORITIZE_CHACHA\fR and \fBSSL_OP_NO_RENEGOTIATION\fR options
were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
