.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_NEW 3"
.TH EVP_PKEY_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_new,
EVP_PKEY_up_ref,
EVP_PKEY_free,
EVP_PKEY_new_raw_private_key,
EVP_PKEY_new_raw_public_key,
EVP_PKEY_new_CMAC_key,
EVP_PKEY_new_mac_key,
EVP_PKEY_get_raw_private_key,
EVP_PKEY_get_raw_public_key
\&\- public/private key allocation and raw key handling functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_PKEY *EVP_PKEY_new(void);
\& int EVP_PKEY_up_ref(EVP_PKEY *key);
\& void EVP_PKEY_free(EVP_PKEY *key);
\&
\& EVP_PKEY *EVP_PKEY_new_raw_private_key(int type, ENGINE *e,
\&                                        const unsigned char *key, size_t keylen);
\& EVP_PKEY *EVP_PKEY_new_raw_public_key(int type, ENGINE *e,
\&                                       const unsigned char *key, size_t keylen);
\& EVP_PKEY *EVP_PKEY_new_CMAC_key(ENGINE *e, const unsigned char *priv,
\&                                 size_t len, const EVP_CIPHER *cipher);
\& EVP_PKEY *EVP_PKEY_new_mac_key(int type, ENGINE *e, const unsigned char *key,
\&                                int keylen);
\&
\& int EVP_PKEY_get_raw_private_key(const EVP_PKEY *pkey, unsigned char *priv,
\&                                  size_t *len);
\& int EVP_PKEY_get_raw_public_key(const EVP_PKEY *pkey, unsigned char *pub,
\&                                 size_t *len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_new()\fR function allocates an empty \fBEVP_PKEY\fR structure which is
used by OpenSSL to store public and private keys. The reference count is set to
\&\fB1\fR.
.PP
\&\fBEVP_PKEY_up_ref()\fR increments the reference count of \fBkey\fR.
.PP
\&\fBEVP_PKEY_free()\fR decrements the reference count of \fBkey\fR and, if the reference
count is zero, frees it up. If \fBkey\fR is NULL, nothing is done.
.PP
\&\fBEVP_PKEY_new_raw_private_key()\fR allocates a new \fBEVP_PKEY\fR. If \fBe\fR is non-NULL
then the new \fBEVP_PKEY\fR structure is associated with the engine \fBe\fR. The
\&\fBtype\fR argument indicates what kind of key this is. The value should be a NID
for a public key algorithm that supports raw private keys, i.e. one of
\&\fBEVP_PKEY_HMAC\fR, \fBEVP_PKEY_POLY1305\fR, \fBEVP_PKEY_SIPHASH\fR, \fBEVP_PKEY_X25519\fR,
\&\fBEVP_PKEY_ED25519\fR, \fBEVP_PKEY_X448\fR or \fBEVP_PKEY_ED448\fR. \fBkey\fR points to the
raw private key data for this \fBEVP_PKEY\fR which should be of length \fBkeylen\fR.
The length should be appropriate for the type of the key. The public key data
will be automatically derived from the given private key data (if appropriate
for the algorithm type).
.PP
\&\fBEVP_PKEY_new_raw_public_key()\fR works in the same way as
\&\fBEVP_PKEY_new_raw_private_key()\fR except that \fBkey\fR points to the raw public key
data. The \fBEVP_PKEY\fR structure will be initialised without any private key
information. Algorithm types that support raw public keys are
\&\fBEVP_PKEY_X25519\fR, \fBEVP_PKEY_ED25519\fR, \fBEVP_PKEY_X448\fR or \fBEVP_PKEY_ED448\fR.
.PP
\&\fBEVP_PKEY_new_CMAC_key()\fR works in the same way as \fBEVP_PKEY_new_raw_private_key()\fR
except it is only for the \fBEVP_PKEY_CMAC\fR algorithm type. In addition to the
raw private key data, it also takes a cipher algorithm to be used during
creation of a CMAC in the \fBcipher\fR argument. The cipher should be a standard
encryption only cipher. For example AEAD and XTS ciphers should not be used.
.PP
\&\fBEVP_PKEY_new_mac_key()\fR works in the same way as \fBEVP_PKEY_new_raw_private_key()\fR.
New applications should use \fBEVP_PKEY_new_raw_private_key()\fR instead.
.PP
\&\fBEVP_PKEY_get_raw_private_key()\fR fills the buffer provided by \fBpriv\fR with raw
private key data. The size of the \fBpriv\fR buffer should be in \fB*len\fR on entry
to the function, and on exit \fB*len\fR is updated with the number of bytes
actually written. If the buffer \fBpriv\fR is NULL then \fB*len\fR is populated with
the number of bytes required to hold the key. The calling application is
responsible for ensuring that the buffer is large enough to receive the private
key data. This function only works for algorithms that support raw private keys.
Currently this is: \fBEVP_PKEY_HMAC\fR, \fBEVP_PKEY_POLY1305\fR, \fBEVP_PKEY_SIPHASH\fR,
\&\fBEVP_PKEY_X25519\fR, \fBEVP_PKEY_ED25519\fR, \fBEVP_PKEY_X448\fR or \fBEVP_PKEY_ED448\fR.
.PP
\&\fBEVP_PKEY_get_raw_public_key()\fR fills the buffer provided by \fBpub\fR with raw
public key data. The size of the \fBpub\fR buffer should be in \fB*len\fR on entry
to the function, and on exit \fB*len\fR is updated with the number of bytes
actually written. If the buffer \fBpub\fR is NULL then \fB*len\fR is populated with
the number of bytes required to hold the key. The calling application is
responsible for ensuring that the buffer is large enough to receive the public
key data. This function only works for algorithms that support raw public  keys.
Currently this is: \fBEVP_PKEY_X25519\fR, \fBEVP_PKEY_ED25519\fR, \fBEVP_PKEY_X448\fR or
\&\fBEVP_PKEY_ED448\fR.
.SH NOTES
.IX Header "NOTES"
The \fBEVP_PKEY\fR structure is used by various OpenSSL functions which require a
general private key without reference to any particular algorithm.
.PP
The structure returned by \fBEVP_PKEY_new()\fR is empty. To add a private or public
key to this empty structure use the appropriate functions described in
\&\fBEVP_PKEY_set1_RSA\fR\|(3), EVP_PKEY_set1_DSA, EVP_PKEY_set1_DH or
EVP_PKEY_set1_EC_KEY.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_new()\fR, \fBEVP_PKEY_new_raw_private_key()\fR, \fBEVP_PKEY_new_raw_public_key()\fR,
\&\fBEVP_PKEY_new_CMAC_key()\fR and \fBEVP_PKEY_new_mac_key()\fR return either the newly
allocated \fBEVP_PKEY\fR structure or \fBNULL\fR if an error occurred.
.PP
\&\fBEVP_PKEY_up_ref()\fR, \fBEVP_PKEY_get_raw_private_key()\fR and
\&\fBEVP_PKEY_get_raw_public_key()\fR return 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_set1_RSA\fR\|(3), EVP_PKEY_set1_DSA, EVP_PKEY_set1_DH or
EVP_PKEY_set1_EC_KEY
.SH HISTORY
.IX Header "HISTORY"
The
\&\fBEVP_PKEY_new()\fR and \fBEVP_PKEY_free()\fR functions exist in all versions of OpenSSL.
.PP
The \fBEVP_PKEY_up_ref()\fR function was added in OpenSSL 1.1.0.
.PP
The
\&\fBEVP_PKEY_new_raw_private_key()\fR, \fBEVP_PKEY_new_raw_public_key()\fR,
\&\fBEVP_PKEY_new_CMAC_key()\fR, \fBEVP_PKEY_new_raw_private_key()\fR and
\&\fBEVP_PKEY_get_raw_public_key()\fR functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
