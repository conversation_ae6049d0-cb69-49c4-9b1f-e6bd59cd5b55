/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
// 在 index.ets 顶部添加
import connection from '@ohos.net.connection';
import promptAction from '@ohos.promptAction';
import {RZXTrans_Lan} from 'librzxlan'
import { ImageProcessingUtils } from '../utils/ImageProcessingUtils';
import { FpsBufferUtils, FpsStateInfo } from '../model/FpsBufferManager';
// import { xClient, SubData, PublishData, SubscribeCallback,PublishAckCallback,NapiError } from 'xclient';
import { Player } from './player';
import buffer from '@ohos.buffer';
import display from '@ohos.display';
import Logger from '../model/Logger';
import { SettingDialog } from '../Dialog/SettingDialog';
import CameraService from '../model/CameraService';
import { CountdownPage } from '../views/CountdownPage';
import { FlashingLightPage } from '../views/FlashingLightPage';
import { FocusPage } from '../views/FocusPage';
import { SlidePage } from '../views/SlidePage';
import { DividerPage } from '../views/DividerPage';
import { FocusAreaPage } from '../views/FocusAreaPage';
import { ModeSwitchPage } from '../views/ModeSwitchPage';
import { Constants } from '../common/Constants';
import DisplayCalculator from '../common/DisplayCalculator';
import image from '@ohos.multimedia.image';
import { CameraConfig } from '../common/CameraConfig';
import { GlobalContext } from '../common/GlobalContext';
import abilityAccessCtrl from '@ohos.abilityAccessCtrl';
import common from '@ohos.app.ability.common';
import { Router } from '@ohos.arkui.UIContext';
import router from '@ohos.router';

const TAG: string = 'indexPage';

let cameraConfig: CameraConfig = {
  mirrorBol: false, // 镜像使能 -> 关闭
  videoStabilizationMode: 0, // 视频防抖 -> 关闭
  exposureMode: 1, // 曝光模式 -> 自动
  focusMode: 2, // 对焦模式 -> 自动
  photoQuality: 1, // 拍照质量 -> 中
  locationBol: false, // 显示地理位置 -> 关闭
  hdrPhotoBol: false, // HDR 拍摄 -> 关闭
  photoFormat: 1, // 照片格式 -> JPG
  photoOrientation: 0, // 照片方向 -> 0
  photoResolution: 0, // 照片分辨率 -> 1920 * 1080
  videoResolution: 0, // 照片分辨率 -> 1920 * 1080
  videoFrame: 1, // 录像帧率 -> 30
  hdrVideoBol:false, // HDR 录制 -> 关闭
  referenceLineBol: false // 分割线 -> 关闭
}
GlobalContext.get().setObject('cameraConfig', cameraConfig);
interface playerWindowPosition{
  x: number,
  y: number
}
@Entry
@Component
struct Index {
  // RZXTrans_Lan
  @State initInterfaceName:string=''
  @State interfaceName:string=''
  private deviceManager: RZXTrans_Lan = new RZXTrans_Lan();
  @State initSymbol:string=''
  @State userName:string='123'
  // ===
  @State showSwitch:boolean=false
  @State message: string = '准备连接';
  @State ipAddress: string = '127.0.0.1'
  @State isSubscribed: boolean = false // 标记是否已订阅
  @State showPlayerWindow: boolean = true // 控制播放器小窗显示
  @State playerWindowPosition: playerWindowPosition = { x: 20, y: 100 } // 播放器小窗位置
  // xclientPayload相关参数引入
  @State @Watch('onRecvCount')recvCount:number = 0;
  onRecvCount(){
    console.log('xlan_接收数据监听中')
  //   进行buffer数据的解析
  }
  // private client: xClient | null = null;
  // private subscribeSensorText:string = 'NN.BB.MM'
  private subscribeSensorText:string = 'BBBB'
  private unsubscribeSensorText:string = 'AA.BB.CC'
  private publishCommandText:string = ''
  private publishCommandSubText:string = '图像数据11111111aaaaaaaaajjjjjjdddddggggsssssss'
  // ======
  @StorageLink ('FpsCount') @Watch('onFpsCount') FpsCount:number=0
  @StorageLink ('FpsBuffer') @Watch('onFpsBuffer') FpsBufferBase:ArrayBuffer=new ArrayBuffer(0)
  private FpsBuffer:ArrayBuffer|undefined|null=null
  async onFpsCount(){
    console.log('帧数在变化',this.FpsCount)
    this.FpsBuffer=FpsBufferUtils.getFpsBuffer();
    if(this.FpsBuffer){
      console.log('全局变量_this.FpsBuffer',this.FpsBuffer)
      console.log('全局变量_this.FpsBuffer长度',this.FpsBuffer.byteLength)
      console.log('进行处理图像')
      const result = await ImageProcessingUtils.quickProcess(this.FpsBuffer);
      console.log(result ? '成功处理图像' : '失败处理图像');

      // xclient命令执行
      // this.publishCommandSub()
    }else{
      console.log('this.FpsBuffer为空||undefined')
    }
  }
  onFpsBuffer(){
    console.log('AppStorage_FpsBufferBase在变化',this.FpsBufferBase)
    console.log('AppStorage_FpsBufferBase在变化_长度',this.FpsBufferBase.byteLength)
    // this.publishCommandSubStream()
  }
  // 使用 @State 管理预览PixelMap，提供更好的性能
  @State previewPixelMap: image.PixelMap | undefined|null = undefined;
  // 添加一个定时器来更新状态
  private updateTimer: number = -1;
  // 添加性能监控
  private lastUpdateTime: number = 0;
  // XComponentCon控制器
  private mXComponentController: XComponentController = new XComponentController();
  // surfaceID值
  @State surfaceId: string = '';
  // 选择模式
  @State modelBagCol: string = 'photo';
  // 曝光区域
  @State focusPointBol: boolean = false;
  // 曝光区域手指点击坐标
  @State focusPointVal: Array<number> = [0, 0];
  // 刻度、焦距值 和 对焦框不能共存的显示
  @State exposureBol: boolean = true;
  // 曝光值
  @State exposureNum: number = 0;
  // 倒计时拍照 录像
  @State countdownNum: number = 0;
  // 相机摄像头
  @State cameraDeviceIndex: number = 0;
  @State xComponentWidth: number = 384;
  @State xComponentHeight: number = 450;
  private deviceType: string | undefined = '';
  private screenHeight: number = 0;
  private screenWidth: number = 0;

  // 设置弹框
  private settingDialogController: CustomDialogController = new CustomDialogController({
    builder: SettingDialog({
      referenceLineBol: $referenceLineBol,
      surfaceId: this.surfaceId,
      cameraDeviceIndex: this.cameraDeviceIndex
    }),
    autoCancel: false,
    customStyle: true
  });
  // 参考线
  @State referenceLineBol: boolean = false;
  @StorageLink('defaultAspectRatio') @Watch('initXComponentSize') defaultAspectRatio: number = Constants.MIN_ASPECT_RATIO;
  // @StorageLink('defaultAspectRatio') @Watch('initXComponentSize') defaultAspectRatio: number = Constants.MAX_ASPECT_RATIO;
  // 主页面是否显示
  @State isShow: boolean = false;
  // 是否支持变焦
  @StorageLink('isSupportZoom') isSupportZoom: boolean = false;
  @StorageLink('previewFPS') previewFPS: string = '0.0';
  @StorageLink('previewFrameCount') previewFrameCount: number = 0;

  private atManager = abilityAccessCtrl.createAtManager();
  private appContext: common.Context = getContext(this);

  /**
   * 获取权限
   */
  async requestPermissionsFn() {
    Logger.info(TAG, `requestPermissionsFn entry`);
    try {
      this.atManager.requestPermissionsFromUser(this.appContext, [
        'ohos.permission.CAMERA',
        'ohos.permission.MICROPHONE',
        'ohos.permission.READ_IMAGEVIDEO',
        'ohos.permission.WRITE_IMAGEVIDEO'
      ]).then(() => {
        Logger.info(TAG, `request Permissions success!`);
        this.isShow = true;
      })
    } catch (err) {
      Logger.info(TAG, `requestPermissionsFromUser call Failed! error: ${err.code}`);
    }
  }

  async aboutToAppear() {
    await this.requestPermissionsFn();
    let mDisplay = display.getDefaultDisplaySync();
    this.screenWidth = px2vp(mDisplay.width);
    this.screenHeight = px2vp(mDisplay.height);
    this.deviceType = AppStorage.get<string>('deviceType');
    await this.getInterfaceName()
    if(this.interfaceName){
      promptAction.showToast({
        // message: `本机IP地址：${this.localIP}本机设备名称：${this.localDeviceName}`,
        message: `interfaceName：${this.interfaceName}`,
        duration: 3000
      });
    }
    this.initRZXTrans_Lan()
    // 初始化
    // this.initClient()
    console.log('index_this.deviceType',this.deviceType)
    if (this.deviceType === Constants.TABLET) {
      this.defaultAspectRatio = Constants.MAX_ASPECT_RATIO;
      console.log('this.defaultAspectRatio',this.defaultAspectRatio)
    }
    this.initXComponentSize();
    // 设置定时器定期从AppStorage获取值，优化性能
    this.updateTimer = setInterval(() => {
      const currentTime = Date.now();

      // 限制更新频率，避免过度渲染
      // if (currentTime - this.lastUpdateTime > 50) { // 最多20FPS更新
      if (currentTime - this.lastUpdateTime > 30) { // 最多20FPS更新
        // 安全地获取值，提供默认值
        const newPixelMap = AppStorage.get<image.PixelMap>('previewPixelMap');
        console.log('newPixelMap外部',newPixelMap)

        // 只有当PixelMap真正改变时才更新
        if (newPixelMap !== this.previewPixelMap) {
          console.log('newPixelMap',newPixelMap)
          this.previewPixelMap = newPixelMap;
        }
        this.previewFrameCount = AppStorage.get<number>('previewFrameCount') || 0;
        this.previewFPS = AppStorage.get<string>('previewFPS') || '0.0';
        this.lastUpdateTime = currentTime;
      }
    }, 15); // 每30ms检查一次
  }
  // xclientFunction
  // 初始化客户端
  // private initClient() {
  //   try {
  //     console.log('111111')
  //     this.client = new xClient("127.0.0.1");
  //     this.message = `已连接 ${this.client.constructor.name}`;
  //   } catch (e) {
  //     const err = e as NapiError;
  //     this.message = `错误[${err.errorCode}]: ${err.message}`;
  //     console.error(err.nativeStack);
  //   }
  // }
  //
  // // public subCallback(topic:string, data:string)  {
  // //   // this.message = `${topic}更新: ${data}℃`;
  // //
  // //   console.log(`${topic}更新: ${data}℃`);
  // //   // 触发UI更新 会报错，需要使用 postTask -> UI线程 来更新ui线程
  // //   // this.updateChart(parseFloat(data));
  // // }
  // private subCallback = (topic: string, data: Uint8Array): void => {
  //   console.log(`${topic}  更新:  ${data.toString()}`);
  //   let serverData:Uint8Array=new Uint8Array(data)
  //   let str=""
  //   for (let i = 0; i < serverData.length; i++) {
  //     str+=String.fromCharCode(serverData[i])
  //   }
  //   console.log('str',str)
  //   this.recvCount ++;
  //   // globalReceiveMessages.push(str.toString())
  //
  //   // return
  //   this.updateChart(str.toString());
  //   console.log('只计数')
  //   //if(this.recvCount <= 2000) {  // 避免太多日志
  //   // this.updateChart(str.toString());
  //   //}
  // }
  // // 订阅温度传感器
  // private subscribeSensor() {
  //   if (!this.client) return;
  //
  //   const subData: SubData = {topic:this.subscribeSensorText}  ;
  //   this.client.subscribe(subData, this.subCallback);
  // }
  //
  // // 发布控制指令
  // private publishCommandSub() {
  //   if (!this.client) return;
  //
  //   // if(this.FpsBuffer){
  //     const pubData: PublishData = {
  //       topic: this.subscribeSensorText,
  //       data: new Uint8Array(buffer.from(this.publishCommandSubText).buffer),
  //       // data: new Uint8Array(buffer.from(this.FpsBuffer).buffer),
  //       // data: new Uint8Array(this.FpsBuffer),
  //       priority: 1,
  //       reliability: 2
  //     };
  //
  //     this.client.publish(pubData, (topic:string, status:number) => {
  //       if (status === 200) {
  //         this.message = `指令已确认: ${topic}`;
  //
  //       } else {
  //         this.message = `发送失败: ${status}`;
  //       }
  //     });
  //   // }
  //
  // }
  // 初始化xlan
  // 网卡信息获取
  // 构建私有函数获取本机IP
  // 获取本机IP地址
  // @State localIP:string=''
  async  getInterfaceName(): Promise<void> {
    try {
      // 获取所有网络连接
      const nets = await connection.getAllNets();
      const nets2 = await connection.getDefaultNet();
      const connProp  = await connection.getConnectionProperties(nets2);
      if(connProp){
        console.log('connProp',connProp.linkAddresses)
        console.log('connProp',JSON.stringify(connProp))
        console.log('interfaceName',connProp.interfaceName)
        let interfaceName=connProp.interfaceName
        if(interfaceName){
          this.initInterfaceName=interfaceName
          this.interfaceName=interfaceName
        }
        // 解析IP地址
        for (const addr of connProp.linkAddresses) {
          const ip = addr.address.address;
          console.log('addr.address.family',addr.address.family)
        }
      }
    } catch (error) {
      console.error(`Failed to get IP: ${error}`);
    }
  }
  // 初始化客户端
  private initRZXTrans_Lan() {
    console.log('xlan_init','执行初始化函数')
    try {
      this.initSymbol=this.deviceManager.init(this.initInterfaceName).toString();
      if(this.initSymbol==='1'){
        this.message = `已连接 RZXLan`;
      }
    } catch (err) {
      this.message = `错误[${err}]`;
      console.log(err);
    }
  }
  // 订阅stream
  private subCallbackStream = (topic: string, userName: string, data: Uint8Array): void => {
    console.log(`${topic}  更新:  ${data.toString()}`);
    console.log(`${userName}  更新:  ${userName.toString()}`);
    // 方法1：类型转换 ArrayBufferLike 到 ArrayBuffer（推荐）
    let getData: ArrayBuffer = data.buffer as ArrayBuffer;
    // let getData1: ArrayBuffer = data.buffer ;
    //   let serverData:Uint8Array=new Uint8Array(data)
    // let getData1: ArrayBuffer = serverData.buffer

    // 方法2：创建新的 ArrayBuffer 并复制数据
    // const getData: ArrayBuffer = new ArrayBuffer(data.length);
    // const newUint8Array = new Uint8Array(getData);
    // newUint8Array.set(data);

    // 方法3：使用 slice 创建副本
    // const getData: ArrayBuffer = data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength) as ArrayBuffer;

    console.log('ArrayBuffer 长度:', getData.byteLength);
    console.log('Uint8Array 长度:', data.length);

    // 转换为字符串（如果需要）
    let str: string = "";
    for (let i = 0; i < data.length; i++) {
      str += String.fromCharCode(data[i]);
    }
    console.log('xlan_接收信息', str);

    this.recvCount++;
    this.updateChart(str.toString());
    console.log('只计数');

    // 现在可以使用 getData (ArrayBuffer) 进行其他处理
    // 例如：保存到文件、传递给其他函数等
  }
  private subscribeSensorStream() {
    if (!this.initSymbol) return;
    if (!this.deviceManager) return;
    console.log('xlan_订阅主题是',this.subscribeSensorText)
    const subData = this.subscribeSensorText ;
    this.deviceManager.subscribeStream(subData, this.subCallbackStream);
  }
  // 发送stream
  private publishCommandSubStream() {
    if (!this.initSymbol) return;
    if (!this.deviceManager) return;
    console.log('xlan_发送stream信息',this.subscribeSensorText)
    // 发送
    // let sendResult = this.deviceManager.publishStream(this.subscribeSensorText,this.userName, new Uint8Array(buffer.from(this.publishCommandSubText.toString()).buffer))
    let sendResult = this.deviceManager.publishStream(this.subscribeSensorText,this.userName, new Uint8Array(buffer.from(this.FpsBuffer).buffer))
    if(sendResult){
      console.log('xlan_发送stream信息成功')
    }
  }
  // 动态取消订阅示例
  // private unsubscribeSensor() {
  //   if (!this.client) return;
  //   this.unsubscribeSensorText=this.subscribeSensorText
  //   console.log('this.unsubscribeSensorText',this.unsubscribeSensorText)
  //   const subData: SubData = { topic: this.unsubscribeSensorText };
  //   this.client.unsubscribe(subData);
  // }
  //
  // // 关闭连接
  // private closeClient() {
  //   this.client?.close();
  //   this.client = null;
  //   this.message = "连接已关闭";
  //
  // }
  private chartCount=0
  // 修改图表更新方法
  private async updateChart(value: string) {
    this.chartCount++
    console.log('chartCount',this.chartCount)
  }
  // ====
  cancelDialog(): void {
    AppStorage.setOrCreate<boolean>('flashingBol', true);
    AppStorage.setOrCreate<boolean>('countdownBol', true);
  }

  initXComponentSize(): void {
    let defaultSize = DisplayCalculator.calcSurfaceDisplaySize(this.screenWidth, this.screenHeight, this.defaultAspectRatio);
    this.xComponentWidth = defaultSize.width;
    this.xComponentHeight = defaultSize.height;
  }

  async aboutToDisAppear(): Promise<void> {
    // 清除定时器
    if (this.updateTimer !== -1) {
      clearInterval(this.updateTimer);
      this.updateTimer = -1;
    }
    await CameraService.releaseCamera();
  }

  async onPageShow(): Promise<void> {
    Logger.info(TAG, 'onPageShow');
    if (this.surfaceId) {
      await CameraService.initCamera(this.surfaceId, this.cameraDeviceIndex);
    }
  }

  async onPageHide(): Promise<void> {
    Logger.info(TAG, 'onPageHide');
    let isRecorder: boolean | undefined = AppStorage.get<boolean>('isRecorder');
    if (isRecorder) {
      GlobalContext.get().apply('stopVideoFun');
    }
    await CameraService.releaseCamera();
  }

  build() {
    Stack() {
      Stack() {
        // Button(`mp4`).onClick(() => {
        //   router.pushUrl({url:"pages/playermp4"})
        // })
        //   .position({ x: 100, y: 300 })
        //   .width(120)
        //   .height(40)
        //   .fontSize(14)
        //   .zIndex(1000)
        Button(`${this.FpsCount}`).onClick(() => {
            this.showPlayerWindow=!this.showPlayerWindow
        })
          .position({ x: 50, y: 300 })
          .width(120)
          .height(40)
          .fontSize(14)
          .zIndex(1000)
        // 在build()中添加一个按钮
        Button('处理图像')
          .onClick(async () => {
            const buffer = FpsBufferUtils.getFpsBuffer();
            console.log('进行处理图像前')
            if (buffer) {
              console.log('进行处理图像')
              const result = await ImageProcessingUtils.quickProcess(buffer);
              console.log(result ? '成功处理图像' : '失败处理图像');
            }
          })
          .position({ x: 50, y: 350 })
          .width(120)
          .height(40)
          .fontSize(14)
          .fontColor(Color.White)
          .zIndex(1000)
        if (this.showSwitch){
          Button('相机列表K')
            .position({ x: 50, y: 400 })
            .width(120)
            .height(40)
            .fontSize(14)
              // .backgroundColor('rgba(0,0,0,0.6)')
            .fontColor(Color.White)
            .onClick(async () => {
              console.log('获取相机列表')
              console.log('this.previewPixelMap',this.previewPixelMap)
              const newPixelMap = AppStorage.get<image.PixelMap>('previewPixelMap');
              console.log('newPixelMap外部',newPixelMap)
              // 只有当PixelMap真正改变时才更新
              if (newPixelMap !== this.previewPixelMap) {
                console.log('newPixelMap',newPixelMap)
                this.previewPixelMap = newPixelMap;
              }
            })
            .zIndex(100)
        }
        Text(`${this.message}`)
          .fontSize(14)
          .fontWeight(FontWeight.Bold)
          .fontColor(Color.Black)
          .backgroundColor(Color.Grey)
          .position({ x: this.screenWidth - 190, y: 250 })
          .width(120)
          .height(40)
          .textAlign(TextAlign.Center)
          .zIndex(1000)
        // TextInput({ placeholder: '订阅地址', text: this.ipAddress })
        //   .width('20%')
        //   .height(40)
        //   .onChange((value) => this.ipAddress = value)
        //   .position({ x: this.screenWidth - 190, y: 300 })
        //   .width(120)
        //   .height(40)
        //   .fontSize(14)
        //   .backgroundColor(Color.Grey)
        //   .zIndex(1000)
        TextInput({ placeholder: 'userName', text: this.userName })
          .width('20%')
          .height(40)
          .onChange((value) => this.userName = value)
          .position({ x: this.screenWidth - 190, y: 300 })
          .width(120)
          .height(40)
          .fontSize(14)
          .backgroundColor(Color.Grey)
          .zIndex(1000)
        TextInput({ placeholder: '订阅主题', text: this.subscribeSensorText })
          .width('20%')
          .height(40)
          .onChange((value) => this.subscribeSensorText = value)
          .position({ x: this.screenWidth - 190, y: 350 })
          .width(120)
          .height(40)
          .fontSize(14)
          .backgroundColor(Color.Grey)
          .zIndex(1000)
        Button(`订阅Stream`).onClick(() => {
          if(this.message=='连接已关闭'){
            this.message='请先创建连接'
            return
          }else{
            // this.subscribeSensor()
            this.subscribeSensorStream()
            // this.startRecvCountCheck() // 订阅时启动检测
            this.isSubscribed = true // 设置订阅状态
          }
        })
          .position({ x: this.screenWidth - 190, y: 400 })
          .width(120)
          .height(40)
          .fontSize(14)
          .fontColor(Color.White)
          .zIndex(1000)
        Button(`发送Stream`).onClick(() => {
          console.log('this.subscribeSensorText',this.subscribeSensorText)
          console.log('this.publishCommandSubText',this.publishCommandSubText)
          // this.publishCommandSub()
          this.publishCommandSubStream()
        })
          .position({ x: this.screenWidth - 190, y: 450 })
          .width(120)
          .height(40)
          .fontSize(14)
          .fontColor(Color.White)
          .zIndex(1000)
        if (this.previewPixelMap !== undefined && this.previewPixelMap !== null) {
          Image(this.previewPixelMap)
            .width(200)
            .height(200)
            .position({ x: this.screenWidth - 220, y: 480 })
            .borderRadius(10)
            .border({ width: 2, color: Color.White })
            .objectFit(ImageFit.Cover) // 保持宽高比
            .interpolation(ImageInterpolation.High) // 高质量插值
            .zIndex(100)
            .onError((error) => {
              Logger.error('Index', `PixelMap display error: ${JSON.stringify(error)}`);
            })
        }
        // Button(this.isSending ? '停止发送' : '定时发送').onClick(() => this.startTimedSending())
        //   .position({ x: this.screenWidth - 220, y: 400 })
        //   .width(120)
        //   .height(40)
        //   .fontSize(14)
        //   .fontColor(Color.White)
        // 显示处理后的预览图像
        // 优化的PixelMap显示组件
        if (this.previewPixelMap !== undefined && this.previewPixelMap !== null) {
          Image(this.previewPixelMap)
            .width(200)
            .height(200)
            .position({ x: this.screenWidth - 220, y: 20 })
            .borderRadius(10)
            .border({ width: 2, color: Color.White })
            .objectFit(ImageFit.Cover) // 保持宽高比
            .interpolation(ImageInterpolation.High) // 高质量插值
            .zIndex(100)
            .onError((error) => {
              Logger.error('Index', `PixelMap display error: ${JSON.stringify(error)}`);
            })
        }
        // 播放器小窗
        if (this.showPlayerWindow) {
          Stack() {
            // 小窗背景
            Column() {
              // 小窗标题栏
              Row() {
                Text('流媒体播放器')
                  .fontSize(12)
                  .fontColor(Color.White)
                  .flexGrow(1)

                // 关闭按钮
                Button('×')
                  .width(20)
                  .height(20)
                  .fontSize(14)
                  .fontColor(Color.White)
                  .backgroundColor(Color.Red)
                  .onClick(() => {
                    this.showPlayerWindow = false;
                  })
              }
              .width('100%')
              .height(30)
              .backgroundColor('rgba(0,0,0,0.8)')
              .padding({ left: 8, right: 8 })
              .justifyContent(FlexAlign.SpaceBetween)
              .alignItems(VerticalAlign.Center)

              // 播放器组件
              Player()
                .width('100%')
                .height(280)
            }
            .width(320)
            .height(310)
            .backgroundColor('rgba(0,0,0,0.9)')
            .borderRadius(8)
            .border({ width: 1, color: Color.Gray })
          }
          .position({
            x: this.playerWindowPosition.x,
            y: this.playerWindowPosition.y
          })
          .zIndex(200)
          .gesture(
            // 添加拖拽手势
            PanGesture()
              .onActionUpdate((event) => {
                this.playerWindowPosition.x += event.offsetX;
                this.playerWindowPosition.y += event.offsetY;

                // 限制拖拽范围，防止拖出屏幕
                if (this.playerWindowPosition.x < 0) {
                  this.playerWindowPosition.x = 0;
                }
                if (this.playerWindowPosition.y < 0) {
                  this.playerWindowPosition.y = 0;
                }
                if (this.playerWindowPosition.x > this.screenWidth - 320) {
                  this.playerWindowPosition.x = this.screenWidth - 320;
                }
                if (this.playerWindowPosition.y > this.screenHeight - 310) {
                  this.playerWindowPosition.y = this.screenHeight - 310;
                }
              })
          )
        }
        if (this.isShow) {
          // 画面
          XComponent({
            id: 'componentId',
            type: 'surface',
            controller: this.mXComponentController
          })
            .onLoad(async () => {
              Logger.info(TAG, 'onLoad is called');
              this.surfaceId = this.mXComponentController.getXComponentSurfaceId();
              Logger.info(TAG, `onLoad surfaceId: ${this.surfaceId}`);
              await CameraService.initCamera(this.surfaceId, this.cameraDeviceIndex);
              GlobalContext.get().setObject('surfaceId', this.surfaceId);
              GlobalContext.get().setObject('cameraDeviceIndex', this.cameraDeviceIndex);
              this.isSupportZoom = CameraService.isSupportZoom();
            })
            .size({
              width: this.xComponentWidth.toString(),
              height: this.xComponentHeight.toString()
            })
            .margin({ bottom: Constants.SURFACE_BOTTOM_MARGIN })

          // 参考线
          DividerPage({
            referenceLineBol: this.referenceLineBol
          })

          // 曝光框和对焦框
          FocusPage({
            focusPointBol: $focusPointBol,
            focusPointVal: $focusPointVal,
            exposureBol: $exposureBol,
            exposureNum: $exposureNum
          })

          // 曝光对焦手指点击区域
          FocusAreaPage({
            focusPointBol: $focusPointBol,
            focusPointVal: $focusPointVal,
            exposureBol: $exposureBol,
            exposureNum: $exposureNum,
            xComponentWidth: this.xComponentWidth,
            xComponentHeight: this.xComponentHeight
          })

          // slide
          if (this.isSupportZoom) {
            SlidePage()
          }

          // 反转摄像头_多机位_拍照_摄像
          ModeSwitchPage({
            surfaceId: this.surfaceId,
            cameraDeviceIndex: $cameraDeviceIndex,
            countdownNum: $countdownNum
          })
        }
      }
      .size({ width: '100%', height: '100%' })

      Row({ space: 24 }) {
        // 设置图标
        // 设置图标
        Button() {
          Image($r('app.media.icon_camera_setting'))
            .size({
              width: '35vp',
              height: '35vp'
            })
        }
        .borderRadius('45vp')
        .width('45vp')
        .height('45vp')
        .backgroundColor('rgba(255,255,255,0.20)')
        .onClick(() => {
          Logger.info(TAG, 'icon_camera_setting onClick is called');
          this.cancelDialog();
          this.countdownNum = 0;
          // 打开设置弹框
          this.settingDialogController.open();
        })

        // 闪光灯
        FlashingLightPage()

      }
      .margin({ left: 24 })
      .alignItems(VerticalAlign.Top)
      .justifyContent(FlexAlign.Start)
      .position({ x: 0, y: 0 })
      .width('100%')
      .onClick(() => {
        this.cancelDialog();
      })
    }
    .onClick(() => {
      this.cancelDialog();
    })
    .size({ width: '100%', height: '100%' })
    .backgroundColor(Color.Black)
  }
}
