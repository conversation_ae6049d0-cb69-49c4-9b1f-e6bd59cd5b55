.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_LOOKUP_METH_NEW 3"
.TH X509_LOOKUP_METH_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_LOOKUP_METHOD,
X509_LOOKUP_meth_new, X509_LOOKUP_meth_free, X509_LOOKUP_meth_set_new_item,
X509_LOOKUP_meth_get_new_item, X509_LOOKUP_meth_set_free,
X509_LOOKUP_meth_get_free, X509_LOOKUP_meth_set_init,
X509_LOOKUP_meth_get_init, X509_LOOKUP_meth_set_shutdown,
X509_LOOKUP_meth_get_shutdown,
X509_LOOKUP_ctrl_fn, X509_LOOKUP_meth_set_ctrl, X509_LOOKUP_meth_get_ctrl,
X509_LOOKUP_get_by_subject_fn, X509_LOOKUP_meth_set_get_by_subject,
X509_LOOKUP_meth_get_get_by_subject,
X509_LOOKUP_get_by_issuer_serial_fn, X509_LOOKUP_meth_set_get_by_issuer_serial,
X509_LOOKUP_meth_get_get_by_issuer_serial,
X509_LOOKUP_get_by_fingerprint_fn, X509_LOOKUP_meth_set_get_by_fingerprint,
X509_LOOKUP_meth_get_get_by_fingerprint,
X509_LOOKUP_get_by_alias_fn, X509_LOOKUP_meth_set_get_by_alias,
X509_LOOKUP_meth_get_get_by_alias,
X509_OBJECT_set1_X509, X509_OBJECT_set1_X509_CRL
\&\- Routines to build up X509_LOOKUP methods
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509_vfy.h>
\&
\& typedef x509_lookup_method_st X509_LOOKUP_METHOD;
\&
\& X509_LOOKUP_METHOD *X509_LOOKUP_meth_new(const char *name);
\& void X509_LOOKUP_meth_free(X509_LOOKUP_METHOD *method);
\&
\& int X509_LOOKUP_meth_set_new_item(X509_LOOKUP_METHOD *method,
\&                                   int (*new_item) (X509_LOOKUP *ctx));
\& int (*X509_LOOKUP_meth_get_new_item(const X509_LOOKUP_METHOD* method))
\&     (X509_LOOKUP *ctx);
\&
\& int X509_LOOKUP_meth_set_free(X509_LOOKUP_METHOD *method,
\&                               void (*free) (X509_LOOKUP *ctx));
\& void (*X509_LOOKUP_meth_get_free(const X509_LOOKUP_METHOD* method))
\&     (X509_LOOKUP *ctx);
\&
\& int X509_LOOKUP_meth_set_init(X509_LOOKUP_METHOD *method,
\&                               int (*init) (X509_LOOKUP *ctx));
\& int (*X509_LOOKUP_meth_get_init(const X509_LOOKUP_METHOD* method))
\&     (X509_LOOKUP *ctx);
\&
\& int X509_LOOKUP_meth_set_shutdown(X509_LOOKUP_METHOD *method,
\&                                   int (*shutdown) (X509_LOOKUP *ctx));
\& int (*X509_LOOKUP_meth_get_shutdown(const X509_LOOKUP_METHOD* method))
\&     (X509_LOOKUP *ctx);
\&
\& typedef int (*X509_LOOKUP_ctrl_fn)(X509_LOOKUP *ctx, int cmd, const char *argc,
\&                                    long argl, char **ret);
\& int X509_LOOKUP_meth_set_ctrl(X509_LOOKUP_METHOD *method,
\&     X509_LOOKUP_ctrl_fn ctrl_fn);
\& X509_LOOKUP_ctrl_fn X509_LOOKUP_meth_get_ctrl(const X509_LOOKUP_METHOD *method);
\&
\& typedef int (*X509_LOOKUP_get_by_subject_fn)(X509_LOOKUP *ctx,
\&                                              X509_LOOKUP_TYPE type,
\&                                              X509_NAME *name,
\&                                              X509_OBJECT *ret);
\& int X509_LOOKUP_meth_set_get_by_subject(X509_LOOKUP_METHOD *method,
\&     X509_LOOKUP_get_by_subject_fn fn);
\& X509_LOOKUP_get_by_subject_fn X509_LOOKUP_meth_get_get_by_subject(
\&     const X509_LOOKUP_METHOD *method);
\&
\& typedef int (*X509_LOOKUP_get_by_issuer_serial_fn)(X509_LOOKUP *ctx,
\&                                                    X509_LOOKUP_TYPE type,
\&                                                    X509_NAME *name,
\&                                                    ASN1_INTEGER *serial,
\&                                                    X509_OBJECT *ret);
\& int X509_LOOKUP_meth_set_get_by_issuer_serial(
\&     X509_LOOKUP_METHOD *method, X509_LOOKUP_get_by_issuer_serial_fn fn);
\& X509_LOOKUP_get_by_issuer_serial_fn X509_LOOKUP_meth_get_get_by_issuer_serial(
\&     const X509_LOOKUP_METHOD *method);
\&
\& typedef int (*X509_LOOKUP_get_by_fingerprint_fn)(X509_LOOKUP *ctx,
\&                                                  X509_LOOKUP_TYPE type,
\&                                                  const unsigned char* bytes,
\&                                                  int len,
\&                                                  X509_OBJECT *ret);
\& int X509_LOOKUP_meth_set_get_by_fingerprint(X509_LOOKUP_METHOD *method,
\&     X509_LOOKUP_get_by_fingerprint_fn fn);
\& X509_LOOKUP_get_by_fingerprint_fn X509_LOOKUP_meth_get_get_by_fingerprint(
\&     const X509_LOOKUP_METHOD *method);
\&
\& typedef int (*X509_LOOKUP_get_by_alias_fn)(X509_LOOKUP *ctx,
\&                                            X509_LOOKUP_TYPE type,
\&                                            const char *str,
\&                                            int len,
\&                                            X509_OBJECT *ret);
\& int X509_LOOKUP_meth_set_get_by_alias(X509_LOOKUP_METHOD *method,
\&     X509_LOOKUP_get_by_alias_fn fn);
\& X509_LOOKUP_get_by_alias_fn X509_LOOKUP_meth_get_get_by_alias(
\&     const X509_LOOKUP_METHOD *method);
\&
\& int X509_OBJECT_set1_X509(X509_OBJECT *a, X509 *obj);
\& int X509_OBJECT_set1_X509_CRL(X509_OBJECT *a, X509_CRL *obj);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBX509_LOOKUP_METHOD\fR type is a structure used for the implementation of new
X509_LOOKUP types. It provides a set of functions used by OpenSSL for the
implementation of various X509 and X509_CRL lookup capabilities. One instance
of an X509_LOOKUP_METHOD can be associated to many instantiations of an
\&\fBX509_LOOKUP\fR structure.
.PP
\&\fBX509_LOOKUP_meth_new()\fR creates a new \fBX509_LOOKUP_METHOD\fR structure. It should
be given a human-readable string containing a brief description of the lookup
method.
.PP
\&\fBX509_LOOKUP_meth_free()\fR destroys a \fBX509_LOOKUP_METHOD\fR structure.
.PP
\&\fBX509_LOOKUP_get_new_item()\fR and \fBX509_LOOKUP_set_new_item()\fR get and set the
function that is called when an \fBX509_LOOKUP\fR object is created with
\&\fBX509_LOOKUP_new()\fR. If an X509_LOOKUP_METHOD requires any per\-X509_LOOKUP
specific data, the supplied new_item function should allocate this data and
invoke \fBX509_LOOKUP_set_method_data\fR\|(3).
.PP
\&\fBX509_LOOKUP_get_free()\fR and \fBX509_LOOKUP_set_free()\fR get and set the function
that is used to free any method data that was allocated and set from within
new_item function.
.PP
\&\fBX509_LOOKUP_meth_get_init()\fR and \fBX509_LOOKUP_meth_set_init()\fR get and set the
function that is used to initialize the method data that was set with
\&\fBX509_LOOKUP_set_method_data\fR\|(3) as part of the new_item routine.
.PP
\&\fBX509_LOOKUP_meth_get_shutdown()\fR and \fBX509_LOOKUP_meth_set_shutdown()\fR get and set
the function that is used to shut down the method data whose state was
previously initialized in the init function.
.PP
\&\fBX509_LOOKUP_meth_get_ctrl()\fR and \fBX509_LOOKUP_meth_set_ctrl()\fR get and set a
function to be used to handle arbitrary control commands issued by
\&\fBX509_LOOKUP_ctrl()\fR. The control function is given the X509_LOOKUP
\&\fBctx\fR, along with the arguments passed by X509_LOOKUP_ctrl. \fBcmd\fR is
an arbitrary integer that defines some operation. \fBargc\fR is a pointer
to an array of characters. \fBargl\fR is an integer. \fBret\fR, if set,
points to a location where any return data should be written to. How
\&\fBargc\fR and \fBargl\fR are used depends entirely on the control function.
.PP
\&\fBX509_LOOKUP_set_get_by_subject()\fR, \fBX509_LOOKUP_set_get_by_issuer_serial()\fR,
\&\fBX509_LOOKUP_set_get_by_fingerprint()\fR, \fBX509_LOOKUP_set_get_by_alias()\fR set
the functions used to retrieve an X509 or X509_CRL object by the object's
subject, issuer, fingerprint, and alias respectively. These functions are given
the X509_LOOKUP context, the type of the X509_OBJECT being requested, parameters
related to the lookup, and an X509_OBJECT that will receive the requested
object.
.PP
Implementations must add objects they find to the \fBX509_STORE\fR object
using \fBX509_STORE_add_cert()\fR or \fBX509_STORE_add_crl()\fR.  This increments
its reference count.  However, the \fBX509_STORE_CTX_get_by_subject()\fR
function also increases the reference count which leads to one too
many references being held.  Therefore, applications should
additionally call \fBX509_free()\fR or \fBX509_CRL_free()\fR to decrement the
reference count again.
.PP
Implementations should also use either \fBX509_OBJECT_set1_X509()\fR or
\&\fBX509_OBJECT_set1_X509_CRL()\fR to set the result.  Note that this also
increments the result's reference count.
.PP
Any method data that was created as a result of the new_item function
set by \fBX509_LOOKUP_meth_set_new_item()\fR can be accessed with
\&\fBX509_LOOKUP_get_method_data\fR\|(3). The \fBX509_STORE\fR object that owns the
X509_LOOKUP may be accessed with \fBX509_LOOKUP_get_store\fR\|(3). Successful
lookups should return 1, and unsuccessful lookups should return 0.
.PP
\&\fBX509_LOOKUP_get_get_by_subject()\fR, \fBX509_LOOKUP_get_get_by_issuer_serial()\fR,
\&\fBX509_LOOKUP_get_get_by_fingerprint()\fR, \fBX509_LOOKUP_get_get_by_alias()\fR retrieve
the function set by the corresponding setter.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBX509_LOOKUP_meth_set\fR functions return 1 on success or 0 on error.
.PP
The \fBX509_LOOKUP_meth_get\fR functions return the corresponding function
pointers.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_STORE_new\fR\|(3), \fBSSL_CTX_set_cert_store\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 1.1.0i.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
