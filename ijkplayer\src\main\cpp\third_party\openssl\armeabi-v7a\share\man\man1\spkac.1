.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SPKAC 1"
.TH SPKAC 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-spkac,
spkac \- SPKAC printing and generating utility
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBspkac\fR
[\fB\-help\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-key keyfile\fR]
[\fB\-keyform PEM|DER|ENGINE\fR]
[\fB\-passin arg\fR]
[\fB\-challenge string\fR]
[\fB\-pubkey\fR]
[\fB\-spkac spkacname\fR]
[\fB\-spksect section\fR]
[\fB\-noout\fR]
[\fB\-verify\fR]
[\fB\-engine id\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBspkac\fR command processes Netscape signed public key and challenge
(SPKAC) files. It can print out their contents, verify the signature and
produce its own SPKACs from a supplied private key.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read from or standard input if this
option is not specified. Ignored if the \fB\-key\fR option is used.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
Specifies the output filename to write to or standard output by
default.
.IP "\fB\-key keyfile\fR" 4
.IX Item "-key keyfile"
Create an SPKAC file using the private key in \fBkeyfile\fR. The
\&\fB\-in\fR, \fB\-noout\fR, \fB\-spksect\fR and \fB\-verify\fR options are ignored if
present.
.IP "\fB\-keyform PEM|DER|ENGINE\fR" 4
.IX Item "-keyform PEM|DER|ENGINE"
Whether the key format is PEM, DER, or an engine-backed key.
The default is PEM.
.IP "\fB\-passin password\fR" 4
.IX Item "-passin password"
The input file password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-challenge string\fR" 4
.IX Item "-challenge string"
Specifies the challenge string if an SPKAC is being created.
.IP "\fB\-spkac spkacname\fR" 4
.IX Item "-spkac spkacname"
Allows an alternative name form the variable containing the
SPKAC. The default is "SPKAC". This option affects both
generated and input SPKAC files.
.IP "\fB\-spksect section\fR" 4
.IX Item "-spksect section"
Allows an alternative name form the section containing the
SPKAC. The default is the default section.
.IP \fB\-noout\fR 4
.IX Item "-noout"
Don't output the text version of the SPKAC (not used if an
SPKAC is being created).
.IP \fB\-pubkey\fR 4
.IX Item "-pubkey"
Output the public key of an SPKAC (not used if an SPKAC is
being created).
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verifies the digital signature on the supplied SPKAC.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBspkac\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.SH EXAMPLES
.IX Header "EXAMPLES"
Print out the contents of an SPKAC:
.PP
.Vb 1
\& openssl spkac \-in spkac.cnf
.Ve
.PP
Verify the signature of an SPKAC:
.PP
.Vb 1
\& openssl spkac \-in spkac.cnf \-noout \-verify
.Ve
.PP
Create an SPKAC using the challenge string "hello":
.PP
.Vb 1
\& openssl spkac \-key key.pem \-challenge hello \-out spkac.cnf
.Ve
.PP
Example of an SPKAC, (long lines split up for clarity):
.PP
.Vb 6
\& SPKAC=MIG5MGUwXDANBgkqhkiG9w0BAQEFAANLADBIAkEA\e
\& 1cCoq2Wa3Ixs47uI7FPVwHVIPDx5yso105Y6zpozam135a\e
\& 8R0CpoRvkkigIyXfcCjiVi5oWk+6FfPaD03uPFoQIDAQAB\e
\& FgVoZWxsbzANBgkqhkiG9w0BAQQFAANBAFpQtY/FojdwkJ\e
\& h1bEIYuc2EeM2KHTWPEepWYeawvHD0gQ3DngSC75YCWnnD\e
\& dq+NQ3F+X4deMx9AaEglZtULwV4=
.Ve
.SH NOTES
.IX Header "NOTES"
A created SPKAC with suitable DN components appended can be fed into
the \fBca\fR utility.
.PP
SPKACs are typically generated by Netscape when a form is submitted
containing the \fBKEYGEN\fR tag as part of the certificate enrollment
process.
.PP
The challenge string permits a primitive form of proof of possession
of private key. By checking the SPKAC signature and a random challenge
string some guarantee is given that the user knows the private key
corresponding to the public key being certified. This is important in
some applications. Without this it is possible for a previous SPKAC
to be used in a "replay attack".
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBca\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
