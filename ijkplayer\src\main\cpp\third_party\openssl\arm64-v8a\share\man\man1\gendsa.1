.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "GENDSA 1"
.TH GENDSA 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-gendsa,
gendsa \- generate a DSA private key from a set of parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBgendsa\fR
[\fB\-help\fR]
[\fB\-out filename\fR]
[\fB\-aes128\fR]
[\fB\-aes192\fR]
[\fB\-aes256\fR]
[\fB\-aria128\fR]
[\fB\-aria192\fR]
[\fB\-aria256\fR]
[\fB\-camellia128\fR]
[\fB\-camellia192\fR]
[\fB\-camellia256\fR]
[\fB\-des\fR]
[\fB\-des3\fR]
[\fB\-idea\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-engine id\fR]
[\fBparamfile\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBgendsa\fR command generates a DSA private key from a DSA parameter file
(which will be typically generated by the \fBopenssl dsaparam\fR command).
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
Output the key to the specified file. If this argument is not specified then
standard output is used.
.IP "\fB\-aes128\fR, \fB\-aes192\fR, \fB\-aes256\fR, \fB\-aria128\fR, \fB\-aria192\fR, \fB\-aria256\fR, \fB\-camellia128\fR, \fB\-camellia192\fR, \fB\-camellia256\fR, \fB\-des\fR, \fB\-des3\fR, \fB\-idea\fR" 4
.IX Item "-aes128, -aes192, -aes256, -aria128, -aria192, -aria256, -camellia128, -camellia192, -camellia256, -des, -des3, -idea"
These options encrypt the private key with specified
cipher before outputting it. A pass phrase is prompted for.
If none of these options is specified no encryption is used.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBgendsa\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.IP \fBparamfile\fR 4
.IX Item "paramfile"
This option specifies the DSA parameter file to use. The parameters in this
file determine the size of the private key. DSA parameters can be generated
and examined using the \fBopenssl dsaparam\fR command.
.SH NOTES
.IX Header "NOTES"
DSA key generation is little more than random number generation so it is
much quicker that RSA key generation for example.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBdsaparam\fR\|(1), \fBdsa\fR\|(1), \fBgenrsa\fR\|(1),
\&\fBrsa\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
