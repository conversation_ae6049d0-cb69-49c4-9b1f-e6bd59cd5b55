.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_SIGN_RECEIPT 3"
.TH CMS_SIGN_RECEIPT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_sign_receipt \- create a CMS signed receipt
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& CMS_ContentInfo *CMS_sign_receipt(CMS_SignerInfo *si, X509 *signcert,
\&                                   EVP_PKEY *pkey, STACK_OF(X509) *certs,
\&                                   unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_sign_receipt()\fR creates and returns a CMS signed receipt structure. \fBsi\fR is
the \fBCMS_SignerInfo\fR structure containing the signed receipt request.
\&\fBsigncert\fR is the certificate to sign with, \fBpkey\fR is the corresponding
private key.  \fBcerts\fR is an optional additional set of certificates to include
in the CMS structure (for example any intermediate CAs in the chain).
.PP
\&\fBflags\fR is an optional set of flags.
.SH NOTES
.IX Header "NOTES"
This functions behaves in a similar way to \fBCMS_sign()\fR except the flag values
\&\fBCMS_DETACHED\fR, \fBCMS_BINARY\fR, \fBCMS_NOATTR\fR, \fBCMS_TEXT\fR and \fBCMS_STREAM\fR
are not supported since they do not make sense in the context of signed
receipts.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_sign_receipt()\fR returns either a valid CMS_ContentInfo structure or NULL if
an error occurred.  The error can be obtained from \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBCMS_verify_receipt\fR\|(3),
\&\fBCMS_sign\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
