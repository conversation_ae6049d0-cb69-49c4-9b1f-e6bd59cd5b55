.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_GET0_NOTBEFORE 3"
.TH X509_GET0_NOTBEFORE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_get0_notBefore, X509_getm_notBefore, X509_get0_notAfter,
X509_getm_notAfter, X509_set1_notBefore, X509_set1_notAfter,
X509_CRL_get0_lastUpdate, X509_CRL_get0_nextUpdate, X509_CRL_set1_lastUpdate,
X509_CRL_set1_nextUpdate \- get or set certificate or CRL dates
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& const ASN1_TIME *X509_get0_notBefore(const X509 *x);
\& const ASN1_TIME *X509_get0_notAfter(const X509 *x);
\&
\& ASN1_TIME *X509_getm_notBefore(const X509 *x);
\& ASN1_TIME *X509_getm_notAfter(const X509 *x);
\&
\& int X509_set1_notBefore(X509 *x, const ASN1_TIME *tm);
\& int X509_set1_notAfter(X509 *x, const ASN1_TIME *tm);
\&
\& const ASN1_TIME *X509_CRL_get0_lastUpdate(const X509_CRL *crl);
\& const ASN1_TIME *X509_CRL_get0_nextUpdate(const X509_CRL *crl);
\&
\& int X509_CRL_set1_lastUpdate(X509_CRL *x, const ASN1_TIME *tm);
\& int X509_CRL_set1_nextUpdate(X509_CRL *x, const ASN1_TIME *tm);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_get0_notBefore()\fR and \fBX509_get0_notAfter()\fR return the \fBnotBefore\fR
and \fBnotAfter\fR fields of certificate \fBx\fR respectively. The value
returned is an internal pointer which must not be freed up after
the call.
.PP
\&\fBX509_getm_notBefore()\fR and \fBX509_getm_notAfter()\fR are similar to
\&\fBX509_get0_notBefore()\fR and \fBX509_get0_notAfter()\fR except they return
non-constant mutable references to the associated date field of
the certificate.
.PP
\&\fBX509_set1_notBefore()\fR and \fBX509_set1_notAfter()\fR set the \fBnotBefore\fR
and \fBnotAfter\fR fields of \fBx\fR to \fBtm\fR. Ownership of the passed
parameter \fBtm\fR is not transferred by these functions so it must
be freed up after the call.
.PP
\&\fBX509_CRL_get0_lastUpdate()\fR and \fBX509_CRL_get0_nextUpdate()\fR return the
\&\fBlastUpdate\fR and \fBnextUpdate\fR fields of \fBcrl\fR. The value
returned is an internal pointer which must not be freed up after
the call. If the \fBnextUpdate\fR field is absent from \fBcrl\fR then
\&\fBNULL\fR is returned.
.PP
\&\fBX509_CRL_set1_lastUpdate()\fR and \fBX509_CRL_set1_nextUpdate()\fR set the \fBlastUpdate\fR
and \fBnextUpdate\fR fields of \fBcrl\fR to \fBtm\fR. Ownership of the passed parameter
\&\fBtm\fR is not transferred by these functions so it must be freed up after the
call.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_get0_notBefore()\fR, \fBX509_get0_notAfter()\fR and \fBX509_CRL_get0_lastUpdate()\fR
return a pointer to an \fBASN1_TIME\fR structure.
.PP
\&\fBX509_CRL_get0_lastUpdate()\fR return a pointer to an \fBASN1_TIME\fR structure
or NULL if the \fBlastUpdate\fR field is absent.
.PP
\&\fBX509_set1_notBefore()\fR, \fBX509_set1_notAfter()\fR, \fBX509_CRL_set1_lastUpdate()\fR and
\&\fBX509_CRL_set1_nextUpdate()\fR return 1 for success or 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3),
\&\fBERR_get_error\fR\|(3),
\&\fBX509_CRL_get0_by_serial\fR\|(3),
\&\fBX509_get0_signature\fR\|(3),
\&\fBX509_get_ext_d2i\fR\|(3),
\&\fBX509_get_extension_flags\fR\|(3),
\&\fBX509_get_pubkey\fR\|(3),
\&\fBX509_get_subject_name\fR\|(3),
\&\fBX509_NAME_add_entry_by_txt\fR\|(3),
\&\fBX509_NAME_ENTRY_get_object\fR\|(3),
\&\fBX509_NAME_get_index_by_NID\fR\|(3),
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBX509_new\fR\|(3),
\&\fBX509_sign\fR\|(3),
\&\fBX509V3_get_d2i\fR\|(3),
\&\fBX509_verify_cert\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions are available in all versions of OpenSSL.
.PP
\&\fBX509_get_notBefore()\fR and \fBX509_get_notAfter()\fR were deprecated in OpenSSL
1.1.0
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
