<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_LOOKUP_hash_dir</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#File-Method">File Method</a></li>
      <li><a href="#Hashed-Directory-Method">Hashed Directory Method</a></li>
    </ul>
  </li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_LOOKUP_hash_dir, X509_LOOKUP_file, X509_load_cert_file, X509_load_crl_file, X509_load_cert_crl_file - Default OpenSSL certificate lookup methods</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509_vfy.h&gt;

X509_LOOKUP_METHOD *X509_LOOKUP_hash_dir(void);
X509_LOOKUP_METHOD *X509_LOOKUP_file(void);

int X509_load_cert_file(X509_LOOKUP *ctx, const char *file, int type);
int X509_load_crl_file(X509_LOOKUP *ctx, const char *file, int type);
int X509_load_cert_crl_file(X509_LOOKUP *ctx, const char *file, int type);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p><b>X509_LOOKUP_hash_dir</b> and <b>X509_LOOKUP_file</b> are two certificate lookup methods to use with <b>X509_STORE</b>, provided by OpenSSL library.</p>

<p>Users of the library typically do not need to create instances of these methods manually, they would be created automatically by <a href="../man3/X509_STORE_load_locations.html">X509_STORE_load_locations(3)</a> or <a href="../man3/SSL_CTX_load_verify_locations.html">SSL_CTX_load_verify_locations(3)</a> functions.</p>

<p>Internally loading of certificates and CRLs is implemented via functions <b>X509_load_cert_crl_file</b>, <b>X509_load_cert_file</b> and <b>X509_load_crl_file</b>. These functions support parameter <i>type</i>, which can be one of constants <b>FILETYPE_PEM</b>, <b>FILETYPE_ASN1</b> and <b>FILETYPE_DEFAULT</b>. They load certificates and/or CRLs from specified file into memory cache of <b>X509_STORE</b> objects which given <b>ctx</b> parameter is associated with.</p>

<p>Functions <b>X509_load_cert_file</b> and <b>X509_load_crl_file</b> can load both PEM and DER formats depending of type value. Because DER format cannot contain more than one certificate or CRL object (while PEM can contain several concatenated PEM objects) <b>X509_load_cert_crl_file</b> with <b>FILETYPE_ASN1</b> is equivalent to <b>X509_load_cert_file</b>.</p>

<p>Constant <b>FILETYPE_DEFAULT</b> with NULL filename causes these functions to load default certificate store file (see <a href="../man3/X509_STORE_set_default_paths.html">X509_STORE_set_default_paths(3)</a>.</p>

<p>Functions return number of objects loaded from file or 0 in case of error.</p>

<p>Both methods support adding several certificate locations into one <b>X509_STORE</b>.</p>

<p>This page documents certificate store formats used by these methods and caching policy.</p>

<h2 id="File-Method">File Method</h2>

<p>The <b>X509_LOOKUP_file</b> method loads all the certificates or CRLs present in a file into memory at the time the file is added as a lookup source.</p>

<p>File format is ASCII text which contains concatenated PEM certificates and CRLs.</p>

<p>This method should be used by applications which work with a small set of CAs.</p>

<h2 id="Hashed-Directory-Method">Hashed Directory Method</h2>

<p><b>X509_LOOKUP_hash_dir</b> is a more advanced method, which loads certificates and CRLs on demand, and caches them in memory once they are loaded. As of OpenSSL 1.0.0, it also checks for newer CRLs upon each lookup, so that newer CRLs are as soon as they appear in the directory.</p>

<p>The directory should contain one certificate or CRL per file in PEM format, with a filename of the form <i>hash</i>.<i>N</i> for a certificate, or <i>hash</i>.<b>r</b><i>N</i> for a CRL. The <i>hash</i> is the value returned by the <a href="../man3/X509_NAME_hash.html">X509_NAME_hash(3)</a> function applied to the subject name for certificates or issuer name for CRLs. The hash can also be obtained via the <b>-hash</b> option of the <a href="../man1/x509.html">x509(1)</a> or <a href="../man1/crl.html">crl(1)</a> commands.</p>

<p>The .<i>N</i> or .<b>r</b><i>N</i> suffix is a sequence number that starts at zero, and is incremented consecutively for each certificate or CRL with the same <i>hash</i> value. Gaps in the sequence numbers are not supported, it is assumed that there are no more objects with the same hash beyond the first missing number in the sequence.</p>

<p>Sequence numbers make it possible for the directory to contain multiple certificates with same subject name hash value. For example, it is possible to have in the store several certificates with same subject or several CRLs with same issuer (and, for example, different validity period).</p>

<p>When checking for new CRLs once one CRL for given hash value is loaded, hash_dir lookup method checks only for certificates with sequence number greater than that of the already cached CRL.</p>

<p>Note that the hash algorithm used for subject name hashing changed in OpenSSL 1.0.0, and all certificate stores have to be rehashed when moving from OpenSSL 0.9.8 to 1.0.0.</p>

<p>OpenSSL includes a <a href="../man1/rehash.html">rehash(1)</a> utility which creates symlinks with correct hashed names for all files with .pem suffix in a given directory.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>X509_LOOKUP_hash_dir() and X509_LOOKUP_file() always return a valid <b>X509_LOOKUP_METHOD</b> structure.</p>

<p>X509_load_cert_file(), X509_load_crl_file() and X509_load_cert_crl_file() return the number of loaded objects or 0 on error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/PEM_read_PrivateKey.html">PEM_read_PrivateKey(3)</a>, <a href="../man3/X509_STORE_load_locations.html">X509_STORE_load_locations(3)</a>, <a href="../man3/X509_store_add_lookup.html">X509_store_add_lookup(3)</a>, <a href="../man3/SSL_CTX_load_verify_locations.html">SSL_CTX_load_verify_locations(3)</a>, <a href="../man3/X509_LOOKUP_meth_new.html">X509_LOOKUP_meth_new(3)</a>,</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


