<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_alloc_buffers</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_free_buffers, SSL_alloc_buffers - manage SSL structure buffers</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

int SSL_free_buffers(SSL *ssl);
int SSL_alloc_buffers(SSL *ssl);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_free_buffers() frees the read and write buffers of the given <b>ssl</b>. SSL_alloc_buffers() allocates the read and write buffers of the given <b>ssl</b>.</p>

<p>The <b>SSL_MODE_RELEASE_BUFFERS</b> mode releases read or write buffers whenever the buffers have been drained. These functions allow applications to manually control when buffers are freed and allocated.</p>

<p>After freeing the buffers, the buffers are automatically reallocated upon a new read or write. The SSL_alloc_buffers() does not need to be called, but can be used to make sure the buffers are preallocated. This can be used to avoid allocation during data processing or with CRYPTO_set_mem_functions() to control where and how buffers are allocated.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The following return values can occur:</p>

<dl>

<dt id="Failure">0 (Failure)</dt>
<dd>

<p>The SSL_free_buffers() function returns 0 when there is pending data to be read or written. The SSL_alloc_buffers() function returns 0 when there is an allocation failure.</p>

</dd>
<dt id="Success">1 (Success)</dt>
<dd>

<p>The SSL_free_buffers() function returns 1 if the buffers have been freed. This value is also returned if the buffers had been freed before calling SSL_free_buffers(). The SSL_alloc_buffers() function returns 1 if the buffers have been allocated. This value is also returned if the buffers had been allocated before calling SSL_alloc_buffers().</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/SSL_free.html">SSL_free(3)</a>, <a href="../man3/SSL_clear.html">SSL_clear(3)</a>, <a href="../man3/SSL_new.html">SSL_new(3)</a>, <a href="../man3/SSL_CTX_set_mode.html">SSL_CTX_set_mode(3)</a>, <a>CRYPTO_set_mem_functions</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


