.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "STOREUTL 1"
.TH STOREUTL 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-storeutl,
storeutl \- STORE utility
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBstoreutl\fR
[\fB\-help\fR]
[\fB\-out file\fR]
[\fB\-noout\fR]
[\fB\-passin arg\fR]
[\fB\-text arg\fR]
[\fB\-engine id\fR]
[\fB\-r\fR]
[\fB\-certs\fR]
[\fB\-keys\fR]
[\fB\-crls\fR]
[\fB\-subject arg\fR]
[\fB\-issuer arg\fR]
[\fB\-serial arg\fR]
[\fB\-alias arg\fR]
[\fB\-fingerprint arg\fR]
[\fB\-\fR\f(BIdigest\fR]
\&\fBuri\fR ...
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBstoreutl\fR command can be used to display the contents (after decryption
as the case may be) fetched from the given URIs.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
specifies the output filename to write to or standard output by
default.
.IP \fB\-noout\fR 4
.IX Item "-noout"
this option prevents output of the PEM data.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
the key password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the objects in text form, similarly to the \fB\-text\fR output from
\&\fBopenssl x509\fR, \fBopenssl pkey\fR, etc.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
specifying an engine (by its unique \fBid\fR string) will cause \fBstoreutl\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed.
The engine will then be set as the default for all available algorithms.
.IP \fB\-r\fR 4
.IX Item "-r"
Fetch objects recursively when possible.
.IP \fB\-certs\fR 4
.IX Item "-certs"
.PD 0
.IP \fB\-keys\fR 4
.IX Item "-keys"
.IP \fB\-crls\fR 4
.IX Item "-crls"
.PD
Only select the certificates, keys or CRLs from the given URI.
However, if this URI would return a set of names (URIs), those are always
returned.
.IP "\fB\-subject arg\fR" 4
.IX Item "-subject arg"
Search for an object having the subject name \fBarg\fR.
The arg must be formatted as \fI/type0=value0/type1=value1/type2=...\fR.
Keyword characters may be escaped by \e (backslash), and whitespace is retained.
Empty values are permitted but are ignored for the search.  That is,
a search with an empty value will have the same effect as not specifying
the type at all.
.IP "\fB\-issuer arg\fR" 4
.IX Item "-issuer arg"
.PD 0
.IP "\fB\-serial arg\fR" 4
.IX Item "-serial arg"
.PD
Search for an object having the given issuer name and serial number.
These two options \fImust\fR be used together.
The issuer arg must be formatted as \fI/type0=value0/type1=value1/type2=...\fR,
characters may be escaped by \e (backslash), no spaces are skipped.
The serial arg may be specified as a decimal value or a hex value if preceded
by \fB0x\fR.
.IP "\fB\-alias arg\fR" 4
.IX Item "-alias arg"
Search for an object having the given alias.
.IP "\fB\-fingerprint arg\fR" 4
.IX Item "-fingerprint arg"
Search for an object having the given fingerprint.
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
The digest that was used to compute the fingerprint given with \fB\-fingerprint\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The \fBopenssl\fR \fBstoreutl\fR app was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
