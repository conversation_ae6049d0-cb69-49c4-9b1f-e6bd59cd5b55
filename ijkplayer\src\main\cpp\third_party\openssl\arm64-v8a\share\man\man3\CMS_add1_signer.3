.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_ADD1_SIGNER 3"
.TH CMS_ADD1_SIGNER 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_add1_signer, CMS_SignerInfo_sign \- add a signer to a CMS_ContentInfo signed data structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& CMS_SignerInfo *CMS_add1_signer(CMS_ContentInfo *cms, X509 *signcert,
\&                                 EVP_PKEY *pkey, const EVP_MD *md,
\&                                 unsigned int flags);
\&
\& int CMS_SignerInfo_sign(CMS_SignerInfo *si);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_add1_signer()\fR adds a signer with certificate \fBsigncert\fR and private
key \fBpkey\fR using message digest \fBmd\fR to CMS_ContentInfo SignedData
structure \fBcms\fR.
.PP
The CMS_ContentInfo structure should be obtained from an initial call to
\&\fBCMS_sign()\fR with the flag \fBCMS_PARTIAL\fR set or in the case or re-signing a
valid CMS_ContentInfo SignedData structure.
.PP
If the \fBmd\fR parameter is \fBNULL\fR then the default digest for the public
key algorithm will be used.
.PP
Unless the \fBCMS_REUSE_DIGEST\fR flag is set the returned CMS_ContentInfo
structure is not complete and must be finalized either by streaming (if
applicable) or a call to \fBCMS_final()\fR.
.PP
The \fBCMS_SignerInfo_sign()\fR function will explicitly sign a CMS_SignerInfo
structure, its main use is when \fBCMS_REUSE_DIGEST\fR and \fBCMS_PARTIAL\fR flags
are both set.
.SH NOTES
.IX Header "NOTES"
The main purpose of \fBCMS_add1_signer()\fR is to provide finer control
over a CMS signed data structure where the simpler \fBCMS_sign()\fR function defaults
are not appropriate. For example if multiple signers or non default digest
algorithms are needed. New attributes can also be added using the returned
CMS_SignerInfo structure and the CMS attribute utility functions or the
CMS signed receipt request functions.
.PP
Any of the following flags (ored together) can be passed in the \fBflags\fR
parameter.
.PP
If \fBCMS_REUSE_DIGEST\fR is set then an attempt is made to copy the content
digest value from the CMS_ContentInfo structure: to add a signer to an existing
structure.  An error occurs if a matching digest value cannot be found to copy.
The returned CMS_ContentInfo structure will be valid and finalized when this
flag is set.
.PP
If \fBCMS_PARTIAL\fR is set in addition to \fBCMS_REUSE_DIGEST\fR then the
CMS_SignerInfo structure will not be finalized so additional attributes
can be added. In this case an explicit call to \fBCMS_SignerInfo_sign()\fR is
needed to finalize it.
.PP
If \fBCMS_NOCERTS\fR is set the signer's certificate will not be included in the
CMS_ContentInfo structure, the signer's certificate must still be supplied in
the \fBsigncert\fR parameter though. This can reduce the size of the signature if
the signers certificate can be obtained by other means: for example a
previously signed message.
.PP
The SignedData structure includes several CMS signedAttributes including the
signing time, the CMS content type and the supported list of ciphers in an
SMIMECapabilities attribute. If \fBCMS_NOATTR\fR is set then no signedAttributes
will be used. If \fBCMS_NOSMIMECAP\fR is set then just the SMIMECapabilities are
omitted.
.PP
OpenSSL will by default identify signing certificates using issuer name
and serial number. If \fBCMS_USE_KEYID\fR is set it will use the subject key
identifier value instead. An error occurs if the signing certificate does not
have a subject key identifier extension.
.PP
If present the SMIMECapabilities attribute indicates support for the following
algorithms in preference order: 256 bit AES, Gost R3411\-94, Gost 28147\-89, 192
bit AES, 128 bit AES, triple DES, 128 bit RC2, 64 bit RC2, DES and 40 bit RC2.
If any of these algorithms is not available then it will not be included: for example the GOST algorithms will not be included if the GOST ENGINE is
not loaded.
.PP
\&\fBCMS_add1_signer()\fR returns an internal pointer to the CMS_SignerInfo
structure just added, this can be used to set additional attributes
before it is finalized.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_add1_signer()\fR returns an internal pointer to the CMS_SignerInfo
structure just added or NULL if an error occurs.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_sign\fR\|(3),
\&\fBCMS_final\fR\|(3),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2014\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
