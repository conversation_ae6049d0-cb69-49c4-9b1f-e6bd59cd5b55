.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CRYPTO_GET_EX_NEW_INDEX 3"
.TH CRYPTO_GET_EX_NEW_INDEX 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CRYPTO_EX_new, CRYPTO_EX_free, CRYPTO_EX_dup,
CRYPTO_free_ex_index, CRYPTO_get_ex_new_index, CRYPTO_set_ex_data,
CRYPTO_get_ex_data, CRYPTO_free_ex_data, CRYPTO_new_ex_data
\&\- functions supporting application\-specific data
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crypto.h>
\&
\& int CRYPTO_get_ex_new_index(int class_index,
\&                             long argl, void *argp,
\&                             CRYPTO_EX_new *new_func,
\&                             CRYPTO_EX_dup *dup_func,
\&                             CRYPTO_EX_free *free_func);
\&
\& typedef void CRYPTO_EX_new(void *parent, void *ptr, CRYPTO_EX_DATA *ad,
\&                            int idx, long argl, void *argp);
\& typedef void CRYPTO_EX_free(void *parent, void *ptr, CRYPTO_EX_DATA *ad,
\&                             int idx, long argl, void *argp);
\& typedef int CRYPTO_EX_dup(CRYPTO_EX_DATA *to, const CRYPTO_EX_DATA *from,
\&                           void *from_d, int idx, long argl, void *argp);
\&
\& int CRYPTO_new_ex_data(int class_index, void *obj, CRYPTO_EX_DATA *ad)
\&
\& int CRYPTO_set_ex_data(CRYPTO_EX_DATA *r, int idx, void *arg);
\&
\& void *CRYPTO_get_ex_data(CRYPTO_EX_DATA *r, int idx);
\&
\& void CRYPTO_free_ex_data(int class_index, void *obj, CRYPTO_EX_DATA *r);
\&
\& int CRYPTO_free_ex_index(int class_index, int idx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Several OpenSSL structures can have application-specific data attached to them,
known as "exdata."
The specific structures are:
.PP
.Vb 10
\&    APP
\&    BIO
\&    DH
\&    DRBG
\&    DSA
\&    EC_KEY
\&    ENGINE
\&    RSA
\&    SSL
\&    SSL_CTX
\&    SSL_SESSION
\&    UI
\&    UI_METHOD
\&    X509
\&    X509_STORE
\&    X509_STORE_CTX
.Ve
.PP
Each is identified by an \fBCRYPTO_EX_INDEX_xxx\fR define in the \fBcrypto.h\fR
header file.  In addition, \fBCRYPTO_EX_INDEX_APP\fR is reserved for
applications to use this facility for their own structures.
.PP
The API described here is used by OpenSSL to manipulate exdata for specific
structures.  Since the application data can be anything at all it is passed
and retrieved as a \fBvoid *\fR type.
.PP
The \fBCRYPTO_EX_DATA\fR type is opaque.  To initialize the exdata part of
a structure, call \fBCRYPTO_new_ex_data()\fR. This is only necessary for
\&\fBCRYPTO_EX_INDEX_APP\fR objects.
.PP
Exdata types are identified by an \fBindex\fR, an integer guaranteed to be
unique within structures for the lifetime of the program.  Applications
using exdata typically call \fBCRYPTO_get_ex_new_index\fR at startup, and
store the result in a global variable, or write a wrapper function to
provide lazy evaluation.  The \fBclass_index\fR should be one of the
\&\fBCRYPTO_EX_INDEX_xxx\fR values. The \fBargl\fR and \fBargp\fR parameters are saved
to be passed to the callbacks but are otherwise not used.  In order to
transparently manipulate exdata, three callbacks must be provided. The
semantics of those callbacks are described below.
.PP
When copying or releasing objects with exdata, the callback functions
are called in increasing order of their \fBindex\fR value.
.PP
If a dynamic library can be unloaded, it should call \fBCRYPTO_free_ex_index()\fR
when this is done.
This will replace the callbacks with no-ops
so that applications don't crash.  Any existing exdata will be leaked.
.PP
To set or get the exdata on an object, the appropriate type-specific
routine must be used.  This is because the containing structure is opaque
and the \fBCRYPTO_EX_DATA\fR field is not accessible.  In both API's, the
\&\fBidx\fR parameter should be an already-created index value.
.PP
When setting exdata, the pointer specified with a particular index is saved,
and returned on a subsequent "get" call.  If the application is going to
release the data, it must make sure to set a \fBNULL\fR value at the index,
to avoid likely double-free crashes.
.PP
The function \fBCRYPTO_free_ex_data\fR is used to free all exdata attached
to a structure. The appropriate type-specific routine must be used.
The \fBclass_index\fR identifies the structure type, the \fBobj\fR is
a pointer to the actual structure, and \fBr\fR is a pointer to the
structure's exdata field.
.SS "Callback Functions"
.IX Subsection "Callback Functions"
This section describes how the callback functions are used. Applications
that are defining their own exdata using \fBCYPRTO_EX_INDEX_APP\fR must
call them as described here.
.PP
When a structure is initially allocated (such as \fBRSA_new()\fR) then the
\&\fBnew_func()\fR is called for every defined index. There is no requirement
that the entire parent, or containing, structure has been set up.
The \fBnew_func()\fR is typically used only to allocate memory to store the
exdata, and perhaps an "initialized" flag within that memory.
The exdata value should be set by calling \fBCRYPTO_set_ex_data()\fR.
.PP
When a structure is free'd (such as \fBSSL_CTX_free()\fR) then the
\&\fBfree_func()\fR is called for every defined index.  Again, the state of the
parent structure is not guaranteed.  The \fBfree_func()\fR may be called with a
NULL pointer.
.PP
Both \fBnew_func()\fR and \fBfree_func()\fR take the same parameters.
The \fBparent\fR is the pointer to the structure that contains the exdata.
The \fBptr\fR is the current exdata item; for \fBnew_func()\fR this will typically
be NULL.  The \fBr\fR parameter is a pointer to the exdata field of the object.
The \fBidx\fR is the index and is the value returned when the callbacks were
initially registered via \fBCRYPTO_get_ex_new_index()\fR and can be used if
the same callback handles different types of exdata.
.PP
\&\fBdup_func()\fR is called when a structure is being copied.  This is only done
for \fBSSL\fR, \fBSSL_SESSION\fR, \fBEC_KEY\fR objects and \fBBIO\fR chains via
\&\fBBIO_dup_chain()\fR.  The \fBto\fR and \fBfrom\fR parameters
are pointers to the destination and source \fBCRYPTO_EX_DATA\fR structures,
respectively.  The \fBfrom_d\fR parameter needs to be cast to a \fBvoid **pptr\fR
as the API has currently the wrong signature; that will be changed in a
future version.  The \fB*pptr\fR is a pointer to the source exdata.
When the \fBdup_func()\fR returns, the value in \fB*pptr\fR is copied to the
destination ex_data.  If the pointer contained in \fB*pptr\fR is not modified
by the \fBdup_func()\fR, then both \fBto\fR and \fBfrom\fR will point to the same data.
The \fBidx\fR, \fBargl\fR and \fBargp\fR parameters are as described for the other
two callbacks.  If the \fBdup_func()\fR returns \fB0\fR the whole \fBCRYPTO_dup_ex_data()\fR
will fail.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCRYPTO_get_ex_new_index()\fR returns a new index or \-1 on failure.
.PP
\&\fBCRYPTO_free_ex_index()\fR and
\&\fBCRYPTO_set_ex_data()\fR return 1 on success or 0 on failure.
.PP
\&\fBCRYPTO_get_ex_data()\fR returns the application data or NULL on failure;
note that NULL may be a valid value.
.PP
\&\fBdup_func()\fR should return 0 for failure and 1 for success.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
