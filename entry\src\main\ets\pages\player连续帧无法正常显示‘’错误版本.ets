import { IjkMediaPlayer } from "@ohos/ijkplayer";
import type { OnPreparedListener } from "@ohos/ijkplayer";
import type { OnVideoSizeChangedListener } from "@ohos/ijkplayer";
import type { OnCompletionListener } from "@ohos/ijkplayer";
import type { OnErrorListener } from "@ohos/ijkplayer";
import { LogUtils } from "@ohos/ijkplayer";
import fs from '@ohos.file.fs';
import { BusinessError } from '@ohos.base';
import util from '@ohos.util';
import buffer from '@ohos.buffer';
import resourceManager from '@ohos.resourceManager';
import { FpsBufferUtils } from '../model/FpsBufferManager';
interface optionsFormat{
  width?: number,
  height?: number,
  pixelFormat?: string,
  framerate?: number
}
@Component
export struct Player {
  @State message: string = 'Stream Player';
  @State isPlaying: boolean = false;
  @State aspRatio: number = 16 / 9; // 默认宽高比
  @State hasStreamData: boolean = false;
  @State autoPlayEnabled: boolean = true; // 自动播放新帧开关
  private mContext: object | null = null;
  private mIjkMediaPlayer: IjkMediaPlayer | null = null;
  private tempFilePath: string = '';
  private innerResource: Resource = $rawfile('videoTest.mp4');
  private frameQueue: ArrayBuffer[] = []; // 帧队列
  private maxFrames: number = 5; // 最大帧数
  @StorageLink ('FpsCount') @Watch('onFpsCount') PlayerFpsCount:number=0
  private FpsBuffer:ArrayBuffer|undefined|null=null
  async onFpsCount(){
    // console.log('player_player_帧数在变化',this.PlayerFpsCount)
    this.FpsBuffer = FpsBufferUtils.getFpsBuffer();

    if(this.FpsBuffer && this.FpsBuffer.byteLength > 0){
      // console.log('player_player_this.FpsBuffer长度',this.FpsBuffer.byteLength)

      // 自动处理新的视频帧数据
      await this.processNewFrame(this.FpsBuffer);

    } else {
      console.log('player_this.FpsBuffer为空||undefined')
    }
  }

  // 处理新的视频帧数据
  private async processNewFrame(frameBuffer: ArrayBuffer) {
    try {
      // 如果自动播放被禁用，直接返回
      if (!this.autoPlayEnabled) {
        return;
      }

      // 如果播放器还没有初始化，先初始化
      if (!this.mIjkMediaPlayer) {
        console.log('player_播放器未初始化，等待初始化完成...');
        return;
      }

      // 首次设置或更新帧数据
      if (!this.hasStreamData) {
        console.log('player_首次设置YUV流数据...');
        // 先收集几帧数据
        this.frameQueue.push(frameBuffer);
        this.frameQueue.push(frameBuffer); // 重复帧确保连续性
        this.frameQueue.push(frameBuffer);

        const success = await this.setYuvStreamData(frameBuffer);
        if (success) {
          console.log('player_开始播放...');
          this.startPlayWithStream();
        }
      } else {
        // 更新帧数据
        await this.updateFrameData(frameBuffer);
      }

    } catch (error) {
      console.log('player_处理新帧数据失败:', error);
    }
  }

  // 更新帧数据 - 创建连续流
  private async updateFrameData(frameBuffer: ArrayBuffer) {
    try {
      // 添加新帧到队列
      this.frameQueue.push(frameBuffer);

      // 限制队列大小
      if (this.frameQueue.length > this.maxFrames) {
        this.frameQueue.shift();
      }

      // 创建连续的YUV流文件
      await this.createContinuousStream();

    } catch (error) {
      console.log('player_更新帧失败:', error);
    }
  }

  // 创建连续的YUV流
  private async createContinuousStream() {
    try {
      // 确保文件路径存在
      if (!this.tempFilePath) {
        await this.createYuvFilePath();
      }

      if (this.tempFilePath && this.frameQueue.length > 0) {
        // 计算总大小
        const totalSize = this.frameQueue.reduce((sum, frame) => sum + frame.byteLength, 0);

        // 创建合并后的数据
        const combinedBuffer = new ArrayBuffer(totalSize);
        const combinedView = new Uint8Array(combinedBuffer);

        let offset = 0;
        for (const frame of this.frameQueue) {
          const frameView = new Uint8Array(frame);
          combinedView.set(frameView, offset);
          offset += frame.byteLength;
        }

        // 写入文件 - 使用 CREATE 模式确保文件存在
        const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY | fs.OpenMode.TRUNC);
        fs.writeSync(file.fd, combinedBuffer);
        fs.closeSync(file);

        console.log(`player_创建连续流: ${this.frameQueue.length} 帧, ${totalSize} 字节`);

        // 只在第一次或播放停止时重新加载
        if (this.mIjkMediaPlayer && !this.isPlaying) {
          this.mIjkMediaPlayer.reset();
          this.mIjkMediaPlayer.setDataSource(this.tempFilePath);
          this.mIjkMediaPlayer.prepareAsync();
        }
      }
    } catch (error) {
      console.log('player_创建连续流失败:', error);
      console.log('tempFilePath:', this.tempFilePath);
      console.log('frameQueue length:', this.frameQueue.length);
    }
  }

  build() {
    Column() {
      // Text(this.message)
      //   .fontSize(24)
      //   .fontWeight(FontWeight.Bold)
      //   .margin({ bottom: 20 })

      // 视频播放区域
      XComponent({
        id: 'xcomponentId',
        type: 'surface',
        libraryname: 'ijkplayer_napi'
      })
        .onLoad((context) => {
          this.mContext = context || null;
          this.initPlayer();
        })
        .onDestroy(() => {
          this.releasePlayer();
        })
        .width(200)
        .height(200)
        .margin({top:20})
        .aspectRatio(this.aspRatio)
        .backgroundColor(Color.Black)
      // 控制按钮
      Column() {
        Row() {
          Button('加载流数据')
            .onClick(() => {
              this.loadStreamData();
            })
            .margin({ right: 10 })
            .enabled(!this.hasStreamData)

          Button('清除数据')
            .onClick(() => {
              this.clearStreamData();
            })
            .enabled(this.hasStreamData)
        }
        .margin({ bottom: 10 })

        Row() {
          Button(this.autoPlayEnabled ? '关闭自动播放' : '开启自动播放')
            .onClick(() => {
              this.autoPlayEnabled = !this.autoPlayEnabled;
              console.log('player_自动播放状态:', this.autoPlayEnabled ? '开启' : '关闭');
            })
            .margin({ right: 10 })

          Text(`帧数: ${this.PlayerFpsCount}`)
            .fontSize(16)
            .margin({ left: 10 })
        }
        .margin({ bottom: 10 })
        Row() {
          Button(this.isPlaying ? '暂停' : '播放')
            .onClick(() => {
              if (this.isPlaying) {
                this.pausePlay();
              } else {
                this.startPlayWithStream();
              }
            })
            .margin({ right: 10 })
            .enabled(this.hasStreamData)

          Button('停止')
            .onClick(() => {
              this.stopPlay();
            })
            .enabled(this.hasStreamData)
        }
      }
      .margin({ top: 20 })
    }
    .width('100%')
    .height('100%')
    .padding(20)
  }

  private initPlayer() {
    if (!this.mContext) {
      LogUtils.getInstance().LOGI("Context is null, cannot initialize player");
      return;
    }

    // 创建播放器实例
    this.mIjkMediaPlayer = IjkMediaPlayer.getInstance();

    // 设置XComponent上下文
    this.mIjkMediaPlayer.setContext(this.mContext, "xcomponentId");

    // 设置调试模式
    this.mIjkMediaPlayer.setDebug(true);

    // 初始化配置
    this.mIjkMediaPlayer.native_setup();

    // 设置播放器选项
    this.setupPlayerOptions();

    // 设置监听器
    this.setupListeners();

    LogUtils.getInstance().LOGI("Player initialized successfully");
  }

  private setupPlayerOptions() {
    if (!this.mIjkMediaPlayer) return;

    // YUV 原始视频格式设置
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "video_size", "1280x960");
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "pixel_format", "yuv420p");
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "framerate", "25");

    // 循环播放设置
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "loop", "0"); // 无限循环
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "autoexit", "0"); // 播放完不退出

    // 使用精确寻帧
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "enable-accurate-seek", "1");

    // 预读数据的缓冲区大小
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-buffer-size", "102400");

    // 停止预读的最小帧数
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "min-frames", "100");

    // 启动预加载
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", "1");

    // 设置无缓冲
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "packet-buffering", "0");

    // 跳帧处理
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "framedrop", "5");

    // 最大缓冲时间3秒
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max_cached_duration", "3000");

    // 无限制收流
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "infbuf", "1");

    // 屏幕常亮
    this.mIjkMediaPlayer.setScreenOnWhilePlaying(true);

    // 设置超时
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "timeout", "10000000");
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "connect_timeout", "10000000");
  }

  private setupListeners() {
    if (!this.mIjkMediaPlayer) return;

    const that = this;

    // 视频尺寸变化监听
    let mOnVideoSizeChangedListener: OnVideoSizeChangedListener = {
      onVideoSizeChanged(width: number, height: number, sar_num: number, sar_den: number) {
        that.aspRatio = width / height;
        LogUtils.getInstance().LOGI(`ffplayer_Video size changed: ${width}x${height}, aspect ratio: ${that.aspRatio}`);
      }
    };
    this.mIjkMediaPlayer.setOnVideoSizeChangedListener(mOnVideoSizeChangedListener);

    // 准备完成监听
    let mOnPreparedListener: OnPreparedListener = {
      onPrepared() {
        LogUtils.getInstance().LOGI("Player prepared, ready to play");
        that.mIjkMediaPlayer?.start();
        that.isPlaying = true;
      }
    };
    this.mIjkMediaPlayer.setOnPreparedListener(mOnPreparedListener);

    // 播放完成监听
    let mOnCompletionListener: OnCompletionListener = {
      onCompletion() {
        LogUtils.getInstance().LOGI("Playback completed");
        that.isPlaying = false;
      }
    };
    this.mIjkMediaPlayer.setOnCompletionListener(mOnCompletionListener);

    // 错误监听
    let mOnErrorListener: OnErrorListener = {
      onError(what: number, extra: number) {
        LogUtils.getInstance().LOGI(`ffplayer_Player error: what=${what}, extra=${extra}`);
        that.isPlaying = false;
      }
    };
    this.mIjkMediaPlayer.setOnErrorListener(mOnErrorListener);

    // 设置消息监听器
    this.mIjkMediaPlayer.setMessageListener();
  }

  private startPlay() {
    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    if (this.isPlaying) {
      LogUtils.getInstance().LOGI("Already playing");
      return;
    }

    this.mIjkMediaPlayer.start();
    this.isPlaying = true;
    LogUtils.getInstance().LOGI("Started playback");
  }

  private pausePlay() {
    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    if (!this.isPlaying) {
      LogUtils.getInstance().LOGI("Already paused");
      return;
    }

    this.mIjkMediaPlayer.pause();
    this.isPlaying = false;
    LogUtils.getInstance().LOGI("Paused playback");
  }

  private stopPlay() {
    if (!this.mIjkMediaPlayer) return;
    this.mIjkMediaPlayer.stop();
    this.isPlaying = false;
  }

  private releasePlayer() {
    if (this.mIjkMediaPlayer) {
      this.mIjkMediaPlayer.release();
      this.mIjkMediaPlayer = null;
      this.isPlaying = false;
    }
  }

  // 手动加载当前的FpsBuffer流数据
  private async loadStreamData() {
    try {
      if (this.FpsBuffer && this.FpsBuffer.byteLength > 0) {
        console.log('player_手动加载FpsBuffer数据...');
        const success = await this.setYuvStreamData(this.FpsBuffer);
        if (success) {
          LogUtils.getInstance().LOGI("Stream data loaded successfully");
          this.message = 'Stream Player - FpsBuffer数据已加载';
        } else {
          throw new Error('设置YUV流数据失败');
        }
      } else {
        // 如果没有FpsBuffer，使用测试数据
        console.log('player_没有FpsBuffer数据，使用测试视频...');
        await this.setupTestVideoSource();
        this.hasStreamData = true;
        this.message = 'Stream Player - 测试数据已加载';
      }
    } catch (error) {
      LogUtils.getInstance().LOGI(`ffplayer_Failed to load stream data: ${error}`);
      this.message = 'Stream Player - 数据加载失败';
    }
  }

  // 创建测试流数据（实际应用中替换为真实的ArrayBuffer数据）
  private async createTestStreamData() {
    // 为了演示YUV播放，我们使用YUV文件
    this.setupYuvVideoSource();
  }

  // 专门处理YUV文件的方法
  private async setupYuvVideoSource() {
    try {
      // 获取YUV文件数据
      const rawFileData = await getContext(this).resourceManager.getRawFileContent('065(49).yuv');

      // 获取应用缓存目录
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      const tempFilePath = `${cacheDir}/065(49).yuv`;

      // 写入临时文件
      const file = fs.openSync(tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      fs.writeSync(file.fd, rawFileData.buffer);
      fs.closeSync(file);

      console.log('player_tempFilePath', tempFilePath);

      // 设置数据源
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setDataSource(tempFilePath);
        LogUtils.getInstance().LOGI(`ffplayer_Set YUV video source: ${tempFilePath}`);
      }

      // 保存临时文件路径，用于清理
      this.tempFilePath = tempFilePath;

    } catch (error) {
      LogUtils.getInstance().LOGI(`ffplayer_Failed to setup YUV video source: ${error}`);
    }
  }

  // 处理真实的ArrayBuffer流数据的方法
  public async setStreamData(buffer: ArrayBuffer): Promise<boolean> {
    try {
      if (!buffer || buffer.byteLength === 0) {
        LogUtils.getInstance().LOGI("Invalid ArrayBuffer data");
        return false;
      }

      // 将ArrayBuffer写入临时文件
      const filePath = await this.writeArrayBufferToFile(buffer);

      // 设置数据源
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setDataSource(filePath);
        LogUtils.getInstance().LOGI(`ffplayer_Set stream data source: ${filePath}`);
      }

      this.hasStreamData = true;
      this.message = `Stream Player - 已加载 ${buffer.byteLength} 字节数据`;

      return true;
    } catch (error) {
      LogUtils.getInstance().LOGI(`ffplayer_Failed to set stream data: ${error}`);
      return false;
    }
  }

  // 设置测试视频源（用于演示）
  private async setupTestVideoSource() {
    try {
      await this.copyRawFileToTemp();
      // // // 方法1：获取 rawfile 的文件描述符
      // const rawFd = await getContext(this).resourceManager.getRawFd('videoTest.mp4');
      // console.log('player_rawFd', rawFd);
      //
      // // 构造文件描述符路径
      // const fdPath = `fd://${rawFd.fd}:${rawFd.offset}:${rawFd.length}`;
      //
      // if (this.mIjkMediaPlayer) {
      //   this.mIjkMediaPlayer.setDataSource(fdPath);
      //   LogUtils.getInstance().LOGI(`ffplayer_Set test video source: ${fdPath}`);
      // }
    } catch (error) {
      LogUtils.getInstance().LOGI(`ffplayer_Failed to setup video source: ${error}`);
      // 备用方案：复制到临时文件
      // await this.copyRawFileToTemp();
    }
  }

  // 备用方案：将 rawfile 复制到临时文件
  private async copyRawFileToTemp(): Promise<void> {
    try {
      // 获取 rawfile 数据
      const rawFileData = await getContext(this).resourceManager.getRawFileContent('videoTest.mp4');
      // const rawFileData = await getContext(this).resourceManager.getRawFileContent('065(49).yuv');

      // 获取应用缓存目录
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      const tempFilePath = `${cacheDir}/temp_video.mp4`;
      // const tempFilePath = `${cacheDir}/065(49).yuv`;

      // 写入临时文件
      const file = fs.openSync(tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      fs.writeSync(file.fd, rawFileData.buffer);
      fs.closeSync(file);
      console.log('player_tempFilePath',tempFilePath)
      // 设置数据源
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setDataSource(tempFilePath);
        LogUtils.getInstance().LOGI(`ffplayer_Set test video source (temp file): ${tempFilePath}`);
      }

      // 保存临时文件路径，用于清理
      this.tempFilePath = tempFilePath;

    } catch (error) {
      LogUtils.getInstance().LOGI(`ffplayer_Failed to copy rawfile to temp: ${error}`);
    }
  }

  // 处理YUV数据 - 使用连续流
  public async setYuvStreamData(buffer: ArrayBuffer): Promise<boolean> {
    try {
      if (!buffer || buffer.byteLength === 0) return false;

      // 创建文件路径
      await this.createYuvFilePath();

      // 创建连续流文件
      await this.createContinuousStream();

      if (this.mIjkMediaPlayer && this.tempFilePath) {
        this.mIjkMediaPlayer.setDataSource(this.tempFilePath);
      }

      this.hasStreamData = true;
      this.message = `Stream Player - 已加载连续流 ${this.frameQueue.length} 帧`;
      return true;
    } catch (error) {
      console.log('setYuvStreamData失败:', error);
      return false;
    }
  }

  // 创建YUV临时文件路径
  private async createYuvFilePath(): Promise<string> {
    try {
      const context = getContext(this);
      const cacheDir = context.cacheDir;

      // 确保缓存目录存在
      if (!fs.accessSync(cacheDir)) {
        fs.mkdirSync(cacheDir, true);
      }

      const fileName = `yuv_stream.yuv`;
      const tempFilePath = `${cacheDir}/${fileName}`;
      this.tempFilePath = tempFilePath;
      return tempFilePath;
    } catch (error) {
      console.log('创建YUV文件路径失败:', error);
      throw new Error('创建YUV文件路径失败');
    }
  }

  // 将ArrayBuffer写入临时文件的方法（实际应用中使用）
  private async writeArrayBufferToFile(buffer: ArrayBuffer): Promise<string> {
    try {
      // 获取应用缓存目录
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      const fileName = `stream_${Date.now()}.mp4`;
      this.tempFilePath = `${cacheDir}/${fileName}`;

      // 将ArrayBuffer写入文件
      const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      const writeLen = fs.writeSync(file.fd, buffer);
      fs.closeSync(file);

      LogUtils.getInstance().LOGI(`ffplayer_Written ${writeLen} bytes to ${this.tempFilePath}`);
      return this.tempFilePath;
    } catch (error) {
      LogUtils.getInstance().LOGI(`ffplayer_Failed to write ArrayBuffer to file: ${error}`);
      throw new Error(`Failed to write ArrayBuffer to file: ${error}`);
    }
  }

  // 使用流数据开始播放
  private startPlayWithStream() {
    if (!this.hasStreamData) {
      LogUtils.getInstance().LOGI("No stream data available");
      return;
    }

    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    try {
      // 准备播放
      this.mIjkMediaPlayer.prepareAsync();
      LogUtils.getInstance().LOGI("Started preparing stream for playback");
    } catch (error) {
      LogUtils.getInstance().LOGI(`ffplayer_Failed to start playback: ${error}`);
    }
  }

  // 清除流数据
  private clearStreamData() {
    this.hasStreamData = false;
    this.message = 'Stream Player';

    // 清理临时文件
    if (this.tempFilePath) {
      try {
        if (fs.accessSync(this.tempFilePath)) {
          fs.unlinkSync(this.tempFilePath);
          LogUtils.getInstance().LOGI(`ffplayer_Deleted temp file: ${this.tempFilePath}`);
        }
      } catch (error) {
        LogUtils.getInstance().LOGI(`ffplayer_Failed to delete temp file: ${error}`);
      }
      this.tempFilePath = '';
    }

    // 重置播放器
    if (this.mIjkMediaPlayer) {
      this.mIjkMediaPlayer.reset();
    }

    LogUtils.getInstance().LOGI("Stream data cleared");
  }
}