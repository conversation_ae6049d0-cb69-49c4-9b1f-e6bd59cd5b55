<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>s_server</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#CONNECTED-COMMANDS">CONNECTED COMMANDS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-s_server, s_server - SSL/TLS server program</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>s_server</b> [<b>-help</b>] [<b>-port +int</b>] [<b>-accept val</b>] [<b>-unix val</b>] [<b>-4</b>] [<b>-6</b>] [<b>-unlink</b>] [<b>-context val</b>] [<b>-verify int</b>] [<b>-Verify int</b>] [<b>-cert infile</b>] [<b>-nameopt val</b>] [<b>-naccept +int</b>] [<b>-serverinfo val</b>] [<b>-certform PEM|DER</b>] [<b>-key infile</b>] [<b>-keyform format</b>] [<b>-pass val</b>] [<b>-dcert infile</b>] [<b>-dcertform PEM|DER</b>] [<b>-dkey infile</b>] [<b>-dkeyform PEM|DER</b>] [<b>-dpass val</b>] [<b>-nbio_test</b>] [<b>-crlf</b>] [<b>-debug</b>] [<b>-msg</b>] [<b>-msgfile outfile</b>] [<b>-state</b>] [<b>-CAfile infile</b>] [<b>-CApath dir</b>] [<b>-no-CAfile</b>] [<b>-no-CApath</b>] [<b>-nocert</b>] [<b>-quiet</b>] [<b>-no_resume_ephemeral</b>] [<b>-www</b>] [<b>-WWW</b>] [<b>-servername</b>] [<b>-servername_fatal</b>] [<b>-cert2 infile</b>] [<b>-key2 infile</b>] [<b>-tlsextdebug</b>] [<b>-HTTP</b>] [<b>-id_prefix val</b>] [<b>-rand file...</b>] [<b>-writerand file</b>] [<b>-keymatexport val</b>] [<b>-keymatexportlen +int</b>] [<b>-CRL infile</b>] [<b>-crl_download</b>] [<b>-cert_chain infile</b>] [<b>-dcert_chain infile</b>] [<b>-chainCApath dir</b>] [<b>-verifyCApath dir</b>] [<b>-no_cache</b>] [<b>-ext_cache</b>] [<b>-CRLform PEM|DER</b>] [<b>-verify_return_error</b>] [<b>-verify_quiet</b>] [<b>-build_chain</b>] [<b>-chainCAfile infile</b>] [<b>-verifyCAfile infile</b>] [<b>-ign_eof</b>] [<b>-no_ign_eof</b>] [<b>-status</b>] [<b>-status_verbose</b>] [<b>-status_timeout int</b>] [<b>-status_url val</b>] [<b>-status_file infile</b>] [<b>-trace</b>] [<b>-security_debug</b>] [<b>-security_debug_verbose</b>] [<b>-brief</b>] [<b>-rev</b>] [<b>-async</b>] [<b>-ssl_config val</b>] [<b>-max_send_frag +int</b>] [<b>-split_send_frag +int</b>] [<b>-max_pipelines +int</b>] [<b>-read_buf +int</b>] [<b>-no_ssl3</b>] [<b>-no_tls1</b>] [<b>-no_tls1_1</b>] [<b>-no_tls1_2</b>] [<b>-no_tls1_3</b>] [<b>-bugs</b>] [<b>-no_comp</b>] [<b>-comp</b>] [<b>-no_ticket</b>] [<b>-num_tickets</b>] [<b>-serverpref</b>] [<b>-legacy_renegotiation</b>] [<b>-no_renegotiation</b>] [<b>-legacy_server_connect</b>] [<b>-no_resumption_on_reneg</b>] [<b>-no_legacy_server_connect</b>] [<b>-allow_no_dhe_kex</b>] [<b>-prioritize_chacha</b>] [<b>-strict</b>] [<b>-sigalgs val</b>] [<b>-client_sigalgs val</b>] [<b>-groups val</b>] [<b>-curves val</b>] [<b>-named_curve val</b>] [<b>-cipher val</b>] [<b>-ciphersuites val</b>] [<b>-dhparam infile</b>] [<b>-record_padding val</b>] [<b>-debug_broken_protocol</b>] [<b>-policy val</b>] [<b>-purpose val</b>] [<b>-verify_name val</b>] [<b>-verify_depth int</b>] [<b>-auth_level int</b>] [<b>-attime intmax</b>] [<b>-verify_hostname val</b>] [<b>-verify_email val</b>] [<b>-verify_ip</b>] [<b>-ignore_critical</b>] [<b>-issuer_checks</b>] [<b>-crl_check</b>] [<b>-crl_check_all</b>] [<b>-policy_check</b>] [<b>-explicit_policy</b>] [<b>-inhibit_any</b>] [<b>-inhibit_map</b>] [<b>-x509_strict</b>] [<b>-extended_crl</b>] [<b>-use_deltas</b>] [<b>-policy_print</b>] [<b>-check_ss_sig</b>] [<b>-trusted_first</b>] [<b>-suiteB_128_only</b>] [<b>-suiteB_128</b>] [<b>-suiteB_192</b>] [<b>-partial_chain</b>] [<b>-no_alt_chains</b>] [<b>-no_check_time</b>] [<b>-allow_proxy_certs</b>] [<b>-xkey</b>] [<b>-xcert</b>] [<b>-xchain</b>] [<b>-xchain_build</b>] [<b>-xcertform PEM|DER</b>] [<b>-xkeyform PEM|DER</b>] [<b>-nbio</b>] [<b>-psk_identity val</b>] [<b>-psk_hint val</b>] [<b>-psk val</b>] [<b>-psk_session file</b>] [<b>-srpvfile infile</b>] [<b>-srpuserseed val</b>] [<b>-ssl3</b>] [<b>-tls1</b>] [<b>-tls1_1</b>] [<b>-tls1_2</b>] [<b>-tls1_3</b>] [<b>-dtls</b>] [<b>-timeout</b>] [<b>-mtu +int</b>] [<b>-listen</b>] [<b>-dtls1</b>] [<b>-dtls1_2</b>] [<b>-sctp</b>] [<b>-sctp_label_bug</b>] [<b>-no_dhe</b>] [<b>-nextprotoneg val</b>] [<b>-use_srtp val</b>] [<b>-alpn val</b>] [<b>-engine val</b>] [<b>-keylogfile outfile</b>] [<b>-max_early_data int</b>] [<b>-early_data</b>] [<b>-anti_replay</b>] [<b>-no_anti_replay</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>s_server</b> command implements a generic SSL/TLS server which listens for connections on a given port using SSL/TLS.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<p>In addition to the options below the <b>s_server</b> utility also supports the common and server only options documented in the &quot;Supported Command Line Commands&quot; section of the <a href="../man3/SSL_CONF_cmd.html">SSL_CONF_cmd(3)</a> manual page.</p>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="port-int"><b>-port +int</b></dt>
<dd>

<p>The TCP port to listen on for connections. If not specified 4433 is used.</p>

</dd>
<dt id="accept-val"><b>-accept val</b></dt>
<dd>

<p>The optional TCP host and port to listen on for connections. If not specified, *:4433 is used.</p>

</dd>
<dt id="unix-val"><b>-unix val</b></dt>
<dd>

<p>Unix domain socket to accept on.</p>

</dd>
<dt id="pod-4"><b>-4</b></dt>
<dd>

<p>Use IPv4 only.</p>

</dd>
<dt id="pod-6"><b>-6</b></dt>
<dd>

<p>Use IPv6 only.</p>

</dd>
<dt id="unlink"><b>-unlink</b></dt>
<dd>

<p>For -unix, unlink any existing socket first.</p>

</dd>
<dt id="context-val"><b>-context val</b></dt>
<dd>

<p>Sets the SSL context id. It can be given any string value. If this option is not present a default value will be used.</p>

</dd>
<dt id="verify-int--Verify-int"><b>-verify int</b>, <b>-Verify int</b></dt>
<dd>

<p>The verify depth to use. This specifies the maximum length of the client certificate chain and makes the server request a certificate from the client. With the <b>-verify</b> option a certificate is requested but the client does not have to send one, with the <b>-Verify</b> option the client must supply a certificate or an error occurs.</p>

<p>If the cipher suite cannot request a client certificate (for example an anonymous cipher suite or PSK) this option has no effect.</p>

</dd>
<dt id="cert-infile"><b>-cert infile</b></dt>
<dd>

<p>The certificate to use, most servers cipher suites require the use of a certificate and some require a certificate with a certain public key type: for example the DSS cipher suites require a certificate containing a DSS (DSA) key. If not specified then the filename &quot;server.pem&quot; will be used.</p>

</dd>
<dt id="cert_chain"><b>-cert_chain</b></dt>
<dd>

<p>A file containing trusted certificates to use when attempting to build the client/server certificate chain related to the certificate specified via the <b>-cert</b> option.</p>

</dd>
<dt id="build_chain"><b>-build_chain</b></dt>
<dd>

<p>Specify whether the application should build the certificate chain to be provided to the client.</p>

</dd>
<dt id="nameopt-val"><b>-nameopt val</b></dt>
<dd>

<p>Option which determines how the subject or issuer names are displayed. The <b>val</b> argument can be a single option or multiple options separated by commas. Alternatively the <b>-nameopt</b> switch may be used more than once to set multiple options. See the <a href="../man1/x509.html">x509(1)</a> manual page for details.</p>

</dd>
<dt id="naccept-int"><b>-naccept +int</b></dt>
<dd>

<p>The server will exit after receiving the specified number of connections, default unlimited.</p>

</dd>
<dt id="serverinfo-val"><b>-serverinfo val</b></dt>
<dd>

<p>A file containing one or more blocks of PEM data. Each PEM block must encode a TLS ServerHello extension (2 bytes type, 2 bytes length, followed by &quot;length&quot; bytes of extension data). If the client sends an empty TLS ClientHello extension matching the type, the corresponding ServerHello extension will be returned.</p>

</dd>
<dt id="certform-PEM-DER"><b>-certform PEM|DER</b></dt>
<dd>

<p>The certificate format to use: DER or PEM. PEM is the default.</p>

</dd>
<dt id="key-infile"><b>-key infile</b></dt>
<dd>

<p>The private key to use. If not specified then the certificate file will be used.</p>

</dd>
<dt id="keyform-format"><b>-keyform format</b></dt>
<dd>

<p>The private format to use: DER or PEM. PEM is the default.</p>

</dd>
<dt id="pass-val"><b>-pass val</b></dt>
<dd>

<p>The private key password source. For more information about the format of <b>val</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="dcert-infile--dkey-infile"><b>-dcert infile</b>, <b>-dkey infile</b></dt>
<dd>

<p>Specify an additional certificate and private key, these behave in the same manner as the <b>-cert</b> and <b>-key</b> options except there is no default if they are not specified (no additional certificate and key is used). As noted above some cipher suites require a certificate containing a key of a certain type. Some cipher suites need a certificate carrying an RSA key and some a DSS (DSA) key. By using RSA and DSS certificates and keys a server can support clients which only support RSA or DSS cipher suites by using an appropriate certificate.</p>

</dd>
<dt id="dcert_chain"><b>-dcert_chain</b></dt>
<dd>

<p>A file containing trusted certificates to use when attempting to build the server certificate chain when a certificate specified via the <b>-dcert</b> option is in use.</p>

</dd>
<dt id="dcertform-PEM-DER--dkeyform-PEM-DER--dpass-val"><b>-dcertform PEM|DER</b>, <b>-dkeyform PEM|DER</b>, <b>-dpass val</b></dt>
<dd>

<p>Additional certificate and private key format and passphrase respectively.</p>

</dd>
<dt id="xkey-infile--xcert-infile--xchain"><b>-xkey infile</b>, <b>-xcert infile</b>, <b>-xchain</b></dt>
<dd>

<p>Specify an extra certificate, private key and certificate chain. These behave in the same manner as the <b>-cert</b>, <b>-key</b> and <b>-cert_chain</b> options. When specified, the callback returning the first valid chain will be in use by the server.</p>

</dd>
<dt id="xchain_build"><b>-xchain_build</b></dt>
<dd>

<p>Specify whether the application should build the certificate chain to be provided to the client for the extra certificates provided via <b>-xkey infile</b>, <b>-xcert infile</b>, <b>-xchain</b> options.</p>

</dd>
<dt id="xcertform-PEM-DER--xkeyform-PEM-DER"><b>-xcertform PEM|DER</b>, <b>-xkeyform PEM|DER</b></dt>
<dd>

<p>Extra certificate and private key format respectively.</p>

</dd>
<dt id="nbio_test"><b>-nbio_test</b></dt>
<dd>

<p>Tests non blocking I/O.</p>

</dd>
<dt id="crlf"><b>-crlf</b></dt>
<dd>

<p>This option translated a line feed from the terminal into CR+LF.</p>

</dd>
<dt id="debug"><b>-debug</b></dt>
<dd>

<p>Print extensive debugging information including a hex dump of all traffic.</p>

</dd>
<dt id="msg"><b>-msg</b></dt>
<dd>

<p>Show all protocol messages with hex dump.</p>

</dd>
<dt id="msgfile-outfile"><b>-msgfile outfile</b></dt>
<dd>

<p>File to send output of <b>-msg</b> or <b>-trace</b> to, default standard output.</p>

</dd>
<dt id="state"><b>-state</b></dt>
<dd>

<p>Prints the SSL session states.</p>

</dd>
<dt id="CAfile-infile"><b>-CAfile infile</b></dt>
<dd>

<p>A file containing trusted certificates to use during client authentication and to use when attempting to build the server certificate chain. The list is also used in the list of acceptable client CAs passed to the client when a certificate is requested.</p>

</dd>
<dt id="CApath-dir"><b>-CApath dir</b></dt>
<dd>

<p>The directory to use for client certificate verification. This directory must be in &quot;hash format&quot;, see <a href="../man1/verify.html">verify(1)</a> for more information. These are also used when building the server certificate chain.</p>

</dd>
<dt id="chainCApath-dir"><b>-chainCApath dir</b></dt>
<dd>

<p>The directory to use for building the chain provided to the client. This directory must be in &quot;hash format&quot;, see <a href="../man1/verify.html">verify(1)</a> for more information.</p>

</dd>
<dt id="chainCAfile-file"><b>-chainCAfile file</b></dt>
<dd>

<p>A file containing trusted certificates to use when attempting to build the server certificate chain.</p>

</dd>
<dt id="no-CAfile"><b>-no-CAfile</b></dt>
<dd>

<p>Do not load the trusted CA certificates from the default file location.</p>

</dd>
<dt id="no-CApath"><b>-no-CApath</b></dt>
<dd>

<p>Do not load the trusted CA certificates from the default directory location.</p>

</dd>
<dt id="nocert"><b>-nocert</b></dt>
<dd>

<p>If this option is set then no certificate is used. This restricts the cipher suites available to the anonymous ones (currently just anonymous DH).</p>

</dd>
<dt id="quiet"><b>-quiet</b></dt>
<dd>

<p>Inhibit printing of session and certificate information.</p>

</dd>
<dt id="www"><b>-www</b></dt>
<dd>

<p>Sends a status message back to the client when it connects. This includes information about the ciphers used and various session parameters. The output is in HTML format so this option will normally be used with a web browser. Cannot be used in conjunction with <b>-early_data</b>.</p>

</dd>
<dt id="WWW"><b>-WWW</b></dt>
<dd>

<p>Emulates a simple web server. Pages will be resolved relative to the current directory, for example if the URL https://myhost/page.html is requested the file ./page.html will be loaded. Cannot be used in conjunction with <b>-early_data</b>.</p>

</dd>
<dt id="tlsextdebug"><b>-tlsextdebug</b></dt>
<dd>

<p>Print a hex dump of any TLS extensions received from the server.</p>

</dd>
<dt id="HTTP"><b>-HTTP</b></dt>
<dd>

<p>Emulates a simple web server. Pages will be resolved relative to the current directory, for example if the URL https://myhost/page.html is requested the file ./page.html will be loaded. The files loaded are assumed to contain a complete and correct HTTP response (lines that are part of the HTTP response line and headers must end with CRLF). Cannot be used in conjunction with <b>-early_data</b>.</p>

</dd>
<dt id="id_prefix-val"><b>-id_prefix val</b></dt>
<dd>

<p>Generate SSL/TLS session IDs prefixed by <b>val</b>. This is mostly useful for testing any SSL/TLS code (e.g. proxies) that wish to deal with multiple servers, when each of which might be generating a unique range of session IDs (e.g. with a certain prefix).</p>

</dd>
<dt id="rand-file"><b>-rand file...</b></dt>
<dd>

<p>A file or files containing random data used to seed the random number generator. Multiple files can be specified separated by an OS-dependent character. The separator is <b>;</b> for MS-Windows, <b>,</b> for OpenVMS, and <b>:</b> for all others.</p>

</dd>
<dt id="writerand-file">[<b>-writerand file</b>]</dt>
<dd>

<p>Writes random data to the specified <i>file</i> upon exit. This can be used with a subsequent <b>-rand</b> flag.</p>

</dd>
<dt id="verify_return_error"><b>-verify_return_error</b></dt>
<dd>

<p>Verification errors normally just print a message but allow the connection to continue, for debugging purposes. If this option is used, then verification errors close the connection.</p>

</dd>
<dt id="status"><b>-status</b></dt>
<dd>

<p>Enables certificate status request support (aka OCSP stapling).</p>

</dd>
<dt id="status_verbose"><b>-status_verbose</b></dt>
<dd>

<p>Enables certificate status request support (aka OCSP stapling) and gives a verbose printout of the OCSP response.</p>

</dd>
<dt id="status_timeout-int"><b>-status_timeout int</b></dt>
<dd>

<p>Sets the timeout for OCSP response to <b>int</b> seconds.</p>

</dd>
<dt id="status_url-val"><b>-status_url val</b></dt>
<dd>

<p>Sets a fallback responder URL to use if no responder URL is present in the server certificate. Without this option an error is returned if the server certificate does not contain a responder address.</p>

</dd>
<dt id="status_file-infile"><b>-status_file infile</b></dt>
<dd>

<p>Overrides any OCSP responder URLs from the certificate and always provides the OCSP Response stored in the file. The file must be in DER format.</p>

</dd>
<dt id="trace"><b>-trace</b></dt>
<dd>

<p>Show verbose trace output of protocol messages. OpenSSL needs to be compiled with <b>enable-ssl-trace</b> for this option to work.</p>

</dd>
<dt id="brief"><b>-brief</b></dt>
<dd>

<p>Provide a brief summary of connection parameters instead of the normal verbose output.</p>

</dd>
<dt id="rev"><b>-rev</b></dt>
<dd>

<p>Simple test server which just reverses the text received from the client and sends it back to the server. Also sets <b>-brief</b>. Cannot be used in conjunction with <b>-early_data</b>.</p>

</dd>
<dt id="async"><b>-async</b></dt>
<dd>

<p>Switch on asynchronous mode. Cryptographic operations will be performed asynchronously. This will only have an effect if an asynchronous capable engine is also used via the <b>-engine</b> option. For test purposes the dummy async engine (dasync) can be used (if available).</p>

</dd>
<dt id="max_send_frag-int"><b>-max_send_frag +int</b></dt>
<dd>

<p>The maximum size of data fragment to send. See <a href="../man3/SSL_CTX_set_max_send_fragment.html">SSL_CTX_set_max_send_fragment(3)</a> for further information.</p>

</dd>
<dt id="split_send_frag-int"><b>-split_send_frag +int</b></dt>
<dd>

<p>The size used to split data for encrypt pipelines. If more data is written in one go than this value then it will be split into multiple pipelines, up to the maximum number of pipelines defined by max_pipelines. This only has an effect if a suitable cipher suite has been negotiated, an engine that supports pipelining has been loaded, and max_pipelines is greater than 1. See <a href="../man3/SSL_CTX_set_split_send_fragment.html">SSL_CTX_set_split_send_fragment(3)</a> for further information.</p>

</dd>
<dt id="max_pipelines-int"><b>-max_pipelines +int</b></dt>
<dd>

<p>The maximum number of encrypt/decrypt pipelines to be used. This will only have an effect if an engine has been loaded that supports pipelining (e.g. the dasync engine) and a suitable cipher suite has been negotiated. The default value is 1. See <a href="../man3/SSL_CTX_set_max_pipelines.html">SSL_CTX_set_max_pipelines(3)</a> for further information.</p>

</dd>
<dt id="read_buf-int"><b>-read_buf +int</b></dt>
<dd>

<p>The default read buffer size to be used for connections. This will only have an effect if the buffer size is larger than the size that would otherwise be used and pipelining is in use (see <a href="../man3/SSL_CTX_set_default_read_buffer_len.html">SSL_CTX_set_default_read_buffer_len(3)</a> for further information).</p>

</dd>
<dt id="ssl2--ssl3--tls1--tls1_1--tls1_2--tls1_3--no_ssl2--no_ssl3--no_tls1--no_tls1_1--no_tls1_2--no_tls1_3"><b>-ssl2</b>, <b>-ssl3</b>, <b>-tls1</b>, <b>-tls1_1</b>, <b>-tls1_2</b>, <b>-tls1_3</b>, <b>-no_ssl2</b>, <b>-no_ssl3</b>, <b>-no_tls1</b>, <b>-no_tls1_1</b>, <b>-no_tls1_2</b>, <b>-no_tls1_3</b></dt>
<dd>

<p>These options require or disable the use of the specified SSL or TLS protocols. By default <b>s_server</b> will negotiate the highest mutually supported protocol version. When a specific TLS version is required, only that version will be accepted from the client. Note that not all protocols and flags may be available, depending on how OpenSSL was built.</p>

</dd>
<dt id="bugs"><b>-bugs</b></dt>
<dd>

<p>There are several known bugs in SSL and TLS implementations. Adding this option enables various workarounds.</p>

</dd>
<dt id="no_comp"><b>-no_comp</b></dt>
<dd>

<p>Disable negotiation of TLS compression. TLS compression is not recommended and is off by default as of OpenSSL 1.1.0.</p>

</dd>
<dt id="comp"><b>-comp</b></dt>
<dd>

<p>Enable negotiation of TLS compression. This option was introduced in OpenSSL 1.1.0. TLS compression is not recommended and is off by default as of OpenSSL 1.1.0.</p>

</dd>
<dt id="no_ticket"><b>-no_ticket</b></dt>
<dd>

<p>Disable RFC4507bis session ticket support. This option has no effect if TLSv1.3 is negotiated. See <b>-num_tickets</b>.</p>

</dd>
<dt id="num_tickets"><b>-num_tickets</b></dt>
<dd>

<p>Control the number of tickets that will be sent to the client after a full handshake in TLSv1.3. The default number of tickets is 2. This option does not affect the number of tickets sent after a resumption handshake.</p>

</dd>
<dt id="serverpref"><b>-serverpref</b></dt>
<dd>

<p>Use the server&#39;s cipher preferences, rather than the client&#39;s preferences.</p>

</dd>
<dt id="prioritize_chacha"><b>-prioritize_chacha</b></dt>
<dd>

<p>Prioritize ChaCha ciphers when preferred by clients. Requires <b>-serverpref</b>.</p>

</dd>
<dt id="no_resumption_on_reneg"><b>-no_resumption_on_reneg</b></dt>
<dd>

<p>Set the <b>SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION</b> option.</p>

</dd>
<dt id="client_sigalgs-val"><b>-client_sigalgs val</b></dt>
<dd>

<p>Signature algorithms to support for client certificate authentication (colon-separated list).</p>

</dd>
<dt id="named_curve-val"><b>-named_curve val</b></dt>
<dd>

<p>Specifies the elliptic curve to use. NOTE: this is single curve, not a list. For a list of all possible curves, use:</p>

<pre><code>$ openssl ecparam -list_curves</code></pre>

</dd>
<dt id="cipher-val"><b>-cipher val</b></dt>
<dd>

<p>This allows the list of TLSv1.2 and below ciphersuites used by the server to be modified. This list is combined with any TLSv1.3 ciphersuites that have been configured. When the client sends a list of supported ciphers the first client cipher also included in the server list is used. Because the client specifies the preference order, the order of the server cipherlist is irrelevant. See the <b>ciphers</b> command for more information.</p>

</dd>
<dt id="ciphersuites-val"><b>-ciphersuites val</b></dt>
<dd>

<p>This allows the list of TLSv1.3 ciphersuites used by the server to be modified. This list is combined with any TLSv1.2 and below ciphersuites that have been configured. When the client sends a list of supported ciphers the first client cipher also included in the server list is used. Because the client specifies the preference order, the order of the server cipherlist is irrelevant. See the <b>ciphers</b> command for more information. The format for this list is a simple colon (&quot;:&quot;) separated list of TLSv1.3 ciphersuite names.</p>

</dd>
<dt id="dhparam-infile"><b>-dhparam infile</b></dt>
<dd>

<p>The DH parameter file to use. The ephemeral DH cipher suites generate keys using a set of DH parameters. If not specified then an attempt is made to load the parameters from the server certificate file. If this fails then a static set of parameters hard coded into the <b>s_server</b> program will be used.</p>

</dd>
<dt id="attime--check_ss_sig--crl_check--crl_check_all--explicit_policy--extended_crl--ignore_critical--inhibit_any--inhibit_map--no_alt_chains--no_check_time--partial_chain--policy--policy_check--policy_print--purpose--suiteB_128--suiteB_128_only--suiteB_192--trusted_first--use_deltas--auth_level--verify_depth--verify_email--verify_hostname--verify_ip--verify_name--x509_strict"><b>-attime</b>, <b>-check_ss_sig</b>, <b>-crl_check</b>, <b>-crl_check_all</b>, <b>-explicit_policy</b>, <b>-extended_crl</b>, <b>-ignore_critical</b>, <b>-inhibit_any</b>, <b>-inhibit_map</b>, <b>-no_alt_chains</b>, <b>-no_check_time</b>, <b>-partial_chain</b>, <b>-policy</b>, <b>-policy_check</b>, <b>-policy_print</b>, <b>-purpose</b>, <b>-suiteB_128</b>, <b>-suiteB_128_only</b>, <b>-suiteB_192</b>, <b>-trusted_first</b>, <b>-use_deltas</b>, <b>-auth_level</b>, <b>-verify_depth</b>, <b>-verify_email</b>, <b>-verify_hostname</b>, <b>-verify_ip</b>, <b>-verify_name</b>, <b>-x509_strict</b></dt>
<dd>

<p>Set different peer certificate verification options. See the <a href="../man1/verify.html">verify(1)</a> manual page for details.</p>

</dd>
<dt id="crl_check--crl_check_all"><b>-crl_check</b>, <b>-crl_check_all</b></dt>
<dd>

<p>Check the peer certificate has not been revoked by its CA. The CRL(s) are appended to the certificate file. With the <b>-crl_check_all</b> option all CRLs of all CAs in the chain are checked.</p>

</dd>
<dt id="nbio"><b>-nbio</b></dt>
<dd>

<p>Turns on non blocking I/O.</p>

</dd>
<dt id="psk_identity-val"><b>-psk_identity val</b></dt>
<dd>

<p>Expect the client to send PSK identity <b>val</b> when using a PSK cipher suite, and warn if they do not. By default, the expected PSK identity is the string &quot;Client_identity&quot;.</p>

</dd>
<dt id="psk_hint-val"><b>-psk_hint val</b></dt>
<dd>

<p>Use the PSK identity hint <b>val</b> when using a PSK cipher suite.</p>

</dd>
<dt id="psk-val"><b>-psk val</b></dt>
<dd>

<p>Use the PSK key <b>val</b> when using a PSK cipher suite. The key is given as a hexadecimal number without leading 0x, for example -psk 1a2b3c4d. This option must be provided in order to use a PSK cipher.</p>

</dd>
<dt id="psk_session-file"><b>-psk_session file</b></dt>
<dd>

<p>Use the pem encoded SSL_SESSION data stored in <b>file</b> as the basis of a PSK. Note that this will only work if TLSv1.3 is negotiated.</p>

</dd>
<dt id="listen"><b>-listen</b></dt>
<dd>

<p>This option can only be used in conjunction with one of the DTLS options above. With this option <b>s_server</b> will listen on a UDP port for incoming connections. Any ClientHellos that arrive will be checked to see if they have a cookie in them or not. Any without a cookie will be responded to with a HelloVerifyRequest. If a ClientHello with a cookie is received then <b>s_server</b> will connect to that peer and complete the handshake.</p>

</dd>
<dt id="dtls--dtls1--dtls1_2"><b>-dtls</b>, <b>-dtls1</b>, <b>-dtls1_2</b></dt>
<dd>

<p>These options make <b>s_server</b> use DTLS protocols instead of TLS. With <b>-dtls</b>, <b>s_server</b> will negotiate any supported DTLS protocol version, whilst <b>-dtls1</b> and <b>-dtls1_2</b> will only support DTLSv1.0 and DTLSv1.2 respectively.</p>

</dd>
<dt id="sctp"><b>-sctp</b></dt>
<dd>

<p>Use SCTP for the transport protocol instead of UDP in DTLS. Must be used in conjunction with <b>-dtls</b>, <b>-dtls1</b> or <b>-dtls1_2</b>. This option is only available where OpenSSL has support for SCTP enabled.</p>

</dd>
<dt id="sctp_label_bug"><b>-sctp_label_bug</b></dt>
<dd>

<p>Use the incorrect behaviour of older OpenSSL implementations when computing endpoint-pair shared secrets for DTLS/SCTP. This allows communication with older broken implementations but breaks interoperability with correct implementations. Must be used in conjunction with <b>-sctp</b>. This option is only available where OpenSSL has support for SCTP enabled.</p>

</dd>
<dt id="no_dhe"><b>-no_dhe</b></dt>
<dd>

<p>If this option is set then no DH parameters will be loaded effectively disabling the ephemeral DH cipher suites.</p>

</dd>
<dt id="alpn-val--nextprotoneg-val"><b>-alpn val</b>, <b>-nextprotoneg val</b></dt>
<dd>

<p>These flags enable the Application-Layer Protocol Negotiation or Next Protocol Negotiation (NPN) extension, respectively. ALPN is the IETF standard and replaces NPN. The <b>val</b> list is a comma-separated list of supported protocol names. The list should contain the most desirable protocols first. Protocol names are printable ASCII strings, for example &quot;http/1.1&quot; or &quot;spdy/3&quot;. The flag <b>-nextprotoneg</b> cannot be specified if <b>-tls1_3</b> is used.</p>

</dd>
<dt id="engine-val"><b>-engine val</b></dt>
<dd>

<p>Specifying an engine (by its unique id string in <b>val</b>) will cause <b>s_server</b> to attempt to obtain a functional reference to the specified engine, thus initialising it if needed. The engine will then be set as the default for all available algorithms.</p>

</dd>
<dt id="keylogfile-outfile"><b>-keylogfile outfile</b></dt>
<dd>

<p>Appends TLS secrets to the specified keylog file such that external programs (like Wireshark) can decrypt TLS connections.</p>

</dd>
<dt id="max_early_data-int"><b>-max_early_data int</b></dt>
<dd>

<p>Change the default maximum early data bytes that are specified for new sessions and any incoming early data (when used in conjunction with the <b>-early_data</b> flag). The default value is approximately 16k. The argument must be an integer greater than or equal to 0.</p>

</dd>
<dt id="early_data"><b>-early_data</b></dt>
<dd>

<p>Accept early data where possible. Cannot be used in conjunction with <b>-www</b>, <b>-WWW</b>, <b>-HTTP</b> or <b>-rev</b>.</p>

</dd>
<dt id="anti_replay--no_anti_replay"><b>-anti_replay</b>, <b>-no_anti_replay</b></dt>
<dd>

<p>Switches replay protection on or off, respectively. Replay protection is on by default unless overridden by a configuration file. When it is on, OpenSSL will automatically detect if a session ticket has been used more than once, TLSv1.3 has been negotiated, and early data is enabled on the server. A full handshake is forced if a session ticket is used a second or subsequent time. Any early data that was sent will be rejected.</p>

</dd>
</dl>

<h1 id="CONNECTED-COMMANDS">CONNECTED COMMANDS</h1>

<p>If a connection request is established with an SSL client and neither the <b>-www</b> nor the <b>-WWW</b> option has been used then normally any data received from the client is displayed and any key presses will be sent to the client.</p>

<p>Certain commands are also recognized which perform special operations. These commands are a letter which must appear at the start of a line. They are listed below.</p>

<dl>

<dt id="q"><b>q</b></dt>
<dd>

<p>End the current SSL connection but still accept new connections.</p>

</dd>
<dt id="Q"><b>Q</b></dt>
<dd>

<p>End the current SSL connection and exit.</p>

</dd>
<dt id="r"><b>r</b></dt>
<dd>

<p>Renegotiate the SSL session (TLSv1.2 and below only).</p>

</dd>
<dt id="R"><b>R</b></dt>
<dd>

<p>Renegotiate the SSL session and request a client certificate (TLSv1.2 and below only).</p>

</dd>
<dt id="P"><b>P</b></dt>
<dd>

<p>Send some plain text down the underlying TCP connection: this should cause the client to disconnect due to a protocol violation.</p>

</dd>
<dt id="S"><b>S</b></dt>
<dd>

<p>Print out some session cache status information.</p>

</dd>
<dt id="B"><b>B</b></dt>
<dd>

<p>Send a heartbeat message to the client (DTLS only)</p>

</dd>
<dt id="k"><b>k</b></dt>
<dd>

<p>Send a key update message to the client (TLSv1.3 only)</p>

</dd>
<dt id="K"><b>K</b></dt>
<dd>

<p>Send a key update message to the client and request one back (TLSv1.3 only)</p>

</dd>
<dt id="c"><b>c</b></dt>
<dd>

<p>Send a certificate request to the client (TLSv1.3 only)</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p><b>s_server</b> can be used to debug SSL clients. To accept connections from a web browser the command:</p>

<pre><code>openssl s_server -accept 443 -www</code></pre>

<p>can be used for example.</p>

<p>Although specifying an empty list of CAs when requesting a client certificate is strictly speaking a protocol violation, some SSL clients interpret this to mean any CA is acceptable. This is useful for debugging purposes.</p>

<p>The session parameters can printed out using the <b>sess_id</b> program.</p>

<h1 id="BUGS">BUGS</h1>

<p>Because this program has a lot of options and also because some of the techniques used are rather old, the C source of <b>s_server</b> is rather hard to read and not a model of how things should be done. A typical SSL server program would be much simpler.</p>

<p>The output of common ciphers is wrong: it just gives the list of ciphers that OpenSSL recognizes and the client supports.</p>

<p>There should be a way for the <b>s_server</b> program to print out details of any unknown cipher suites a client says it supports.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/SSL_CONF_cmd.html">SSL_CONF_cmd(3)</a>, <a href="../man1/sess_id.html">sess_id(1)</a>, <a href="../man1/s_client.html">s_client(1)</a>, <a href="../man1/ciphers.html">ciphers(1)</a> <a href="../man3/SSL_CTX_set_max_send_fragment.html">SSL_CTX_set_max_send_fragment(3)</a>, <a href="../man3/SSL_CTX_set_split_send_fragment.html">SSL_CTX_set_split_send_fragment(3)</a>, <a href="../man3/SSL_CTX_set_max_pipelines.html">SSL_CTX_set_max_pipelines(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The -no_alt_chains option was added in OpenSSL 1.1.0.</p>

<p>The -allow-no-dhe-kex and -prioritize_chacha options were added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


