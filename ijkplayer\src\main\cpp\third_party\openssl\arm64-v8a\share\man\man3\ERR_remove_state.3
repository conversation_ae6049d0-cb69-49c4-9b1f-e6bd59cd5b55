.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ERR_REMOVE_STATE 3"
.TH ERR_REMOVE_STATE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ERR_remove_thread_state, ERR_remove_state \- DEPRECATED
.SH SYNOPSIS
.IX Header "SYNOPSIS"
Deprecated:
.PP
.Vb 3
\& #if OPENSSL_API_COMPAT < 0x10000000L
\& void ERR_remove_state(unsigned long tid);
\& #endif
\&
\& #if OPENSSL_API_COMPAT < 0x10100000L
\& void ERR_remove_thread_state(void *tid);
\& #endif
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBERR_remove_state()\fR frees the error queue associated with the specified
thread, identified by \fBtid\fR.
\&\fBERR_remove_thread_state()\fR does the same thing, except the identifier is
an opaque pointer.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBERR_remove_state()\fR and \fBERR_remove_thread_state()\fR return no value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
L\fBOPENSSL_init_crypto\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBERR_remove_state()\fR was deprecated in OpenSSL 1.0.0 and
\&\fBERR_remove_thread_state()\fR was deprecated in OpenSSL 1.1.0; these functions
and should not be used.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
