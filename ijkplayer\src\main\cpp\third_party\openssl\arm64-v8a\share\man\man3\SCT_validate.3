.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SCT_VALIDATE 3"
.TH SCT_VALIDATE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SCT_validate, SCT_LIST_validate, SCT_get_validation_status \-
checks Signed Certificate Timestamps (SCTs) are valid
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ct.h>
\&
\& typedef enum {
\&     SCT_VALIDATION_STATUS_NOT_SET,
\&     SCT_VALIDATION_STATUS_UNKNOWN_LOG,
\&     SCT_VALIDATION_STATUS_VALID,
\&     SCT_VALIDATION_STATUS_INVALID,
\&     SCT_VALIDATION_STATUS_UNVERIFIED,
\&     SCT_VALIDATION_STATUS_UNKNOWN_VERSION
\& } sct_validation_status_t;
\&
\& int SCT_validate(SCT *sct, const CT_POLICY_EVAL_CTX *ctx);
\& int SCT_LIST_validate(const STACK_OF(SCT) *scts, CT_POLICY_EVAL_CTX *ctx);
\& sct_validation_status_t SCT_get_validation_status(const SCT *sct);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSCT_validate()\fR will check that an SCT is valid and verify its signature.
\&\fBSCT_LIST_validate()\fR performs the same checks on an entire stack of SCTs.
The result of the validation checks can be obtained by passing the SCT to
\&\fBSCT_get_validation_status()\fR.
.PP
A CT_POLICY_EVAL_CTX must be provided that specifies:
.IP \(bu 2
The certificate the SCT was issued for.
.Sp
Failure to provide the certificate will result in the validation status being
SCT_VALIDATION_STATUS_UNVERIFIED.
.IP \(bu 2
The issuer of that certificate.
.Sp
This is only required if the SCT was issued for a pre-certificate
(see RFC 6962). If it is required but not provided, the validation status will
be SCT_VALIDATION_STATUS_UNVERIFIED.
.IP \(bu 2
A CTLOG_STORE that contains the CT log that issued this SCT.
.Sp
If the SCT was issued by a log that is not in this CTLOG_STORE, the validation
status will be SCT_VALIDATION_STATUS_UNKNOWN_LOG.
.PP
If the SCT is of an unsupported version (only v1 is currently supported), the
validation status will be SCT_VALIDATION_STATUS_UNKNOWN_VERSION.
.PP
If the SCT's signature is incorrect, its timestamp is in the future (relative to
the time in CT_POLICY_EVAL_CTX), or if it is otherwise invalid, the validation
status will be SCT_VALIDATION_STATUS_INVALID.
.PP
If all checks pass, the validation status will be SCT_VALIDATION_STATUS_VALID.
.SH NOTES
.IX Header "NOTES"
A return value of 0 from \fBSCT_LIST_validate()\fR should not be interpreted as a
failure. At a minimum, only one valid SCT may provide sufficient confidence
that a certificate has been publicly logged.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSCT_validate()\fR returns a negative integer if an internal error occurs, 0 if the
SCT fails validation, or 1 if the SCT passes validation.
.PP
\&\fBSCT_LIST_validate()\fR returns a negative integer if an internal error occurs, 0
if any of SCTs fails validation, or 1 if they all pass validation.
.PP
\&\fBSCT_get_validation_status()\fR returns the validation status of the SCT.
If \fBSCT_validate()\fR or \fBSCT_LIST_validate()\fR have not been passed that SCT, the
returned value will be SCT_VALIDATION_STATUS_NOT_SET.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBct\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
