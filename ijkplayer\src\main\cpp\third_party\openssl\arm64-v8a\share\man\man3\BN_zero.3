.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_ZERO 3"
.TH BN_ZERO 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_zero, BN_one, BN_value_one, BN_set_word, BN_get_word \- BIGNUM assignment
operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& void BN_zero(BIGNUM *a);
\& int BN_one(BIGNUM *a);
\&
\& const BIGNUM *BN_value_one(void);
\&
\& int BN_set_word(BIGNUM *a, BN_ULONG w);
\& unsigned BN_ULONG BN_get_word(BIGNUM *a);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_ULONG\fR is a macro that will be an unsigned integral type optimized
for the most efficient implementation on the local platform.
.PP
\&\fBBN_zero()\fR, \fBBN_one()\fR and \fBBN_set_word()\fR set \fBa\fR to the values 0, 1 and
\&\fBw\fR respectively.  \fBBN_zero()\fR and \fBBN_one()\fR are macros.
.PP
\&\fBBN_value_one()\fR returns a \fBBIGNUM\fR constant of value 1. This constant
is useful for use in comparisons and assignment.
.PP
\&\fBBN_get_word()\fR returns \fBa\fR, if it can be represented as a \fBBN_ULONG\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_get_word()\fR returns the value \fBa\fR, or all-bits-set if \fBa\fR cannot
be represented as a single integer.
.PP
\&\fBBN_one()\fR and \fBBN_set_word()\fR return 1 on success, 0 otherwise.
\&\fBBN_value_one()\fR returns the constant.
\&\fBBN_zero()\fR never fails and returns no value.
.SH BUGS
.IX Header "BUGS"
If a \fBBIGNUM\fR is equal to the value of all-bits-set, it will collide
with the error condition returned by \fBBN_get_word()\fR which uses that
as an error value.
.PP
\&\fBBN_ULONG\fR should probably be a typedef.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBN_bn2bin\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
In OpenSSL 0.9.8, \fBBN_zero()\fR was changed to not return a value; previous
versions returned an int.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
