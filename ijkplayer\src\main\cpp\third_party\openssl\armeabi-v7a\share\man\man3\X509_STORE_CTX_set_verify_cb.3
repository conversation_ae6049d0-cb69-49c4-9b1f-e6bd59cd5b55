.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_STORE_CTX_SET_VERIFY_CB 3"
.TH X509_STORE_CTX_SET_VERIFY_CB 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_STORE_CTX_get_cleanup,
X509_STORE_CTX_get_lookup_crls,
X509_STORE_CTX_get_lookup_certs,
X509_STORE_CTX_get_check_policy,
X509_STORE_CTX_get_cert_crl,
X509_STORE_CTX_get_check_crl,
X509_STORE_CTX_get_get_crl,
X509_STORE_CTX_get_check_revocation,
X509_STORE_CTX_get_check_issued,
X509_STORE_CTX_get_get_issuer,
X509_STORE_CTX_get_verify_cb,
X509_STORE_CTX_set_verify_cb,
X509_STORE_CTX_verify_cb
\&\- get and set verification callback
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509_vfy.h>
\&
\& typedef int (*X509_STORE_CTX_verify_cb)(int, X509_STORE_CTX *);
\&
\& X509_STORE_CTX_verify_cb X509_STORE_CTX_get_verify_cb(X509_STORE_CTX *ctx);
\&
\& void X509_STORE_CTX_set_verify_cb(X509_STORE_CTX *ctx,
\&                                   X509_STORE_CTX_verify_cb verify_cb);
\&
\& X509_STORE_CTX_get_issuer_fn X509_STORE_CTX_get_get_issuer(X509_STORE_CTX *ctx);
\& X509_STORE_CTX_check_issued_fn X509_STORE_CTX_get_check_issued(X509_STORE_CTX *ctx);
\& X509_STORE_CTX_check_revocation_fn X509_STORE_CTX_get_check_revocation(X509_STORE_CTX *ctx);
\& X509_STORE_CTX_get_crl_fn X509_STORE_CTX_get_get_crl(X509_STORE_CTX *ctx);
\& X509_STORE_CTX_check_crl_fn X509_STORE_CTX_get_check_crl(X509_STORE_CTX *ctx);
\& X509_STORE_CTX_cert_crl_fn X509_STORE_CTX_get_cert_crl(X509_STORE_CTX *ctx);
\& X509_STORE_CTX_check_policy_fn X509_STORE_CTX_get_check_policy(X509_STORE_CTX *ctx);
\& X509_STORE_CTX_lookup_certs_fn X509_STORE_CTX_get_lookup_certs(X509_STORE_CTX *ctx);
\& X509_STORE_CTX_lookup_crls_fn X509_STORE_CTX_get_lookup_crls(X509_STORE_CTX *ctx);
\& X509_STORE_CTX_cleanup_fn X509_STORE_CTX_get_cleanup(X509_STORE_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_STORE_CTX_set_verify_cb()\fR sets the verification callback of \fBctx\fR to
\&\fBverify_cb\fR overwriting any existing callback.
.PP
The verification callback can be used to customise the operation of certificate
verification, either by overriding error conditions or logging errors for
debugging purposes.
.PP
However, a verification callback is \fBnot\fR essential and the default operation
is often sufficient.
.PP
The \fBok\fR parameter to the callback indicates the value the callback should
return to retain the default behaviour. If it is zero then an error condition
is indicated. If it is 1 then no error occurred. If the flag
\&\fBX509_V_FLAG_NOTIFY_POLICY\fR is set then \fBok\fR is set to 2 to indicate the
policy checking is complete.
.PP
The \fBctx\fR parameter to the callback is the \fBX509_STORE_CTX\fR structure that
is performing the verification operation. A callback can examine this
structure and receive additional information about the error, for example
by calling \fBX509_STORE_CTX_get_current_cert()\fR. Additional application data can
be passed to the callback via the \fBex_data\fR mechanism.
.PP
\&\fBX509_STORE_CTX_get_verify_cb()\fR returns the value of the current callback
for the specific \fBctx\fR.
.PP
\&\fBX509_STORE_CTX_get_get_issuer()\fR,
\&\fBX509_STORE_CTX_get_check_issued()\fR, \fBX509_STORE_CTX_get_check_revocation()\fR,
\&\fBX509_STORE_CTX_get_get_crl()\fR, \fBX509_STORE_CTX_get_check_crl()\fR,
\&\fBX509_STORE_CTX_get_cert_crl()\fR, \fBX509_STORE_CTX_get_check_policy()\fR,
\&\fBX509_STORE_CTX_get_lookup_certs()\fR, \fBX509_STORE_CTX_get_lookup_crls()\fR
and \fBX509_STORE_CTX_get_cleanup()\fR return the function pointers cached
from the corresponding \fBX509_STORE\fR, please see
\&\fBX509_STORE_set_verify\fR\|(3) for more information.
.SH WARNINGS
.IX Header "WARNINGS"
In general a verification callback should \fBNOT\fR unconditionally return 1 in
all circumstances because this will allow verification to succeed no matter
what the error. This effectively removes all security from the application
because \fBany\fR certificate (including untrusted generated ones) will be
accepted.
.SH NOTES
.IX Header "NOTES"
The verification callback can be set and inherited from the parent structure
performing the operation. In some cases (such as S/MIME verification) the
\&\fBX509_STORE_CTX\fR structure is created and destroyed internally and the
only way to set a custom verification callback is by inheriting it from the
associated \fBX509_STORE\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_STORE_CTX_set_verify_cb()\fR does not return a value.
.SH EXAMPLES
.IX Header "EXAMPLES"
Default callback operation:
.PP
.Vb 3
\& int verify_callback(int ok, X509_STORE_CTX *ctx) {
\&     return ok;
\& }
.Ve
.PP
Simple example, suppose a certificate in the chain is expired and we wish
to continue after this error:
.PP
.Vb 7
\& int verify_callback(int ok, X509_STORE_CTX *ctx) {
\&     /* Tolerate certificate expiration */
\&     if (X509_STORE_CTX_get_error(ctx) == X509_V_ERR_CERT_HAS_EXPIRED)
\&         return 1;
\&     /* Otherwise don\*(Aqt override */
\&     return ok;
\& }
.Ve
.PP
More complex example, we don't wish to continue after \fBany\fR certificate has
expired just one specific case:
.PP
.Vb 4
\& int verify_callback(int ok, X509_STORE_CTX *ctx)
\& {
\&     int err = X509_STORE_CTX_get_error(ctx);
\&     X509 *err_cert = X509_STORE_CTX_get_current_cert(ctx);
\&
\&     if (err == X509_V_ERR_CERT_HAS_EXPIRED) {
\&         if (check_is_acceptable_expired_cert(err_cert)
\&             return 1;
\&     }
\&     return ok;
\& }
.Ve
.PP
Full featured logging callback. In this case the \fBbio_err\fR is assumed to be
a global logging \fBBIO\fR, an alternative would to store a BIO in \fBctx\fR using
\&\fBex_data\fR.
.PP
.Vb 4
\& int verify_callback(int ok, X509_STORE_CTX *ctx)
\& {
\&     X509 *err_cert;
\&     int err, depth;
\&
\&     err_cert = X509_STORE_CTX_get_current_cert(ctx);
\&     err = X509_STORE_CTX_get_error(ctx);
\&     depth = X509_STORE_CTX_get_error_depth(ctx);
\&
\&     BIO_printf(bio_err, "depth=%d ", depth);
\&     if (err_cert) {
\&         X509_NAME_print_ex(bio_err, X509_get_subject_name(err_cert),
\&                            0, XN_FLAG_ONELINE);
\&         BIO_puts(bio_err, "\en");
\&     }
\&     else
\&         BIO_puts(bio_err, "<no cert>\en");
\&     if (!ok)
\&         BIO_printf(bio_err, "verify error:num=%d:%s\en", err,
\&                    X509_verify_cert_error_string(err));
\&     switch (err) {
\&     case X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT:
\&         BIO_puts(bio_err, "issuer= ");
\&         X509_NAME_print_ex(bio_err, X509_get_issuer_name(err_cert),
\&                            0, XN_FLAG_ONELINE);
\&         BIO_puts(bio_err, "\en");
\&         break;
\&     case X509_V_ERR_CERT_NOT_YET_VALID:
\&     case X509_V_ERR_ERROR_IN_CERT_NOT_BEFORE_FIELD:
\&         BIO_printf(bio_err, "notBefore=");
\&         ASN1_TIME_print(bio_err, X509_get_notBefore(err_cert));
\&         BIO_printf(bio_err, "\en");
\&         break;
\&     case X509_V_ERR_CERT_HAS_EXPIRED:
\&     case X509_V_ERR_ERROR_IN_CERT_NOT_AFTER_FIELD:
\&         BIO_printf(bio_err, "notAfter=");
\&         ASN1_TIME_print(bio_err, X509_get_notAfter(err_cert));
\&         BIO_printf(bio_err, "\en");
\&         break;
\&     case X509_V_ERR_NO_EXPLICIT_POLICY:
\&         policies_print(bio_err, ctx);
\&         break;
\&     }
\&     if (err == X509_V_OK && ok == 2)
\&         /* print out policies */
\&
\&     BIO_printf(bio_err, "verify return:%d\en", ok);
\&     return(ok);
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_STORE_CTX_get_error\fR\|(3)
\&\fBX509_STORE_set_verify_cb_func\fR\|(3)
\&\fBX509_STORE_CTX_get_ex_new_index\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The
\&\fBX509_STORE_CTX_get_get_issuer()\fR,
\&\fBX509_STORE_CTX_get_check_issued()\fR, \fBX509_STORE_CTX_get_check_revocation()\fR,
\&\fBX509_STORE_CTX_get_get_crl()\fR, \fBX509_STORE_CTX_get_check_crl()\fR,
\&\fBX509_STORE_CTX_get_cert_crl()\fR, \fBX509_STORE_CTX_get_check_policy()\fR,
\&\fBX509_STORE_CTX_get_lookup_certs()\fR, \fBX509_STORE_CTX_get_lookup_crls()\fR
and \fBX509_STORE_CTX_get_cleanup()\fR functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2009\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
