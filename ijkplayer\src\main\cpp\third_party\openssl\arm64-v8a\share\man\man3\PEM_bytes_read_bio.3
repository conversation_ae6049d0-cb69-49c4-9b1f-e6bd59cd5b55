.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PEM_BYTES_READ_BIO 3"
.TH PEM_BYTES_READ_BIO 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PEM_bytes_read_bio, PEM_bytes_read_bio_secmem \- read a PEM\-encoded data structure from a BIO
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pem.h>
\&
\& int PEM_bytes_read_bio(unsigned char **pdata, long *plen, char **pnm,
\&                        const char *name, BIO *bp, pem_password_cb *cb,
\&                        void *u);
\& int PEM_bytes_read_bio_secmem(unsigned char **pdata, long *plen, char **pnm,
\&                               const char *name, BIO *bp, pem_password_cb *cb,
\&                               void *u);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPEM_bytes_read_bio()\fR reads PEM-formatted (IETF RFC 1421 and IETF RFC 7468)
data from the BIO
\&\fIbp\fR for the data type given in \fIname\fR (RSA PRIVATE KEY, CERTIFICATE,
etc.).  If multiple PEM-encoded data structures are present in the same
stream, \fBPEM_bytes_read_bio()\fR will skip non-matching data types and
continue reading.  Non-PEM data present in the stream may cause an
error.
.PP
The PEM header may indicate that the following data is encrypted; if so,
the data will be decrypted, waiting on user input to supply a passphrase
if needed.  The password callback \fIcb\fR and rock \fIu\fR are used to obtain
the decryption passphrase, if applicable.
.PP
Some data types have compatibility aliases, such as a file containing
X509 CERTIFICATE matching a request for the deprecated type CERTIFICATE.
The actual type indicated by the file is returned in \fI*pnm\fR if \fIpnm\fR is
non-NULL.  The caller must free the storage pointed to by \fI*pnm\fR.
.PP
The returned data is the DER-encoded form of the requested type, in
\&\fI*pdata\fR with length \fI*plen\fR.  The caller must free the storage pointed
to by \fI*pdata\fR.
.PP
\&\fBPEM_bytes_read_bio_secmem()\fR is similar to \fBPEM_bytes_read_bio()\fR, but uses
memory from the secure heap for its temporary buffers and the storage
returned in \fI*pdata\fR and \fI*pnm\fR.  Accordingly, the caller must use
\&\fBOPENSSL_secure_free()\fR to free that storage.
.SH NOTES
.IX Header "NOTES"
\&\fBPEM_bytes_read_bio_secmem()\fR only enforces that the secure heap is used for
storage allocated within the PEM processing stack.  The BIO stack from
which input is read may also use temporary buffers, which are not necessarily
allocated from the secure heap.  In cases where it is desirable to ensure
that the contents of the PEM file only appears in memory from the secure heap,
care is needed in generating the BIO passed as \fIbp\fR.  In particular, the
use of \fBBIO_s_file()\fR indicates the use of the operating system stdio
functionality, which includes buffering as a feature; \fBBIO_s_fd()\fR is likely
to be more appropriate in such cases.
.PP
These functions make no assumption regarding the pass phrase received from the
password callback.
It will simply be treated as a byte sequence.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPEM_bytes_read_bio()\fR and \fBPEM_bytes_read_bio_secmem()\fR return 1 for success or
0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPEM_read_bio_ex\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBPEM_bytes_read_bio_secmem()\fR was introduced in OpenSSL 1.1.1
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
