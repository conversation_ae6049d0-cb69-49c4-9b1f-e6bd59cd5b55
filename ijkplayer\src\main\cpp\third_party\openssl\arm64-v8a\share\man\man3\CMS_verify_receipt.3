.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_VERIFY_RECEIPT 3"
.TH CMS_VERIFY_RECEIPT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_verify_receipt \- verify a CMS signed receipt
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& int CMS_verify_receipt(CMS_ContentInfo *rcms, CMS_ContentInfo *ocms,
\&                        STACK_OF(X509) *certs, X509_STORE *store,
\&                        unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_verify_receipt()\fR verifies a CMS signed receipt. \fBrcms\fR is the signed
receipt to verify. \fBocms\fR is the original SignedData structure containing the
receipt request. \fBcerts\fR is a set of certificates in which to search for the
signing certificate. \fBstore\fR is a trusted certificate store (used for chain
verification).
.PP
\&\fBflags\fR is an optional set of flags, which can be used to modify the verify
operation.
.SH NOTES
.IX Header "NOTES"
This functions behaves in a similar way to \fBCMS_verify()\fR except the flag values
\&\fBCMS_DETACHED\fR, \fBCMS_BINARY\fR, \fBCMS_TEXT\fR and \fBCMS_STREAM\fR are not
supported since they do not make sense in the context of signed receipts.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_verify_receipt()\fR returns 1 for a successful verification and zero if an
error occurred.
.PP
The error can be obtained from \fBERR_get_error\fR\|(3)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBCMS_sign_receipt\fR\|(3),
\&\fBCMS_verify\fR\|(3),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
