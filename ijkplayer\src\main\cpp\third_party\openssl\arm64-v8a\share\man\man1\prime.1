.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PRIME 1"
.TH PRIME 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-prime,
prime \- compute prime numbers
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl prime\fR
[\fB\-help\fR]
[\fB\-hex\fR]
[\fB\-generate\fR]
[\fB\-bits\fR]
[\fB\-safe\fR]
[\fB\-checks\fR]
[\fInumber...\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBprime\fR command checks if the specified numbers are prime.
.PP
If no numbers are given on the command line, the \fB\-generate\fR flag should
be used to generate primes according to the requirements specified by the
rest of the flags.
.SH OPTIONS
.IX Header "OPTIONS"
.IP [\fB\-help\fR] 4
.IX Item "[-help]"
Display an option summary.
.IP [\fB\-hex\fR] 4
.IX Item "[-hex]"
Generate hex output.
.IP [\fB\-generate\fR] 4
.IX Item "[-generate]"
Generate a prime number.
.IP "[\fB\-bits num\fR]" 4
.IX Item "[-bits num]"
Generate a prime with \fBnum\fR bits.
.IP [\fB\-safe\fR] 4
.IX Item "[-safe]"
When used with \fB\-generate\fR, generates a "safe" prime. If the number
generated is \fBn\fR, then check that \fB(n\-1)/2\fR is also prime.
.IP "[\fB\-checks num\fR]" 4
.IX Item "[-checks num]"
Perform the checks \fBnum\fR times to see that the generated number
is prime.  The default is 20.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
