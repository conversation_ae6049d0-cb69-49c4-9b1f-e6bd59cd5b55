.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_CLIENT_HELLO_CB 3"
.TH SSL_CTX_SET_CLIENT_HELLO_CB 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_client_hello_cb, SSL_client_hello_cb_fn, SSL_client_hello_isv2, SSL_client_hello_get0_legacy_version, SSL_client_hello_get0_random, SSL_client_hello_get0_session_id, SSL_client_hello_get0_ciphers, SSL_client_hello_get0_compression_methods, SSL_client_hello_get1_extensions_present, SSL_client_hello_get0_ext \- callback functions for early server\-side ClientHello processing
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 10
\& typedef int (*SSL_client_hello_cb_fn)(SSL *s, int *al, void *arg);
\& void SSL_CTX_set_client_hello_cb(SSL_CTX *c, SSL_client_hello_cb_fn *f,
\&                                  void *arg);
\& int SSL_client_hello_isv2(SSL *s);
\& unsigned int SSL_client_hello_get0_legacy_version(SSL *s);
\& size_t SSL_client_hello_get0_random(SSL *s, const unsigned char **out);
\& size_t SSL_client_hello_get0_session_id(SSL *s, const unsigned char **out);
\& size_t SSL_client_hello_get0_ciphers(SSL *s, const unsigned char **out);
\& size_t SSL_client_hello_get0_compression_methods(SSL *s,
\&                                                  const unsigned char **out);
\& int SSL_client_hello_get1_extensions_present(SSL *s, int **out,
\&                                              size_t *outlen);
\& int SSL_client_hello_get0_ext(SSL *s, int type, const unsigned char **out,
\&                               size_t *outlen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_client_hello_cb()\fR sets the callback function, which is automatically
called during the early stages of ClientHello processing on the server.
The argument supplied when setting the callback is passed back to the
callback at runtime.  A callback that returns failure (0) will cause the
connection to terminate, and callbacks returning failure should indicate
what alert value is to be sent in the \fBal\fR parameter.  A callback may
also return a negative value to suspend the handshake, and the handshake
function will return immediately.  \fBSSL_get_error\fR\|(3) will return
SSL_ERROR_WANT_CLIENT_HELLO_CB to indicate that the handshake was suspended.
It is the job of the ClientHello callback to store information about the state
of the last call if needed to continue.  On the next call into the handshake
function, the ClientHello callback will be called again, and, if it returns
success, normal handshake processing will continue from that point.
.PP
\&\fBSSL_client_hello_isv2()\fR indicates whether the ClientHello was carried in a
SSLv2 record and is in the SSLv2 format.  The SSLv2 format has substantial
differences from the normal SSLv3 format, including using three bytes per
cipher suite, and not allowing extensions.  Additionally, the SSLv2 format
\&'challenge' field is exposed via \fBSSL_client_hello_get0_random()\fR, padded to
SSL3_RANDOM_SIZE bytes with zeros if needed.  For SSLv2 format ClientHellos,
\&\fBSSL_client_hello_get0_compression_methods()\fR returns a dummy list that only includes
the null compression method, since the SSLv2 format does not include a
mechanism by which to negotiate compression.
.PP
\&\fBSSL_client_hello_get0_random()\fR, \fBSSL_client_hello_get0_session_id()\fR,
\&\fBSSL_client_hello_get0_ciphers()\fR, and
\&\fBSSL_client_hello_get0_compression_methods()\fR provide access to the corresponding
ClientHello fields, returning the field length and optionally setting an out
pointer to the octets of that field.
.PP
Similarly, \fBSSL_client_hello_get0_ext()\fR provides access to individual extensions
from the ClientHello on a per-extension basis.  For the provided wire
protocol extension type value, the extension value and length are returned
in the output parameters (if present).
.PP
\&\fBSSL_client_hello_get1_extensions_present()\fR can be used prior to
\&\fBSSL_client_hello_get0_ext()\fR, to determine which extensions are present in the
ClientHello before querying for them.  The \fBout\fR and \fBoutlen\fR parameters are
both required, and on success the caller must release the storage allocated for
\&\fB*out\fR using \fBOPENSSL_free()\fR.  The contents of \fB*out\fR is an array of integers
holding the numerical value of the TLS extension types in the order they appear
in the ClientHello.  \fB*outlen\fR contains the number of elements in the array.
In situations when the ClientHello has no extensions, the function will return
success with \fB*out\fR set to NULL and \fB*outlen\fR set to 0.
.SH NOTES
.IX Header "NOTES"
The ClientHello callback provides a vast window of possibilities for application
code to affect the TLS handshake.  A primary use of the callback is to
allow the server to examine the server name indication extension provided
by the client in order to select an appropriate certificate to present,
and make other configuration adjustments relevant to that server name
and its configuration.  Such configuration changes can include swapping out
the associated SSL_CTX pointer, modifying the server's list of permitted TLS
versions, changing the server's cipher list in response to the client's
cipher list, etc.
.PP
It is also recommended that applications utilize a ClientHello callback and
not use a servername callback, in order to avoid unexpected behavior that
occurs due to the relative order of processing between things like session
resumption and the historical servername callback.
.PP
The SSL_client_hello_* family of functions may only be called from code executing
within a ClientHello callback.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The application's supplied ClientHello callback returns
SSL_CLIENT_HELLO_SUCCESS on success, SSL_CLIENT_HELLO_ERROR on failure, and
SSL_CLIENT_HELLO_RETRY to suspend processing.
.PP
\&\fBSSL_client_hello_isv2()\fR returns 1 for SSLv2\-format ClientHellos and 0 otherwise.
.PP
\&\fBSSL_client_hello_get0_random()\fR, \fBSSL_client_hello_get0_session_id()\fR,
\&\fBSSL_client_hello_get0_ciphers()\fR, and
\&\fBSSL_client_hello_get0_compression_methods()\fR return the length of the
corresponding ClientHello fields.  If zero is returned, the output pointer
should not be assumed to be valid.
.PP
\&\fBSSL_client_hello_get0_ext()\fR returns 1 if the extension of type 'type' is present, and
0 otherwise.
.PP
\&\fBSSL_client_hello_get1_extensions_present()\fR returns 1 on success and 0 on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_set_tlsext_servername_callback\fR\|(3),
SSL_bytes_to_cipher_list
.SH HISTORY
.IX Header "HISTORY"
The SSL ClientHello callback, \fBSSL_client_hello_isv2()\fR,
\&\fBSSL_client_hello_get0_random()\fR, \fBSSL_client_hello_get0_session_id()\fR,
\&\fBSSL_client_hello_get0_ciphers()\fR, \fBSSL_client_hello_get0_compression_methods()\fR,
\&\fBSSL_client_hello_get0_ext()\fR, and \fBSSL_client_hello_get1_extensions_present()\fR
were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
