.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_CTX_NEW 3"
.TH EVP_PKEY_CTX_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_CTX_new, EVP_PKEY_CTX_new_id, EVP_PKEY_CTX_dup, EVP_PKEY_CTX_free \- public key algorithm context functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_PKEY_CTX *EVP_PKEY_CTX_new(EVP_PKEY *pkey, ENGINE *e);
\& EVP_PKEY_CTX *EVP_PKEY_CTX_new_id(int id, ENGINE *e);
\& EVP_PKEY_CTX *EVP_PKEY_CTX_dup(EVP_PKEY_CTX *ctx);
\& void EVP_PKEY_CTX_free(EVP_PKEY_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_CTX_new()\fR function allocates public key algorithm context using
the algorithm specified in \fBpkey\fR and ENGINE \fBe\fR.
.PP
The \fBEVP_PKEY_CTX_new_id()\fR function allocates public key algorithm context
using the algorithm specified by \fBid\fR and ENGINE \fBe\fR. It is normally used
when no \fBEVP_PKEY\fR structure is associated with the operations, for example
during parameter generation of key generation for some algorithms.
.PP
\&\fBEVP_PKEY_CTX_dup()\fR duplicates the context \fBctx\fR.
.PP
\&\fBEVP_PKEY_CTX_free()\fR frees up the context \fBctx\fR.
If \fBctx\fR is NULL, nothing is done.
.SH NOTES
.IX Header "NOTES"
The \fBEVP_PKEY_CTX\fR structure is an opaque public key algorithm context used
by the OpenSSL high-level public key API. Contexts \fBMUST NOT\fR be shared between
threads: that is it is not permissible to use the same context simultaneously
in two threads.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_CTX_new()\fR, \fBEVP_PKEY_CTX_new_id()\fR, \fBEVP_PKEY_CTX_dup()\fR returns either
the newly allocated \fBEVP_PKEY_CTX\fR structure of \fBNULL\fR if an error occurred.
.PP
\&\fBEVP_PKEY_CTX_free()\fR does not return a value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
