<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>req</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#CONFIGURATION-FILE-FORMAT">CONFIGURATION FILE FORMAT</a></li>
  <li><a href="#DISTINGUISHED-NAME-AND-ATTRIBUTE-SECTION-FORMAT">DISTINGUISHED NAME AND ATTRIBUTE SECTION FORMAT</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#DIAGNOSTICS">DIAGNOSTICS</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-req, req - PKCS#10 certificate request and certificate generating utility</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>req</b> [<b>-help</b>] [<b>-inform PEM|DER</b>] [<b>-outform PEM|DER</b>] [<b>-in filename</b>] [<b>-passin arg</b>] [<b>-out filename</b>] [<b>-passout arg</b>] [<b>-text</b>] [<b>-pubkey</b>] [<b>-noout</b>] [<b>-verify</b>] [<b>-modulus</b>] [<b>-new</b>] [<b>-rand file...</b>] [<b>-writerand file</b>] [<b>-newkey rsa:bits</b>] [<b>-newkey alg:file</b>] [<b>-nodes</b>] [<b>-key filename</b>] [<b>-keyform PEM|DER</b>] [<b>-keyout filename</b>] [<b>-keygen_engine id</b>] [<b>-<i>digest</i></b>] [<b>-config filename</b>] [<b>-multivalue-rdn</b>] [<b>-x509</b>] [<b>-days n</b>] [<b>-set_serial n</b>] [<b>-newhdr</b>] [<b>-addext ext</b>] [<b>-extensions section</b>] [<b>-reqexts section</b>] [<b>-precert</b>] [<b>-utf8</b>] [<b>-nameopt</b>] [<b>-reqopt</b>] [<b>-subject</b>] [<b>-subj arg</b>] [<b>-sigopt nm:v</b>] [<b>-batch</b>] [<b>-verbose</b>] [<b>-engine id</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>req</b> command primarily creates and processes certificate requests in PKCS#10 format. It can additionally create self signed certificates for use as root CAs for example.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="inform-DER-PEM"><b>-inform DER|PEM</b></dt>
<dd>

<p>This specifies the input format. The <b>DER</b> option uses an ASN1 DER encoded form compatible with the PKCS#10. The <b>PEM</b> form is the default format: it consists of the <b>DER</b> format base64 encoded with additional header and footer lines.</p>

</dd>
<dt id="outform-DER-PEM"><b>-outform DER|PEM</b></dt>
<dd>

<p>This specifies the output format, the options have the same meaning and default as the <b>-inform</b> option.</p>

</dd>
<dt id="in-filename"><b>-in filename</b></dt>
<dd>

<p>This specifies the input filename to read a request from or standard input if this option is not specified. A request is only read if the creation options (<b>-new</b> and <b>-newkey</b>) are not specified.</p>

</dd>
<dt id="sigopt-nm:v"><b>-sigopt nm:v</b></dt>
<dd>

<p>Pass options to the signature algorithm during sign or verify operations. Names and values of these options are algorithm-specific.</p>

</dd>
<dt id="passin-arg"><b>-passin arg</b></dt>
<dd>

<p>The input file password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="out-filename"><b>-out filename</b></dt>
<dd>

<p>This specifies the output filename to write to or standard output by default.</p>

</dd>
<dt id="passout-arg"><b>-passout arg</b></dt>
<dd>

<p>The output file password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>Prints out the certificate request in text form.</p>

</dd>
<dt id="subject"><b>-subject</b></dt>
<dd>

<p>Prints out the request subject (or certificate subject if <b>-x509</b> is specified)</p>

</dd>
<dt id="pubkey"><b>-pubkey</b></dt>
<dd>

<p>Outputs the public key.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>This option prevents output of the encoded version of the request.</p>

</dd>
<dt id="modulus"><b>-modulus</b></dt>
<dd>

<p>This option prints out the value of the modulus of the public key contained in the request.</p>

</dd>
<dt id="verify"><b>-verify</b></dt>
<dd>

<p>Verifies the signature on the request.</p>

</dd>
<dt id="new"><b>-new</b></dt>
<dd>

<p>This option generates a new certificate request. It will prompt the user for the relevant field values. The actual fields prompted for and their maximum and minimum sizes are specified in the configuration file and any requested extensions.</p>

<p>If the <b>-key</b> option is not used it will generate a new RSA private key using information specified in the configuration file.</p>

</dd>
<dt id="rand-file"><b>-rand file...</b></dt>
<dd>

<p>A file or files containing random data used to seed the random number generator. Multiple files can be specified separated by an OS-dependent character. The separator is <b>;</b> for MS-Windows, <b>,</b> for OpenVMS, and <b>:</b> for all others.</p>

</dd>
<dt id="writerand-file">[<b>-writerand file</b>]</dt>
<dd>

<p>Writes random data to the specified <i>file</i> upon exit. This can be used with a subsequent <b>-rand</b> flag.</p>

</dd>
<dt id="newkey-arg"><b>-newkey arg</b></dt>
<dd>

<p>This option creates a new certificate request and a new private key. The argument takes one of several forms. <b>rsa:nbits</b>, where <b>nbits</b> is the number of bits, generates an RSA key <b>nbits</b> in size. If <b>nbits</b> is omitted, i.e. <b>-newkey rsa</b> specified, the default key size, specified in the configuration file is used.</p>

<p>All other algorithms support the <b>-newkey alg:file</b> form, where file may be an algorithm parameter file, created by the <b>genpkey -genparam</b> command or and X.509 certificate for a key with appropriate algorithm.</p>

<p><b>param:file</b> generates a key using the parameter file or certificate <b>file</b>, the algorithm is determined by the parameters. <b>algname:file</b> use algorithm <b>algname</b> and parameter file <b>file</b>: the two algorithms must match or an error occurs. <b>algname</b> just uses algorithm <b>algname</b>, and parameters, if necessary should be specified via <b>-pkeyopt</b> parameter.</p>

<p><b>dsa:filename</b> generates a DSA key using the parameters in the file <b>filename</b>. <b>ec:filename</b> generates EC key (usable both with ECDSA or ECDH algorithms), <b>gost2001:filename</b> generates GOST R 34.10-2001 key (requires <b>ccgost</b> engine configured in the configuration file). If just <b>gost2001</b> is specified a parameter set should be specified by <b>-pkeyopt paramset:X</b></p>

</dd>
<dt id="pkeyopt-opt:value"><b>-pkeyopt opt:value</b></dt>
<dd>

<p>Set the public key algorithm option <b>opt</b> to <b>value</b>. The precise set of options supported depends on the public key algorithm used and its implementation. See <b>KEY GENERATION OPTIONS</b> in the <b>genpkey</b> manual page for more details.</p>

</dd>
<dt id="key-filename"><b>-key filename</b></dt>
<dd>

<p>This specifies the file to read the private key from. It also accepts PKCS#8 format private keys for PEM format files.</p>

</dd>
<dt id="keyform-PEM-DER"><b>-keyform PEM|DER</b></dt>
<dd>

<p>The format of the private key file specified in the <b>-key</b> argument. PEM is the default.</p>

</dd>
<dt id="keyout-filename"><b>-keyout filename</b></dt>
<dd>

<p>This gives the filename to write the newly created private key to. If this option is not specified then the filename present in the configuration file is used.</p>

</dd>
<dt id="nodes"><b>-nodes</b></dt>
<dd>

<p>If this option is specified then if a private key is created it will not be encrypted.</p>

</dd>
<dt id="digest"><b>-<i>digest</i></b></dt>
<dd>

<p>This specifies the message digest to sign the request. Any digest supported by the OpenSSL <b>dgst</b> command can be used. This overrides the digest algorithm specified in the configuration file.</p>

<p>Some public key algorithms may override this choice. For instance, DSA signatures always use SHA1, GOST R 34.10 signatures always use GOST R 34.11-94 (<b>-md_gost94</b>), Ed25519 and Ed448 never use any digest.</p>

</dd>
<dt id="config-filename"><b>-config filename</b></dt>
<dd>

<p>This allows an alternative configuration file to be specified. Optional; for a description of the default value, see <a href="../man1/openssl.html">&quot;COMMAND SUMMARY&quot; in openssl(1)</a>.</p>

</dd>
<dt id="subj-arg"><b>-subj arg</b></dt>
<dd>

<p>Sets subject name for new request or supersedes the subject name when processing a request. The arg must be formatted as <i>/type0=value0/type1=value1/type2=...</i>. Keyword characters may be escaped by \ (backslash), and whitespace is retained. Empty values are permitted, but the corresponding type will not be included in the request.</p>

</dd>
<dt id="multivalue-rdn"><b>-multivalue-rdn</b></dt>
<dd>

<p>This option causes the -subj argument to be interpreted with full support for multivalued RDNs. Example:</p>

<p><i>/DC=org/DC=OpenSSL/DC=users/UID=123456+CN=John Doe</i></p>

<p>If -multi-rdn is not used then the UID value is <i>123456+CN=John Doe</i>.</p>

</dd>
<dt id="x509"><b>-x509</b></dt>
<dd>

<p>This option outputs a self signed certificate instead of a certificate request. This is typically used to generate a test certificate or a self signed root CA. The extensions added to the certificate (if any) are specified in the configuration file. Unless specified using the <b>set_serial</b> option, a large random number will be used for the serial number.</p>

<p>If existing request is specified with the <b>-in</b> option, it is converted to the self signed certificate otherwise new request is created.</p>

</dd>
<dt id="days-n"><b>-days n</b></dt>
<dd>

<p>When the <b>-x509</b> option is being used this specifies the number of days to certify the certificate for, otherwise it is ignored. <b>n</b> should be a positive integer. The default is 30 days.</p>

</dd>
<dt id="set_serial-n"><b>-set_serial n</b></dt>
<dd>

<p>Serial number to use when outputting a self signed certificate. This may be specified as a decimal value or a hex value if preceded by <b>0x</b>.</p>

</dd>
<dt id="addext-ext"><b>-addext ext</b></dt>
<dd>

<p>Add a specific extension to the certificate (if the <b>-x509</b> option is present) or certificate request. The argument must have the form of a key=value pair as it would appear in a config file.</p>

<p>This option can be given multiple times.</p>

</dd>
<dt id="extensions-section"><b>-extensions section</b></dt>
<dd>

</dd>
<dt id="reqexts-section"><b>-reqexts section</b></dt>
<dd>

<p>These options specify alternative sections to include certificate extensions (if the <b>-x509</b> option is present) or certificate request extensions. This allows several different sections to be used in the same configuration file to specify requests for a variety of purposes.</p>

</dd>
<dt id="precert"><b>-precert</b></dt>
<dd>

<p>A poison extension will be added to the certificate, making it a &quot;pre-certificate&quot; (see RFC6962). This can be submitted to Certificate Transparency logs in order to obtain signed certificate timestamps (SCTs). These SCTs can then be embedded into the pre-certificate as an extension, before removing the poison and signing the certificate.</p>

<p>This implies the <b>-new</b> flag.</p>

</dd>
<dt id="utf8"><b>-utf8</b></dt>
<dd>

<p>This option causes field values to be interpreted as UTF8 strings, by default they are interpreted as ASCII. This means that the field values, whether prompted from a terminal or obtained from a configuration file, must be valid UTF8 strings.</p>

</dd>
<dt id="nameopt-option"><b>-nameopt option</b></dt>
<dd>

<p>Option which determines how the subject or issuer names are displayed. The <b>option</b> argument can be a single option or multiple options separated by commas. Alternatively the <b>-nameopt</b> switch may be used more than once to set multiple options. See the <a href="../man1/x509.html">x509(1)</a> manual page for details.</p>

</dd>
<dt id="reqopt"><b>-reqopt</b></dt>
<dd>

<p>Customise the output format used with <b>-text</b>. The <b>option</b> argument can be a single option or multiple options separated by commas.</p>

<p>See discussion of the <b>-certopt</b> parameter in the <a href="../man1/x509.html">x509(1)</a> command.</p>

</dd>
<dt id="newhdr"><b>-newhdr</b></dt>
<dd>

<p>Adds the word <b>NEW</b> to the PEM file header and footer lines on the outputted request. Some software (Netscape certificate server) and some CAs need this.</p>

</dd>
<dt id="batch"><b>-batch</b></dt>
<dd>

<p>Non-interactive mode.</p>

</dd>
<dt id="verbose"><b>-verbose</b></dt>
<dd>

<p>Print extra details about the operations being performed.</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Specifying an engine (by its unique <b>id</b> string) will cause <b>req</b> to attempt to obtain a functional reference to the specified engine, thus initialising it if needed. The engine will then be set as the default for all available algorithms.</p>

</dd>
<dt id="keygen_engine-id"><b>-keygen_engine id</b></dt>
<dd>

<p>Specifies an engine (by its unique <b>id</b> string) which would be used for key generation operations.</p>

</dd>
</dl>

<h1 id="CONFIGURATION-FILE-FORMAT">CONFIGURATION FILE FORMAT</h1>

<p>The configuration options are specified in the <b>req</b> section of the configuration file. As with all configuration files if no value is specified in the specific section (i.e. <b>req</b>) then the initial unnamed or <b>default</b> section is searched too.</p>

<p>The options available are described in detail below.</p>

<dl>

<dt id="input_password-output_password"><b>input_password output_password</b></dt>
<dd>

<p>The passwords for the input private key file (if present) and the output private key file (if one will be created). The command line options <b>passin</b> and <b>passout</b> override the configuration file values.</p>

</dd>
<dt id="default_bits"><b>default_bits</b></dt>
<dd>

<p>Specifies the default key size in bits.</p>

<p>This option is used in conjunction with the <b>-new</b> option to generate a new key. It can be overridden by specifying an explicit key size in the <b>-newkey</b> option. The smallest accepted key size is 512 bits. If no key size is specified then 2048 bits is used.</p>

</dd>
<dt id="default_keyfile"><b>default_keyfile</b></dt>
<dd>

<p>This is the default filename to write a private key to. If not specified the key is written to standard output. This can be overridden by the <b>-keyout</b> option.</p>

</dd>
<dt id="oid_file"><b>oid_file</b></dt>
<dd>

<p>This specifies a file containing additional <b>OBJECT IDENTIFIERS</b>. Each line of the file should consist of the numerical form of the object identifier followed by white space then the short name followed by white space and finally the long name.</p>

</dd>
<dt id="oid_section"><b>oid_section</b></dt>
<dd>

<p>This specifies a section in the configuration file containing extra object identifiers. Each line should consist of the short name of the object identifier followed by <b>=</b> and the numerical form. The short and long names are the same when this option is used.</p>

</dd>
<dt id="RANDFILE"><b>RANDFILE</b></dt>
<dd>

<p>At startup the specified file is loaded into the random number generator, and at exit 256 bytes will be written to it. It is used for private key generation.</p>

</dd>
<dt id="encrypt_key"><b>encrypt_key</b></dt>
<dd>

<p>If this is set to <b>no</b> then if a private key is generated it is <b>not</b> encrypted. This is equivalent to the <b>-nodes</b> command line option. For compatibility <b>encrypt_rsa_key</b> is an equivalent option.</p>

</dd>
<dt id="default_md"><b>default_md</b></dt>
<dd>

<p>This option specifies the digest algorithm to use. Any digest supported by the OpenSSL <b>dgst</b> command can be used. This option can be overridden on the command line. Certain signing algorithms (i.e. Ed25519 and Ed448) will ignore any digest that has been set.</p>

</dd>
<dt id="string_mask"><b>string_mask</b></dt>
<dd>

<p>This option masks out the use of certain string types in certain fields. Most users will not need to change this option.</p>

<p>It can be set to several values <b>default</b> which is also the default option uses PrintableStrings, T61Strings and BMPStrings if the <b>pkix</b> value is used then only PrintableStrings and BMPStrings will be used. This follows the PKIX recommendation in RFC2459. If the <b>utf8only</b> option is used then only UTF8Strings will be used: this is the PKIX recommendation in RFC2459 after 2003. Finally the <b>nombstr</b> option just uses PrintableStrings and T61Strings: certain software has problems with BMPStrings and UTF8Strings: in particular Netscape.</p>

</dd>
<dt id="req_extensions"><b>req_extensions</b></dt>
<dd>

<p>This specifies the configuration file section containing a list of extensions to add to the certificate request. It can be overridden by the <b>-reqexts</b> command line switch. See the <a href="../man5/x509v3_config.html">x509v3_config(5)</a> manual page for details of the extension section format.</p>

</dd>
<dt id="x509_extensions"><b>x509_extensions</b></dt>
<dd>

<p>This specifies the configuration file section containing a list of extensions to add to certificate generated when the <b>-x509</b> switch is used. It can be overridden by the <b>-extensions</b> command line switch.</p>

</dd>
<dt id="prompt"><b>prompt</b></dt>
<dd>

<p>If set to the value <b>no</b> this disables prompting of certificate fields and just takes values from the config file directly. It also changes the expected format of the <b>distinguished_name</b> and <b>attributes</b> sections.</p>

</dd>
<dt id="utf81"><b>utf8</b></dt>
<dd>

<p>If set to the value <b>yes</b> then field values to be interpreted as UTF8 strings, by default they are interpreted as ASCII. This means that the field values, whether prompted from a terminal or obtained from a configuration file, must be valid UTF8 strings.</p>

</dd>
<dt id="attributes"><b>attributes</b></dt>
<dd>

<p>This specifies the section containing any request attributes: its format is the same as <b>distinguished_name</b>. Typically these may contain the challengePassword or unstructuredName types. They are currently ignored by OpenSSL&#39;s request signing utilities but some CAs might want them.</p>

</dd>
<dt id="distinguished_name"><b>distinguished_name</b></dt>
<dd>

<p>This specifies the section containing the distinguished name fields to prompt for when generating a certificate or certificate request. The format is described in the next section.</p>

</dd>
</dl>

<h1 id="DISTINGUISHED-NAME-AND-ATTRIBUTE-SECTION-FORMAT">DISTINGUISHED NAME AND ATTRIBUTE SECTION FORMAT</h1>

<p>There are two separate formats for the distinguished name and attribute sections. If the <b>prompt</b> option is set to <b>no</b> then these sections just consist of field names and values: for example,</p>

<pre><code>CN=My Name
OU=My Organization
emailAddress=<EMAIL></code></pre>

<p>This allows external programs (e.g. GUI based) to generate a template file with all the field names and values and just pass it to <b>req</b>. An example of this kind of configuration file is contained in the <b>EXAMPLES</b> section.</p>

<p>Alternatively if the <b>prompt</b> option is absent or not set to <b>no</b> then the file contains field prompting information. It consists of lines of the form:</p>

<pre><code>fieldName=&quot;prompt&quot;
fieldName_default=&quot;default field value&quot;
fieldName_min= 2
fieldName_max= 4</code></pre>

<p>&quot;fieldName&quot; is the field name being used, for example commonName (or CN). The &quot;prompt&quot; string is used to ask the user to enter the relevant details. If the user enters nothing then the default value is used if no default value is present then the field is omitted. A field can still be omitted if a default value is present if the user just enters the &#39;.&#39; character.</p>

<p>The number of characters entered must be between the fieldName_min and fieldName_max limits: there may be additional restrictions based on the field being used (for example countryName can only ever be two characters long and must fit in a PrintableString).</p>

<p>Some fields (such as organizationName) can be used more than once in a DN. This presents a problem because configuration files will not recognize the same name occurring twice. To avoid this problem if the fieldName contains some characters followed by a full stop they will be ignored. So for example a second organizationName can be input by calling it &quot;1.organizationName&quot;.</p>

<p>The actual permitted field names are any object identifier short or long names. These are compiled into OpenSSL and include the usual values such as commonName, countryName, localityName, organizationName, organizationalUnitName, stateOrProvinceName. Additionally emailAddress is included as well as name, surname, givenName, initials, and dnQualifier.</p>

<p>Additional object identifiers can be defined with the <b>oid_file</b> or <b>oid_section</b> options in the configuration file. Any additional fields will be treated as though they were a DirectoryString.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Examine and verify certificate request:</p>

<pre><code>openssl req -in req.pem -text -verify -noout</code></pre>

<p>Create a private key and then generate a certificate request from it:</p>

<pre><code>openssl genrsa -out key.pem 2048
openssl req -new -key key.pem -out req.pem</code></pre>

<p>The same but just using req:</p>

<pre><code>openssl req -newkey rsa:2048 -keyout key.pem -out req.pem</code></pre>

<p>Generate a self signed root certificate:</p>

<pre><code>openssl req -x509 -newkey rsa:2048 -keyout key.pem -out req.pem</code></pre>

<p>Example of a file pointed to by the <b>oid_file</b> option:</p>

<pre><code>*******        shortName       A longer Name
1.2.3.6        otherName       Other longer Name</code></pre>

<p>Example of a section pointed to by <b>oid_section</b> making use of variable expansion:</p>

<pre><code>testoid1=1.2.3.5
testoid2=${testoid1}.6</code></pre>

<p>Sample configuration file prompting for field values:</p>

<pre><code>[ req ]
default_bits           = 2048
default_keyfile        = privkey.pem
distinguished_name     = req_distinguished_name
attributes             = req_attributes
req_extensions         = v3_ca

dirstring_type = nobmp

[ req_distinguished_name ]
countryName                    = Country Name (2 letter code)
countryName_default            = AU
countryName_min                = 2
countryName_max                = 2

localityName                   = Locality Name (eg, city)

organizationalUnitName         = Organizational Unit Name (eg, section)

commonName                     = Common Name (eg, YOUR name)
commonName_max                 = 64

emailAddress                   = Email Address
emailAddress_max               = 40

[ req_attributes ]
challengePassword              = A challenge password
challengePassword_min          = 4
challengePassword_max          = 20

[ v3_ca ]

subjectKeyIdentifier=hash
authorityKeyIdentifier=keyid:always,issuer:always
basicConstraints = critical, CA:true</code></pre>

<p>Sample configuration containing all field values:</p>

<pre><code>RANDFILE               = $ENV::HOME/.rnd

[ req ]
default_bits           = 2048
default_keyfile        = keyfile.pem
distinguished_name     = req_distinguished_name
attributes             = req_attributes
prompt                 = no
output_password        = mypass

[ req_distinguished_name ]
C                      = GB
ST                     = Test State or Province
L                      = Test Locality
O                      = Organization Name
OU                     = Organizational Unit Name
CN                     = Common Name
emailAddress           = <EMAIL>

[ req_attributes ]
challengePassword              = A challenge password</code></pre>

<p>Example of giving the most common attributes (subject and extensions) on the command line:</p>

<pre><code>openssl req -new -subj &quot;/C=GB/CN=foo&quot; \
                 -addext &quot;subjectAltName = DNS:foo.co.uk&quot; \
                 -addext &quot;certificatePolicies = *******&quot; \
                 -newkey rsa:2048 -keyout key.pem -out req.pem</code></pre>

<h1 id="NOTES">NOTES</h1>

<p>The header and footer lines in the <b>PEM</b> format are normally:</p>

<pre><code>-----BEGIN CERTIFICATE REQUEST-----
-----END CERTIFICATE REQUEST-----</code></pre>

<p>some software (some versions of Netscape certificate server) instead needs:</p>

<pre><code>-----BEGIN NEW CERTIFICATE REQUEST-----
-----END NEW CERTIFICATE REQUEST-----</code></pre>

<p>which is produced with the <b>-newhdr</b> option but is otherwise compatible. Either form is accepted transparently on input.</p>

<p>The certificate requests generated by <b>Xenroll</b> with MSIE have extensions added. It includes the <b>keyUsage</b> extension which determines the type of key (signature only or general purpose) and any additional OIDs entered by the script in an extendedKeyUsage extension.</p>

<h1 id="DIAGNOSTICS">DIAGNOSTICS</h1>

<p>The following messages are frequently asked about:</p>

<pre><code>Using configuration from /some/path/openssl.cnf
Unable to load config info</code></pre>

<p>This is followed some time later by...</p>

<pre><code>unable to find &#39;distinguished_name&#39; in config
problems making Certificate Request</code></pre>

<p>The first error message is the clue: it can&#39;t find the configuration file! Certain operations (like examining a certificate request) don&#39;t need a configuration file so its use isn&#39;t enforced. Generation of certificates or requests however does need a configuration file. This could be regarded as a bug.</p>

<p>Another puzzling message is this:</p>

<pre><code>Attributes:
    a0:00</code></pre>

<p>this is displayed when no attributes are present and the request includes the correct empty <b>SET OF</b> structure (the DER encoding of which is 0xa0 0x00). If you just see:</p>

<pre><code>Attributes:</code></pre>

<p>then the <b>SET OF</b> is missing and the encoding is technically invalid (but it is tolerated). See the description of the command line option <b>-asn1-kludge</b> for more information.</p>

<h1 id="BUGS">BUGS</h1>

<p>OpenSSL&#39;s handling of T61Strings (aka TeletexStrings) is broken: it effectively treats them as ISO-8859-1 (Latin 1), Netscape and MSIE have similar behaviour. This can cause problems if you need characters that aren&#39;t available in PrintableStrings and you don&#39;t want to or can&#39;t use BMPStrings.</p>

<p>As a consequence of the T61String handling the only correct way to represent accented characters in OpenSSL is to use a BMPString: unfortunately Netscape currently chokes on these. If you have to use accented characters with Netscape and MSIE then you currently need to use the invalid T61String form.</p>

<p>The current prompting is not very friendly. It doesn&#39;t allow you to confirm what you&#39;ve just entered. Other things like extensions in certificate requests are statically defined in the configuration file. Some of these: like an email address in subjectAltName should be input by the user.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/x509.html">x509(1)</a>, <a href="../man1/ca.html">ca(1)</a>, <a href="../man1/genrsa.html">genrsa(1)</a>, <a href="../man1/gendsa.html">gendsa(1)</a>, <a href="../man5/config.html">config(5)</a>, <a href="../man5/x509v3_config.html">x509v3_config(5)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


