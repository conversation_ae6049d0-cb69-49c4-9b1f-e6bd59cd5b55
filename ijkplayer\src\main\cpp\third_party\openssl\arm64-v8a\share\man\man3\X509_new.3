.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_NEW 3"
.TH X509_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_chain_up_ref,
X509_new, X509_free, X509_up_ref \- X509 certificate ASN1 allocation functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& X509 *X509_new(void);
\& void X509_free(X509 *a);
\& int X509_up_ref(X509 *a);
\& STACK_OF(X509) *X509_chain_up_ref(STACK_OF(X509) *x);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The X509 ASN1 allocation routines, allocate and free an
X509 structure, which represents an X509 certificate.
.PP
\&\fBX509_new()\fR allocates and initializes a X509 structure with reference count
\&\fB1\fR.
.PP
\&\fBX509_free()\fR decrements the reference count of \fBX509\fR structure \fBa\fR and
frees it up if the reference count is zero. If \fBa\fR is NULL nothing is done.
.PP
\&\fBX509_up_ref()\fR increments the reference count of \fBa\fR.
.PP
\&\fBX509_chain_up_ref()\fR increases the reference count of all certificates in
chain \fBx\fR and returns a copy of the stack.
.SH NOTES
.IX Header "NOTES"
The function \fBX509_up_ref()\fR if useful if a certificate structure is being
used by several different operations each of which will free it up after
use: this avoids the need to duplicate the entire certificate structure.
.PP
The function \fBX509_chain_up_ref()\fR doesn't just up the reference count of
each certificate it also returns a copy of the stack, using \fBsk_X509_dup()\fR,
but it serves a similar purpose: the returned chain persists after the
original has been freed.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If the allocation fails, \fBX509_new()\fR returns \fBNULL\fR and sets an error
code that can be obtained by \fBERR_get_error\fR\|(3).
Otherwise it returns a pointer to the newly allocated structure.
.PP
\&\fBX509_up_ref()\fR returns 1 for success and 0 for failure.
.PP
\&\fBX509_chain_up_ref()\fR returns a copy of the stack or \fBNULL\fR if an error
occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3),
\&\fBERR_get_error\fR\|(3),
\&\fBX509_CRL_get0_by_serial\fR\|(3),
\&\fBX509_get0_signature\fR\|(3),
\&\fBX509_get_ext_d2i\fR\|(3),
\&\fBX509_get_extension_flags\fR\|(3),
\&\fBX509_get_pubkey\fR\|(3),
\&\fBX509_get_subject_name\fR\|(3),
\&\fBX509_get_version\fR\|(3),
\&\fBX509_NAME_add_entry_by_txt\fR\|(3),
\&\fBX509_NAME_ENTRY_get_object\fR\|(3),
\&\fBX509_NAME_get_index_by_NID\fR\|(3),
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBX509_sign\fR\|(3),
\&\fBX509V3_get_d2i\fR\|(3),
\&\fBX509_verify_cert\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
