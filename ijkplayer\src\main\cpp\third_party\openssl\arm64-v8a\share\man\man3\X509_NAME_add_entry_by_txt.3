.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_NAME_ADD_ENTRY_BY_TXT 3"
.TH X509_NAME_ADD_ENTRY_BY_TXT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_NAME_add_entry_by_txt, X509_NAME_add_entry_by_OBJ, X509_NAME_add_entry_by_NID,
X509_NAME_add_entry, X509_NAME_delete_entry \- X509_NAME modification functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_NAME_add_entry_by_txt(X509_NAME *name, const char *field, int type,
\&                                const unsigned char *bytes, int len, int loc, int set);
\&
\& int X509_NAME_add_entry_by_OBJ(X509_NAME *name, const ASN1_OBJECT *obj, int type,
\&                                const unsigned char *bytes, int len, int loc, int set);
\&
\& int X509_NAME_add_entry_by_NID(X509_NAME *name, int nid, int type,
\&                                const unsigned char *bytes, int len, int loc, int set);
\&
\& int X509_NAME_add_entry(X509_NAME *name, const X509_NAME_ENTRY *ne, int loc, int set);
\&
\& X509_NAME_ENTRY *X509_NAME_delete_entry(X509_NAME *name, int loc);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_NAME_add_entry_by_txt()\fR, \fBX509_NAME_add_entry_by_OBJ()\fR and
\&\fBX509_NAME_add_entry_by_NID()\fR add a field whose name is defined
by a string \fBfield\fR, an object \fBobj\fR or a NID \fBnid\fR respectively.
The field value to be added is in \fBbytes\fR of length \fBlen\fR. If
\&\fBlen\fR is \-1 then the field length is calculated internally using
strlen(bytes).
.PP
The type of field is determined by \fBtype\fR which can either be a
definition of the type of \fBbytes\fR (such as \fBMBSTRING_ASC\fR) or a
standard ASN1 type (such as \fBV_ASN1_IA5STRING\fR). The new entry is
added to a position determined by \fBloc\fR and \fBset\fR.
.PP
\&\fBX509_NAME_add_entry()\fR adds a copy of \fBX509_NAME_ENTRY\fR structure \fBne\fR
to \fBname\fR. The new entry is added to a position determined by \fBloc\fR
and \fBset\fR. Since a copy of \fBne\fR is added \fBne\fR must be freed up after
the call.
.PP
\&\fBX509_NAME_delete_entry()\fR deletes an entry from \fBname\fR at position
\&\fBloc\fR. The deleted entry is returned and must be freed up.
.SH NOTES
.IX Header "NOTES"
The use of string types such as \fBMBSTRING_ASC\fR or \fBMBSTRING_UTF8\fR
is strongly recommended for the \fBtype\fR parameter. This allows the
internal code to correctly determine the type of the field and to
apply length checks according to the relevant standards. This is
done using \fBASN1_STRING_set_by_NID()\fR.
.PP
If instead an ASN1 type is used no checks are performed and the
supplied data in \fBbytes\fR is used directly.
.PP
In \fBX509_NAME_add_entry_by_txt()\fR the \fBfield\fR string represents
the field name using OBJ_txt2obj(field, 0).
.PP
The \fBloc\fR and \fBset\fR parameters determine where a new entry should
be added. For almost all applications \fBloc\fR can be set to \-1 and \fBset\fR
to 0. This adds a new entry to the end of \fBname\fR as a single valued
RelativeDistinguishedName (RDN).
.PP
\&\fBloc\fR actually determines the index where the new entry is inserted:
if it is \-1 it is appended.
.PP
\&\fBset\fR determines how the new type is added. If it is zero a
new RDN is created.
.PP
If \fBset\fR is \-1 or 1 it is added to the previous or next RDN
structure respectively. This will then be a multivalued RDN:
since multivalues RDNs are very seldom used \fBset\fR is almost
always set to zero.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_NAME_add_entry_by_txt()\fR, \fBX509_NAME_add_entry_by_OBJ()\fR,
\&\fBX509_NAME_add_entry_by_NID()\fR and \fBX509_NAME_add_entry()\fR return 1 for
success of 0 if an error occurred.
.PP
\&\fBX509_NAME_delete_entry()\fR returns either the deleted \fBX509_NAME_ENTRY\fR
structure of \fBNULL\fR if an error occurred.
.SH EXAMPLES
.IX Header "EXAMPLES"
Create an \fBX509_NAME\fR structure:
.PP
"C=UK, O=Disorganized Organization, CN=Joe Bloggs"
.PP
.Vb 1
\& X509_NAME *nm;
\&
\& nm = X509_NAME_new();
\& if (nm == NULL)
\&     /* Some error */
\& if (!X509_NAME_add_entry_by_txt(nm, "C", MBSTRING_ASC,
\&                                 "UK", \-1, \-1, 0))
\&     /* Error */
\& if (!X509_NAME_add_entry_by_txt(nm, "O", MBSTRING_ASC,
\&                                 "Disorganized Organization", \-1, \-1, 0))
\&     /* Error */
\& if (!X509_NAME_add_entry_by_txt(nm, "CN", MBSTRING_ASC,
\&                                 "Joe Bloggs", \-1, \-1, 0))
\&     /* Error */
.Ve
.SH BUGS
.IX Header "BUGS"
\&\fBtype\fR can still be set to \fBV_ASN1_APP_CHOOSE\fR to use a
different algorithm to determine field types. Since this form does
not understand multicharacter types, performs no length checks and
can result in invalid field types its use is strongly discouraged.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBd2i_X509_NAME\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
