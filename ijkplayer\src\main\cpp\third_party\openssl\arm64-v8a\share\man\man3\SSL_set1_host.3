.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SET1_HOST 3"
.TH SSL_SET1_HOST 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_set1_host, SSL_add1_host, SSL_set_hostflags, SSL_get0_peername \-
SSL server verification parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_set1_host(SSL *s, const char *hostname);
\& int SSL_add1_host(SSL *s, const char *hostname);
\& void SSL_set_hostflags(SSL *s, unsigned int flags);
\& const char *SSL_get0_peername(SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions configure server hostname checks in the SSL client.
.PP
\&\fBSSL_set1_host()\fR sets the expected DNS hostname to \fBname\fR clearing
any previously specified hostname or names.  If \fBname\fR is NULL,
or the empty string the list of hostnames is cleared, and name
checks are not performed on the peer certificate.  When a nonempty
\&\fBname\fR is specified, certificate verification automatically checks
the peer hostname via \fBX509_check_host\fR\|(3) with \fBflags\fR as specified
via \fBSSL_set_hostflags()\fR.  Clients that enable DANE TLSA authentication
via \fBSSL_dane_enable\fR\|(3) should leave it to that function to set
the primary reference identifier of the peer, and should not call
\&\fBSSL_set1_host()\fR.
.PP
\&\fBSSL_add1_host()\fR adds \fBname\fR as an additional reference identifier
that can match the peer's certificate.  Any previous names set via
\&\fBSSL_set1_host()\fR or \fBSSL_add1_host()\fR are retained, no change is made
if \fBname\fR is NULL or empty.  When multiple names are configured,
the peer is considered verified when any name matches.  This function
is required for DANE TLSA in the presence of service name indirection
via CNAME, MX or SRV records as specified in RFC7671, RFC7672 or
RFC7673.
.PP
\&\fBSSL_set_hostflags()\fR sets the \fBflags\fR that will be passed to
\&\fBX509_check_host\fR\|(3) when name checks are applicable, by default
the \fBflags\fR value is 0.  See \fBX509_check_host\fR\|(3) for the list
of available flags and their meaning.
.PP
\&\fBSSL_get0_peername()\fR returns the DNS hostname or subject CommonName
from the peer certificate that matched one of the reference
identifiers.  When wildcard matching is not disabled, the name
matched in the peer certificate may be a wildcard name.  When one
of the reference identifiers configured via \fBSSL_set1_host()\fR or
\&\fBSSL_add1_host()\fR starts with ".", which indicates a parent domain prefix
rather than a fixed name, the matched peer name may be a sub-domain
of the reference identifier.  The returned string is allocated by
the library and is no longer valid once the associated \fBssl\fR handle
is cleared or freed, or a renegotiation takes place.  Applications
must not free the return value.
.PP
SSL clients are advised to use these functions in preference to
explicitly calling \fBX509_check_host\fR\|(3).  Hostname checks may be out
of scope with the RFC7671 \fBDANE\-EE\fR\|(3) certificate usage, and the
internal check will be suppressed as appropriate when DANE is
enabled.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_set1_host()\fR and \fBSSL_add1_host()\fR return 1 for success and 0 for
failure.
.PP
\&\fBSSL_get0_peername()\fR returns NULL if peername verification is not
applicable (as with RFC7671 \fBDANE\-EE\fR\|(3)), or no trusted peername was
matched.  Otherwise, it returns the matched peername.  To determine
whether verification succeeded call \fBSSL_get_verify_result\fR\|(3).
.SH EXAMPLES
.IX Header "EXAMPLES"
Suppose "smtp.example.com" is the MX host of the domain "example.com".
The calls below will arrange to match either the MX hostname or the
destination domain name in the SMTP server certificate.  Wildcards
are supported, but must match the entire label.  The actual name
matched in the certificate (which might be a wildcard) is retrieved,
and must be copied by the application if it is to be retained beyond
the lifetime of the SSL connection.
.PP
.Vb 5
\& SSL_set_hostflags(ssl, X509_CHECK_FLAG_NO_PARTIAL_WILDCARDS);
\& if (!SSL_set1_host(ssl, "smtp.example.com"))
\&     /* error */
\& if (!SSL_add1_host(ssl, "example.com"))
\&     /* error */
\&
\& /* XXX: Perform SSL_connect() handshake and handle errors here */
\&
\& if (SSL_get_verify_result(ssl) == X509_V_OK) {
\&     const char *peername = SSL_get0_peername(ssl);
\&
\&     if (peername != NULL)
\&         /* Name checks were in scope and matched the peername */
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_check_host\fR\|(3),
\&\fBSSL_get_verify_result\fR\|(3).
\&\fBSSL_dane_enable\fR\|(3).
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
