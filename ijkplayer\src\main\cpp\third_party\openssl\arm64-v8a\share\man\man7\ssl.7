.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL 7"
.TH SSL 7 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ssl \- OpenSSL SSL/TLS library
.SH SYNOPSIS
.IX Header "SYNOPSIS"
See the individual manual pages for details.
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The OpenSSL \fBssl\fR library implements the Secure Sockets Layer (SSL v2/v3) and
Transport Layer Security (TLS v1) protocols. It provides a rich API which is
documented here.
.PP
An \fBSSL_CTX\fR object is created as a framework to establish
TLS/SSL enabled connections (see \fBSSL_CTX_new\fR\|(3)).
Various options regarding certificates, algorithms etc. can be set
in this object.
.PP
When a network connection has been created, it can be assigned to an
\&\fBSSL\fR object. After the \fBSSL\fR object has been created using
\&\fBSSL_new\fR\|(3), \fBSSL_set_fd\fR\|(3) or
\&\fBSSL_set_bio\fR\|(3) can be used to associate the network
connection with the object.
.PP
When the TLS/SSL handshake is performed using
\&\fBSSL_accept\fR\|(3) or \fBSSL_connect\fR\|(3)
respectively.
\&\fBSSL_read_ex\fR\|(3), \fBSSL_read\fR\|(3), \fBSSL_write_ex\fR\|(3) and \fBSSL_write\fR\|(3) are
used to read and write data on the TLS/SSL connection.
\&\fBSSL_shutdown\fR\|(3) can be used to shut down the
TLS/SSL connection.
.SH "DATA STRUCTURES"
.IX Header "DATA STRUCTURES"
Currently the OpenSSL \fBssl\fR library functions deals with the following data
structures:
.IP "\fBSSL_METHOD\fR (SSL Method)" 4
.IX Item "SSL_METHOD (SSL Method)"
This is a dispatch structure describing the internal \fBssl\fR library
methods/functions which implement the various protocol versions (SSLv3
TLSv1, ...). It's needed to create an \fBSSL_CTX\fR.
.IP "\fBSSL_CIPHER\fR (SSL Cipher)" 4
.IX Item "SSL_CIPHER (SSL Cipher)"
This structure holds the algorithm information for a particular cipher which
are a core part of the SSL/TLS protocol. The available ciphers are configured
on a \fBSSL_CTX\fR basis and the actual ones used are then part of the
\&\fBSSL_SESSION\fR.
.IP "\fBSSL_CTX\fR (SSL Context)" 4
.IX Item "SSL_CTX (SSL Context)"
This is the global context structure which is created by a server or client
once per program life-time and which holds mainly default values for the
\&\fBSSL\fR structures which are later created for the connections.
.IP "\fBSSL_SESSION\fR (SSL Session)" 4
.IX Item "SSL_SESSION (SSL Session)"
This is a structure containing the current TLS/SSL session details for a
connection: \fBSSL_CIPHER\fRs, client and server certificates, keys, etc.
.IP "\fBSSL\fR (SSL Connection)" 4
.IX Item "SSL (SSL Connection)"
This is the main SSL/TLS structure which is created by a server or client per
established connection. This actually is the core structure in the SSL API.
At run-time the application usually deals with this structure which has
links to mostly all other structures.
.SH "HEADER FILES"
.IX Header "HEADER FILES"
Currently the OpenSSL \fBssl\fR library provides the following C header files
containing the prototypes for the data structures and functions:
.IP \fBssl.h\fR 4
.IX Item "ssl.h"
This is the common header file for the SSL/TLS API.  Include it into your
program to make the API of the \fBssl\fR library available. It internally
includes both more private SSL headers and headers from the \fBcrypto\fR library.
Whenever you need hard-core details on the internals of the SSL API, look
inside this header file.
.IP \fBssl2.h\fR 4
.IX Item "ssl2.h"
Unused. Present for backwards compatibility only.
.IP \fBssl3.h\fR 4
.IX Item "ssl3.h"
This is the sub header file dealing with the SSLv3 protocol only.
\&\fIUsually you don't have to include it explicitly because
it's already included by ssl.h\fR.
.IP \fBtls1.h\fR 4
.IX Item "tls1.h"
This is the sub header file dealing with the TLSv1 protocol only.
\&\fIUsually you don't have to include it explicitly because
it's already included by ssl.h\fR.
.SH "API FUNCTIONS"
.IX Header "API FUNCTIONS"
Currently the OpenSSL \fBssl\fR library exports 214 API functions.
They are documented in the following:
.SS "Dealing with Protocol Methods"
.IX Subsection "Dealing with Protocol Methods"
Here we document the various API functions which deal with the SSL/TLS
protocol methods defined in \fBSSL_METHOD\fR structures.
.IP "const SSL_METHOD *\fBTLS_method\fR(void);" 4
.IX Item "const SSL_METHOD *TLS_method(void);"
Constructor for the \fIversion-flexible\fR SSL_METHOD structure for clients,
servers or both.
See \fBSSL_CTX_new\fR\|(3) for details.
.IP "const SSL_METHOD *\fBTLS_client_method\fR(void);" 4
.IX Item "const SSL_METHOD *TLS_client_method(void);"
Constructor for the \fIversion-flexible\fR SSL_METHOD structure for clients.
Must be used to support the TLSv1.3 protocol.
.IP "const SSL_METHOD *\fBTLS_server_method\fR(void);" 4
.IX Item "const SSL_METHOD *TLS_server_method(void);"
Constructor for the \fIversion-flexible\fR SSL_METHOD structure for servers.
Must be used to support the TLSv1.3 protocol.
.IP "const SSL_METHOD *\fBTLSv1_2_method\fR(void);" 4
.IX Item "const SSL_METHOD *TLSv1_2_method(void);"
Constructor for the TLSv1.2 SSL_METHOD structure for clients, servers or both.
.IP "const SSL_METHOD *\fBTLSv1_2_client_method\fR(void);" 4
.IX Item "const SSL_METHOD *TLSv1_2_client_method(void);"
Constructor for the TLSv1.2 SSL_METHOD structure for clients.
.IP "const SSL_METHOD *\fBTLSv1_2_server_method\fR(void);" 4
.IX Item "const SSL_METHOD *TLSv1_2_server_method(void);"
Constructor for the TLSv1.2 SSL_METHOD structure for servers.
.IP "const SSL_METHOD *\fBTLSv1_1_method\fR(void);" 4
.IX Item "const SSL_METHOD *TLSv1_1_method(void);"
Constructor for the TLSv1.1 SSL_METHOD structure for clients, servers or both.
.IP "const SSL_METHOD *\fBTLSv1_1_client_method\fR(void);" 4
.IX Item "const SSL_METHOD *TLSv1_1_client_method(void);"
Constructor for the TLSv1.1 SSL_METHOD structure for clients.
.IP "const SSL_METHOD *\fBTLSv1_1_server_method\fR(void);" 4
.IX Item "const SSL_METHOD *TLSv1_1_server_method(void);"
Constructor for the TLSv1.1 SSL_METHOD structure for servers.
.IP "const SSL_METHOD *\fBTLSv1_method\fR(void);" 4
.IX Item "const SSL_METHOD *TLSv1_method(void);"
Constructor for the TLSv1 SSL_METHOD structure for clients, servers or both.
.IP "const SSL_METHOD *\fBTLSv1_client_method\fR(void);" 4
.IX Item "const SSL_METHOD *TLSv1_client_method(void);"
Constructor for the TLSv1 SSL_METHOD structure for clients.
.IP "const SSL_METHOD *\fBTLSv1_server_method\fR(void);" 4
.IX Item "const SSL_METHOD *TLSv1_server_method(void);"
Constructor for the TLSv1 SSL_METHOD structure for servers.
.IP "const SSL_METHOD *\fBSSLv3_method\fR(void);" 4
.IX Item "const SSL_METHOD *SSLv3_method(void);"
Constructor for the SSLv3 SSL_METHOD structure for clients, servers or both.
.IP "const SSL_METHOD *\fBSSLv3_client_method\fR(void);" 4
.IX Item "const SSL_METHOD *SSLv3_client_method(void);"
Constructor for the SSLv3 SSL_METHOD structure for clients.
.IP "const SSL_METHOD *\fBSSLv3_server_method\fR(void);" 4
.IX Item "const SSL_METHOD *SSLv3_server_method(void);"
Constructor for the SSLv3 SSL_METHOD structure for servers.
.SS "Dealing with Ciphers"
.IX Subsection "Dealing with Ciphers"
Here we document the various API functions which deal with the SSL/TLS
ciphers defined in \fBSSL_CIPHER\fR structures.
.IP "char *\fBSSL_CIPHER_description\fR(SSL_CIPHER *cipher, char *buf, int len);" 4
.IX Item "char *SSL_CIPHER_description(SSL_CIPHER *cipher, char *buf, int len);"
Write a string to \fIbuf\fR (with a maximum size of \fIlen\fR) containing a human
readable description of \fIcipher\fR. Returns \fIbuf\fR.
.IP "int \fBSSL_CIPHER_get_bits\fR(SSL_CIPHER *cipher, int *alg_bits);" 4
.IX Item "int SSL_CIPHER_get_bits(SSL_CIPHER *cipher, int *alg_bits);"
Determine the number of bits in \fIcipher\fR. Because of export crippled ciphers
there are two bits: The bits the algorithm supports in general (stored to
\&\fIalg_bits\fR) and the bits which are actually used (the return value).
.IP "const char *\fBSSL_CIPHER_get_name\fR(SSL_CIPHER *cipher);" 4
.IX Item "const char *SSL_CIPHER_get_name(SSL_CIPHER *cipher);"
Return the internal name of \fIcipher\fR as a string. These are the various
strings defined by the \fISSL3_TXT_xxx\fR and \fITLS1_TXT_xxx\fR
definitions in the header files.
.IP "const char *\fBSSL_CIPHER_get_version\fR(SSL_CIPHER *cipher);" 4
.IX Item "const char *SSL_CIPHER_get_version(SSL_CIPHER *cipher);"
Returns a string like "\f(CW\*(C`SSLv3\*(C'\fR" or "\f(CW\*(C`TLSv1.2\*(C'\fR" which indicates the
SSL/TLS protocol version to which \fIcipher\fR belongs (i.e. where it was defined
in the specification the first time).
.SS "Dealing with Protocol Contexts"
.IX Subsection "Dealing with Protocol Contexts"
Here we document the various API functions which deal with the SSL/TLS
protocol context defined in the \fBSSL_CTX\fR structure.
.IP "int \fBSSL_CTX_add_client_CA\fR(SSL_CTX *ctx, X509 *x);" 4
.IX Item "int SSL_CTX_add_client_CA(SSL_CTX *ctx, X509 *x);"
.PD 0
.IP "long \fBSSL_CTX_add_extra_chain_cert\fR(SSL_CTX *ctx, X509 *x509);" 4
.IX Item "long SSL_CTX_add_extra_chain_cert(SSL_CTX *ctx, X509 *x509);"
.IP "int \fBSSL_CTX_add_session\fR(SSL_CTX *ctx, SSL_SESSION *c);" 4
.IX Item "int SSL_CTX_add_session(SSL_CTX *ctx, SSL_SESSION *c);"
.IP "int \fBSSL_CTX_check_private_key\fR(const SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_check_private_key(const SSL_CTX *ctx);"
.IP "long \fBSSL_CTX_ctrl\fR(SSL_CTX *ctx, int cmd, long larg, char *parg);" 4
.IX Item "long SSL_CTX_ctrl(SSL_CTX *ctx, int cmd, long larg, char *parg);"
.IP "void \fBSSL_CTX_flush_sessions\fR(SSL_CTX *s, long t);" 4
.IX Item "void SSL_CTX_flush_sessions(SSL_CTX *s, long t);"
.IP "void \fBSSL_CTX_free\fR(SSL_CTX *a);" 4
.IX Item "void SSL_CTX_free(SSL_CTX *a);"
.IP "char *\fBSSL_CTX_get_app_data\fR(SSL_CTX *ctx);" 4
.IX Item "char *SSL_CTX_get_app_data(SSL_CTX *ctx);"
.IP "X509_STORE *\fBSSL_CTX_get_cert_store\fR(SSL_CTX *ctx);" 4
.IX Item "X509_STORE *SSL_CTX_get_cert_store(SSL_CTX *ctx);"
.IP "STACK *\fBSSL_CTX_get_ciphers\fR(const SSL_CTX *ctx);" 4
.IX Item "STACK *SSL_CTX_get_ciphers(const SSL_CTX *ctx);"
.IP "STACK *\fBSSL_CTX_get_client_CA_list\fR(const SSL_CTX *ctx);" 4
.IX Item "STACK *SSL_CTX_get_client_CA_list(const SSL_CTX *ctx);"
.IP "int (*\fBSSL_CTX_get_client_cert_cb\fR(SSL_CTX *ctx))(SSL *ssl, X509 **x509, EVP_PKEY **pkey);" 4
.IX Item "int (*SSL_CTX_get_client_cert_cb(SSL_CTX *ctx))(SSL *ssl, X509 **x509, EVP_PKEY **pkey);"
.IP "void \fBSSL_CTX_get_default_read_ahead\fR(SSL_CTX *ctx);" 4
.IX Item "void SSL_CTX_get_default_read_ahead(SSL_CTX *ctx);"
.IP "char *\fBSSL_CTX_get_ex_data\fR(const SSL_CTX *s, int idx);" 4
.IX Item "char *SSL_CTX_get_ex_data(const SSL_CTX *s, int idx);"
.IP "int \fBSSL_CTX_get_ex_new_index\fR(long argl, char *argp, int (*new_func);(void), int (*dup_func)(void), void (*free_func)(void))" 4
.IX Item "int SSL_CTX_get_ex_new_index(long argl, char *argp, int (*new_func);(void), int (*dup_func)(void), void (*free_func)(void))"
.IP "void (*\fBSSL_CTX_get_info_callback\fR(SSL_CTX *ctx))(SSL *ssl, int cb, int ret);" 4
.IX Item "void (*SSL_CTX_get_info_callback(SSL_CTX *ctx))(SSL *ssl, int cb, int ret);"
.IP "int \fBSSL_CTX_get_quiet_shutdown\fR(const SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_get_quiet_shutdown(const SSL_CTX *ctx);"
.IP "void \fBSSL_CTX_get_read_ahead\fR(SSL_CTX *ctx);" 4
.IX Item "void SSL_CTX_get_read_ahead(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_get_session_cache_mode\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_get_session_cache_mode(SSL_CTX *ctx);"
.IP "long \fBSSL_CTX_get_timeout\fR(const SSL_CTX *ctx);" 4
.IX Item "long SSL_CTX_get_timeout(const SSL_CTX *ctx);"
.IP "int (*\fBSSL_CTX_get_verify_callback\fR(const SSL_CTX *ctx))(int ok, X509_STORE_CTX *ctx);" 4
.IX Item "int (*SSL_CTX_get_verify_callback(const SSL_CTX *ctx))(int ok, X509_STORE_CTX *ctx);"
.IP "int \fBSSL_CTX_get_verify_mode\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_get_verify_mode(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_load_verify_locations\fR(SSL_CTX *ctx, const char *CAfile, const char *CApath);" 4
.IX Item "int SSL_CTX_load_verify_locations(SSL_CTX *ctx, const char *CAfile, const char *CApath);"
.IP "SSL_CTX *\fBSSL_CTX_new\fR(const SSL_METHOD *meth);" 4
.IX Item "SSL_CTX *SSL_CTX_new(const SSL_METHOD *meth);"
.IP "int SSL_CTX_up_ref(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_up_ref(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_remove_session\fR(SSL_CTX *ctx, SSL_SESSION *c);" 4
.IX Item "int SSL_CTX_remove_session(SSL_CTX *ctx, SSL_SESSION *c);"
.IP "int \fBSSL_CTX_sess_accept\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_accept(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_sess_accept_good\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_accept_good(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_sess_accept_renegotiate\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_accept_renegotiate(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_sess_cache_full\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_cache_full(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_sess_cb_hits\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_cb_hits(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_sess_connect\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_connect(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_sess_connect_good\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_connect_good(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_sess_connect_renegotiate\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_connect_renegotiate(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_sess_get_cache_size\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_get_cache_size(SSL_CTX *ctx);"
.IP "SSL_SESSION *(*\fBSSL_CTX_sess_get_get_cb\fR(SSL_CTX *ctx))(SSL *ssl, unsigned char *data, int len, int *copy);" 4
.IX Item "SSL_SESSION *(*SSL_CTX_sess_get_get_cb(SSL_CTX *ctx))(SSL *ssl, unsigned char *data, int len, int *copy);"
.IP "int (*\fBSSL_CTX_sess_get_new_cb\fR(SSL_CTX *ctx)(SSL *ssl, SSL_SESSION *sess);" 4
.IX Item "int (*SSL_CTX_sess_get_new_cb(SSL_CTX *ctx)(SSL *ssl, SSL_SESSION *sess);"
.IP "void (*\fBSSL_CTX_sess_get_remove_cb\fR(SSL_CTX *ctx)(SSL_CTX *ctx, SSL_SESSION *sess);" 4
.IX Item "void (*SSL_CTX_sess_get_remove_cb(SSL_CTX *ctx)(SSL_CTX *ctx, SSL_SESSION *sess);"
.IP "int \fBSSL_CTX_sess_hits\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_hits(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_sess_misses\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_misses(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_sess_number\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_number(SSL_CTX *ctx);"
.IP "void \fBSSL_CTX_sess_set_cache_size\fR(SSL_CTX *ctx, t);" 4
.IX Item "void SSL_CTX_sess_set_cache_size(SSL_CTX *ctx, t);"
.IP "void \fBSSL_CTX_sess_set_get_cb\fR(SSL_CTX *ctx, SSL_SESSION *(*cb)(SSL *ssl, unsigned char *data, int len, int *copy));" 4
.IX Item "void SSL_CTX_sess_set_get_cb(SSL_CTX *ctx, SSL_SESSION *(*cb)(SSL *ssl, unsigned char *data, int len, int *copy));"
.IP "void \fBSSL_CTX_sess_set_new_cb\fR(SSL_CTX *ctx, int (*cb)(SSL *ssl, SSL_SESSION *sess));" 4
.IX Item "void SSL_CTX_sess_set_new_cb(SSL_CTX *ctx, int (*cb)(SSL *ssl, SSL_SESSION *sess));"
.IP "void \fBSSL_CTX_sess_set_remove_cb\fR(SSL_CTX *ctx, void (*cb)(SSL_CTX *ctx, SSL_SESSION *sess));" 4
.IX Item "void SSL_CTX_sess_set_remove_cb(SSL_CTX *ctx, void (*cb)(SSL_CTX *ctx, SSL_SESSION *sess));"
.IP "int \fBSSL_CTX_sess_timeouts\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_sess_timeouts(SSL_CTX *ctx);"
.IP "LHASH *\fBSSL_CTX_sessions\fR(SSL_CTX *ctx);" 4
.IX Item "LHASH *SSL_CTX_sessions(SSL_CTX *ctx);"
.IP "int \fBSSL_CTX_set_app_data\fR(SSL_CTX *ctx, void *arg);" 4
.IX Item "int SSL_CTX_set_app_data(SSL_CTX *ctx, void *arg);"
.IP "void \fBSSL_CTX_set_cert_store\fR(SSL_CTX *ctx, X509_STORE *cs);" 4
.IX Item "void SSL_CTX_set_cert_store(SSL_CTX *ctx, X509_STORE *cs);"
.IP "void \fBSSL_CTX_set1_cert_store\fR(SSL_CTX *ctx, X509_STORE *cs);" 4
.IX Item "void SSL_CTX_set1_cert_store(SSL_CTX *ctx, X509_STORE *cs);"
.IP "void \fBSSL_CTX_set_cert_verify_cb\fR(SSL_CTX *ctx, int (*cb)(), char *arg)" 4
.IX Item "void SSL_CTX_set_cert_verify_cb(SSL_CTX *ctx, int (*cb)(), char *arg)"
.IP "int \fBSSL_CTX_set_cipher_list\fR(SSL_CTX *ctx, char *str);" 4
.IX Item "int SSL_CTX_set_cipher_list(SSL_CTX *ctx, char *str);"
.IP "void \fBSSL_CTX_set_client_CA_list\fR(SSL_CTX *ctx, STACK *list);" 4
.IX Item "void SSL_CTX_set_client_CA_list(SSL_CTX *ctx, STACK *list);"
.IP "void \fBSSL_CTX_set_client_cert_cb\fR(SSL_CTX *ctx, int (*cb)(SSL *ssl, X509 **x509, EVP_PKEY **pkey));" 4
.IX Item "void SSL_CTX_set_client_cert_cb(SSL_CTX *ctx, int (*cb)(SSL *ssl, X509 **x509, EVP_PKEY **pkey));"
.IP "int \fBSSL_CTX_set_ct_validation_callback\fR(SSL_CTX *ctx, ssl_ct_validation_cb callback, void *arg);" 4
.IX Item "int SSL_CTX_set_ct_validation_callback(SSL_CTX *ctx, ssl_ct_validation_cb callback, void *arg);"
.IP "void \fBSSL_CTX_set_default_passwd_cb\fR(SSL_CTX *ctx, int (*cb);(void))" 4
.IX Item "void SSL_CTX_set_default_passwd_cb(SSL_CTX *ctx, int (*cb);(void))"
.IP "void \fBSSL_CTX_set_default_read_ahead\fR(SSL_CTX *ctx, int m);" 4
.IX Item "void SSL_CTX_set_default_read_ahead(SSL_CTX *ctx, int m);"
.IP "int \fBSSL_CTX_set_default_verify_paths\fR(SSL_CTX *ctx);" 4
.IX Item "int SSL_CTX_set_default_verify_paths(SSL_CTX *ctx);"
.PD
Use the default paths to locate trusted CA certificates. There is one default
directory path and one default file path. Both are set via this call.
.IP "int \fBSSL_CTX_set_default_verify_dir\fR(SSL_CTX *ctx)" 4
.IX Item "int SSL_CTX_set_default_verify_dir(SSL_CTX *ctx)"
Use the default directory path to locate trusted CA certificates.
.IP "int \fBSSL_CTX_set_default_verify_file\fR(SSL_CTX *ctx)" 4
.IX Item "int SSL_CTX_set_default_verify_file(SSL_CTX *ctx)"
Use the file path to locate trusted CA certificates.
.IP "int \fBSSL_CTX_set_ex_data\fR(SSL_CTX *s, int idx, char *arg);" 4
.IX Item "int SSL_CTX_set_ex_data(SSL_CTX *s, int idx, char *arg);"
.PD 0
.IP "void \fBSSL_CTX_set_info_callback\fR(SSL_CTX *ctx, void (*cb)(SSL *ssl, int cb, int ret));" 4
.IX Item "void SSL_CTX_set_info_callback(SSL_CTX *ctx, void (*cb)(SSL *ssl, int cb, int ret));"
.IP "void \fBSSL_CTX_set_msg_callback\fR(SSL_CTX *ctx, void (*cb)(int write_p, int version, int content_type, const void *buf, size_t len, SSL *ssl, void *arg));" 4
.IX Item "void SSL_CTX_set_msg_callback(SSL_CTX *ctx, void (*cb)(int write_p, int version, int content_type, const void *buf, size_t len, SSL *ssl, void *arg));"
.IP "void \fBSSL_CTX_set_msg_callback_arg\fR(SSL_CTX *ctx, void *arg);" 4
.IX Item "void SSL_CTX_set_msg_callback_arg(SSL_CTX *ctx, void *arg);"
.IP "unsigned long \fBSSL_CTX_clear_options\fR(SSL_CTX *ctx, unsigned long op);" 4
.IX Item "unsigned long SSL_CTX_clear_options(SSL_CTX *ctx, unsigned long op);"
.IP "unsigned long \fBSSL_CTX_get_options\fR(SSL_CTX *ctx);" 4
.IX Item "unsigned long SSL_CTX_get_options(SSL_CTX *ctx);"
.IP "unsigned long \fBSSL_CTX_set_options\fR(SSL_CTX *ctx, unsigned long op);" 4
.IX Item "unsigned long SSL_CTX_set_options(SSL_CTX *ctx, unsigned long op);"
.IP "void \fBSSL_CTX_set_quiet_shutdown\fR(SSL_CTX *ctx, int mode);" 4
.IX Item "void SSL_CTX_set_quiet_shutdown(SSL_CTX *ctx, int mode);"
.IP "void \fBSSL_CTX_set_read_ahead\fR(SSL_CTX *ctx, int m);" 4
.IX Item "void SSL_CTX_set_read_ahead(SSL_CTX *ctx, int m);"
.IP "void \fBSSL_CTX_set_session_cache_mode\fR(SSL_CTX *ctx, int mode);" 4
.IX Item "void SSL_CTX_set_session_cache_mode(SSL_CTX *ctx, int mode);"
.IP "int \fBSSL_CTX_set_ssl_version\fR(SSL_CTX *ctx, const SSL_METHOD *meth);" 4
.IX Item "int SSL_CTX_set_ssl_version(SSL_CTX *ctx, const SSL_METHOD *meth);"
.IP "void \fBSSL_CTX_set_timeout\fR(SSL_CTX *ctx, long t);" 4
.IX Item "void SSL_CTX_set_timeout(SSL_CTX *ctx, long t);"
.IP "long \fBSSL_CTX_set_tmp_dh\fR(SSL_CTX* ctx, DH *dh);" 4
.IX Item "long SSL_CTX_set_tmp_dh(SSL_CTX* ctx, DH *dh);"
.IP "long \fBSSL_CTX_set_tmp_dh_callback\fR(SSL_CTX *ctx, DH *(*cb)(void));" 4
.IX Item "long SSL_CTX_set_tmp_dh_callback(SSL_CTX *ctx, DH *(*cb)(void));"
.IP "void \fBSSL_CTX_set_verify\fR(SSL_CTX *ctx, int mode, int (*cb);(void))" 4
.IX Item "void SSL_CTX_set_verify(SSL_CTX *ctx, int mode, int (*cb);(void))"
.IP "int \fBSSL_CTX_use_PrivateKey\fR(SSL_CTX *ctx, EVP_PKEY *pkey);" 4
.IX Item "int SSL_CTX_use_PrivateKey(SSL_CTX *ctx, EVP_PKEY *pkey);"
.IP "int \fBSSL_CTX_use_PrivateKey_ASN1\fR(int type, SSL_CTX *ctx, unsigned char *d, long len);" 4
.IX Item "int SSL_CTX_use_PrivateKey_ASN1(int type, SSL_CTX *ctx, unsigned char *d, long len);"
.IP "int \fBSSL_CTX_use_PrivateKey_file\fR(SSL_CTX *ctx, const char *file, int type);" 4
.IX Item "int SSL_CTX_use_PrivateKey_file(SSL_CTX *ctx, const char *file, int type);"
.IP "int \fBSSL_CTX_use_RSAPrivateKey\fR(SSL_CTX *ctx, RSA *rsa);" 4
.IX Item "int SSL_CTX_use_RSAPrivateKey(SSL_CTX *ctx, RSA *rsa);"
.IP "int \fBSSL_CTX_use_RSAPrivateKey_ASN1\fR(SSL_CTX *ctx, unsigned char *d, long len);" 4
.IX Item "int SSL_CTX_use_RSAPrivateKey_ASN1(SSL_CTX *ctx, unsigned char *d, long len);"
.IP "int \fBSSL_CTX_use_RSAPrivateKey_file\fR(SSL_CTX *ctx, const char *file, int type);" 4
.IX Item "int SSL_CTX_use_RSAPrivateKey_file(SSL_CTX *ctx, const char *file, int type);"
.IP "int \fBSSL_CTX_use_certificate\fR(SSL_CTX *ctx, X509 *x);" 4
.IX Item "int SSL_CTX_use_certificate(SSL_CTX *ctx, X509 *x);"
.IP "int \fBSSL_CTX_use_certificate_ASN1\fR(SSL_CTX *ctx, int len, unsigned char *d);" 4
.IX Item "int SSL_CTX_use_certificate_ASN1(SSL_CTX *ctx, int len, unsigned char *d);"
.IP "int \fBSSL_CTX_use_certificate_file\fR(SSL_CTX *ctx, const char *file, int type);" 4
.IX Item "int SSL_CTX_use_certificate_file(SSL_CTX *ctx, const char *file, int type);"
.IP "int \fBSSL_CTX_use_cert_and_key\fR(SSL_CTX *ctx, X509 *x, EVP_PKEY *pkey, STACK_OF(X509) *chain, int override);" 4
.IX Item "int SSL_CTX_use_cert_and_key(SSL_CTX *ctx, X509 *x, EVP_PKEY *pkey, STACK_OF(X509) *chain, int override);"
.IP "X509 *\fBSSL_CTX_get0_certificate\fR(const SSL_CTX *ctx);" 4
.IX Item "X509 *SSL_CTX_get0_certificate(const SSL_CTX *ctx);"
.IP "EVP_PKEY *\fBSSL_CTX_get0_privatekey\fR(const SSL_CTX *ctx);" 4
.IX Item "EVP_PKEY *SSL_CTX_get0_privatekey(const SSL_CTX *ctx);"
.IP "void \fBSSL_CTX_set_psk_client_callback\fR(SSL_CTX *ctx, unsigned int (*callback)(SSL *ssl, const char *hint, char *identity, unsigned int max_identity_len, unsigned char *psk, unsigned int max_psk_len));" 4
.IX Item "void SSL_CTX_set_psk_client_callback(SSL_CTX *ctx, unsigned int (*callback)(SSL *ssl, const char *hint, char *identity, unsigned int max_identity_len, unsigned char *psk, unsigned int max_psk_len));"
.IP "int \fBSSL_CTX_use_psk_identity_hint\fR(SSL_CTX *ctx, const char *hint);" 4
.IX Item "int SSL_CTX_use_psk_identity_hint(SSL_CTX *ctx, const char *hint);"
.IP "void \fBSSL_CTX_set_psk_server_callback\fR(SSL_CTX *ctx, unsigned int (*callback)(SSL *ssl, const char *identity, unsigned char *psk, int max_psk_len));" 4
.IX Item "void SSL_CTX_set_psk_server_callback(SSL_CTX *ctx, unsigned int (*callback)(SSL *ssl, const char *identity, unsigned char *psk, int max_psk_len));"
.PD
.SS "Dealing with Sessions"
.IX Subsection "Dealing with Sessions"
Here we document the various API functions which deal with the SSL/TLS
sessions defined in the \fBSSL_SESSION\fR structures.
.IP "int \fBSSL_SESSION_cmp\fR(const SSL_SESSION *a, const SSL_SESSION *b);" 4
.IX Item "int SSL_SESSION_cmp(const SSL_SESSION *a, const SSL_SESSION *b);"
.PD 0
.IP "void \fBSSL_SESSION_free\fR(SSL_SESSION *ss);" 4
.IX Item "void SSL_SESSION_free(SSL_SESSION *ss);"
.IP "char *\fBSSL_SESSION_get_app_data\fR(SSL_SESSION *s);" 4
.IX Item "char *SSL_SESSION_get_app_data(SSL_SESSION *s);"
.IP "char *\fBSSL_SESSION_get_ex_data\fR(const SSL_SESSION *s, int idx);" 4
.IX Item "char *SSL_SESSION_get_ex_data(const SSL_SESSION *s, int idx);"
.IP "int \fBSSL_SESSION_get_ex_new_index\fR(long argl, char *argp, int (*new_func);(void), int (*dup_func)(void), void (*free_func)(void))" 4
.IX Item "int SSL_SESSION_get_ex_new_index(long argl, char *argp, int (*new_func);(void), int (*dup_func)(void), void (*free_func)(void))"
.IP "long \fBSSL_SESSION_get_time\fR(const SSL_SESSION *s);" 4
.IX Item "long SSL_SESSION_get_time(const SSL_SESSION *s);"
.IP "long \fBSSL_SESSION_get_timeout\fR(const SSL_SESSION *s);" 4
.IX Item "long SSL_SESSION_get_timeout(const SSL_SESSION *s);"
.IP "unsigned long \fBSSL_SESSION_hash\fR(const SSL_SESSION *a);" 4
.IX Item "unsigned long SSL_SESSION_hash(const SSL_SESSION *a);"
.IP "SSL_SESSION *\fBSSL_SESSION_new\fR(void);" 4
.IX Item "SSL_SESSION *SSL_SESSION_new(void);"
.IP "int \fBSSL_SESSION_print\fR(BIO *bp, const SSL_SESSION *x);" 4
.IX Item "int SSL_SESSION_print(BIO *bp, const SSL_SESSION *x);"
.IP "int \fBSSL_SESSION_print_fp\fR(FILE *fp, const SSL_SESSION *x);" 4
.IX Item "int SSL_SESSION_print_fp(FILE *fp, const SSL_SESSION *x);"
.IP "int \fBSSL_SESSION_set_app_data\fR(SSL_SESSION *s, char *a);" 4
.IX Item "int SSL_SESSION_set_app_data(SSL_SESSION *s, char *a);"
.IP "int \fBSSL_SESSION_set_ex_data\fR(SSL_SESSION *s, int idx, char *arg);" 4
.IX Item "int SSL_SESSION_set_ex_data(SSL_SESSION *s, int idx, char *arg);"
.IP "long \fBSSL_SESSION_set_time\fR(SSL_SESSION *s, long t);" 4
.IX Item "long SSL_SESSION_set_time(SSL_SESSION *s, long t);"
.IP "long \fBSSL_SESSION_set_timeout\fR(SSL_SESSION *s, long t);" 4
.IX Item "long SSL_SESSION_set_timeout(SSL_SESSION *s, long t);"
.PD
.SS "Dealing with Connections"
.IX Subsection "Dealing with Connections"
Here we document the various API functions which deal with the SSL/TLS
connection defined in the \fBSSL\fR structure.
.IP "int \fBSSL_accept\fR(SSL *ssl);" 4
.IX Item "int SSL_accept(SSL *ssl);"
.PD 0
.IP "int \fBSSL_add_dir_cert_subjects_to_stack\fR(STACK *stack, const char *dir);" 4
.IX Item "int SSL_add_dir_cert_subjects_to_stack(STACK *stack, const char *dir);"
.IP "int \fBSSL_add_file_cert_subjects_to_stack\fR(STACK *stack, const char *file);" 4
.IX Item "int SSL_add_file_cert_subjects_to_stack(STACK *stack, const char *file);"
.IP "int \fBSSL_add_client_CA\fR(SSL *ssl, X509 *x);" 4
.IX Item "int SSL_add_client_CA(SSL *ssl, X509 *x);"
.IP "char *\fBSSL_alert_desc_string\fR(int value);" 4
.IX Item "char *SSL_alert_desc_string(int value);"
.IP "char *\fBSSL_alert_desc_string_long\fR(int value);" 4
.IX Item "char *SSL_alert_desc_string_long(int value);"
.IP "char *\fBSSL_alert_type_string\fR(int value);" 4
.IX Item "char *SSL_alert_type_string(int value);"
.IP "char *\fBSSL_alert_type_string_long\fR(int value);" 4
.IX Item "char *SSL_alert_type_string_long(int value);"
.IP "int \fBSSL_check_private_key\fR(const SSL *ssl);" 4
.IX Item "int SSL_check_private_key(const SSL *ssl);"
.IP "void \fBSSL_clear\fR(SSL *ssl);" 4
.IX Item "void SSL_clear(SSL *ssl);"
.IP "long \fBSSL_clear_num_renegotiations\fR(SSL *ssl);" 4
.IX Item "long SSL_clear_num_renegotiations(SSL *ssl);"
.IP "int \fBSSL_connect\fR(SSL *ssl);" 4
.IX Item "int SSL_connect(SSL *ssl);"
.IP "int \fBSSL_copy_session_id\fR(SSL *t, const SSL *f);" 4
.IX Item "int SSL_copy_session_id(SSL *t, const SSL *f);"
.PD
Sets the session details for \fBt\fR to be the same as in \fBf\fR. Returns 1 on
success or 0 on failure.
.IP "long \fBSSL_ctrl\fR(SSL *ssl, int cmd, long larg, char *parg);" 4
.IX Item "long SSL_ctrl(SSL *ssl, int cmd, long larg, char *parg);"
.PD 0
.IP "int \fBSSL_do_handshake\fR(SSL *ssl);" 4
.IX Item "int SSL_do_handshake(SSL *ssl);"
.IP "SSL *\fBSSL_dup\fR(SSL *ssl);" 4
.IX Item "SSL *SSL_dup(SSL *ssl);"
.PD
\&\fBSSL_dup()\fR allows applications to configure an SSL handle for use
in multiple SSL connections, and then duplicate it prior to initiating
each connection with the duplicated handle.
Use of \fBSSL_dup()\fR avoids the need to repeat the configuration of the
handles for each connection.
.Sp
For \fBSSL_dup()\fR to work, the connection MUST be in its initial state
and MUST NOT have not yet have started the SSL handshake.
For connections that are not in their initial state \fBSSL_dup()\fR just
increments an internal reference count and returns the \fIsame\fR
handle.
It may be possible to use \fBSSL_clear\fR\|(3) to recycle an SSL handle
that is not in its initial state for re-use, but this is best
avoided.
Instead, save and restore the session, if desired, and construct a
fresh handle for each connection.
.IP "STACK *\fBSSL_dup_CA_list\fR(STACK *sk);" 4
.IX Item "STACK *SSL_dup_CA_list(STACK *sk);"
.PD 0
.IP "void \fBSSL_free\fR(SSL *ssl);" 4
.IX Item "void SSL_free(SSL *ssl);"
.IP "SSL_CTX *\fBSSL_get_SSL_CTX\fR(const SSL *ssl);" 4
.IX Item "SSL_CTX *SSL_get_SSL_CTX(const SSL *ssl);"
.IP "char *\fBSSL_get_app_data\fR(SSL *ssl);" 4
.IX Item "char *SSL_get_app_data(SSL *ssl);"
.IP "X509 *\fBSSL_get_certificate\fR(const SSL *ssl);" 4
.IX Item "X509 *SSL_get_certificate(const SSL *ssl);"
.IP "const char *\fBSSL_get_cipher\fR(const SSL *ssl);" 4
.IX Item "const char *SSL_get_cipher(const SSL *ssl);"
.IP "int \fBSSL_is_dtls\fR(const SSL *ssl);" 4
.IX Item "int SSL_is_dtls(const SSL *ssl);"
.IP "int \fBSSL_get_cipher_bits\fR(const SSL *ssl, int *alg_bits);" 4
.IX Item "int SSL_get_cipher_bits(const SSL *ssl, int *alg_bits);"
.IP "char *\fBSSL_get_cipher_list\fR(const SSL *ssl, int n);" 4
.IX Item "char *SSL_get_cipher_list(const SSL *ssl, int n);"
.IP "char *\fBSSL_get_cipher_name\fR(const SSL *ssl);" 4
.IX Item "char *SSL_get_cipher_name(const SSL *ssl);"
.IP "char *\fBSSL_get_cipher_version\fR(const SSL *ssl);" 4
.IX Item "char *SSL_get_cipher_version(const SSL *ssl);"
.IP "STACK *\fBSSL_get_ciphers\fR(const SSL *ssl);" 4
.IX Item "STACK *SSL_get_ciphers(const SSL *ssl);"
.IP "STACK *\fBSSL_get_client_CA_list\fR(const SSL *ssl);" 4
.IX Item "STACK *SSL_get_client_CA_list(const SSL *ssl);"
.IP "SSL_CIPHER *\fBSSL_get_current_cipher\fR(SSL *ssl);" 4
.IX Item "SSL_CIPHER *SSL_get_current_cipher(SSL *ssl);"
.IP "long \fBSSL_get_default_timeout\fR(const SSL *ssl);" 4
.IX Item "long SSL_get_default_timeout(const SSL *ssl);"
.IP "int \fBSSL_get_error\fR(const SSL *ssl, int i);" 4
.IX Item "int SSL_get_error(const SSL *ssl, int i);"
.IP "char *\fBSSL_get_ex_data\fR(const SSL *ssl, int idx);" 4
.IX Item "char *SSL_get_ex_data(const SSL *ssl, int idx);"
.IP "int \fBSSL_get_ex_data_X509_STORE_CTX_idx\fR(void);" 4
.IX Item "int SSL_get_ex_data_X509_STORE_CTX_idx(void);"
.IP "int \fBSSL_get_ex_new_index\fR(long argl, char *argp, int (*new_func);(void), int (*dup_func)(void), void (*free_func)(void))" 4
.IX Item "int SSL_get_ex_new_index(long argl, char *argp, int (*new_func);(void), int (*dup_func)(void), void (*free_func)(void))"
.IP "int \fBSSL_get_fd\fR(const SSL *ssl);" 4
.IX Item "int SSL_get_fd(const SSL *ssl);"
.IP "void (*\fBSSL_get_info_callback\fR(const SSL *ssl);)()" 4
.IX Item "void (*SSL_get_info_callback(const SSL *ssl);)()"
.IP "int \fBSSL_get_key_update_type\fR(SSL *s);" 4
.IX Item "int SSL_get_key_update_type(SSL *s);"
.IP "STACK *\fBSSL_get_peer_cert_chain\fR(const SSL *ssl);" 4
.IX Item "STACK *SSL_get_peer_cert_chain(const SSL *ssl);"
.IP "X509 *\fBSSL_get_peer_certificate\fR(const SSL *ssl);" 4
.IX Item "X509 *SSL_get_peer_certificate(const SSL *ssl);"
.IP "const STACK_OF(SCT) *\fBSSL_get0_peer_scts\fR(SSL *s);" 4
.IX Item "const STACK_OF(SCT) *SSL_get0_peer_scts(SSL *s);"
.IP "EVP_PKEY *\fBSSL_get_privatekey\fR(const SSL *ssl);" 4
.IX Item "EVP_PKEY *SSL_get_privatekey(const SSL *ssl);"
.IP "int \fBSSL_get_quiet_shutdown\fR(const SSL *ssl);" 4
.IX Item "int SSL_get_quiet_shutdown(const SSL *ssl);"
.IP "BIO *\fBSSL_get_rbio\fR(const SSL *ssl);" 4
.IX Item "BIO *SSL_get_rbio(const SSL *ssl);"
.IP "int \fBSSL_get_read_ahead\fR(const SSL *ssl);" 4
.IX Item "int SSL_get_read_ahead(const SSL *ssl);"
.IP "SSL_SESSION *\fBSSL_get_session\fR(const SSL *ssl);" 4
.IX Item "SSL_SESSION *SSL_get_session(const SSL *ssl);"
.IP "char *\fBSSL_get_shared_ciphers\fR(const SSL *ssl, char *buf, int size);" 4
.IX Item "char *SSL_get_shared_ciphers(const SSL *ssl, char *buf, int size);"
.IP "int \fBSSL_get_shutdown\fR(const SSL *ssl);" 4
.IX Item "int SSL_get_shutdown(const SSL *ssl);"
.IP "const SSL_METHOD *\fBSSL_get_ssl_method\fR(SSL *ssl);" 4
.IX Item "const SSL_METHOD *SSL_get_ssl_method(SSL *ssl);"
.IP "int \fBSSL_get_state\fR(const SSL *ssl);" 4
.IX Item "int SSL_get_state(const SSL *ssl);"
.IP "long \fBSSL_get_time\fR(const SSL *ssl);" 4
.IX Item "long SSL_get_time(const SSL *ssl);"
.IP "long \fBSSL_get_timeout\fR(const SSL *ssl);" 4
.IX Item "long SSL_get_timeout(const SSL *ssl);"
.IP "int (*\fBSSL_get_verify_callback\fR(const SSL *ssl))(int, X509_STORE_CTX *)" 4
.IX Item "int (*SSL_get_verify_callback(const SSL *ssl))(int, X509_STORE_CTX *)"
.IP "int \fBSSL_get_verify_mode\fR(const SSL *ssl);" 4
.IX Item "int SSL_get_verify_mode(const SSL *ssl);"
.IP "long \fBSSL_get_verify_result\fR(const SSL *ssl);" 4
.IX Item "long SSL_get_verify_result(const SSL *ssl);"
.IP "char *\fBSSL_get_version\fR(const SSL *ssl);" 4
.IX Item "char *SSL_get_version(const SSL *ssl);"
.IP "BIO *\fBSSL_get_wbio\fR(const SSL *ssl);" 4
.IX Item "BIO *SSL_get_wbio(const SSL *ssl);"
.IP "int \fBSSL_in_accept_init\fR(SSL *ssl);" 4
.IX Item "int SSL_in_accept_init(SSL *ssl);"
.IP "int \fBSSL_in_before\fR(SSL *ssl);" 4
.IX Item "int SSL_in_before(SSL *ssl);"
.IP "int \fBSSL_in_connect_init\fR(SSL *ssl);" 4
.IX Item "int SSL_in_connect_init(SSL *ssl);"
.IP "int \fBSSL_in_init\fR(SSL *ssl);" 4
.IX Item "int SSL_in_init(SSL *ssl);"
.IP "int \fBSSL_is_init_finished\fR(SSL *ssl);" 4
.IX Item "int SSL_is_init_finished(SSL *ssl);"
.IP "int \fBSSL_key_update\fR(SSL *s, int updatetype);" 4
.IX Item "int SSL_key_update(SSL *s, int updatetype);"
.IP "STACK *\fBSSL_load_client_CA_file\fR(const char *file);" 4
.IX Item "STACK *SSL_load_client_CA_file(const char *file);"
.IP "SSL *\fBSSL_new\fR(SSL_CTX *ctx);" 4
.IX Item "SSL *SSL_new(SSL_CTX *ctx);"
.IP "int SSL_up_ref(SSL *s);" 4
.IX Item "int SSL_up_ref(SSL *s);"
.IP "long \fBSSL_num_renegotiations\fR(SSL *ssl);" 4
.IX Item "long SSL_num_renegotiations(SSL *ssl);"
.IP "int \fBSSL_peek\fR(SSL *ssl, void *buf, int num);" 4
.IX Item "int SSL_peek(SSL *ssl, void *buf, int num);"
.IP "int \fBSSL_pending\fR(const SSL *ssl);" 4
.IX Item "int SSL_pending(const SSL *ssl);"
.IP "int \fBSSL_read\fR(SSL *ssl, void *buf, int num);" 4
.IX Item "int SSL_read(SSL *ssl, void *buf, int num);"
.IP "int \fBSSL_renegotiate\fR(SSL *ssl);" 4
.IX Item "int SSL_renegotiate(SSL *ssl);"
.IP "char *\fBSSL_rstate_string\fR(SSL *ssl);" 4
.IX Item "char *SSL_rstate_string(SSL *ssl);"
.IP "char *\fBSSL_rstate_string_long\fR(SSL *ssl);" 4
.IX Item "char *SSL_rstate_string_long(SSL *ssl);"
.IP "long \fBSSL_session_reused\fR(SSL *ssl);" 4
.IX Item "long SSL_session_reused(SSL *ssl);"
.IP "void \fBSSL_set_accept_state\fR(SSL *ssl);" 4
.IX Item "void SSL_set_accept_state(SSL *ssl);"
.IP "void \fBSSL_set_app_data\fR(SSL *ssl, char *arg);" 4
.IX Item "void SSL_set_app_data(SSL *ssl, char *arg);"
.IP "void \fBSSL_set_bio\fR(SSL *ssl, BIO *rbio, BIO *wbio);" 4
.IX Item "void SSL_set_bio(SSL *ssl, BIO *rbio, BIO *wbio);"
.IP "int \fBSSL_set_cipher_list\fR(SSL *ssl, char *str);" 4
.IX Item "int SSL_set_cipher_list(SSL *ssl, char *str);"
.IP "void \fBSSL_set_client_CA_list\fR(SSL *ssl, STACK *list);" 4
.IX Item "void SSL_set_client_CA_list(SSL *ssl, STACK *list);"
.IP "void \fBSSL_set_connect_state\fR(SSL *ssl);" 4
.IX Item "void SSL_set_connect_state(SSL *ssl);"
.IP "int \fBSSL_set_ct_validation_callback\fR(SSL *ssl, ssl_ct_validation_cb callback, void *arg);" 4
.IX Item "int SSL_set_ct_validation_callback(SSL *ssl, ssl_ct_validation_cb callback, void *arg);"
.IP "int \fBSSL_set_ex_data\fR(SSL *ssl, int idx, char *arg);" 4
.IX Item "int SSL_set_ex_data(SSL *ssl, int idx, char *arg);"
.IP "int \fBSSL_set_fd\fR(SSL *ssl, int fd);" 4
.IX Item "int SSL_set_fd(SSL *ssl, int fd);"
.IP "void \fBSSL_set_info_callback\fR(SSL *ssl, void (*cb);(void))" 4
.IX Item "void SSL_set_info_callback(SSL *ssl, void (*cb);(void))"
.IP "void \fBSSL_set_msg_callback\fR(SSL *ctx, void (*cb)(int write_p, int version, int content_type, const void *buf, size_t len, SSL *ssl, void *arg));" 4
.IX Item "void SSL_set_msg_callback(SSL *ctx, void (*cb)(int write_p, int version, int content_type, const void *buf, size_t len, SSL *ssl, void *arg));"
.IP "void \fBSSL_set_msg_callback_arg\fR(SSL *ctx, void *arg);" 4
.IX Item "void SSL_set_msg_callback_arg(SSL *ctx, void *arg);"
.IP "unsigned long \fBSSL_clear_options\fR(SSL *ssl, unsigned long op);" 4
.IX Item "unsigned long SSL_clear_options(SSL *ssl, unsigned long op);"
.IP "unsigned long \fBSSL_get_options\fR(SSL *ssl);" 4
.IX Item "unsigned long SSL_get_options(SSL *ssl);"
.IP "unsigned long \fBSSL_set_options\fR(SSL *ssl, unsigned long op);" 4
.IX Item "unsigned long SSL_set_options(SSL *ssl, unsigned long op);"
.IP "void \fBSSL_set_quiet_shutdown\fR(SSL *ssl, int mode);" 4
.IX Item "void SSL_set_quiet_shutdown(SSL *ssl, int mode);"
.IP "void \fBSSL_set_read_ahead\fR(SSL *ssl, int yes);" 4
.IX Item "void SSL_set_read_ahead(SSL *ssl, int yes);"
.IP "int \fBSSL_set_rfd\fR(SSL *ssl, int fd);" 4
.IX Item "int SSL_set_rfd(SSL *ssl, int fd);"
.IP "int \fBSSL_set_session\fR(SSL *ssl, SSL_SESSION *session);" 4
.IX Item "int SSL_set_session(SSL *ssl, SSL_SESSION *session);"
.IP "void \fBSSL_set_shutdown\fR(SSL *ssl, int mode);" 4
.IX Item "void SSL_set_shutdown(SSL *ssl, int mode);"
.IP "int \fBSSL_set_ssl_method\fR(SSL *ssl, const SSL_METHOD *meth);" 4
.IX Item "int SSL_set_ssl_method(SSL *ssl, const SSL_METHOD *meth);"
.IP "void \fBSSL_set_time\fR(SSL *ssl, long t);" 4
.IX Item "void SSL_set_time(SSL *ssl, long t);"
.IP "void \fBSSL_set_timeout\fR(SSL *ssl, long t);" 4
.IX Item "void SSL_set_timeout(SSL *ssl, long t);"
.IP "void \fBSSL_set_verify\fR(SSL *ssl, int mode, int (*callback);(void))" 4
.IX Item "void SSL_set_verify(SSL *ssl, int mode, int (*callback);(void))"
.IP "void \fBSSL_set_verify_result\fR(SSL *ssl, long arg);" 4
.IX Item "void SSL_set_verify_result(SSL *ssl, long arg);"
.IP "int \fBSSL_set_wfd\fR(SSL *ssl, int fd);" 4
.IX Item "int SSL_set_wfd(SSL *ssl, int fd);"
.IP "int \fBSSL_shutdown\fR(SSL *ssl);" 4
.IX Item "int SSL_shutdown(SSL *ssl);"
.IP "OSSL_HANDSHAKE_STATE \fBSSL_get_state\fR(const SSL *ssl);" 4
.IX Item "OSSL_HANDSHAKE_STATE SSL_get_state(const SSL *ssl);"
.PD
Returns the current handshake state.
.IP "char *\fBSSL_state_string\fR(const SSL *ssl);" 4
.IX Item "char *SSL_state_string(const SSL *ssl);"
.PD 0
.IP "char *\fBSSL_state_string_long\fR(const SSL *ssl);" 4
.IX Item "char *SSL_state_string_long(const SSL *ssl);"
.IP "long \fBSSL_total_renegotiations\fR(SSL *ssl);" 4
.IX Item "long SSL_total_renegotiations(SSL *ssl);"
.IP "int \fBSSL_use_PrivateKey\fR(SSL *ssl, EVP_PKEY *pkey);" 4
.IX Item "int SSL_use_PrivateKey(SSL *ssl, EVP_PKEY *pkey);"
.IP "int \fBSSL_use_PrivateKey_ASN1\fR(int type, SSL *ssl, unsigned char *d, long len);" 4
.IX Item "int SSL_use_PrivateKey_ASN1(int type, SSL *ssl, unsigned char *d, long len);"
.IP "int \fBSSL_use_PrivateKey_file\fR(SSL *ssl, const char *file, int type);" 4
.IX Item "int SSL_use_PrivateKey_file(SSL *ssl, const char *file, int type);"
.IP "int \fBSSL_use_RSAPrivateKey\fR(SSL *ssl, RSA *rsa);" 4
.IX Item "int SSL_use_RSAPrivateKey(SSL *ssl, RSA *rsa);"
.IP "int \fBSSL_use_RSAPrivateKey_ASN1\fR(SSL *ssl, unsigned char *d, long len);" 4
.IX Item "int SSL_use_RSAPrivateKey_ASN1(SSL *ssl, unsigned char *d, long len);"
.IP "int \fBSSL_use_RSAPrivateKey_file\fR(SSL *ssl, const char *file, int type);" 4
.IX Item "int SSL_use_RSAPrivateKey_file(SSL *ssl, const char *file, int type);"
.IP "int \fBSSL_use_certificate\fR(SSL *ssl, X509 *x);" 4
.IX Item "int SSL_use_certificate(SSL *ssl, X509 *x);"
.IP "int \fBSSL_use_certificate_ASN1\fR(SSL *ssl, int len, unsigned char *d);" 4
.IX Item "int SSL_use_certificate_ASN1(SSL *ssl, int len, unsigned char *d);"
.IP "int \fBSSL_use_certificate_file\fR(SSL *ssl, const char *file, int type);" 4
.IX Item "int SSL_use_certificate_file(SSL *ssl, const char *file, int type);"
.IP "int \fBSSL_use_cert_and_key\fR(SSL *ssl, X509 *x, EVP_PKEY *pkey, STACK_OF(X509) *chain, int override);" 4
.IX Item "int SSL_use_cert_and_key(SSL *ssl, X509 *x, EVP_PKEY *pkey, STACK_OF(X509) *chain, int override);"
.IP "int \fBSSL_version\fR(const SSL *ssl);" 4
.IX Item "int SSL_version(const SSL *ssl);"
.IP "int \fBSSL_want\fR(const SSL *ssl);" 4
.IX Item "int SSL_want(const SSL *ssl);"
.IP "int \fBSSL_want_nothing\fR(const SSL *ssl);" 4
.IX Item "int SSL_want_nothing(const SSL *ssl);"
.IP "int \fBSSL_want_read\fR(const SSL *ssl);" 4
.IX Item "int SSL_want_read(const SSL *ssl);"
.IP "int \fBSSL_want_write\fR(const SSL *ssl);" 4
.IX Item "int SSL_want_write(const SSL *ssl);"
.IP "int \fBSSL_want_x509_lookup\fR(const SSL *ssl);" 4
.IX Item "int SSL_want_x509_lookup(const SSL *ssl);"
.IP "int \fBSSL_write\fR(SSL *ssl, const void *buf, int num);" 4
.IX Item "int SSL_write(SSL *ssl, const void *buf, int num);"
.IP "void \fBSSL_set_psk_client_callback\fR(SSL *ssl, unsigned int (*callback)(SSL *ssl, const char *hint, char *identity, unsigned int max_identity_len, unsigned char *psk, unsigned int max_psk_len));" 4
.IX Item "void SSL_set_psk_client_callback(SSL *ssl, unsigned int (*callback)(SSL *ssl, const char *hint, char *identity, unsigned int max_identity_len, unsigned char *psk, unsigned int max_psk_len));"
.IP "int \fBSSL_use_psk_identity_hint\fR(SSL *ssl, const char *hint);" 4
.IX Item "int SSL_use_psk_identity_hint(SSL *ssl, const char *hint);"
.IP "void \fBSSL_set_psk_server_callback\fR(SSL *ssl, unsigned int (*callback)(SSL *ssl, const char *identity, unsigned char *psk, int max_psk_len));" 4
.IX Item "void SSL_set_psk_server_callback(SSL *ssl, unsigned int (*callback)(SSL *ssl, const char *identity, unsigned char *psk, int max_psk_len));"
.IP "const char *\fBSSL_get_psk_identity_hint\fR(SSL *ssl);" 4
.IX Item "const char *SSL_get_psk_identity_hint(SSL *ssl);"
.IP "const char *\fBSSL_get_psk_identity\fR(SSL *ssl);" 4
.IX Item "const char *SSL_get_psk_identity(SSL *ssl);"
.PD
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
See the individual manual pages for details.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1), \fBcrypto\fR\|(7),
\&\fBCRYPTO_get_ex_new_index\fR\|(3),
\&\fBSSL_accept\fR\|(3), \fBSSL_clear\fR\|(3),
\&\fBSSL_connect\fR\|(3),
\&\fBSSL_CIPHER_get_name\fR\|(3),
\&\fBSSL_COMP_add_compression_method\fR\|(3),
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3),
\&\fBSSL_CTX_add_session\fR\|(3),
\&\fBSSL_CTX_ctrl\fR\|(3),
\&\fBSSL_CTX_flush_sessions\fR\|(3),
\&\fBSSL_CTX_get_verify_mode\fR\|(3),
\&\fBSSL_CTX_load_verify_locations\fR\|(3)
\&\fBSSL_CTX_new\fR\|(3),
\&\fBSSL_CTX_sess_number\fR\|(3),
\&\fBSSL_CTX_sess_set_cache_size\fR\|(3),
\&\fBSSL_CTX_sess_set_get_cb\fR\|(3),
\&\fBSSL_CTX_sessions\fR\|(3),
\&\fBSSL_CTX_set_cert_store\fR\|(3),
\&\fBSSL_CTX_set_cert_verify_callback\fR\|(3),
\&\fBSSL_CTX_set_cipher_list\fR\|(3),
\&\fBSSL_CTX_set_client_CA_list\fR\|(3),
\&\fBSSL_CTX_set_client_cert_cb\fR\|(3),
\&\fBSSL_CTX_set_default_passwd_cb\fR\|(3),
\&\fBSSL_CTX_set_generate_session_id\fR\|(3),
\&\fBSSL_CTX_set_info_callback\fR\|(3),
\&\fBSSL_CTX_set_max_cert_list\fR\|(3),
\&\fBSSL_CTX_set_mode\fR\|(3),
\&\fBSSL_CTX_set_msg_callback\fR\|(3),
\&\fBSSL_CTX_set_options\fR\|(3),
\&\fBSSL_CTX_set_quiet_shutdown\fR\|(3),
\&\fBSSL_CTX_set_read_ahead\fR\|(3),
\&\fBSSL_CTX_set_security_level\fR\|(3),
\&\fBSSL_CTX_set_session_cache_mode\fR\|(3),
\&\fBSSL_CTX_set_session_id_context\fR\|(3),
\&\fBSSL_CTX_set_ssl_version\fR\|(3),
\&\fBSSL_CTX_set_timeout\fR\|(3),
\&\fBSSL_CTX_set_tmp_dh_callback\fR\|(3),
\&\fBSSL_CTX_set_verify\fR\|(3),
\&\fBSSL_CTX_use_certificate\fR\|(3),
\&\fBSSL_alert_type_string\fR\|(3),
\&\fBSSL_do_handshake\fR\|(3),
\&\fBSSL_enable_ct\fR\|(3),
\&\fBSSL_get_SSL_CTX\fR\|(3),
\&\fBSSL_get_ciphers\fR\|(3),
\&\fBSSL_get_client_CA_list\fR\|(3),
\&\fBSSL_get_default_timeout\fR\|(3),
\&\fBSSL_get_error\fR\|(3),
\&\fBSSL_get_ex_data_X509_STORE_CTX_idx\fR\|(3),
\&\fBSSL_get_fd\fR\|(3),
\&\fBSSL_get_peer_cert_chain\fR\|(3),
\&\fBSSL_get_rbio\fR\|(3),
\&\fBSSL_get_session\fR\|(3),
\&\fBSSL_get_verify_result\fR\|(3),
\&\fBSSL_get_version\fR\|(3),
\&\fBSSL_load_client_CA_file\fR\|(3),
\&\fBSSL_new\fR\|(3),
\&\fBSSL_pending\fR\|(3),
\&\fBSSL_read_ex\fR\|(3),
\&\fBSSL_read\fR\|(3),
\&\fBSSL_rstate_string\fR\|(3),
\&\fBSSL_session_reused\fR\|(3),
\&\fBSSL_set_bio\fR\|(3),
\&\fBSSL_set_connect_state\fR\|(3),
\&\fBSSL_set_fd\fR\|(3),
\&\fBSSL_set_session\fR\|(3),
\&\fBSSL_set_shutdown\fR\|(3),
\&\fBSSL_shutdown\fR\|(3),
\&\fBSSL_state_string\fR\|(3),
\&\fBSSL_want\fR\|(3),
\&\fBSSL_write_ex\fR\|(3),
\&\fBSSL_write\fR\|(3),
\&\fBSSL_SESSION_free\fR\|(3),
\&\fBSSL_SESSION_get_time\fR\|(3),
\&\fBd2i_SSL_SESSION\fR\|(3),
\&\fBSSL_CTX_set_psk_client_callback\fR\|(3),
\&\fBSSL_CTX_use_psk_identity_hint\fR\|(3),
\&\fBSSL_get_psk_identity\fR\|(3),
\&\fBDTLSv1_listen\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBSSLv2_client_method\fR, \fBSSLv2_server_method\fR and \fBSSLv2_method\fR were removed
in OpenSSL 1.1.0.
.PP
The return type of \fBSSL_copy_session_id\fR was changed from void to int in
OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
