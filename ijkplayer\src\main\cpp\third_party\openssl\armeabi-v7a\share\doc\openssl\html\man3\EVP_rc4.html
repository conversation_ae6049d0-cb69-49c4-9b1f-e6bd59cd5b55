<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_rc4</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_rc4, EVP_rc4_40, EVP_rc4_hmac_md5 - EVP RC4 stream cipher</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

const EVP_CIPHER *EVP_rc4(void)
const EVP_CIPHER *EVP_rc4_40(void)
const EVP_CIPHER *EVP_rc4_hmac_md5(void)</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The RC4 stream cipher for EVP.</p>

<dl>

<dt id="EVP_rc4">EVP_rc4()</dt>
<dd>

<p>RC4 stream cipher. This is a variable key length cipher with a default key length of 128 bits.</p>

</dd>
<dt id="EVP_rc4_40">EVP_rc4_40()</dt>
<dd>

<p>RC4 stream cipher with 40 bit key length.</p>

<p>WARNING: this function is obsolete. Its usage should be replaced with the EVP_rc4() and the EVP_CIPHER_CTX_set_key_length() functions.</p>

</dd>
<dt id="EVP_rc4_hmac_md5">EVP_rc4_hmac_md5()</dt>
<dd>

<p>Authenticated encryption with the RC4 stream cipher with MD5 as HMAC.</p>

<p>WARNING: this is not intended for usage outside of TLS and requires calling of some undocumented ctrl functions. These ciphers do not conform to the EVP AEAD interface.</p>

</dd>
</dl>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>These functions return an <b>EVP_CIPHER</b> structure that contains the implementation of the symmetric cipher. See <a href="../man3/EVP_CIPHER_meth_new.html">EVP_CIPHER_meth_new(3)</a> for details of the <b>EVP_CIPHER</b> structure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/evp.html">evp(7)</a>, <a href="../man3/EVP_EncryptInit.html">EVP_EncryptInit(3)</a>, <a href="../man3/EVP_CIPHER_meth_new.html">EVP_CIPHER_meth_new(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


