.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "UI_UTIL_READ_PW 3"
.TH UI_UTIL_READ_PW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
UI_UTIL_read_pw_string, UI_UTIL_read_pw,
UI_UTIL_wrap_read_pem_callback \- user interface utilities
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ui.h>
\&
\& int UI_UTIL_read_pw_string(char *buf, int length, const char *prompt,
\&                            int verify);
\& int UI_UTIL_read_pw(char *buf, char *buff, int size, const char *prompt,
\&                     int verify);
\& UI_METHOD *UI_UTIL_wrap_read_pem_callback(pem_password_cb *cb, int rwflag);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBUI_UTIL_read_pw_string()\fR asks for a passphrase, using \fBprompt\fR as a
prompt, and stores it in \fBbuf\fR.
The maximum allowed size is given with \fBlength\fR, including the
terminating NUL byte.
If \fBverify\fR is nonzero, the password will be verified as well.
.PP
\&\fBUI_UTIL_read_pw()\fR does the same as \fBUI_UTIL_read_pw_string()\fR, the
difference is that you can give it an external buffer \fBbuff\fR for the
verification passphrase.
.PP
\&\fBUI_UTIL_wrap_read_pem_callback()\fR can be used to create a temporary
\&\fBUI_METHOD\fR that wraps a given PEM password callback \fBcb\fR.
\&\fBrwflag\fR is used to specify if this method will be used for
passphrase entry without (0) or with (1) verification.
When not used any more, the returned method should be freed with
\&\fBUI_destroy_method()\fR.
.SH NOTES
.IX Header "NOTES"
\&\fBUI_UTIL_read_pw_string()\fR and \fBUI_UTIL_read_pw()\fR use default
\&\fBUI_METHOD\fR.
See \fBUI_get_default_method\fR\|(3) and friends for more information.
.PP
The result from the \fBUI_METHOD\fR created by
\&\fBUI_UTIL_wrap_read_pem_callback()\fR will generate password strings in the
encoding that the given password callback generates.
The default password prompting functions (apart from
\&\fBUI_UTIL_read_pw_string()\fR and \fBUI_UTIL_read_pw()\fR, there is
\&\fBPEM_def_callback()\fR, \fBEVP_read_pw_string()\fR and \fBEVP_read_pw_string_min()\fR)
all use the default \fBUI_METHOD\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBUI_UTIL_read_pw_string()\fR and \fBUI_UTIL_read_pw()\fR return 0 on success or a negative
value on error.
.PP
\&\fBUI_UTIL_wrap_read_pem_callback()\fR returns a valid \fBUI_METHOD\fR structure or NULL
if an error occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBUI_get_default_method\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
