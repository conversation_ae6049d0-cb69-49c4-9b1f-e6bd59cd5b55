.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_SHA224 3"
.TH EVP_SHA224 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_sha224,
EVP_sha256,
EVP_sha512_224,
EVP_sha512_256,
EVP_sha384,
EVP_sha512
\&\- SHA\-2 For EVP
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& const EVP_MD *EVP_sha224(void);
\& const EVP_MD *EVP_sha256(void);
\& const EVP_MD *EVP_sha512_224(void);
\& const EVP_MD *EVP_sha512_256(void);
\& const EVP_MD *EVP_sha384(void);
\& const EVP_MD *EVP_sha512(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
SHA\-2 (Secure Hash Algorithm 2) is a family of cryptographic hash functions
standardized in NIST FIPS 180\-4, first published in 2001.
.IP "\fBEVP_sha224()\fR, \fBEVP_sha256()\fR, EVP_sha512_224, EVP_sha512_256, \fBEVP_sha384()\fR, \fBEVP_sha512()\fR" 4
.IX Item "EVP_sha224(), EVP_sha256(), EVP_sha512_224, EVP_sha512_256, EVP_sha384(), EVP_sha512()"
The SHA\-2 SHA\-224, SHA\-256, SHA\-512/224, SHA512/256, SHA\-384 and SHA\-512
algorithms, which generate 224, 256, 224, 256, 384 and 512 bits
respectively of output from a given input.
.Sp
The two algorithms: SHA\-512/224 and SHA512/256 are truncated forms of the
SHA\-512 algorithm. They are distinct from SHA\-224 and SHA\-256 even though
their outputs are of the same size.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return a \fBEVP_MD\fR structure that contains the
implementation of the symmetric cipher. See \fBEVP_MD_meth_new\fR\|(3) for
details of the \fBEVP_MD\fR structure.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
NIST FIPS 180\-4.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7),
\&\fBEVP_DigestInit\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
