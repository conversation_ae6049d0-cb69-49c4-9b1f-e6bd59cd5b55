.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_DRBG_GET0_MASTER 3"
.TH RAND_DRBG_GET0_MASTER 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_DRBG_get0_master,
RAND_DRBG_get0_public,
RAND_DRBG_get0_private
\&\- get access to the global RAND_DRBG instances
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand_drbg.h>
\&
\& RAND_DRBG *RAND_DRBG_get0_master(void);
\& RAND_DRBG *RAND_DRBG_get0_public(void);
\& RAND_DRBG *RAND_DRBG_get0_private(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The default RAND API implementation (\fBRAND_OpenSSL()\fR) utilizes three
shared DRBG instances which are accessed via the RAND API:
.PP
The <public> and <private> DRBG are thread-local instances, which are used
by \fBRAND_bytes()\fR and \fBRAND_priv_bytes()\fR, respectively.
The <master> DRBG is a global instance, which is not intended to be used
directly, but is used internally to reseed the other two instances.
.PP
These functions here provide access to the shared DRBG instances.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_DRBG_get0_master()\fR returns a pointer to the <master> DRBG instance.
.PP
\&\fBRAND_DRBG_get0_public()\fR returns a pointer to the <public> DRBG instance.
.PP
\&\fBRAND_DRBG_get0_private()\fR returns a pointer to the <private> DRBG instance.
.SH NOTES
.IX Header "NOTES"
It is not thread-safe to access the <master> DRBG instance.
The <public> and <private> DRBG instance can be accessed safely, because
they are thread-local. Note however, that changes to these two instances
apply only to the current thread.
.PP
For that reason it is recommended not to change the settings of these
three instances directly.
Instead, an application should change the default settings for new DRBG instances
at initialization time, before creating additional threads.
.PP
During initialization, it is possible to change the reseed interval
and reseed time interval.
It is also possible to exchange the reseeding callbacks entirely.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND_DRBG_set_callbacks\fR\|(3),
\&\fBRAND_DRBG_set_reseed_defaults\fR\|(3),
\&\fBRAND_DRBG_set_reseed_interval\fR\|(3),
\&\fBRAND_DRBG_set_reseed_time_interval\fR\|(3),
\&\fBRAND_DRBG_set_callbacks\fR\|(3),
\&\fBRAND_DRBG_generate\fR\|(3),
\&\fBRAND_DRBG\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The RAND_DRBG functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
