.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS 1"
.TH CMS 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-cms,
cms \- CMS utility
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBcms\fR
[\fB\-help\fR]
[\fB\-encrypt\fR]
[\fB\-decrypt\fR]
[\fB\-sign\fR]
[\fB\-verify\fR]
[\fB\-cmsout\fR]
[\fB\-resign\fR]
[\fB\-data_create\fR]
[\fB\-data_out\fR]
[\fB\-digest_create\fR]
[\fB\-digest_verify\fR]
[\fB\-compress\fR]
[\fB\-uncompress\fR]
[\fB\-EncryptedData_encrypt\fR]
[\fB\-sign_receipt\fR]
[\fB\-verify_receipt receipt\fR]
[\fB\-in filename\fR]
[\fB\-inform SMIME|PEM|DER\fR]
[\fB\-rctform SMIME|PEM|DER\fR]
[\fB\-out filename\fR]
[\fB\-outform SMIME|PEM|DER\fR]
[\fB\-stream \-indef \-noindef\fR]
[\fB\-noindef\fR]
[\fB\-content filename\fR]
[\fB\-text\fR]
[\fB\-noout\fR]
[\fB\-print\fR]
[\fB\-CAfile file\fR]
[\fB\-CApath dir\fR]
[\fB\-no\-CAfile\fR]
[\fB\-no\-CApath\fR]
[\fB\-attime timestamp\fR]
[\fB\-check_ss_sig\fR]
[\fB\-crl_check\fR]
[\fB\-crl_check_all\fR]
[\fB\-explicit_policy\fR]
[\fB\-extended_crl\fR]
[\fB\-ignore_critical\fR]
[\fB\-inhibit_any\fR]
[\fB\-inhibit_map\fR]
[\fB\-no_check_time\fR]
[\fB\-partial_chain\fR]
[\fB\-policy arg\fR]
[\fB\-policy_check\fR]
[\fB\-policy_print\fR]
[\fB\-purpose purpose\fR]
[\fB\-suiteB_128\fR]
[\fB\-suiteB_128_only\fR]
[\fB\-suiteB_192\fR]
[\fB\-trusted_first\fR]
[\fB\-no_alt_chains\fR]
[\fB\-use_deltas\fR]
[\fB\-auth_level num\fR]
[\fB\-verify_depth num\fR]
[\fB\-verify_email email\fR]
[\fB\-verify_hostname hostname\fR]
[\fB\-verify_ip ip\fR]
[\fB\-verify_name name\fR]
[\fB\-x509_strict\fR]
[\fB\-md digest\fR]
[\fB\-\fR\f(BIcipher\fR]
[\fB\-nointern\fR]
[\fB\-noverify\fR]
[\fB\-nocerts\fR]
[\fB\-noattr\fR]
[\fB\-nosmimecap\fR]
[\fB\-binary\fR]
[\fB\-crlfeol\fR]
[\fB\-asciicrlf\fR]
[\fB\-nodetach\fR]
[\fB\-certfile file\fR]
[\fB\-certsout file\fR]
[\fB\-signer file\fR]
[\fB\-recip file\fR]
[\fB\-keyid\fR]
[\fB\-receipt_request_all\fR]
[\fB\-receipt_request_first\fR]
[\fB\-receipt_request_from emailaddress\fR]
[\fB\-receipt_request_to emailaddress\fR]
[\fB\-receipt_request_print\fR]
[\fB\-secretkey key\fR]
[\fB\-secretkeyid id\fR]
[\fB\-econtent_type type\fR]
[\fB\-inkey file\fR]
[\fB\-keyopt name:parameter\fR]
[\fB\-passin arg\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fBcert.pem...\fR]
[\fB\-to addr\fR]
[\fB\-from addr\fR]
[\fB\-subject subj\fR]
[cert.pem]...
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBcms\fR command handles S/MIME v3.1 mail. It can encrypt, decrypt, sign and
verify, compress and uncompress S/MIME messages.
.SH OPTIONS
.IX Header "OPTIONS"
There are fourteen operation options that set the type of operation to be
performed. The meaning of the other options varies according to the operation
type.
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP \fB\-encrypt\fR 4
.IX Item "-encrypt"
Encrypt mail for the given recipient certificates. Input file is the message
to be encrypted. The output file is the encrypted mail in MIME format. The
actual CMS type is <B>EnvelopedData<B>.
.Sp
Note that no revocation check is done for the recipient cert, so if that
key has been compromised, others may be able to decrypt the text.
.IP \fB\-decrypt\fR 4
.IX Item "-decrypt"
Decrypt mail using the supplied certificate and private key. Expects an
encrypted mail message in MIME format for the input file. The decrypted mail
is written to the output file.
.IP \fB\-debug_decrypt\fR 4
.IX Item "-debug_decrypt"
This option sets the \fBCMS_DEBUG_DECRYPT\fR flag. This option should be used
with caution: see the notes section below.
.IP \fB\-sign\fR 4
.IX Item "-sign"
Sign mail using the supplied certificate and private key. Input file is
the message to be signed. The signed message in MIME format is written
to the output file.
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verify signed mail. Expects a signed mail message on input and outputs
the signed data. Both clear text and opaque signing is supported.
.IP \fB\-cmsout\fR 4
.IX Item "-cmsout"
Takes an input message and writes out a PEM encoded CMS structure.
.IP \fB\-resign\fR 4
.IX Item "-resign"
Resign a message: take an existing message and one or more new signers.
.IP \fB\-data_create\fR 4
.IX Item "-data_create"
Create a CMS \fBData\fR type.
.IP \fB\-data_out\fR 4
.IX Item "-data_out"
\&\fBData\fR type and output the content.
.IP \fB\-digest_create\fR 4
.IX Item "-digest_create"
Create a CMS \fBDigestedData\fR type.
.IP \fB\-digest_verify\fR 4
.IX Item "-digest_verify"
Verify a CMS \fBDigestedData\fR type and output the content.
.IP \fB\-compress\fR 4
.IX Item "-compress"
Create a CMS \fBCompressedData\fR type. OpenSSL must be compiled with \fBzlib\fR
support for this option to work, otherwise it will output an error.
.IP \fB\-uncompress\fR 4
.IX Item "-uncompress"
Uncompress a CMS \fBCompressedData\fR type and output the content. OpenSSL must be
compiled with \fBzlib\fR support for this option to work, otherwise it will
output an error.
.IP \fB\-EncryptedData_encrypt\fR 4
.IX Item "-EncryptedData_encrypt"
Encrypt content using supplied symmetric key and algorithm using a CMS
\&\fBEncryptedData\fR type and output the content.
.IP \fB\-sign_receipt\fR 4
.IX Item "-sign_receipt"
Generate and output a signed receipt for the supplied message. The input
message \fBmust\fR contain a signed receipt request. Functionality is otherwise
similar to the \fB\-sign\fR operation.
.IP "\fB\-verify_receipt receipt\fR" 4
.IX Item "-verify_receipt receipt"
Verify a signed receipt in filename \fBreceipt\fR. The input message \fBmust\fR
contain the original receipt request. Functionality is otherwise similar
to the \fB\-verify\fR operation.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
The input message to be encrypted or signed or the message to be decrypted
or verified.
.IP "\fB\-inform SMIME|PEM|DER\fR" 4
.IX Item "-inform SMIME|PEM|DER"
This specifies the input format for the CMS structure. The default
is \fBSMIME\fR which reads an S/MIME format message. \fBPEM\fR and \fBDER\fR
format change this to expect PEM and DER format CMS structures
instead. This currently only affects the input format of the CMS
structure, if no CMS structure is being input (for example with
\&\fB\-encrypt\fR or \fB\-sign\fR) this option has no effect.
.IP "\fB\-rctform SMIME|PEM|DER\fR" 4
.IX Item "-rctform SMIME|PEM|DER"
Specify the format for a signed receipt for use with the \fB\-receipt_verify\fR
operation.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
The message text that has been decrypted or verified or the output MIME
format message that has been signed or verified.
.IP "\fB\-outform SMIME|PEM|DER\fR" 4
.IX Item "-outform SMIME|PEM|DER"
This specifies the output format for the CMS structure. The default
is \fBSMIME\fR which writes an S/MIME format message. \fBPEM\fR and \fBDER\fR
format change this to write PEM and DER format CMS structures
instead. This currently only affects the output format of the CMS
structure, if no CMS structure is being output (for example with
\&\fB\-verify\fR or \fB\-decrypt\fR) this option has no effect.
.IP "\fB\-stream \-indef \-noindef\fR" 4
.IX Item "-stream -indef -noindef"
The \fB\-stream\fR and \fB\-indef\fR options are equivalent and enable streaming I/O
for encoding operations. This permits single pass processing of data without
the need to hold the entire contents in memory, potentially supporting very
large files. Streaming is automatically set for S/MIME signing with detached
data if the output format is \fBSMIME\fR it is currently off by default for all
other operations.
.IP \fB\-noindef\fR 4
.IX Item "-noindef"
Disable streaming I/O where it would produce and indefinite length constructed
encoding. This option currently has no effect. In future streaming will be
enabled by default on all relevant operations and this option will disable it.
.IP "\fB\-content filename\fR" 4
.IX Item "-content filename"
This specifies a file containing the detached content, this is only
useful with the \fB\-verify\fR command. This is only usable if the CMS
structure is using the detached signature form where the content is
not included. This option will override any content if the input format
is S/MIME and it uses the multipart/signed MIME content type.
.IP \fB\-text\fR 4
.IX Item "-text"
This option adds plain text (text/plain) MIME headers to the supplied
message if encrypting or signing. If decrypting or verifying it strips
off text headers: if the decrypted or verified message is not of MIME
type text/plain then an error occurs.
.IP \fB\-noout\fR 4
.IX Item "-noout"
For the \fB\-cmsout\fR operation do not output the parsed CMS structure. This
is useful when combined with the \fB\-print\fR option or if the syntax of the CMS
structure is being checked.
.IP \fB\-print\fR 4
.IX Item "-print"
For the \fB\-cmsout\fR operation print out all fields of the CMS structure. This
is mainly useful for testing purposes.
.IP "\fB\-CAfile file\fR" 4
.IX Item "-CAfile file"
A file containing trusted CA certificates, only used with \fB\-verify\fR.
.IP "\fB\-CApath dir\fR" 4
.IX Item "-CApath dir"
A directory containing trusted CA certificates, only used with
\&\fB\-verify\fR. This directory must be a standard certificate directory: that
is a hash of each subject name (using \fBx509 \-hash\fR) should be linked
to each certificate.
.IP \fB\-no\-CAfile\fR 4
.IX Item "-no-CAfile"
Do not load the trusted CA certificates from the default file location
.IP \fB\-no\-CApath\fR 4
.IX Item "-no-CApath"
Do not load the trusted CA certificates from the default directory location
.IP "\fB\-md digest\fR" 4
.IX Item "-md digest"
Digest algorithm to use when signing or resigning. If not present then the
default digest algorithm for the signing key will be used (usually SHA1).
.IP \fB\-\fR\f(BIcipher\fR 4
.IX Item "-cipher"
The encryption algorithm to use. For example triple DES (168 bits) \- \fB\-des3\fR
or 256 bit AES \- \fB\-aes256\fR. Any standard algorithm name (as used by the
\&\fBEVP_get_cipherbyname()\fR function) can also be used preceded by a dash, for
example \fB\-aes\-128\-cbc\fR. See \fBenc\fR\|(1) for a list of ciphers
supported by your version of OpenSSL.
.Sp
If not specified triple DES is used. Only used with \fB\-encrypt\fR and
\&\fB\-EncryptedData_create\fR commands.
.IP \fB\-nointern\fR 4
.IX Item "-nointern"
When verifying a message normally certificates (if any) included in
the message are searched for the signing certificate. With this option
only the certificates specified in the \fB\-certfile\fR option are used.
The supplied certificates can still be used as untrusted CAs however.
.IP \fB\-noverify\fR 4
.IX Item "-noverify"
Do not verify the signers certificate of a signed message.
.IP \fB\-nocerts\fR 4
.IX Item "-nocerts"
When signing a message the signer's certificate is normally included
with this option it is excluded. This will reduce the size of the
signed message but the verifier must have a copy of the signers certificate
available locally (passed using the \fB\-certfile\fR option for example).
.IP \fB\-noattr\fR 4
.IX Item "-noattr"
Normally when a message is signed a set of attributes are included which
include the signing time and supported symmetric algorithms. With this
option they are not included.
.IP \fB\-nosmimecap\fR 4
.IX Item "-nosmimecap"
Exclude the list of supported algorithms from signed attributes, other options
such as signing time and content type are still included.
.IP \fB\-binary\fR 4
.IX Item "-binary"
Normally the input message is converted to "canonical" format which is
effectively using CR and LF as end of line: as required by the S/MIME
specification. When this option is present no translation occurs. This
is useful when handling binary data which may not be in MIME format.
.IP \fB\-crlfeol\fR 4
.IX Item "-crlfeol"
Normally the output file uses a single \fBLF\fR as end of line. When this
option is present \fBCRLF\fR is used instead.
.IP \fB\-asciicrlf\fR 4
.IX Item "-asciicrlf"
When signing use ASCII CRLF format canonicalisation. This strips trailing
whitespace from all lines, deletes trailing blank lines at EOF and sets
the encapsulated content type. This option is normally used with detached
content and an output signature format of DER. This option is not normally
needed when verifying as it is enabled automatically if the encapsulated
content format is detected.
.IP \fB\-nodetach\fR 4
.IX Item "-nodetach"
When signing a message use opaque signing: this form is more resistant
to translation by mail relays but it cannot be read by mail agents that
do not support S/MIME.  Without this option cleartext signing with
the MIME type multipart/signed is used.
.IP "\fB\-certfile file\fR" 4
.IX Item "-certfile file"
Allows additional certificates to be specified. When signing these will
be included with the message. When verifying these will be searched for
the signers certificates. The certificates should be in PEM format.
.IP "\fB\-certsout file\fR" 4
.IX Item "-certsout file"
Any certificates contained in the message are written to \fBfile\fR.
.IP "\fB\-signer file\fR" 4
.IX Item "-signer file"
A signing certificate when signing or resigning a message, this option can be
used multiple times if more than one signer is required. If a message is being
verified then the signers certificates will be written to this file if the
verification was successful.
.IP "\fB\-recip file\fR" 4
.IX Item "-recip file"
When decrypting a message this specifies the recipients certificate. The
certificate must match one of the recipients of the message or an error
occurs.
.Sp
When encrypting a message this option may be used multiple times to specify
each recipient. This form \fBmust\fR be used if customised parameters are
required (for example to specify RSA-OAEP).
.Sp
Only certificates carrying RSA, Diffie-Hellman or EC keys are supported by this
option.
.IP \fB\-keyid\fR 4
.IX Item "-keyid"
Use subject key identifier to identify certificates instead of issuer name and
serial number. The supplied certificate \fBmust\fR include a subject key
identifier extension. Supported by \fB\-sign\fR and \fB\-encrypt\fR options.
.IP "\fB\-receipt_request_all\fR, \fB\-receipt_request_first\fR" 4
.IX Item "-receipt_request_all, -receipt_request_first"
For \fB\-sign\fR option include a signed receipt request. Indicate requests should
be provided by all recipient or first tier recipients (those mailed directly
and not from a mailing list). Ignored it \fB\-receipt_request_from\fR is included.
.IP "\fB\-receipt_request_from emailaddress\fR" 4
.IX Item "-receipt_request_from emailaddress"
For \fB\-sign\fR option include a signed receipt request. Add an explicit email
address where receipts should be supplied.
.IP "\fB\-receipt_request_to emailaddress\fR" 4
.IX Item "-receipt_request_to emailaddress"
Add an explicit email address where signed receipts should be sent to. This
option \fBmust\fR but supplied if a signed receipt it requested.
.IP \fB\-receipt_request_print\fR 4
.IX Item "-receipt_request_print"
For the \fB\-verify\fR operation print out the contents of any signed receipt
requests.
.IP "\fB\-secretkey key\fR" 4
.IX Item "-secretkey key"
Specify symmetric key to use. The key must be supplied in hex format and be
consistent with the algorithm used. Supported by the \fB\-EncryptedData_encrypt\fR
\&\fB\-EncryptedData_decrypt\fR, \fB\-encrypt\fR and \fB\-decrypt\fR options. When used
with \fB\-encrypt\fR or \fB\-decrypt\fR the supplied key is used to wrap or unwrap the
content encryption key using an AES key in the \fBKEKRecipientInfo\fR type.
.IP "\fB\-secretkeyid id\fR" 4
.IX Item "-secretkeyid id"
The key identifier for the supplied symmetric key for \fBKEKRecipientInfo\fR type.
This option \fBmust\fR be present if the \fB\-secretkey\fR option is used with
\&\fB\-encrypt\fR. With \fB\-decrypt\fR operations the \fBid\fR is used to locate the
relevant key if it is not supplied then an attempt is used to decrypt any
\&\fBKEKRecipientInfo\fR structures.
.IP "\fB\-econtent_type type\fR" 4
.IX Item "-econtent_type type"
Set the encapsulated content type to \fBtype\fR if not supplied the \fBData\fR type
is used. The \fBtype\fR argument can be any valid OID name in either text or
numerical format.
.IP "\fB\-inkey file\fR" 4
.IX Item "-inkey file"
The private key to use when signing or decrypting. This must match the
corresponding certificate. If this option is not specified then the
private key must be included in the certificate file specified with
the \fB\-recip\fR or \fB\-signer\fR file. When signing this option can be used
multiple times to specify successive keys.
.IP "\fB\-keyopt name:opt\fR" 4
.IX Item "-keyopt name:opt"
For signing and encryption this option can be used multiple times to
set customised parameters for the preceding key or certificate. It can
currently be used to set RSA-PSS for signing, RSA-OAEP for encryption
or to modify default parameters for ECDH.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
The private key password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP \fBcert.pem...\fR 4
.IX Item "cert.pem..."
One or more certificates of message recipients: used when encrypting
a message.
.IP "\fB\-to, \-from, \-subject\fR" 4
.IX Item "-to, -from, -subject"
The relevant mail headers. These are included outside the signed
portion of a message so they may be included manually. If signing
then many S/MIME mail clients check the signers certificate's email
address matches that specified in the From: address.
.IP "\fB\-attime\fR, \fB\-check_ss_sig\fR, \fB\-crl_check\fR, \fB\-crl_check_all\fR, \fB\-explicit_policy\fR, \fB\-extended_crl\fR, \fB\-ignore_critical\fR, \fB\-inhibit_any\fR, \fB\-inhibit_map\fR, \fB\-no_alt_chains\fR, \fB\-no_check_time\fR, \fB\-partial_chain\fR, \fB\-policy\fR, \fB\-policy_check\fR, \fB\-policy_print\fR, \fB\-purpose\fR, \fB\-suiteB_128\fR, \fB\-suiteB_128_only\fR, \fB\-suiteB_192\fR, \fB\-trusted_first\fR, \fB\-use_deltas\fR, \fB\-auth_level\fR, \fB\-verify_depth\fR, \fB\-verify_email\fR, \fB\-verify_hostname\fR, \fB\-verify_ip\fR, \fB\-verify_name\fR, \fB\-x509_strict\fR" 4
.IX Item "-attime, -check_ss_sig, -crl_check, -crl_check_all, -explicit_policy, -extended_crl, -ignore_critical, -inhibit_any, -inhibit_map, -no_alt_chains, -no_check_time, -partial_chain, -policy, -policy_check, -policy_print, -purpose, -suiteB_128, -suiteB_128_only, -suiteB_192, -trusted_first, -use_deltas, -auth_level, -verify_depth, -verify_email, -verify_hostname, -verify_ip, -verify_name, -x509_strict"
Set various certificate chain validation options. See the
\&\fBverify\fR\|(1) manual page for details.
.SH NOTES
.IX Header "NOTES"
The MIME message must be sent without any blank lines between the
headers and the output. Some mail programs will automatically add
a blank line. Piping the mail directly to sendmail is one way to
achieve the correct format.
.PP
The supplied message to be signed or encrypted must include the
necessary MIME headers or many S/MIME clients won't display it
properly (if at all). You can use the \fB\-text\fR option to automatically
add plain text headers.
.PP
A "signed and encrypted" message is one where a signed message is
then encrypted. This can be produced by encrypting an already signed
message: see the examples section.
.PP
This version of the program only allows one signer per message but it
will verify multiple signers on received messages. Some S/MIME clients
choke if a message contains multiple signers. It is possible to sign
messages "in parallel" by signing an already signed message.
.PP
The options \fB\-encrypt\fR and \fB\-decrypt\fR reflect common usage in S/MIME
clients. Strictly speaking these process CMS enveloped data: CMS
encrypted data is used for other purposes.
.PP
The \fB\-resign\fR option uses an existing message digest when adding a new
signer. This means that attributes must be present in at least one existing
signer using the same message digest or this operation will fail.
.PP
The \fB\-stream\fR and \fB\-indef\fR options enable streaming I/O support.
As a result the encoding is BER using indefinite length constructed encoding
and no longer DER. Streaming is supported for the \fB\-encrypt\fR operation and the
\&\fB\-sign\fR operation if the content is not detached.
.PP
Streaming is always used for the \fB\-sign\fR operation with detached data but
since the content is no longer part of the CMS structure the encoding
remains DER.
.PP
If the \fB\-decrypt\fR option is used without a recipient certificate then an
attempt is made to locate the recipient by trying each potential recipient
in turn using the supplied private key. To thwart the MMA attack
(Bleichenbacher's attack on PKCS #1 v1.5 RSA padding) all recipients are
tried whether they succeed or not and if no recipients match the message
is "decrypted" using a random key which will typically output garbage.
The \fB\-debug_decrypt\fR option can be used to disable the MMA attack protection
and return an error if no recipient can be found: this option should be used
with caution. For a fuller description see \fBCMS_decrypt\fR\|(3)).
.SH "EXIT CODES"
.IX Header "EXIT CODES"
.IP 0 4
The operation was completely successfully.
.IP 1 4
.IX Item "1"
An error occurred parsing the command options.
.IP 2 4
.IX Item "2"
One of the input files could not be read.
.IP 3 4
.IX Item "3"
An error occurred creating the CMS file or when reading the MIME
message.
.IP 4 4
.IX Item "4"
An error occurred decrypting or verifying the message.
.IP 5 4
.IX Item "5"
The message was verified correctly but an error occurred writing out
the signers certificates.
.SH "COMPATIBILITY WITH PKCS#7 format."
.IX Header "COMPATIBILITY WITH PKCS#7 format."
The \fBsmime\fR utility can only process the older \fBPKCS#7\fR format. The \fBcms\fR
utility supports Cryptographic Message Syntax format. Use of some features
will result in messages which cannot be processed by applications which only
support the older format. These are detailed below.
.PP
The use of the \fB\-keyid\fR option with \fB\-sign\fR or \fB\-encrypt\fR.
.PP
The \fB\-outform PEM\fR option uses different headers.
.PP
The \fB\-compress\fR option.
.PP
The \fB\-secretkey\fR option when used with \fB\-encrypt\fR.
.PP
The use of PSS with \fB\-sign\fR.
.PP
The use of OAEP or non-RSA keys with \fB\-encrypt\fR.
.PP
Additionally the \fB\-EncryptedData_create\fR and \fB\-data_create\fR type cannot
be processed by the older \fBsmime\fR command.
.SH EXAMPLES
.IX Header "EXAMPLES"
Create a cleartext signed message:
.PP
.Vb 2
\& openssl cms \-sign \-in message.txt \-text \-out mail.msg \e
\&        \-signer mycert.pem
.Ve
.PP
Create an opaque signed message
.PP
.Vb 2
\& openssl cms \-sign \-in message.txt \-text \-out mail.msg \-nodetach \e
\&        \-signer mycert.pem
.Ve
.PP
Create a signed message, include some additional certificates and
read the private key from another file:
.PP
.Vb 2
\& openssl cms \-sign \-in in.txt \-text \-out mail.msg \e
\&        \-signer mycert.pem \-inkey mykey.pem \-certfile mycerts.pem
.Ve
.PP
Create a signed message with two signers, use key identifier:
.PP
.Vb 2
\& openssl cms \-sign \-in message.txt \-text \-out mail.msg \e
\&        \-signer mycert.pem \-signer othercert.pem \-keyid
.Ve
.PP
Send a signed message under Unix directly to sendmail, including headers:
.PP
.Vb 3
\& openssl cms \-sign \-in in.txt \-text \-signer mycert.pem \e
\&        \-from <EMAIL> \-to someone@somewhere \e
\&        \-subject "Signed message" | sendmail someone@somewhere
.Ve
.PP
Verify a message and extract the signer's certificate if successful:
.PP
.Vb 1
\& openssl cms \-verify \-in mail.msg \-signer user.pem \-out signedtext.txt
.Ve
.PP
Send encrypted mail using triple DES:
.PP
.Vb 3
\& openssl cms \-encrypt \-in in.txt \-from <EMAIL> \e
\&        \-to someone@somewhere \-subject "Encrypted message" \e
\&        \-des3 user.pem \-out mail.msg
.Ve
.PP
Sign and encrypt mail:
.PP
.Vb 4
\& openssl cms \-sign \-in ml.txt \-signer my.pem \-text \e
\&        | openssl cms \-encrypt \-out mail.msg \e
\&        \-from <EMAIL> \-to someone@somewhere \e
\&        \-subject "Signed and Encrypted message" \-des3 user.pem
.Ve
.PP
Note: the encryption command does not include the \fB\-text\fR option because the
message being encrypted already has MIME headers.
.PP
Decrypt mail:
.PP
.Vb 1
\& openssl cms \-decrypt \-in mail.msg \-recip mycert.pem \-inkey key.pem
.Ve
.PP
The output from Netscape form signing is a PKCS#7 structure with the
detached signature format. You can use this program to verify the
signature by line wrapping the base64 encoded structure and surrounding
it with:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN PKCS7\-\-\-\-\-
\& \-\-\-\-\-END PKCS7\-\-\-\-\-
.Ve
.PP
and using the command,
.PP
.Vb 1
\& openssl cms \-verify \-inform PEM \-in signature.pem \-content content.txt
.Ve
.PP
alternatively you can base64 decode the signature and use
.PP
.Vb 1
\& openssl cms \-verify \-inform DER \-in signature.der \-content content.txt
.Ve
.PP
Create an encrypted message using 128 bit Camellia:
.PP
.Vb 1
\& openssl cms \-encrypt \-in plain.txt \-camellia128 \-out mail.msg cert.pem
.Ve
.PP
Add a signer to an existing message:
.PP
.Vb 1
\& openssl cms \-resign \-in mail.msg \-signer newsign.pem \-out mail2.msg
.Ve
.PP
Sign mail using RSA-PSS:
.PP
.Vb 2
\& openssl cms \-sign \-in message.txt \-text \-out mail.msg \e
\&        \-signer mycert.pem \-keyopt rsa_padding_mode:pss
.Ve
.PP
Create encrypted mail using RSA-OAEP:
.PP
.Vb 2
\& openssl cms \-encrypt \-in plain.txt \-out mail.msg \e
\&        \-recip cert.pem \-keyopt rsa_padding_mode:oaep
.Ve
.PP
Use SHA256 KDF with an ECDH certificate:
.PP
.Vb 2
\& openssl cms \-encrypt \-in plain.txt \-out mail.msg \e
\&        \-recip ecdhcert.pem \-keyopt ecdh_kdf_md:sha256
.Ve
.SH BUGS
.IX Header "BUGS"
The MIME parser isn't very clever: it seems to handle most messages that I've
thrown at it but it may choke on others.
.PP
The code currently will only write out the signer's certificate to a file: if
the signer has a separate encryption certificate this must be manually
extracted. There should be some heuristic that determines the correct
encryption certificate.
.PP
Ideally a database should be maintained of a certificates for each email
address.
.PP
The code doesn't currently take note of the permitted symmetric encryption
algorithms as supplied in the SMIMECapabilities signed attribute. this means the
user has to manually include the correct encryption algorithm. It should store
the list of permitted ciphers in a database and only use those.
.PP
No revocation checking is done on the signer's certificate.
.PP
The \fB\-binary\fR option does not work correctly when processing text input which
(contrary to the S/MIME specification) uses LF rather than CRLF line endings.
.SH HISTORY
.IX Header "HISTORY"
The use of multiple \fB\-signer\fR options and the \fB\-resign\fR command were first
added in OpenSSL 1.0.0.
.PP
The \fBkeyopt\fR option was added in OpenSSL 1.0.2.
.PP
Support for RSA-OAEP and RSA-PSS was added in OpenSSL 1.0.2.
.PP
The use of non-RSA keys with \fB\-encrypt\fR and \fB\-decrypt\fR
was added in OpenSSL 1.0.2.
.PP
The \-no_alt_chains option was added in OpenSSL 1.0.2b.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
