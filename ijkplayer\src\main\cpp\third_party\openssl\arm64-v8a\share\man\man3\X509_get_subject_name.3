.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_GET_SUBJECT_NAME 3"
.TH X509_GET_SUBJECT_NAME 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_get_subject_name, X509_set_subject_name, X509_get_issuer_name,
X509_set_issuer_name, X509_REQ_get_subject_name, X509_REQ_set_subject_name,
X509_CRL_get_issuer, X509_CRL_set_issuer_name \- get and set issuer or
subject names
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& X509_NAME *X509_get_subject_name(const X509 *x);
\& int X509_set_subject_name(X509 *x, X509_NAME *name);
\&
\& X509_NAME *X509_get_issuer_name(const X509 *x);
\& int X509_set_issuer_name(X509 *x, X509_NAME *name);
\&
\& X509_NAME *X509_REQ_get_subject_name(const X509_REQ *req);
\& int X509_REQ_set_subject_name(X509_REQ *req, X509_NAME *name);
\&
\& X509_NAME *X509_CRL_get_issuer(const X509_CRL *crl);
\& int X509_CRL_set_issuer_name(X509_CRL *x, X509_NAME *name);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_get_subject_name()\fR returns the subject name of certificate \fBx\fR. The
returned value is an internal pointer which \fBMUST NOT\fR be freed.
.PP
\&\fBX509_set_subject_name()\fR sets the issuer name of certificate \fBx\fR to
\&\fBname\fR. The \fBname\fR parameter is copied internally and should be freed
up when it is no longer needed.
.PP
\&\fBX509_get_issuer_name()\fR and \fBX509_set_issuer_name()\fR are identical to
\&\fBX509_get_subject_name()\fR and \fBX509_set_subject_name()\fR except the get and
set the issuer name of \fBx\fR.
.PP
Similarly \fBX509_REQ_get_subject_name()\fR, \fBX509_REQ_set_subject_name()\fR,
\&\fBX509_CRL_get_issuer()\fR and \fBX509_CRL_set_issuer_name()\fR get or set the subject
or issuer names of certificate requests of CRLs respectively.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_get_subject_name()\fR, \fBX509_get_issuer_name()\fR, \fBX509_REQ_get_subject_name()\fR
and \fBX509_CRL_get_issuer()\fR return an \fBX509_NAME\fR pointer.
.PP
\&\fBX509_set_subject_name()\fR, \fBX509_set_issuer_name()\fR, \fBX509_REQ_set_subject_name()\fR
and \fBX509_CRL_set_issuer_name()\fR return 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3),
\&\fBERR_get_error\fR\|(3), \fBd2i_X509\fR\|(3)
\&\fBX509_CRL_get0_by_serial\fR\|(3),
\&\fBX509_get0_signature\fR\|(3),
\&\fBX509_get_ext_d2i\fR\|(3),
\&\fBX509_get_extension_flags\fR\|(3),
\&\fBX509_get_pubkey\fR\|(3),
\&\fBX509_NAME_add_entry_by_txt\fR\|(3),
\&\fBX509_NAME_ENTRY_get_object\fR\|(3),
\&\fBX509_NAME_get_index_by_NID\fR\|(3),
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBX509_new\fR\|(3),
\&\fBX509_sign\fR\|(3),
\&\fBX509V3_get_d2i\fR\|(3),
\&\fBX509_verify_cert\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBX509_REQ_get_subject_name()\fR is a function in OpenSSL 1.1.0 and a macro in
earlier versions.
.PP
\&\fBX509_CRL_get_issuer()\fR is a function in OpenSSL 1.1.0. It was previously
added in OpenSSL 1.0.0 as a macro.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
