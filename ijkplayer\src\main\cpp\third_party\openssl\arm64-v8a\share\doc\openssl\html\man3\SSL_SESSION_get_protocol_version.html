<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_SESSION_get_protocol_version</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_SESSION_get_protocol_version, SSL_SESSION_set_protocol_version - get and set the session protocol version</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

int SSL_SESSION_get_protocol_version(const SSL_SESSION *s);
int SSL_SESSION_set_protocol_version(SSL_SESSION *s, int version);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_SESSION_get_protocol_version() returns the protocol version number used by session <b>s</b>.</p>

<p>SSL_SESSION_set_protocol_version() sets the protocol version associated with the SSL_SESSION object <b>s</b> to the value <b>version</b>. This value should be a version constant such as <b>TLS1_3_VERSION</b> etc. For example, this could be used to set up a session based PSK (see <a href="../man3/SSL_CTX_set_psk_use_session_callback.html">SSL_CTX_set_psk_use_session_callback(3)</a>).</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_SESSION_get_protocol_version() returns a number indicating the protocol version used for the session; this number matches the constants <i>e.g.</i> <b>TLS1_VERSION</b>, <b>TLS1_2_VERSION</b> or <b>TLS1_3_VERSION</b>.</p>

<p>Note that the SSL_SESSION_get_protocol_version() function does <b>not</b> perform a null check on the provided session <b>s</b> pointer.</p>

<p>SSL_SESSION_set_protocol_version() returns 1 on success or 0 on failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CTX_set_psk_use_session_callback.html">SSL_CTX_set_psk_use_session_callback(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The SSL_SESSION_get_protocol_version() function was added in OpenSSL 1.1.0. The SSL_SESSION_set_protocol_version() function was added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2001-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


