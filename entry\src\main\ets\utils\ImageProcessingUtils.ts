/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the 'License');
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an 'AS IS' BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import image from '@ohos.multimedia.image';
import Logger from '../model/Logger';

const TAG: string = 'ImageProcessingUtils';

/**
 * 图像处理配置接口
 */
export interface ImageProcessingConfig {
  width?: number;           // 图像宽度，默认1920
  height?: number;          // 图像高度，默认1080
  pixelFormat?: image.PixelMapFormat; // 输出像素格式，默认RGBA_8888
  editable?: boolean;       // 是否可编辑，默认false
  saveToAppStorage?: boolean; // 是否保存到AppStorage，默认true
  appStorageKey?: string;   // AppStorage键名，默认'previewPixelMap'
}

/**
 * 处理结果接口
 */
export interface ProcessingResult {
  success: boolean;
  nv21Buffer?: ArrayBuffer;
  pixelMap?: image.PixelMap;
  error?: string;
}

/**
 * 图像处理工具类
 * 封装了从YUV420P到PixelMap的完整转换流程
 */
export class ImageProcessingUtils {
  
  /**
   * 默认配置
   */
  private static readonly DEFAULT_CONFIG: Required<ImageProcessingConfig> = {
    width: 1920,
    height: 1080,
    pixelFormat: image.PixelMapFormat.RGBA_8888,
    editable: false,
    saveToAppStorage: true,
    appStorageKey: 'previewPixelMap'
  };

  /**
   * 将YUV420P格式的ArrayBuffer转换为NV21格式
   * 
   * @param yuv420pBuffer YUV420P格式的输入缓冲区
   * @param width 图像宽度
   * @param height 图像高度
   * @returns NV21格式的ArrayBuffer，失败返回null
   */
  public static convertYUV420PToNV21(
    yuv420pBuffer: ArrayBuffer, 
    // width: number = 1920,
    // height: number = 1080
  width: number = 1920,
  height: number = 1080
  ): ArrayBuffer | null {
    try {
      Logger.info(TAG, `开始YUV420P到NV21转换，尺寸: ${width}x${height}`);
      
      // 计算各平面大小
      const ySize = width * height;
      const uvSize = ySize / 4;
      const totalSize = ySize + uvSize * 2;

      // 验证输入缓冲区大小
      if (yuv420pBuffer.byteLength !== totalSize) {
        Logger.error(TAG, `无效的YUV420P缓冲区大小: ${yuv420pBuffer.byteLength}, 期望: ${totalSize}`);
        return null;
      }

      // 创建输出缓冲区
      const nv21Buffer = new ArrayBuffer(totalSize);
      const nv21View = new Uint8Array(nv21Buffer);
      const yuv420pView = new Uint8Array(yuv420pBuffer);

      // YUV420P 格式中的平面分布
      const yPlane = yuv420pView.subarray(0, ySize);                // Y 平面
      const uPlane = yuv420pView.subarray(ySize, ySize + uvSize);   // U 平面
      const vPlane = yuv420pView.subarray(ySize + uvSize);          // V 平面

      // 复制 Y 平面 (两种格式的 Y 平面相同)
      nv21View.set(yPlane, 0);

      // 交错复制 V 和 U 平面到 NV21 格式 (NV21 中是 VU 交错)
      const vuPlane = nv21View.subarray(ySize);
      for (let i = 0; i < uvSize; i++) {
        vuPlane[i * 2] = vPlane[i];     // V 在偶数位置
        vuPlane[i * 2 + 1] = uPlane[i]; // U 在奇数位置
      }

      Logger.info(TAG, `YUV420P到NV21转换成功，输出大小: ${nv21Buffer.byteLength}`);
      return nv21Buffer;
      
    } catch (error) {
      Logger.error(TAG, `YUV420P到NV21转换失败: ${JSON.stringify(error)}`);
      return null;
    }
  }

  /**
   * 将ArrayBuffer转换为PixelMap
   * 
   * @param buffer 输入的ArrayBuffer（NV21格式）
   * @param config 转换配置
   * @returns PixelMap对象，失败返回null
   */
  public static async convertBufferToPixelMap(
    buffer: ArrayBuffer, 
    config: Partial<ImageProcessingConfig> = {}
  ): Promise<image.PixelMap | null> {
    try {
      // 合并配置
      const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
      
      Logger.info(TAG, `开始Buffer到PixelMap转换，配置: ${JSON.stringify(finalConfig)}`);
      let sourceOptions: image.SourceOptions = {
        // sourceDensity: 0,
        sourceDensity: 120,
        // sourcePixelFormat: image.PixelMapFormat.NV21, // NV21
        sourcePixelFormat: 8, // NV21
        // sourcePixelFormat: image.PixelMapFormat.RGBA_8888, // NV21
        sourceSize: {
          // width: 1920,
          // height: 1080
          width: 1920,
          height: 1080
        }
      }
      // 创建ImageSource
      const imageSource = image.createImageSource(buffer,sourceOptions);
      if (!imageSource) {
        Logger.error(TAG, '创建ImageSource失败');
        return null;
      }else{
        Logger.info(TAG, '创建ImageSource成功');
      }

      // 设置解码选项
      const decodingOptions: image.DecodingOptions = {
        editable: finalConfig.editable,
        desiredPixelFormat: finalConfig.pixelFormat
      };

      // 设置初始化选项
      const opts: image.InitializationOptions = {
        editable: finalConfig.editable,
        pixelFormat: finalConfig.pixelFormat,
        size: {
          width: finalConfig.width,
          height: finalConfig.height
        }
      };

      Logger.info(TAG, '开始创建PixelMap');
      let defaultOpts: image.InitializationOptions = {
        editable: false,
        pixelFormat: image.PixelMapFormat.RGBA_8888,
        // pixelFormat: image.PixelMapFormat.NV21,
        size: {
          width: 1920,
          height: 1080
        }
      }
      const pixelMap = await imageSource.createPixelMap(defaultOpts);
      
      if (pixelMap) {
        Logger.info(TAG, `PixelMap创建成功，格式: ${finalConfig.pixelFormat}`);
        Logger.info(TAG, `PixelMap属性 - 可编辑: ${pixelMap.isEditable}`);
      } else {
        Logger.error(TAG, 'PixelMap创建失败');
        await imageSource.release();
        return null;
      }

      // 释放ImageSource资源
      await imageSource.release();
      return pixelMap;
      
    } catch (error) {
      Logger.error(TAG, `Buffer到PixelMap转换失败: ${JSON.stringify(error)}`);
      return null;
    }
  }

  /**
   * 完整的图像处理流程：YUV420P -> NV21 -> PixelMap -> AppStorage
   * 
   * @param yuv420pBuffer YUV420P格式的输入缓冲区
   * @param config 处理配置
   * @returns 处理结果
   */
  public static async processImageComplete(
    yuv420pBuffer: ArrayBuffer,
    config: Partial<ImageProcessingConfig> = {}
  ): Promise<ProcessingResult> {
    try {
      // 合并配置
      const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
      
      Logger.info(TAG, `开始完整图像处理流程，输入大小: ${yuv420pBuffer.byteLength}`);

      // 步骤1: YUV420P转换为NV21
      const nv21Buffer = this.convertYUV420PToNV21(
        yuv420pBuffer, 
        finalConfig.width, 
        finalConfig.height
      );
      Logger.info(TAG, `开始完整图像处理流程，输出大小: ${nv21Buffer.byteLength}`);

      if (!nv21Buffer) {
        return {
          success: false,
          error: 'YUV420P到NV21转换失败'
        };
      }

      // 步骤2: NV21转换为PixelMap
      const pixelMap = await this.convertBufferToPixelMap(nv21Buffer, finalConfig);

      if (!pixelMap) {
        return {
          success: false,
          nv21Buffer,
          error: 'NV21到PixelMap转换失败'
        };
      }

      // 步骤3: 保存到AppStorage（如果需要）
      if (finalConfig.saveToAppStorage) {
        try {
          AppStorage.setOrCreate(finalConfig.appStorageKey, pixelMap);
          Logger.info(TAG, `PixelMap已保存到AppStorage，键名: ${finalConfig.appStorageKey}`);
        } catch (error) {
          Logger.warn(TAG, `保存到AppStorage失败: ${JSON.stringify(error)}`);
        }
      }

      Logger.info(TAG, '完整图像处理流程成功完成');
      return {
        success: true,
        nv21Buffer,
        pixelMap
      };

    } catch (error) {
      Logger.error(TAG, `完整图像处理流程失败: ${JSON.stringify(error)}`);
      return {
        success: false,
        error: `处理失败: ${JSON.stringify(error)}`
      };
    }
  }

  /**
   * 快速处理方法（使用默认配置）
   * 
   * @param yuv420pBuffer YUV420P格式的输入缓冲区
   * @returns PixelMap对象，失败返回null
   */
  public static async quickProcess(yuv420pBuffer: ArrayBuffer): Promise<image.PixelMap | null> {
    const result = await this.processImageComplete(yuv420pBuffer);
    return result.success ? result.pixelMap! : null;
  }

  /**
   * 仅转换格式，不创建PixelMap
   * 
   * @param yuv420pBuffer YUV420P格式的输入缓冲区
   * @param width 图像宽度
   * @param height 图像高度
   * @returns NV21格式的ArrayBuffer
   */
  public static convertFormatOnly(
    yuv420pBuffer: ArrayBuffer,
    width: number = 1920,
    height: number = 1080
  ): ArrayBuffer | null {
    return this.convertYUV420PToNV21(yuv420pBuffer, width, height);
  }

  /**
   * 获取支持的像素格式列表
   */
  public static getSupportedPixelFormats(): image.PixelMapFormat[] {
    return [
      image.PixelMapFormat.RGBA_8888,
      image.PixelMapFormat.RGB_565,
      image.PixelMapFormat.NV21,
      image.PixelMapFormat.NV12
    ];
  }

  /**
   * 验证输入参数
   */
  public static validateInput(
    buffer: ArrayBuffer, 
    width: number, 
    height: number
  ): { valid: boolean; error?: string } {
    if (!buffer || buffer.byteLength === 0) {
      return { valid: false, error: '输入缓冲区为空' };
    }

    if (width <= 0 || height <= 0) {
      return { valid: false, error: '图像尺寸无效' };
    }

    const expectedSize = width * height * 1.5; // YUV420P格式
    if (buffer.byteLength !== expectedSize) {
      return { 
        valid: false, 
        error: `缓冲区大小不匹配，期望: ${expectedSize}, 实际: ${buffer.byteLength}` 
      };
    }

    return { valid: true };
  }
}
