<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BIO_parse_hostserv</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BIO_hostserv_priorities, BIO_parse_hostserv - utility routines to parse a standard host and service string</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bio.h&gt;

enum BIO_hostserv_priorities {
    BIO_PARSE_PRIO_HOST, BIO_PARSE_PRIO_SERV
};
int BIO_parse_hostserv(const char *hostserv, char **host, char **service,
                       enum BIO_hostserv_priorities hostserv_prio);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>BIO_parse_hostserv() will parse the information given in <b>hostserv</b>, create strings with the hostname and service name and give those back via <b>host</b> and <b>service</b>. Those will need to be freed after they are used. <b>hostserv_prio</b> helps determine if <b>hostserv</b> shall be interpreted primarily as a hostname or a service name in ambiguous cases.</p>

<p>The syntax the BIO_parse_hostserv() recognises is:</p>

<pre><code>host + &#39;:&#39; + service
host + &#39;:&#39; + &#39;*&#39;
host + &#39;:&#39;
       &#39;:&#39; + service
&#39;*&#39;  + &#39;:&#39; + service
host
service</code></pre>

<p>The host part can be a name or an IP address. If it&#39;s a IPv6 address, it MUST be enclosed in brackets, such as &#39;[::1]&#39;.</p>

<p>The service part can be a service name or its port number.</p>

<p>The returned values will depend on the given <b>hostserv</b> string and <b>hostserv_prio</b>, as follows:</p>

<pre><code>host + &#39;:&#39; + service  =&gt; *host = &quot;host&quot;, *service = &quot;service&quot;
host + &#39;:&#39; + &#39;*&#39;      =&gt; *host = &quot;host&quot;, *service = NULL
host + &#39;:&#39;            =&gt; *host = &quot;host&quot;, *service = NULL
       &#39;:&#39; + service  =&gt; *host = NULL, *service = &quot;service&quot;
 &#39;*&#39; + &#39;:&#39; + service  =&gt; *host = NULL, *service = &quot;service&quot;

in case no &#39;:&#39; is present in the string, the result depends on
hostserv_prio, as follows:

when hostserv_prio == BIO_PARSE_PRIO_HOST
host                 =&gt; *host = &quot;host&quot;, *service untouched

when hostserv_prio == BIO_PARSE_PRIO_SERV
service              =&gt; *host untouched, *service = &quot;service&quot;</code></pre>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BIO_parse_hostserv() returns 1 on success or 0 on error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/BIO_ADDRINFO.html">BIO_ADDRINFO(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


