<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CMS_add0_cert</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>CMS_add0_cert, CMS_add1_cert, CMS_get1_certs, CMS_add0_crl, CMS_add1_crl, CMS_get1_crls - CMS certificate and CRL utility functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/cms.h&gt;

int CMS_add0_cert(CMS_ContentInfo *cms, X509 *cert);
int CMS_add1_cert(CMS_ContentInfo *cms, X509 *cert);
STACK_OF(X509) *CMS_get1_certs(CMS_ContentInfo *cms);

int CMS_add0_crl(CMS_ContentInfo *cms, X509_CRL *crl);
int CMS_add1_crl(CMS_ContentInfo *cms, X509_CRL *crl);
STACK_OF(X509_CRL) *CMS_get1_crls(CMS_ContentInfo *cms);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>CMS_add0_cert() and CMS_add1_cert() add certificate <i>cert</i> to <i>cms</i>. <i>cms</i> must be of type signed data or (authenticated) enveloped data. For signed data, such a certificate can be used when signing or verifying to fill in the signer certificate or to provide an extra CA certificate that may be needed for chain building in certificate validation.</p>

<p>CMS_get1_certs() returns all certificates in <i>cms</i>.</p>

<p>CMS_add0_crl() and CMS_add1_crl() add CRL <i>crl</i> to <i>cms</i>. <i>cms</i> must be of type signed data or (authenticated) enveloped data. For signed data, such a CRL may be used in certificate validation. It may be given both for inclusion when signing a CMS message and when verifying a signed CMS message.</p>

<p>CMS_get1_crls() returns all CRLs in <i>cms</i>.</p>

<h1 id="NOTES">NOTES</h1>

<p>The CMS_ContentInfo structure <i>cms</i> must be of type signed data or enveloped data or an error will be returned.</p>

<p>For signed data certificates and CRLs are added to the <i>certificates</i> and <i>crls</i> fields of SignedData structure. For enveloped data they are added to <b>OriginatorInfo</b>.</p>

<p>As the <i>0</i> implies CMS_add0_cert() adds <i>cert</i> internally to <i>cms</i> and it must not be freed up after the call as opposed to CMS_add1_cert() where <i>cert</i> must be freed up.</p>

<p>The same certificate or CRL must not be added to the same cms structure more than once.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>CMS_add0_cert(), CMS_add1_cert() and CMS_add0_crl() and CMS_add1_crl() return 1 for success and 0 for failure.</p>

<p>CMS_get1_certs() and CMS_get1_crls() return the STACK of certificates or CRLs or NULL if there are none or an error occurs. The only error which will occur in practice is if the <i>cms</i> type is invalid.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/CMS_sign.html">CMS_sign(3)</a>, <a href="../man3/CMS_encrypt.html">CMS_encrypt(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2008-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


