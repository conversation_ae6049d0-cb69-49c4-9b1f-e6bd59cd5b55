<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OCSP_response_status</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OCSP_response_status, OCSP_response_get1_basic, OCSP_response_create, OCSP_RESPONSE_free, OCSP_RESPID_set_by_name, OCSP_RESPID_set_by_key, OCSP_RESPID_match, OCSP_basic_sign, OCSP_basic_sign_ctx - OCSP response functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ocsp.h&gt;

int OCSP_response_status(OCSP_RESPONSE *resp);
OCSP_BASICRESP *OCSP_response_get1_basic(OCSP_RESPONSE *resp);
OCSP_RESPONSE *OCSP_response_create(int status, OCSP_BASICRESP *bs);
void OCSP_RESPONSE_free(OCSP_RESPONSE *resp);

int OCSP_RESPID_set_by_name(OCSP_RESPID *respid, X509 *cert);
int OCSP_RESPID_set_by_key(OCSP_RESPID *respid, X509 *cert);
int OCSP_RESPID_match(OCSP_RESPID *respid, X509 *cert);

int OCSP_basic_sign(OCSP_BASICRESP *brsp, X509 *signer, EVP_PKEY *key,
                    const EVP_MD *dgst, STACK_OF(X509) *certs,
                    unsigned long flags);
int OCSP_basic_sign_ctx(OCSP_BASICRESP *brsp, X509 *signer, EVP_MD_CTX *ctx,
                        STACK_OF(X509) *certs, unsigned long flags);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>OCSP_response_status() returns the OCSP response status of <b>resp</b>. It returns one of the values: <b>OCSP_RESPONSE_STATUS_SUCCESSFUL</b>, <b>OCSP_RESPONSE_STATUS_MALFORMEDREQUEST</b>, <b>OCSP_RESPONSE_STATUS_INTERNALERROR</b>, <b>OCSP_RESPONSE_STATUS_TRYLATER</b> <b>OCSP_RESPONSE_STATUS_SIGREQUIRED</b>, or <b>OCSP_RESPONSE_STATUS_UNAUTHORIZED</b>.</p>

<p>OCSP_response_get1_basic() decodes and returns the <b>OCSP_BASICRESP</b> structure contained in <b>resp</b>.</p>

<p>OCSP_response_create() creates and returns an <b>OCSP_RESPONSE</b> structure for <b>status</b> and optionally including basic response <b>bs</b>.</p>

<p>OCSP_RESPONSE_free() frees up OCSP response <b>resp</b>.</p>

<p>OCSP_RESPID_set_by_name() sets the name of the OCSP_RESPID to be the same as the subject name in the supplied X509 certificate <b>cert</b> for the OCSP responder.</p>

<p>OCSP_RESPID_set_by_key() sets the key of the OCSP_RESPID to be the same as the key in the supplied X509 certificate <b>cert</b> for the OCSP responder. The key is stored as a SHA1 hash.</p>

<p>Note that an OCSP_RESPID can only have one of the name, or the key set. Calling OCSP_RESPID_set_by_name() or OCSP_RESPID_set_by_key() will clear any existing setting.</p>

<p>OCSP_RESPID_match() tests whether the OCSP_RESPID given in <b>respid</b> matches with the X509 certificate <b>cert</b>.</p>

<p>OCSP_basic_sign() signs OCSP response <b>brsp</b> using certificate <b>signer</b>, private key <b>key</b>, digest <b>dgst</b> and additional certificates <b>certs</b>. If the <b>flags</b> option <b>OCSP_NOCERTS</b> is set then no certificates will be included in the response. If the <b>flags</b> option <b>OCSP_RESPID_KEY</b> is set then the responder is identified by key ID rather than by name. OCSP_basic_sign_ctx() also signs OCSP response <b>brsp</b> but uses the parameters contained in digest context <b>ctx</b>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OCSP_RESPONSE_status() returns a status value.</p>

<p>OCSP_response_get1_basic() returns an <b>OCSP_BASICRESP</b> structure pointer or <b>NULL</b> if an error occurred.</p>

<p>OCSP_response_create() returns an <b>OCSP_RESPONSE</b> structure pointer or <b>NULL</b> if an error occurred.</p>

<p>OCSP_RESPONSE_free() does not return a value.</p>

<p>OCSP_RESPID_set_by_name(), OCSP_RESPID_set_by_key(), OCSP_basic_sign(), and OCSP_basic_sign_ctx() return 1 on success or 0 on failure.</p>

<p>OCSP_RESPID_match() returns 1 if the OCSP_RESPID and the X509 certificate match or 0 otherwise.</p>

<h1 id="NOTES">NOTES</h1>

<p>OCSP_response_get1_basic() is only called if the status of a response is <b>OCSP_RESPONSE_STATUS_SUCCESSFUL</b>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/crypto.html">crypto(7)</a> <a href="../man3/OCSP_cert_to_id.html">OCSP_cert_to_id(3)</a> <a href="../man3/OCSP_request_add1_nonce.html">OCSP_request_add1_nonce(3)</a> <a href="../man3/OCSP_REQUEST_new.html">OCSP_REQUEST_new(3)</a> <a href="../man3/OCSP_resp_find_status.html">OCSP_resp_find_status(3)</a> <a href="../man3/OCSP_sendreq_new.html">OCSP_sendreq_new(3)</a> <a href="../man3/OCSP_RESPID_new.html">OCSP_RESPID_new(3)</a> <a href="../man3/OCSP_RESPID_free.html">OCSP_RESPID_free(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The OCSP_RESPID_set_by_name(), OCSP_RESPID_set_by_key() and OCSP_RESPID_match() functions were added in OpenSSL 1.1.0a.</p>

<p>The OCSP_basic_sign_ctx() function was added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


