<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>RAND_cleanup</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>RAND_cleanup - erase the PRNG state</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/rand.h&gt;

#if OPENSSL_API_COMPAT &lt; 0x10100000L
void RAND_cleanup(void)
#endif</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Prior to OpenSSL 1.1.0, RAND_cleanup() released all resources used by the PRNG. As of version 1.1.0, it does nothing and should not be called, since no explicit initialisation or de-initialisation is necessary. See <a href="../man3/OPENSSL_init_crypto.html">OPENSSL_init_crypto(3)</a>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>RAND_cleanup() returns no value.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/RAND.html">RAND(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>RAND_cleanup() was deprecated in OpenSSL 1.1.0; do not use it. See <a href="../man3/OPENSSL_init_crypto.html">OPENSSL_init_crypto(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2019 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


