.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_CTX_START 3"
.TH BN_CTX_START 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_CTX_start, BN_CTX_get, BN_CTX_end \- use temporary BIGNUM variables
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& void BN_CTX_start(BN_CTX *ctx);
\&
\& BIGNUM *BN_CTX_get(BN_CTX *ctx);
\&
\& void BN_CTX_end(BN_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions are used to obtain temporary \fBBIGNUM\fR variables from
a \fBBN_CTX\fR (which can been created by using \fBBN_CTX_new\fR\|(3))
in order to save the overhead of repeatedly creating and
freeing \fBBIGNUM\fRs in functions that are called from inside a loop.
.PP
A function must call \fBBN_CTX_start()\fR first. Then, \fBBN_CTX_get()\fR may be
called repeatedly to obtain temporary \fBBIGNUM\fRs. All \fBBN_CTX_get()\fR
calls must be made before calling any other functions that use the
\&\fBctx\fR as an argument.
.PP
Finally, \fBBN_CTX_end()\fR must be called before returning from the function.
If \fBctx\fR is NULL, nothing is done.
When \fBBN_CTX_end()\fR is called, the \fBBIGNUM\fR pointers obtained from
\&\fBBN_CTX_get()\fR become invalid.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_CTX_start()\fR and \fBBN_CTX_end()\fR return no values.
.PP
\&\fBBN_CTX_get()\fR returns a pointer to the \fBBIGNUM\fR, or \fBNULL\fR on error.
Once \fBBN_CTX_get()\fR has failed, the subsequent calls will return \fBNULL\fR
as well, so it is sufficient to check the return value of the last
\&\fBBN_CTX_get()\fR call. In case of an error, an error code is set, which
can be obtained by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBN_CTX_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
