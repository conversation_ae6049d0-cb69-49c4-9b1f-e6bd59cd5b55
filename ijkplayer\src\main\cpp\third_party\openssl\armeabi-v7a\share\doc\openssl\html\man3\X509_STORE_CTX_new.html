<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_STORE_CTX_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_STORE_CTX_new, X509_STORE_CTX_cleanup, X509_STORE_CTX_free, X509_STORE_CTX_init, X509_STORE_CTX_set0_trusted_stack, X509_STORE_CTX_set_cert, X509_STORE_CTX_set0_crls, X509_STORE_CTX_get0_chain, X509_STORE_CTX_set0_verified_chain, X509_STORE_CTX_get0_param, X509_STORE_CTX_set0_param, X509_STORE_CTX_get0_untrusted, X509_STORE_CTX_set0_untrusted, X509_STORE_CTX_get_num_untrusted, X509_STORE_CTX_set_default, X509_STORE_CTX_set_verify, X509_STORE_CTX_verify_fn, X509_STORE_CTX_set_purpose, X509_STORE_CTX_set_trust, X509_STORE_CTX_purpose_inherit - X509_STORE_CTX initialisation</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509_vfy.h&gt;

X509_STORE_CTX *X509_STORE_CTX_new(void);
void X509_STORE_CTX_cleanup(X509_STORE_CTX *ctx);
void X509_STORE_CTX_free(X509_STORE_CTX *ctx);

int X509_STORE_CTX_init(X509_STORE_CTX *ctx, X509_STORE *store,
                        X509 *x509, STACK_OF(X509) *chain);

void X509_STORE_CTX_set0_trusted_stack(X509_STORE_CTX *ctx, STACK_OF(X509) *sk);

void X509_STORE_CTX_set_cert(X509_STORE_CTX *ctx, X509 *x);
STACK_OF(X509) *X509_STORE_CTX_get0_chain(X509_STORE_CTX *ctx);
void X509_STORE_CTX_set0_verified_chain(X509_STORE_CTX *ctx, STACK_OF(X509) *chain);
void X509_STORE_CTX_set0_crls(X509_STORE_CTX *ctx, STACK_OF(X509_CRL) *sk);

X509_VERIFY_PARAM *X509_STORE_CTX_get0_param(X509_STORE_CTX *ctx);
void X509_STORE_CTX_set0_param(X509_STORE_CTX *ctx, X509_VERIFY_PARAM *param);
int X509_STORE_CTX_set_default(X509_STORE_CTX *ctx, const char *name);

STACK_OF(X509)* X509_STORE_CTX_get0_untrusted(X509_STORE_CTX *ctx);
void X509_STORE_CTX_set0_untrusted(X509_STORE_CTX *ctx, STACK_OF(X509) *sk);

int X509_STORE_CTX_get_num_untrusted(X509_STORE_CTX *ctx);

typedef int (*X509_STORE_CTX_verify_fn)(X509_STORE_CTX *);
void X509_STORE_CTX_set_verify(X509_STORE_CTX *ctx, X509_STORE_CTX_verify_fn verify);

int X509_STORE_CTX_set_purpose(X509_STORE_CTX *ctx, int purpose);
int X509_STORE_CTX_set_trust(X509_STORE_CTX *ctx, int trust);
int X509_STORE_CTX_purpose_inherit(X509_STORE_CTX *ctx, int def_purpose,
                                   int purpose, int trust);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>These functions initialise an <b>X509_STORE_CTX</b> structure for subsequent use by X509_verify_cert().</p>

<p>X509_STORE_CTX_new() returns a newly initialised <b>X509_STORE_CTX</b> structure.</p>

<p>X509_STORE_CTX_cleanup() internally cleans up an <b>X509_STORE_CTX</b> structure. The context can then be reused with a new call to X509_STORE_CTX_init().</p>

<p>X509_STORE_CTX_free() completely frees up <b>ctx</b>. After this call <b>ctx</b> is no longer valid. If <b>ctx</b> is NULL nothing is done.</p>

<p>X509_STORE_CTX_init() sets up <b>ctx</b> for a subsequent verification operation. It must be called before each call to X509_verify_cert(), i.e. a <b>ctx</b> is only good for one call to X509_verify_cert(); if you want to verify a second certificate with the same <b>ctx</b> then you must call X509_STORE_CTX_cleanup() and then X509_STORE_CTX_init() again before the second call to X509_verify_cert(). The trusted certificate store is set to <b>store</b>, the end entity certificate to be verified is set to <b>x509</b> and a set of additional certificates (which will be untrusted but may be used to build the chain) in <b>chain</b>. Any or all of the <b>store</b>, <b>x509</b> and <b>chain</b> parameters can be <b>NULL</b>.</p>

<p>X509_STORE_CTX_set0_trusted_stack() sets the set of trusted certificates of <b>ctx</b> to <b>sk</b>. This is an alternative way of specifying trusted certificates instead of using an <b>X509_STORE</b>.</p>

<p>X509_STORE_CTX_set_cert() sets the certificate to be verified in <b>ctx</b> to <b>x</b>.</p>

<p>X509_STORE_CTX_set0_verified_chain() sets the validated chain used by <b>ctx</b> to be <b>chain</b>. Ownership of the chain is transferred to <b>ctx</b> and should not be free&#39;d by the caller. X509_STORE_CTX_get0_chain() returns the internal pointer used by the <b>ctx</b> that contains the validated chain.</p>

<p>X509_STORE_CTX_set0_crls() sets a set of CRLs to use to aid certificate verification to <b>sk</b>. These CRLs will only be used if CRL verification is enabled in the associated <b>X509_VERIFY_PARAM</b> structure. This might be used where additional &quot;useful&quot; CRLs are supplied as part of a protocol, for example in a PKCS#7 structure.</p>

<p>X509_STORE_CTX_get0_param() retrieves an internal pointer to the verification parameters associated with <b>ctx</b>.</p>

<p>X509_STORE_CTX_get0_untrusted() retrieves an internal pointer to the stack of untrusted certificates associated with <b>ctx</b>.</p>

<p>X509_STORE_CTX_set0_untrusted() sets the internal point to the stack of untrusted certificates associated with <b>ctx</b> to <b>sk</b>.</p>

<p>X509_STORE_CTX_set0_param() sets the internal verification parameter pointer to <b>param</b>. After this call <b>param</b> should not be used.</p>

<p>X509_STORE_CTX_set_default() looks up and sets the default verification method to <b>name</b>. This uses the function X509_VERIFY_PARAM_lookup() to find an appropriate set of parameters from <b>name</b>.</p>

<p>X509_STORE_CTX_get_num_untrusted() returns the number of untrusted certificates that were used in building the chain following a call to X509_verify_cert().</p>

<p>X509_STORE_CTX_set_verify() provides the capability for overriding the default verify function. This function is responsible for verifying chain signatures and expiration times.</p>

<p>A verify function is defined as an X509_STORE_CTX_verify type which has the following signature:</p>

<pre><code>int (*verify)(X509_STORE_CTX *);</code></pre>

<p>This function should receive the current X509_STORE_CTX as a parameter and return 1 on success or 0 on failure.</p>

<p>X509 certificates may contain information about what purposes keys contained within them can be used for. For example &quot;TLS WWW Server Authentication&quot; or &quot;Email Protection&quot;. This &quot;key usage&quot; information is held internally to the certificate itself. In addition the trust store containing trusted certificates can declare what purposes we trust different certificates for. This &quot;trust&quot; information is not held within the certificate itself but is &quot;meta&quot; information held alongside it. This &quot;meta&quot; information is associated with the certificate after it is issued and could be determined by a system administrator. For example a certificate might declare that it is suitable for use for both &quot;TLS WWW Server Authentication&quot; and &quot;TLS Client Authentication&quot;, but a system administrator might only trust it for the former. An X.509 certificate extension exists that can record extended key usage information to supplement the purpose information described above. This extended mechanism is arbitrarily extensible and not well suited for a generic library API; applications that need to validate extended key usage information in certifiates will need to define a custom &quot;purpose&quot; (see below) or supply a nondefault verification callback (<a href="../man3/X509_STORE_set_verify_cb_func.html">X509_STORE_set_verify_cb_func(3)</a>).</p>

<p>X509_STORE_CTX_set_purpose() sets the purpose for the target certificate being verified in the <i>ctx</i>. Built-in available values for the <i>purpose</i> argument are <b>X509_PURPOSE_SSL_CLIENT</b>, <b>X509_PURPOSE_SSL_SERVER</b>, <b>X509_PURPOSE_NS_SSL_SERVER</b>, <b>X509_PURPOSE_SMIME_SIGN</b>, <b>X509_PURPOSE_SMIME_ENCRYPT</b>, <b>X509_PURPOSE_CRL_SIGN</b>, <b>X509_PURPOSE_ANY</b>, <b>X509_PURPOSE_OCSP_HELPER</b> and <b>X509_PURPOSE_TIMESTAMP_SIGN</b>. It is also possible to create a custom purpose value. Setting a purpose will ensure that the key usage declared within certificates in the chain being verified is consistent with that purpose as well as, potentially, other checks. Every purpose also has an associated default trust value which will also be set at the same time. During verification this trust setting will be verified to check it is consistent with the trust set by the system administrator for certificates in the chain.</p>

<p>X509_STORE_CTX_set_trust() sets the trust value for the target certificate being verified in the <i>ctx</i>. Built-in available values for the <i>trust</i> argument are <b>X509_TRUST_COMPAT</b>, <b>X509_TRUST_SSL_CLIENT</b>, <b>X509_TRUST_SSL_SERVER</b>, <b>X509_TRUST_EMAIL</b>, <b>X509_TRUST_OBJECT_SIGN</b>, <b>X509_TRUST_OCSP_SIGN</b>, <b>X509_TRUST_OCSP_REQUEST</b> and <b>X509_TRUST_TSA</b>. It is also possible to create a custom trust value. Since X509_STORE_CTX_set_purpose() also sets the trust value it is normally sufficient to only call that function. If both are called then X509_STORE_CTX_set_trust() should be called after X509_STORE_CTX_set_purpose() since the trust setting of the last call will be used.</p>

<p>It should not normally be necessary for end user applications to call X509_STORE_CTX_purpose_inherit() directly. Typically applications should call X509_STORE_CTX_set_purpose() or X509_STORE_CTX_set_trust() instead. Using this function it is possible to set the purpose and trust values for the <i>ctx</i> at the same time. Both <i>ctx</i> and its internal verification parameter pointer must not be NULL. The <i>def_purpose</i> and <i>purpose</i> arguments can have the same purpose values as described for X509_STORE_CTX_set_purpose() above. The <i>trust</i> argument can have the same trust values as described in X509_STORE_CTX_set_trust() above. Any of the <i>def_purpose</i>, <i>purpose</i> or <i>trust</i> values may also have the value 0 to indicate that the supplied parameter should be ignored. After calling this function the purpose to be used for verification is set from the <i>purpose</i> argument unless the purpose was already set in <i>ctx</i> before, and the trust is set from the <i>trust</i> argument unless the trust was already set in <i>ctx</i> before. If <i>trust</i> is 0 then the trust value will be set from the default trust value for <i>purpose</i>. If the default trust value for the purpose is <i>X509_TRUST_DEFAULT</i> and <i>trust</i> is 0 then the default trust value associated with the <i>def_purpose</i> value is used for the trust setting instead.</p>

<h1 id="NOTES">NOTES</h1>

<p>The certificates and CRLs in a store are used internally and should <b>not</b> be freed up until after the associated <b>X509_STORE_CTX</b> is freed.</p>

<h1 id="BUGS">BUGS</h1>

<p>The certificates and CRLs in a context are used internally and should <b>not</b> be freed up until after the associated <b>X509_STORE_CTX</b> is freed. Copies should be made or reference counts increased instead.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>X509_STORE_CTX_new() returns a newly allocated context or <b>NULL</b> if an error occurred.</p>

<p>X509_STORE_CTX_init() returns 1 for success or 0 if an error occurred.</p>

<p>X509_STORE_CTX_get0_param() returns a pointer to an <b>X509_VERIFY_PARAM</b> structure or <b>NULL</b> if an error occurred.</p>

<p>X509_STORE_CTX_cleanup(), X509_STORE_CTX_free(), X509_STORE_CTX_set0_trusted_stack(), X509_STORE_CTX_set_cert(), X509_STORE_CTX_set0_crls() and X509_STORE_CTX_set0_param() do not return values.</p>

<p>X509_STORE_CTX_set_default() returns 1 for success or 0 if an error occurred.</p>

<p>X509_STORE_CTX_get_num_untrusted() returns the number of untrusted certificates used.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/X509_verify_cert.html">X509_verify_cert(3)</a> <a href="../man3/X509_VERIFY_PARAM_set_flags.html">X509_VERIFY_PARAM_set_flags(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The X509_STORE_CTX_set0_crls() function was added in OpenSSL 1.0.0. The X509_STORE_CTX_get_num_untrusted() function was added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2009-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


