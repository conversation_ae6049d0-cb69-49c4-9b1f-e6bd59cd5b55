<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_check_ca</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_check_ca - check if given certificate is CA certificate</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509v3.h&gt;

int X509_check_ca(X509 *cert);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This function checks if given certificate is CA certificate (can be used to sign other certificates).</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>Function return 0, if it is not CA certificate, 1 if it is proper X509v3 CA certificate with <b>basicConstraints</b> extension CA:TRUE, 3, if it is self-signed X509 v1 certificate, 4, if it is certificate with <b>keyUsage</b> extension with bit <b>keyCertSign</b> set, but without <b>basicConstraints</b>, and 5 if it has outdated Netscape Certificate Type extension telling that it is CA certificate.</p>

<p>Actually, any nonzero value means that this certificate could have been used to sign other certificates.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/X509_verify_cert.html">X509_verify_cert(3)</a>, <a href="../man3/X509_check_issued.html">X509_check_issued(3)</a>, <a href="../man3/X509_check_purpose.html">X509_check_purpose(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


