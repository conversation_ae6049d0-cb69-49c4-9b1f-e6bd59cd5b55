.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_SIGN 3"
.TH X509_SIGN 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_sign, X509_sign_ctx, X509_verify, X509_REQ_sign, X509_REQ_sign_ctx,
X509_REQ_verify, X509_CRL_sign, X509_CRL_sign_ctx, X509_CRL_verify \-
sign or verify certificate, certificate request or CRL signature
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_sign(X509 *x, EVP_PKEY *pkey, const EVP_MD *md);
\& int X509_sign_ctx(X509 *x, EVP_MD_CTX *ctx);
\& int X509_verify(X509 *a, EVP_PKEY *r);
\&
\& int X509_REQ_sign(X509_REQ *x, EVP_PKEY *pkey, const EVP_MD *md);
\& int X509_REQ_sign_ctx(X509_REQ *x, EVP_MD_CTX *ctx);
\& int X509_REQ_verify(X509_REQ *a, EVP_PKEY *r);
\&
\& int X509_CRL_sign(X509_CRL *x, EVP_PKEY *pkey, const EVP_MD *md);
\& int X509_CRL_sign_ctx(X509_CRL *x, EVP_MD_CTX *ctx);
\& int X509_CRL_verify(X509_CRL *a, EVP_PKEY *r);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_sign()\fR signs certificate \fBx\fR using private key \fBpkey\fR and message
digest \fBmd\fR and sets the signature in \fBx\fR. \fBX509_sign_ctx()\fR also signs
certificate \fBx\fR but uses the parameters contained in digest context \fBctx\fR.
.PP
\&\fBX509_verify()\fR verifies the signature of certificate \fBx\fR using public key
\&\fBpkey\fR. Only the signature is checked: no other checks (such as certificate
chain validity) are performed.
.PP
\&\fBX509_REQ_sign()\fR, \fBX509_REQ_sign_ctx()\fR, \fBX509_REQ_verify()\fR,
\&\fBX509_CRL_sign()\fR, \fBX509_CRL_sign_ctx()\fR and \fBX509_CRL_verify()\fR sign and verify
certificate requests and CRLs respectively.
.SH NOTES
.IX Header "NOTES"
\&\fBX509_sign_ctx()\fR is used where the default parameters for the corresponding
public key and digest are not suitable. It can be used to sign keys using
RSA-PSS for example.
.PP
For efficiency reasons and to work around ASN.1 encoding issues the encoding
of the signed portion of a certificate, certificate request and CRL is cached
internally. If the signed portion of the structure is modified the encoding
is not always updated meaning a stale version is sometimes used. This is not
normally a problem because modifying the signed portion will invalidate the
signature and signing will always update the encoding.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_sign()\fR, \fBX509_sign_ctx()\fR, \fBX509_REQ_sign()\fR, \fBX509_REQ_sign_ctx()\fR,
\&\fBX509_CRL_sign()\fR and \fBX509_CRL_sign_ctx()\fR return the size of the signature
in bytes for success and zero for failure.
.PP
\&\fBX509_verify()\fR, \fBX509_REQ_verify()\fR and \fBX509_CRL_verify()\fR return 1 if the
signature is valid and 0 if the signature check fails. If the signature
could not be checked at all because it was invalid or some other error
occurred then \-1 is returned.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3),
\&\fBERR_get_error\fR\|(3),
\&\fBX509_CRL_get0_by_serial\fR\|(3),
\&\fBX509_get0_signature\fR\|(3),
\&\fBX509_get_ext_d2i\fR\|(3),
\&\fBX509_get_extension_flags\fR\|(3),
\&\fBX509_get_pubkey\fR\|(3),
\&\fBX509_get_subject_name\fR\|(3),
\&\fBX509_get_version\fR\|(3),
\&\fBX509_NAME_add_entry_by_txt\fR\|(3),
\&\fBX509_NAME_ENTRY_get_object\fR\|(3),
\&\fBX509_NAME_get_index_by_NID\fR\|(3),
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBX509_new\fR\|(3),
\&\fBX509V3_get_d2i\fR\|(3),
\&\fBX509_verify_cert\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBX509_sign()\fR, \fBX509_REQ_sign()\fR and \fBX509_CRL_sign()\fR functions are
available in all versions of OpenSSL.
.PP
The \fBX509_sign_ctx()\fR, \fBX509_REQ_sign_ctx()\fR
and \fBX509_CRL_sign_ctx()\fR functions were added OpenSSL 1.0.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
