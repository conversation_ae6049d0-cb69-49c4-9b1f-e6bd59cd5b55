.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_ADD1_RECIPIENT_CERT 3"
.TH CMS_ADD1_RECIPIENT_CERT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_add1_recipient_cert, CMS_add0_recipient_key \- add recipients to a CMS enveloped data structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& CMS_RecipientInfo *CMS_add1_recipient_cert(CMS_ContentInfo *cms,
\&                                            X509 *recip, unsigned int flags);
\&
\& CMS_RecipientInfo *CMS_add0_recipient_key(CMS_ContentInfo *cms, int nid,
\&                                           unsigned char *key, size_t keylen,
\&                                           unsigned char *id, size_t idlen,
\&                                           ASN1_GENERALIZEDTIME *date,
\&                                           ASN1_OBJECT *otherTypeId,
\&                                           ASN1_TYPE *otherType);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_add1_recipient_cert()\fR adds recipient \fBrecip\fR to CMS_ContentInfo enveloped
data structure \fBcms\fR as a KeyTransRecipientInfo structure.
.PP
\&\fBCMS_add0_recipient_key()\fR adds symmetric key \fBkey\fR of length \fBkeylen\fR using
wrapping algorithm \fBnid\fR, identifier \fBid\fR of length \fBidlen\fR and optional
values \fBdate\fR, \fBotherTypeId\fR and \fBotherType\fR to CMS_ContentInfo enveloped
data structure \fBcms\fR as a KEKRecipientInfo structure.
.PP
The CMS_ContentInfo structure should be obtained from an initial call to
\&\fBCMS_encrypt()\fR with the flag \fBCMS_PARTIAL\fR set.
.SH NOTES
.IX Header "NOTES"
The main purpose of this function is to provide finer control over a CMS
enveloped data structure where the simpler \fBCMS_encrypt()\fR function defaults are
not appropriate. For example if one or more KEKRecipientInfo structures
need to be added. New attributes can also be added using the returned
CMS_RecipientInfo structure and the CMS attribute utility functions.
.PP
OpenSSL will by default identify recipient certificates using issuer name
and serial number. If \fBCMS_USE_KEYID\fR is set it will use the subject key
identifier value instead. An error occurs if all recipient certificates do not
have a subject key identifier extension.
.PP
Currently only AES based key wrapping algorithms are supported for \fBnid\fR,
specifically: NID_id_aes128_wrap, NID_id_aes192_wrap and NID_id_aes256_wrap.
If \fBnid\fR is set to \fBNID_undef\fR then an AES wrap algorithm will be used
consistent with \fBkeylen\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_add1_recipient_cert()\fR and \fBCMS_add0_recipient_key()\fR return an internal
pointer to the CMS_RecipientInfo structure just added or NULL if an error
occurs.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_decrypt\fR\|(3),
\&\fBCMS_final\fR\|(3),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
