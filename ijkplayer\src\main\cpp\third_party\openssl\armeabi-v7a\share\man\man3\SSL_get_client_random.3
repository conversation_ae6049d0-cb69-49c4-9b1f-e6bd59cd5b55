.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_CLIENT_RANDOM 3"
.TH SSL_GET_CLIENT_RANDOM 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_client_random,
SSL_get_server_random,
SSL_SESSION_get_master_key,
SSL_SESSION_set1_master_key
\&\- get internal TLS/SSL random values and get/set master key
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& size_t SSL_get_client_random(const SSL *ssl, unsigned char *out, size_t outlen);
\& size_t SSL_get_server_random(const SSL *ssl, unsigned char *out, size_t outlen);
\& size_t SSL_SESSION_get_master_key(const SSL_SESSION *session,
\&                                   unsigned char *out, size_t outlen);
\& int SSL_SESSION_set1_master_key(SSL_SESSION *sess, const unsigned char *in,
\&                                 size_t len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_client_random()\fR extracts the random value sent from the client
to the server during the initial SSL/TLS handshake.  It copies as many
bytes as it can of this value into the buffer provided in \fBout\fR,
which must have at least \fBoutlen\fR bytes available. It returns the
total number of bytes that were actually copied.  If \fBoutlen\fR is
zero, \fBSSL_get_client_random()\fR copies nothing, and returns the
total size of the client_random value.
.PP
\&\fBSSL_get_server_random()\fR behaves the same, but extracts the random value
sent from the server to the client during the initial SSL/TLS handshake.
.PP
\&\fBSSL_SESSION_get_master_key()\fR behaves the same, but extracts the master
secret used to guarantee the security of the SSL/TLS session.  This one
can be dangerous if misused; see NOTES below.
.PP
\&\fBSSL_SESSION_set1_master_key()\fR sets the master key value associated with the
SSL_SESSION \fBsess\fR. For example, this could be used to set up a session based
PSK (see \fBSSL_CTX_set_psk_use_session_callback\fR\|(3)). The master key of length
\&\fBlen\fR should be provided at \fBin\fR. The supplied master key is copied by the
function, so the caller is responsible for freeing and cleaning any memory
associated with \fBin\fR. The caller must ensure that the length of the key is
suitable for the ciphersuite associated with the SSL_SESSION.
.SH NOTES
.IX Header "NOTES"
You probably shouldn't use these functions.
.PP
These functions expose internal values from the TLS handshake, for
use in low-level protocols.  You probably should not use them, unless
you are implementing something that needs access to the internal protocol
details.
.PP
Despite the names of \fBSSL_get_client_random()\fR and \fBSSL_get_server_random()\fR, they
ARE NOT random number generators.  Instead, they return the mostly-random values that
were already generated and used in the TLS protocol.  Using them
in place of \fBRAND_bytes()\fR would be grossly foolish.
.PP
The security of your TLS session depends on keeping the master key secret:
do not expose it, or any information about it, to anybody.
If you need to calculate another secret value that depends on the master
secret, you should probably use \fBSSL_export_keying_material()\fR instead, and
forget that you ever saw these functions.
.PP
In current versions of the TLS protocols, the length of client_random
(and also server_random) is always SSL3_RANDOM_SIZE bytes. Support for
other outlen arguments to the SSL_get_*\fB_random()\fR functions is provided
in case of the unlikely event that a future version or variant of TLS
uses some other length there.
.PP
Finally, though the "client_random" and "server_random" values are called
"random", many TLS implementations will generate four bytes of those
values based on their view of the current time.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_SESSION_set1_master_key()\fR returns 1 on success or 0 on failure.
.PP
For the other functions, if \fBoutlen\fR is greater than 0 then these functions
return the number of bytes actually copied, which will be less than or equal to
\&\fBoutlen\fR. If \fBoutlen\fR is 0 then these functions return the maximum number
of bytes they would copy \-\- that is, the length of the underlying field.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBRAND_bytes\fR\|(3),
\&\fBSSL_export_keying_material\fR\|(3),
\&\fBSSL_CTX_set_psk_use_session_callback\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
