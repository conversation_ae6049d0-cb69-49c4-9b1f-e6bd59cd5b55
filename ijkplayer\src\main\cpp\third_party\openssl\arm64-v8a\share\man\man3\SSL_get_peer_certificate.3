.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_PEER_CERTIFICATE 3"
.TH SSL_GET_PEER_CERTIFICATE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_peer_certificate \- get the X509 certificate of the peer
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& X509 *SSL_get_peer_certificate(const SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_peer_certificate()\fR returns a pointer to the X509 certificate the
peer presented. If the peer did not present a certificate, NULL is returned.
.SH NOTES
.IX Header "NOTES"
Due to the protocol definition, a TLS/SSL server will always send a
certificate, if present. A client will only send a certificate when
explicitly requested to do so by the server (see
\&\fBSSL_CTX_set_verify\fR\|(3)). If an anonymous cipher
is used, no certificates are sent.
.PP
That a certificate is returned does not indicate information about the
verification state, use \fBSSL_get_verify_result\fR\|(3)
to check the verification state.
.PP
The reference count of the X509 object is incremented by one, so that it
will not be destroyed when the session containing the peer certificate is
freed. The X509 object must be explicitly freed using \fBX509_free()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur:
.IP NULL 4
.IX Item "NULL"
No certificate was presented by the peer or no connection was established.
.IP "Pointer to an X509 certificate" 4
.IX Item "Pointer to an X509 certificate"
The return value points to the certificate presented by the peer.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_get_verify_result\fR\|(3),
\&\fBSSL_CTX_set_verify\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
