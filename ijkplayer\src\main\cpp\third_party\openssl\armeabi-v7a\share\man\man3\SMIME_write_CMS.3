.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SMIME_WRITE_CMS 3"
.TH SMIME_WRITE_CMS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SMIME_write_CMS \- convert CMS structure to S/MIME format
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& int SMIME_write_CMS(BIO *out, CMS_ContentInfo *cms, BIO *data, int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSMIME_write_CMS()\fR adds the appropriate MIME headers to a CMS
structure to produce an S/MIME message.
.PP
\&\fBout\fR is the BIO to write the data to. \fBcms\fR is the appropriate
\&\fBCMS_ContentInfo\fR structure. If streaming is enabled then the content must be
supplied in the \fBdata\fR argument. \fBflags\fR is an optional set of flags.
.SH NOTES
.IX Header "NOTES"
The following flags can be passed in the \fBflags\fR parameter.
.PP
If \fBCMS_DETACHED\fR is set then cleartext signing will be used, this option only
makes sense for SignedData where \fBCMS_DETACHED\fR is also set when \fBCMS_sign()\fR is
called.
.PP
If the \fBCMS_TEXT\fR flag is set MIME headers for type \fBtext/plain\fR are added to
the content, this only makes sense if \fBCMS_DETACHED\fR is also set.
.PP
If the \fBCMS_STREAM\fR flag is set streaming is performed. This flag should only
be set if \fBCMS_STREAM\fR was also set in the previous call to a CMS_ContentInfo
creation function.
.PP
If cleartext signing is being used and \fBCMS_STREAM\fR not set then the data must
be read twice: once to compute the signature in \fBCMS_sign()\fR and once to output
the S/MIME message.
.PP
If streaming is performed the content is output in BER format using indefinite
length constructed encoding except in the case of signed data with detached
content where the content is absent and DER format is used.
.SH BUGS
.IX Header "BUGS"
\&\fBSMIME_write_CMS()\fR always base64 encodes CMS structures, there should be an
option to disable this.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSMIME_write_CMS()\fR returns 1 for success or 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_sign\fR\|(3),
\&\fBCMS_verify\fR\|(3), \fBCMS_encrypt\fR\|(3)
\&\fBCMS_decrypt\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
