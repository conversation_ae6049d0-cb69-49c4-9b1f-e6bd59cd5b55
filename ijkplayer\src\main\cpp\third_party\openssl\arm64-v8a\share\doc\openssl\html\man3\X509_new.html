<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_chain_up_ref, X509_new, X509_free, X509_up_ref - X509 certificate ASN1 allocation functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

X509 *X509_new(void);
void X509_free(X509 *a);
int X509_up_ref(X509 *a);
STACK_OF(X509) *X509_chain_up_ref(STACK_OF(X509) *x);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The X509 ASN1 allocation routines, allocate and free an X509 structure, which represents an X509 certificate.</p>

<p>X509_new() allocates and initializes a X509 structure with reference count <b>1</b>.</p>

<p>X509_free() decrements the reference count of <b>X509</b> structure <b>a</b> and frees it up if the reference count is zero. If <b>a</b> is NULL nothing is done.</p>

<p>X509_up_ref() increments the reference count of <b>a</b>.</p>

<p>X509_chain_up_ref() increases the reference count of all certificates in chain <b>x</b> and returns a copy of the stack.</p>

<h1 id="NOTES">NOTES</h1>

<p>The function X509_up_ref() if useful if a certificate structure is being used by several different operations each of which will free it up after use: this avoids the need to duplicate the entire certificate structure.</p>

<p>The function X509_chain_up_ref() doesn&#39;t just up the reference count of each certificate it also returns a copy of the stack, using sk_X509_dup(), but it serves a similar purpose: the returned chain persists after the original has been freed.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>If the allocation fails, X509_new() returns <b>NULL</b> and sets an error code that can be obtained by <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>. Otherwise it returns a pointer to the newly allocated structure.</p>

<p>X509_up_ref() returns 1 for success and 0 for failure.</p>

<p>X509_chain_up_ref() returns a copy of the stack or <b>NULL</b> if an error occurred.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/d2i_X509.html">d2i_X509(3)</a>, <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/X509_CRL_get0_by_serial.html">X509_CRL_get0_by_serial(3)</a>, <a href="../man3/X509_get0_signature.html">X509_get0_signature(3)</a>, <a href="../man3/X509_get_ext_d2i.html">X509_get_ext_d2i(3)</a>, <a href="../man3/X509_get_extension_flags.html">X509_get_extension_flags(3)</a>, <a href="../man3/X509_get_pubkey.html">X509_get_pubkey(3)</a>, <a href="../man3/X509_get_subject_name.html">X509_get_subject_name(3)</a>, <a href="../man3/X509_get_version.html">X509_get_version(3)</a>, <a href="../man3/X509_NAME_add_entry_by_txt.html">X509_NAME_add_entry_by_txt(3)</a>, <a href="../man3/X509_NAME_ENTRY_get_object.html">X509_NAME_ENTRY_get_object(3)</a>, <a href="../man3/X509_NAME_get_index_by_NID.html">X509_NAME_get_index_by_NID(3)</a>, <a href="../man3/X509_NAME_print_ex.html">X509_NAME_print_ex(3)</a>, <a href="../man3/X509_sign.html">X509_sign(3)</a>, <a href="../man3/X509V3_get_d2i.html">X509V3_get_d2i(3)</a>, <a href="../man3/X509_verify_cert.html">X509_verify_cert(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2002-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


