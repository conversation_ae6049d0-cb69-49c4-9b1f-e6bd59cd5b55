.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET1_SIGALGS 3"
.TH SSL_CTX_SET1_SIGALGS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set1_sigalgs, SSL_set1_sigalgs, SSL_CTX_set1_sigalgs_list,
SSL_set1_sigalgs_list, SSL_CTX_set1_client_sigalgs,
SSL_set1_client_sigalgs, SSL_CTX_set1_client_sigalgs_list,
SSL_set1_client_sigalgs_list \- set supported signature algorithms
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_set1_sigalgs(SSL_CTX *ctx, const int *slist, long slistlen);
\& long SSL_set1_sigalgs(SSL *ssl, const int *slist, long slistlen);
\& long SSL_CTX_set1_sigalgs_list(SSL_CTX *ctx, const char *str);
\& long SSL_set1_sigalgs_list(SSL *ssl, const char *str);
\&
\& long SSL_CTX_set1_client_sigalgs(SSL_CTX *ctx, const int *slist, long slistlen);
\& long SSL_set1_client_sigalgs(SSL *ssl, const int *slist, long slistlen);
\& long SSL_CTX_set1_client_sigalgs_list(SSL_CTX *ctx, const char *str);
\& long SSL_set1_client_sigalgs_list(SSL *ssl, const char *str);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set1_sigalgs()\fR and \fBSSL_set1_sigalgs()\fR set the supported signature
algorithms for \fBctx\fR or \fBssl\fR. The array \fBslist\fR of length \fBslistlen\fR
must consist of pairs of NIDs corresponding to digest and public key
algorithms.
.PP
\&\fBSSL_CTX_set1_sigalgs_list()\fR and \fBSSL_set1_sigalgs_list()\fR set the supported
signature algorithms for \fBctx\fR or \fBssl\fR. The \fBstr\fR parameter
must be a null terminated string consisting of a colon separated list of
elements, where each element is either a combination of a public key
algorithm and a digest separated by \fB+\fR, or a TLS 1.3\-style named
SignatureScheme such as rsa_pss_pss_sha256.
.PP
\&\fBSSL_CTX_set1_client_sigalgs()\fR, \fBSSL_set1_client_sigalgs()\fR,
\&\fBSSL_CTX_set1_client_sigalgs_list()\fR and \fBSSL_set1_client_sigalgs_list()\fR set
signature algorithms related to client authentication, otherwise they are
identical to \fBSSL_CTX_set1_sigalgs()\fR, \fBSSL_set1_sigalgs()\fR,
\&\fBSSL_CTX_set1_sigalgs_list()\fR and \fBSSL_set1_sigalgs_list()\fR.
.PP
All these functions are implemented as macros. The signature algorithm
parameter (integer array or string) is not freed: the application should
free it, if necessary.
.SH NOTES
.IX Header "NOTES"
If an application wishes to allow the setting of signature algorithms
as one of many user configurable options it should consider using the more
flexible SSL_CONF API instead.
.PP
The signature algorithms set by a client are used directly in the supported
signature algorithm in the client hello message.
.PP
The supported signature algorithms set by a server are not sent to the
client but are used to determine the set of shared signature algorithms
and (if server preferences are set with SSL_OP_CIPHER_SERVER_PREFERENCE)
their order.
.PP
The client authentication signature algorithms set by a server are sent
in a certificate request message if client authentication is enabled,
otherwise they are unused.
.PP
Similarly client authentication signature algorithms set by a client are
used to determined the set of client authentication shared signature
algorithms.
.PP
Signature algorithms will neither be advertised nor used if the security level
prohibits them (for example SHA1 if the security level is 4 or more).
.PP
Currently the NID_md5, NID_sha1, NID_sha224, NID_sha256, NID_sha384 and
NID_sha512 digest NIDs are supported and the public key algorithm NIDs
EVP_PKEY_RSA, EVP_PKEY_RSA_PSS, EVP_PKEY_DSA and EVP_PKEY_EC.
.PP
The short or long name values for digests can be used in a string (for
example "MD5", "SHA1", "SHA224", "SHA256", "SHA384", "SHA512") and
the public key algorithm strings "RSA", "RSA-PSS", "DSA" or "ECDSA".
.PP
The TLS 1.3 signature scheme names (such as "rsa_pss_pss_sha256") can also
be used with the \fB_list\fR forms of the API.
.PP
The use of MD5 as a digest is strongly discouraged due to security weaknesses.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All these functions return 1 for success and 0 for failure.
.SH EXAMPLES
.IX Header "EXAMPLES"
Set supported signature algorithms to SHA256 with ECDSA and SHA256 with RSA
using an array:
.PP
.Vb 1
\& const int slist[] = {NID_sha256, EVP_PKEY_EC, NID_sha256, EVP_PKEY_RSA};
\&
\& SSL_CTX_set1_sigalgs(ctx, slist, 4);
.Ve
.PP
Set supported signature algorithms to SHA256 with ECDSA and SHA256 with RSA
using a string:
.PP
.Vb 1
\& SSL_CTX_set1_sigalgs_list(ctx, "ECDSA+SHA256:RSA+SHA256");
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_get_shared_sigalgs\fR\|(3),
\&\fBSSL_CONF_CTX_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
