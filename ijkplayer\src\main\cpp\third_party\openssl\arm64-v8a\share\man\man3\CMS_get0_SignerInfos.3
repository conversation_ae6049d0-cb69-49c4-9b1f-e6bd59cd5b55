.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_GET0_SIGNERINFOS 3"
.TH CMS_GET0_SIGNERINFOS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_SignerInfo_set1_signer_cert,
CMS_get0_SignerInfos, CMS_SignerInfo_get0_signer_id,
CMS_SignerInfo_get0_signature, CMS_SignerInfo_cert_cmp
\&\- CMS signedData signer functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& STACK_OF(CMS_SignerInfo) *CMS_get0_SignerInfos(CMS_ContentInfo *cms);
\&
\& int CMS_SignerInfo_get0_signer_id(CMS_SignerInfo *si, ASN1_OCTET_STRING **keyid,
\&                                   X509_NAME **issuer, ASN1_INTEGER **sno);
\& ASN1_OCTET_STRING *CMS_SignerInfo_get0_signature(CMS_SignerInfo *si);
\& int CMS_SignerInfo_cert_cmp(CMS_SignerInfo *si, X509 *cert);
\& void CMS_SignerInfo_set1_signer_cert(CMS_SignerInfo *si, X509 *signer);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The function \fBCMS_get0_SignerInfos()\fR returns all the CMS_SignerInfo structures
associated with a CMS signedData structure.
.PP
\&\fBCMS_SignerInfo_get0_signer_id()\fR retrieves the certificate signer identifier
associated with a specific CMS_SignerInfo structure \fBsi\fR. Either the
keyidentifier will be set in \fBkeyid\fR or \fBboth\fR issuer name and serial number
in \fBissuer\fR and \fBsno\fR.
.PP
\&\fBCMS_SignerInfo_get0_signature()\fR retrieves the signature associated with
\&\fBsi\fR in a pointer to an ASN1_OCTET_STRING structure. This pointer returned
corresponds to the internal signature value if \fBsi\fR so it may be read or
modified.
.PP
\&\fBCMS_SignerInfo_cert_cmp()\fR compares the certificate \fBcert\fR against the signer
identifier \fBsi\fR. It returns zero if the comparison is successful and non zero
if not.
.PP
\&\fBCMS_SignerInfo_set1_signer_cert()\fR sets the signers certificate of \fBsi\fR to
\&\fBsigner\fR.
.SH NOTES
.IX Header "NOTES"
The main purpose of these functions is to enable an application to lookup
signers certificates using any appropriate technique when the simpler method
of \fBCMS_verify()\fR is not appropriate.
.PP
In typical usage and application will retrieve all CMS_SignerInfo structures
using \fBCMS_get0_SignerInfo()\fR and retrieve the identifier information using
CMS. It will then obtain the signer certificate by some unspecified means
(or return and error if it cannot be found) and set it using
\&\fBCMS_SignerInfo_set1_signer_cert()\fR.
.PP
Once all signer certificates have been set \fBCMS_verify()\fR can be used.
.PP
Although \fBCMS_get0_SignerInfos()\fR can return NULL if an error occurs \fBor\fR if
there are no signers this is not a problem in practice because the only
error which can occur is if the \fBcms\fR structure is not of type signedData
due to application error.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_get0_SignerInfos()\fR returns all CMS_SignerInfo structures, or NULL there
are no signers or an error occurs.
.PP
\&\fBCMS_SignerInfo_get0_signer_id()\fR returns 1 for success and 0 for failure.
.PP
\&\fBCMS_SignerInfo_cert_cmp()\fR returns 0 for a successful comparison and non
zero otherwise.
.PP
\&\fBCMS_SignerInfo_set1_signer_cert()\fR does not return a value.
.PP
Any error can be obtained from \fBERR_get_error\fR\|(3)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_verify\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
