.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_GET_EXTENSION_FLAGS 3"
.TH X509_GET_EXTENSION_FLAGS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_get0_subject_key_id,
X509_get0_authority_key_id,
X509_get0_authority_issuer,
X509_get0_authority_serial,
X509_get_pathlen,
X509_get_extension_flags,
X509_get_key_usage,
X509_get_extended_key_usage,
X509_set_proxy_flag,
X509_set_proxy_pathlen,
X509_get_proxy_pathlen \- retrieve certificate extension data
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509v3.h>
\&
\& long X509_get_pathlen(X509 *x);
\& uint32_t X509_get_extension_flags(X509 *x);
\& uint32_t X509_get_key_usage(X509 *x);
\& uint32_t X509_get_extended_key_usage(X509 *x);
\& const ASN1_OCTET_STRING *X509_get0_subject_key_id(X509 *x);
\& const ASN1_OCTET_STRING *X509_get0_authority_key_id(X509 *x);
\& const GENERAL_NAMES *X509_get0_authority_issuer(X509 *x);
\& const ASN1_INTEGER *X509_get0_authority_serial(X509 *x);
\& void X509_set_proxy_flag(X509 *x);
\& void X509_set_proxy_pathlen(int l);
\& long X509_get_proxy_pathlen(X509 *x);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions retrieve information related to commonly used certificate extensions.
.PP
\&\fBX509_get_pathlen()\fR retrieves the path length extension from a certificate.
This extension is used to limit the length of a cert chain that may be
issued from that CA.
.PP
\&\fBX509_get_extension_flags()\fR retrieves general information about a certificate,
it will return one or more of the following flags ored together.
.IP \fBEXFLAG_V1\fR 4
.IX Item "EXFLAG_V1"
The certificate is an obsolete version 1 certificate.
.IP \fBEXFLAG_BCONS\fR 4
.IX Item "EXFLAG_BCONS"
The certificate contains a basic constraints extension.
.IP \fBEXFLAG_CA\fR 4
.IX Item "EXFLAG_CA"
The certificate contains basic constraints and asserts the CA flag.
.IP \fBEXFLAG_PROXY\fR 4
.IX Item "EXFLAG_PROXY"
The certificate is a valid proxy certificate.
.IP \fBEXFLAG_SI\fR 4
.IX Item "EXFLAG_SI"
The certificate is self issued (that is subject and issuer names match).
.IP \fBEXFLAG_SS\fR 4
.IX Item "EXFLAG_SS"
The subject and issuer names match and extension values imply it is self
signed.
.IP \fBEXFLAG_FRESHEST\fR 4
.IX Item "EXFLAG_FRESHEST"
The freshest CRL extension is present in the certificate.
.IP \fBEXFLAG_CRITICAL\fR 4
.IX Item "EXFLAG_CRITICAL"
The certificate contains an unhandled critical extension.
.IP \fBEXFLAG_INVALID\fR 4
.IX Item "EXFLAG_INVALID"
Some certificate extension values are invalid or inconsistent.
The certificate should be rejected.
This bit may also be raised after an out-of-memory error while
processing the X509 object, so it may not be related to the processed
ASN1 object itself.
.IP \fBEXFLAG_NO_FINGERPRINT\fR 4
.IX Item "EXFLAG_NO_FINGERPRINT"
Failed to compute the internal SHA1 hash value of the certificate.
This may be due to malloc failure or because no SHA1 implementation was found.
.IP \fBEXFLAG_INVALID_POLICY\fR 4
.IX Item "EXFLAG_INVALID_POLICY"
The NID_certificate_policies certificate extension is invalid or
inconsistent. The certificate should be rejected.
This bit may also be raised after an out-of-memory error while
processing the X509 object, so it may not be related to the processed
ASN1 object itself.
.IP \fBEXFLAG_KUSAGE\fR 4
.IX Item "EXFLAG_KUSAGE"
The certificate contains a key usage extension. The value can be retrieved
using \fBX509_get_key_usage()\fR.
.IP \fBEXFLAG_XKUSAGE\fR 4
.IX Item "EXFLAG_XKUSAGE"
The certificate contains an extended key usage extension. The value can be
retrieved using \fBX509_get_extended_key_usage()\fR.
.PP
\&\fBX509_get_key_usage()\fR returns the value of the key usage extension.  If key
usage is present will return zero or more of the flags:
\&\fBKU_DIGITAL_SIGNATURE\fR, \fBKU_NON_REPUDIATION\fR, \fBKU_KEY_ENCIPHERMENT\fR,
\&\fBKU_DATA_ENCIPHERMENT\fR, \fBKU_KEY_AGREEMENT\fR, \fBKU_KEY_CERT_SIGN\fR,
\&\fBKU_CRL_SIGN\fR, \fBKU_ENCIPHER_ONLY\fR or \fBKU_DECIPHER_ONLY\fR corresponding to
individual key usage bits. If key usage is absent then \fBUINT32_MAX\fR is
returned.
.PP
\&\fBX509_get_extended_key_usage()\fR returns the value of the extended key usage
extension. If extended key usage is present it will return zero or more of the
flags: \fBXKU_SSL_SERVER\fR, \fBXKU_SSL_CLIENT\fR, \fBXKU_SMIME\fR, \fBXKU_CODE_SIGN\fR
\&\fBXKU_OCSP_SIGN\fR, \fBXKU_TIMESTAMP\fR, \fBXKU_DVCS\fR or \fBXKU_ANYEKU\fR. These
correspond to the OIDs \fBid-kp-serverAuth\fR, \fBid-kp-clientAuth\fR,
\&\fBid-kp-emailProtection\fR, \fBid-kp-codeSigning\fR, \fBid-kp-OCSPSigning\fR,
\&\fBid-kp-timeStamping\fR, \fBid-kp-dvcs\fR and \fBanyExtendedKeyUsage\fR respectively.
Additionally \fBXKU_SGC\fR is set if either Netscape or Microsoft SGC OIDs are
present.
.PP
\&\fBX509_get0_subject_key_id()\fR returns an internal pointer to the subject key
identifier of \fBx\fR as an \fBASN1_OCTET_STRING\fR or \fBNULL\fR if the extension
is not present or cannot be parsed.
.PP
\&\fBX509_get0_authority_key_id()\fR returns an internal pointer to the authority key
identifier of \fBx\fR as an \fBASN1_OCTET_STRING\fR or \fBNULL\fR if the extension
is not present or cannot be parsed.
.PP
\&\fBX509_get0_authority_issuer()\fR returns an internal pointer to the authority
certificate issuer of \fBx\fR as a stack of \fBGENERAL_NAME\fR structures or
\&\fBNULL\fR if the extension is not present or cannot be parsed.
.PP
\&\fBX509_get0_authority_serial()\fR returns an internal pointer to the authority
certificate serial number of \fBx\fR as an \fBASN1_INTEGER\fR or \fBNULL\fR if the
extension is not present or cannot be parsed.
.PP
\&\fBX509_set_proxy_flag()\fR marks the certificate with the \fBEXFLAG_PROXY\fR flag.
This is for the users who need to mark non\-RFC3820 proxy certificates as
such, as OpenSSL only detects RFC3820 compliant ones.
.PP
\&\fBX509_set_proxy_pathlen()\fR sets the proxy certificate path length for the given
certificate \fBx\fR.  This is for the users who need to mark non\-RFC3820 proxy
certificates as such, as OpenSSL only detects RFC3820 compliant ones.
.PP
\&\fBX509_get_proxy_pathlen()\fR returns the proxy certificate path length for the
given certificate \fBx\fR if it is a proxy certificate.
.SH NOTES
.IX Header "NOTES"
The value of the flags correspond to extension values which are cached
in the \fBX509\fR structure. If the flags returned do not provide sufficient
information an application should examine extension values directly
for example using \fBX509_get_ext_d2i()\fR.
.PP
If the key usage or extended key usage extension is absent then typically usage
is unrestricted. For this reason \fBX509_get_key_usage()\fR and
\&\fBX509_get_extended_key_usage()\fR return \fBUINT32_MAX\fR when the corresponding
extension is absent. Applications can additionally check the return value of
\&\fBX509_get_extension_flags()\fR and take appropriate action is an extension is
absent.
.PP
If \fBX509_get0_subject_key_id()\fR returns \fBNULL\fR then the extension may be
absent or malformed. Applications can determine the precise reason using
\&\fBX509_get_ext_d2i()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_get_pathlen()\fR returns the path length value, or \-1 if the extension
is not present.
.PP
\&\fBX509_get_extension_flags()\fR, \fBX509_get_key_usage()\fR and
\&\fBX509_get_extended_key_usage()\fR return sets of flags corresponding to the
certificate extension values.
.PP
\&\fBX509_get0_subject_key_id()\fR returns the subject key identifier as a
pointer to an \fBASN1_OCTET_STRING\fR structure or \fBNULL\fR if the extension
is absent or an error occurred during parsing.
.PP
\&\fBX509_get_proxy_pathlen()\fR returns the path length value if the given
certificate is a proxy one and has a path length set, and \-1 otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_check_purpose\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBX509_get_pathlen()\fR, \fBX509_set_proxy_flag()\fR, \fBX509_set_proxy_pathlen()\fR and
\&\fBX509_get_proxy_pathlen()\fR were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
