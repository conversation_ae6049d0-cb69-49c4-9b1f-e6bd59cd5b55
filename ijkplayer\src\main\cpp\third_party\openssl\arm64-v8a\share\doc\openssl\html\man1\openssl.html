<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#COMMAND-SUMMARY">COMMAND SUMMARY</a>
    <ul>
      <li><a href="#Standard-Commands">Standard Commands</a></li>
      <li><a href="#Message-Digest-Commands">Message Digest Commands</a></li>
      <li><a href="#Encoding-and-Cipher-Commands">Encoding and Cipher Commands</a></li>
    </ul>
  </li>
  <li><a href="#OPTIONS">OPTIONS</a>
    <ul>
      <li><a href="#Common-Options">Common Options</a></li>
      <li><a href="#Pass-Phrase-Options">Pass Phrase Options</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl - OpenSSL command line tool</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <i>command</i> [ <i>command_opts</i> ] [ <i>command_args</i> ]</p>

<p><b>openssl</b> <b>list</b> [ <b>standard-commands</b> | <b>digest-commands</b> | <b>cipher-commands</b> | <b>cipher-algorithms</b> | <b>digest-algorithms</b> | <b>public-key-algorithms</b>]</p>

<p><b>openssl</b> <b>no-</b><i>XXX</i> [ <i>arbitrary options</i> ]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>OpenSSL is a cryptography toolkit implementing the Secure Sockets Layer (SSL v2/v3) and Transport Layer Security (TLS v1) network protocols and related cryptography standards required by them.</p>

<p>The <b>openssl</b> program is a command line tool for using the various cryptography functions of OpenSSL&#39;s <b>crypto</b> library from the shell. It can be used for</p>

<pre><code>o  Creation and management of private keys, public keys and parameters
o  Public key cryptographic operations
o  Creation of X.509 certificates, CSRs and CRLs
o  Calculation of Message Digests
o  Encryption and Decryption with Ciphers
o  SSL/TLS Client and Server Tests
o  Handling of S/MIME signed or encrypted mail
o  Time Stamp requests, generation and verification</code></pre>

<h1 id="COMMAND-SUMMARY">COMMAND SUMMARY</h1>

<p>The <b>openssl</b> program provides a rich variety of commands (<i>command</i> in the SYNOPSIS above), each of which often has a wealth of options and arguments (<i>command_opts</i> and <i>command_args</i> in the SYNOPSIS).</p>

<p>Detailed documentation and use cases for most standard subcommands are available (e.g., <a href="../man1/x509.html">x509(1)</a> or <a href="../man1/openssl-x509.html">openssl-x509(1)</a>).</p>

<p>Many commands use an external configuration file for some or all of their arguments and have a <b>-config</b> option to specify that file. The environment variable <b>OPENSSL_CONF</b> can be used to specify the location of the file. If the environment variable is not specified, then the file is named <b>openssl.cnf</b> in the default certificate storage area, whose value depends on the configuration flags specified when the OpenSSL was built.</p>

<p>The list parameters <b>standard-commands</b>, <b>digest-commands</b>, and <b>cipher-commands</b> output a list (one entry per line) of the names of all standard commands, message digest commands, or cipher commands, respectively, that are available in the present <b>openssl</b> utility.</p>

<p>The list parameters <b>cipher-algorithms</b> and <b>digest-algorithms</b> list all cipher and message digest names, one entry per line. Aliases are listed as:</p>

<pre><code>from =&gt; to</code></pre>

<p>The list parameter <b>public-key-algorithms</b> lists all supported public key algorithms.</p>

<p>The command <b>no-</b><i>XXX</i> tests whether a command of the specified name is available. If no command named <i>XXX</i> exists, it returns 0 (success) and prints <b>no-</b><i>XXX</i>; otherwise it returns 1 and prints <i>XXX</i>. In both cases, the output goes to <b>stdout</b> and nothing is printed to <b>stderr</b>. Additional command line arguments are always ignored. Since for each cipher there is a command of the same name, this provides an easy way for shell scripts to test for the availability of ciphers in the <b>openssl</b> program. (<b>no-</b><i>XXX</i> is not able to detect pseudo-commands such as <b>quit</b>, <b>list</b>, or <b>no-</b><i>XXX</i> itself.)</p>

<h2 id="Standard-Commands">Standard Commands</h2>

<dl>

<dt id="asn1parse"><b>asn1parse</b></dt>
<dd>

<p>Parse an ASN.1 sequence.</p>

</dd>
<dt id="ca"><b>ca</b></dt>
<dd>

<p>Certificate Authority (CA) Management.</p>

</dd>
<dt id="ciphers"><b>ciphers</b></dt>
<dd>

<p>Cipher Suite Description Determination.</p>

</dd>
<dt id="cms"><b>cms</b></dt>
<dd>

<p>CMS (Cryptographic Message Syntax) utility.</p>

</dd>
<dt id="crl"><b>crl</b></dt>
<dd>

<p>Certificate Revocation List (CRL) Management.</p>

</dd>
<dt id="crl2pkcs7"><b>crl2pkcs7</b></dt>
<dd>

<p>CRL to PKCS#7 Conversion.</p>

</dd>
<dt id="dgst"><b>dgst</b></dt>
<dd>

<p>Message Digest Calculation.</p>

</dd>
<dt id="dh"><b>dh</b></dt>
<dd>

<p>Diffie-Hellman Parameter Management. Obsoleted by <a href="../man1/dhparam.html">dhparam(1)</a>.</p>

</dd>
<dt id="dhparam"><b>dhparam</b></dt>
<dd>

<p>Generation and Management of Diffie-Hellman Parameters. Superseded by <a href="../man1/genpkey.html">genpkey(1)</a> and <a href="../man1/pkeyparam.html">pkeyparam(1)</a>.</p>

</dd>
<dt id="dsa"><b>dsa</b></dt>
<dd>

<p>DSA Data Management.</p>

</dd>
<dt id="dsaparam"><b>dsaparam</b></dt>
<dd>

<p>DSA Parameter Generation and Management. Superseded by <a href="../man1/genpkey.html">genpkey(1)</a> and <a href="../man1/pkeyparam.html">pkeyparam(1)</a>.</p>

</dd>
<dt id="ec"><b>ec</b></dt>
<dd>

<p>EC (Elliptic curve) key processing.</p>

</dd>
<dt id="ecparam"><b>ecparam</b></dt>
<dd>

<p>EC parameter manipulation and generation.</p>

</dd>
<dt id="enc"><b>enc</b></dt>
<dd>

<p>Encoding with Ciphers.</p>

</dd>
<dt id="engine"><b>engine</b></dt>
<dd>

<p>Engine (loadable module) information and manipulation.</p>

</dd>
<dt id="errstr"><b>errstr</b></dt>
<dd>

<p>Error Number to Error String Conversion.</p>

</dd>
<dt id="gendh"><b>gendh</b></dt>
<dd>

<p>Generation of Diffie-Hellman Parameters. Obsoleted by <a href="../man1/dhparam.html">dhparam(1)</a>.</p>

</dd>
<dt id="gendsa"><b>gendsa</b></dt>
<dd>

<p>Generation of DSA Private Key from Parameters. Superseded by <a href="../man1/genpkey.html">genpkey(1)</a> and <a href="../man1/pkey.html">pkey(1)</a>.</p>

</dd>
<dt id="genpkey"><b>genpkey</b></dt>
<dd>

<p>Generation of Private Key or Parameters.</p>

</dd>
<dt id="genrsa"><b>genrsa</b></dt>
<dd>

<p>Generation of RSA Private Key. Superseded by <a href="../man1/genpkey.html">genpkey(1)</a>.</p>

</dd>
<dt id="nseq"><b>nseq</b></dt>
<dd>

<p>Create or examine a Netscape certificate sequence.</p>

</dd>
<dt id="ocsp"><b>ocsp</b></dt>
<dd>

<p>Online Certificate Status Protocol utility.</p>

</dd>
<dt id="passwd"><b>passwd</b></dt>
<dd>

<p>Generation of hashed passwords.</p>

</dd>
<dt id="pkcs12"><b>pkcs12</b></dt>
<dd>

<p>PKCS#12 Data Management.</p>

</dd>
<dt id="pkcs7"><b>pkcs7</b></dt>
<dd>

<p>PKCS#7 Data Management.</p>

</dd>
<dt id="pkcs8"><b>pkcs8</b></dt>
<dd>

<p>PKCS#8 format private key conversion tool.</p>

</dd>
<dt id="pkey"><b>pkey</b></dt>
<dd>

<p>Public and private key management.</p>

</dd>
<dt id="pkeyparam"><b>pkeyparam</b></dt>
<dd>

<p>Public key algorithm parameter management.</p>

</dd>
<dt id="pkeyutl"><b>pkeyutl</b></dt>
<dd>

<p>Public key algorithm cryptographic operation utility.</p>

</dd>
<dt id="prime"><b>prime</b></dt>
<dd>

<p>Compute prime numbers.</p>

</dd>
<dt id="rand"><b>rand</b></dt>
<dd>

<p>Generate pseudo-random bytes.</p>

</dd>
<dt id="rehash"><b>rehash</b></dt>
<dd>

<p>Create symbolic links to certificate and CRL files named by the hash values.</p>

</dd>
<dt id="req"><b>req</b></dt>
<dd>

<p>PKCS#10 X.509 Certificate Signing Request (CSR) Management.</p>

</dd>
<dt id="rsa"><b>rsa</b></dt>
<dd>

<p>RSA key management.</p>

</dd>
<dt id="rsautl"><b>rsautl</b></dt>
<dd>

<p>RSA utility for signing, verification, encryption, and decryption. Superseded by <a href="../man1/pkeyutl.html">pkeyutl(1)</a>.</p>

</dd>
<dt id="s_client"><b>s_client</b></dt>
<dd>

<p>This implements a generic SSL/TLS client which can establish a transparent connection to a remote server speaking SSL/TLS. It&#39;s intended for testing purposes only and provides only rudimentary interface functionality but internally uses mostly all functionality of the OpenSSL <b>ssl</b> library.</p>

</dd>
<dt id="s_server"><b>s_server</b></dt>
<dd>

<p>This implements a generic SSL/TLS server which accepts connections from remote clients speaking SSL/TLS. It&#39;s intended for testing purposes only and provides only rudimentary interface functionality but internally uses mostly all functionality of the OpenSSL <b>ssl</b> library. It provides both an own command line oriented protocol for testing SSL functions and a simple HTTP response facility to emulate an SSL/TLS-aware webserver.</p>

</dd>
<dt id="s_time"><b>s_time</b></dt>
<dd>

<p>SSL Connection Timer.</p>

</dd>
<dt id="sess_id"><b>sess_id</b></dt>
<dd>

<p>SSL Session Data Management.</p>

</dd>
<dt id="smime"><b>smime</b></dt>
<dd>

<p>S/MIME mail processing.</p>

</dd>
<dt id="speed"><b>speed</b></dt>
<dd>

<p>Algorithm Speed Measurement.</p>

</dd>
<dt id="spkac"><b>spkac</b></dt>
<dd>

<p>SPKAC printing and generating utility.</p>

</dd>
<dt id="srp"><b>srp</b></dt>
<dd>

<p>Maintain SRP password file.</p>

</dd>
<dt id="storeutl"><b>storeutl</b></dt>
<dd>

<p>Utility to list and display certificates, keys, CRLs, etc.</p>

</dd>
<dt id="ts"><b>ts</b></dt>
<dd>

<p>Time Stamping Authority tool (client/server).</p>

</dd>
<dt id="verify"><b>verify</b></dt>
<dd>

<p>X.509 Certificate Verification.</p>

</dd>
<dt id="version"><b>version</b></dt>
<dd>

<p>OpenSSL Version Information.</p>

</dd>
<dt id="x509"><b>x509</b></dt>
<dd>

<p>X.509 Certificate Data Management.</p>

</dd>
</dl>

<h2 id="Message-Digest-Commands">Message Digest Commands</h2>

<dl>

<dt id="blake2b512"><b>blake2b512</b></dt>
<dd>

<p>BLAKE2b-512 Digest</p>

</dd>
<dt id="blake2s256"><b>blake2s256</b></dt>
<dd>

<p>BLAKE2s-256 Digest</p>

</dd>
<dt id="md2"><b>md2</b></dt>
<dd>

<p>MD2 Digest</p>

</dd>
<dt id="md4"><b>md4</b></dt>
<dd>

<p>MD4 Digest</p>

</dd>
<dt id="md5"><b>md5</b></dt>
<dd>

<p>MD5 Digest</p>

</dd>
<dt id="mdc2"><b>mdc2</b></dt>
<dd>

<p>MDC2 Digest</p>

</dd>
<dt id="rmd160"><b>rmd160</b></dt>
<dd>

<p>RMD-160 Digest</p>

</dd>
<dt id="sha1"><b>sha1</b></dt>
<dd>

<p>SHA-1 Digest</p>

</dd>
<dt id="sha224"><b>sha224</b></dt>
<dd>

<p>SHA-2 224 Digest</p>

</dd>
<dt id="sha256"><b>sha256</b></dt>
<dd>

<p>SHA-2 256 Digest</p>

</dd>
<dt id="sha384"><b>sha384</b></dt>
<dd>

<p>SHA-2 384 Digest</p>

</dd>
<dt id="sha512"><b>sha512</b></dt>
<dd>

<p>SHA-2 512 Digest</p>

</dd>
<dt id="sha3-224"><b>sha3-224</b></dt>
<dd>

<p>SHA-3 224 Digest</p>

</dd>
<dt id="sha3-256"><b>sha3-256</b></dt>
<dd>

<p>SHA-3 256 Digest</p>

</dd>
<dt id="sha3-384"><b>sha3-384</b></dt>
<dd>

<p>SHA-3 384 Digest</p>

</dd>
<dt id="sha3-512"><b>sha3-512</b></dt>
<dd>

<p>SHA-3 512 Digest</p>

</dd>
<dt id="shake128"><b>shake128</b></dt>
<dd>

<p>SHA-3 SHAKE128 Digest</p>

</dd>
<dt id="shake256"><b>shake256</b></dt>
<dd>

<p>SHA-3 SHAKE256 Digest</p>

</dd>
<dt id="sm3"><b>sm3</b></dt>
<dd>

<p>SM3 Digest</p>

</dd>
</dl>

<h2 id="Encoding-and-Cipher-Commands">Encoding and Cipher Commands</h2>

<p>The following aliases provide convenient access to the most used encodings and ciphers.</p>

<p>Depending on how OpenSSL was configured and built, not all ciphers listed here may be present. See <a href="../man1/enc.html">enc(1)</a> for more information and command usage.</p>

<dl>

<dt id="aes128-aes-128-cbc-aes-128-cfb-aes-128-ctr-aes-128-ecb-aes-128-ofb"><b>aes128</b>, <b>aes-128-cbc</b>, <b>aes-128-cfb</b>, <b>aes-128-ctr</b>, <b>aes-128-ecb</b>, <b>aes-128-ofb</b></dt>
<dd>

<p>AES-128 Cipher</p>

</dd>
<dt id="aes192-aes-192-cbc-aes-192-cfb-aes-192-ctr-aes-192-ecb-aes-192-ofb"><b>aes192</b>, <b>aes-192-cbc</b>, <b>aes-192-cfb</b>, <b>aes-192-ctr</b>, <b>aes-192-ecb</b>, <b>aes-192-ofb</b></dt>
<dd>

<p>AES-192 Cipher</p>

</dd>
<dt id="aes256-aes-256-cbc-aes-256-cfb-aes-256-ctr-aes-256-ecb-aes-256-ofb"><b>aes256</b>, <b>aes-256-cbc</b>, <b>aes-256-cfb</b>, <b>aes-256-ctr</b>, <b>aes-256-ecb</b>, <b>aes-256-ofb</b></dt>
<dd>

<p>AES-256 Cipher</p>

</dd>
<dt id="aria128-aria-128-cbc-aria-128-cfb-aria-128-ctr-aria-128-ecb-aria-128-ofb"><b>aria128</b>, <b>aria-128-cbc</b>, <b>aria-128-cfb</b>, <b>aria-128-ctr</b>, <b>aria-128-ecb</b>, <b>aria-128-ofb</b></dt>
<dd>

<p>Aria-128 Cipher</p>

</dd>
<dt id="aria192-aria-192-cbc-aria-192-cfb-aria-192-ctr-aria-192-ecb-aria-192-ofb"><b>aria192</b>, <b>aria-192-cbc</b>, <b>aria-192-cfb</b>, <b>aria-192-ctr</b>, <b>aria-192-ecb</b>, <b>aria-192-ofb</b></dt>
<dd>

<p>Aria-192 Cipher</p>

</dd>
<dt id="aria256-aria-256-cbc-aria-256-cfb-aria-256-ctr-aria-256-ecb-aria-256-ofb"><b>aria256</b>, <b>aria-256-cbc</b>, <b>aria-256-cfb</b>, <b>aria-256-ctr</b>, <b>aria-256-ecb</b>, <b>aria-256-ofb</b></dt>
<dd>

<p>Aria-256 Cipher</p>

</dd>
<dt id="base64"><b>base64</b></dt>
<dd>

<p>Base64 Encoding</p>

</dd>
<dt id="bf-bf-cbc-bf-cfb-bf-ecb-bf-ofb"><b>bf</b>, <b>bf-cbc</b>, <b>bf-cfb</b>, <b>bf-ecb</b>, <b>bf-ofb</b></dt>
<dd>

<p>Blowfish Cipher</p>

</dd>
<dt id="camellia128-camellia-128-cbc-camellia-128-cfb-camellia-128-ctr-camellia-128-ecb-camellia-128-ofb"><b>camellia128</b>, <b>camellia-128-cbc</b>, <b>camellia-128-cfb</b>, <b>camellia-128-ctr</b>, <b>camellia-128-ecb</b>, <b>camellia-128-ofb</b></dt>
<dd>

<p>Camellia-128 Cipher</p>

</dd>
<dt id="camellia192-camellia-192-cbc-camellia-192-cfb-camellia-192-ctr-camellia-192-ecb-camellia-192-ofb"><b>camellia192</b>, <b>camellia-192-cbc</b>, <b>camellia-192-cfb</b>, <b>camellia-192-ctr</b>, <b>camellia-192-ecb</b>, <b>camellia-192-ofb</b></dt>
<dd>

<p>Camellia-192 Cipher</p>

</dd>
<dt id="camellia256-camellia-256-cbc-camellia-256-cfb-camellia-256-ctr-camellia-256-ecb-camellia-256-ofb"><b>camellia256</b>, <b>camellia-256-cbc</b>, <b>camellia-256-cfb</b>, <b>camellia-256-ctr</b>, <b>camellia-256-ecb</b>, <b>camellia-256-ofb</b></dt>
<dd>

<p>Camellia-256 Cipher</p>

</dd>
<dt id="cast-cast-cbc"><b>cast</b>, <b>cast-cbc</b></dt>
<dd>

<p>CAST Cipher</p>

</dd>
<dt id="cast5-cbc-cast5-cfb-cast5-ecb-cast5-ofb"><b>cast5-cbc</b>, <b>cast5-cfb</b>, <b>cast5-ecb</b>, <b>cast5-ofb</b></dt>
<dd>

<p>CAST5 Cipher</p>

</dd>
<dt id="chacha20"><b>chacha20</b></dt>
<dd>

<p>Chacha20 Cipher</p>

</dd>
<dt id="des-des-cbc-des-cfb-des-ecb-des-ede-des-ede-cbc-des-ede-cfb-des-ede-ofb-des-ofb"><b>des</b>, <b>des-cbc</b>, <b>des-cfb</b>, <b>des-ecb</b>, <b>des-ede</b>, <b>des-ede-cbc</b>, <b>des-ede-cfb</b>, <b>des-ede-ofb</b>, <b>des-ofb</b></dt>
<dd>

<p>DES Cipher</p>

</dd>
<dt id="des3-desx-des-ede3-des-ede3-cbc-des-ede3-cfb-des-ede3-ofb"><b>des3</b>, <b>desx</b>, <b>des-ede3</b>, <b>des-ede3-cbc</b>, <b>des-ede3-cfb</b>, <b>des-ede3-ofb</b></dt>
<dd>

<p>Triple-DES Cipher</p>

</dd>
<dt id="idea-idea-cbc-idea-cfb-idea-ecb-idea-ofb"><b>idea</b>, <b>idea-cbc</b>, <b>idea-cfb</b>, <b>idea-ecb</b>, <b>idea-ofb</b></dt>
<dd>

<p>IDEA Cipher</p>

</dd>
<dt id="rc2-rc2-cbc-rc2-cfb-rc2-ecb-rc2-ofb"><b>rc2</b>, <b>rc2-cbc</b>, <b>rc2-cfb</b>, <b>rc2-ecb</b>, <b>rc2-ofb</b></dt>
<dd>

<p>RC2 Cipher</p>

</dd>
<dt id="rc4"><b>rc4</b></dt>
<dd>

<p>RC4 Cipher</p>

</dd>
<dt id="rc5-rc5-cbc-rc5-cfb-rc5-ecb-rc5-ofb"><b>rc5</b>, <b>rc5-cbc</b>, <b>rc5-cfb</b>, <b>rc5-ecb</b>, <b>rc5-ofb</b></dt>
<dd>

<p>RC5 Cipher</p>

</dd>
<dt id="seed-seed-cbc-seed-cfb-seed-ecb-seed-ofb"><b>seed</b>, <b>seed-cbc</b>, <b>seed-cfb</b>, <b>seed-ecb</b>, <b>seed-ofb</b></dt>
<dd>

<p>SEED Cipher</p>

</dd>
<dt id="sm4-sm4-cbc-sm4-cfb-sm4-ctr-sm4-ecb-sm4-ofb"><b>sm4</b>, <b>sm4-cbc</b>, <b>sm4-cfb</b>, <b>sm4-ctr</b>, <b>sm4-ecb</b>, <b>sm4-ofb</b></dt>
<dd>

<p>SM4 Cipher</p>

</dd>
</dl>

<h1 id="OPTIONS">OPTIONS</h1>

<p>Details of which options are available depend on the specific command. This section describes some common options with common behavior.</p>

<h2 id="Common-Options">Common Options</h2>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Provides a terse summary of all options.</p>

</dd>
</dl>

<h2 id="Pass-Phrase-Options">Pass Phrase Options</h2>

<p>Several commands accept password arguments, typically using <b>-passin</b> and <b>-passout</b> for input and output passwords respectively. These allow the password to be obtained from a variety of sources. Both of these options take a single argument whose format is described below. If no password argument is given and a password is required then the user is prompted to enter one: this will typically be read from the current terminal with echoing turned off.</p>

<p>Note that character encoding may be relevant, please see <a href="../man7/passphrase-encoding.html">passphrase-encoding(7)</a>.</p>

<dl>

<dt id="pass:password"><b>pass:password</b></dt>
<dd>

<p>The actual password is <b>password</b>. Since the password is visible to utilities (like &#39;ps&#39; under Unix) this form should only be used where security is not important.</p>

</dd>
<dt id="env:var"><b>env:var</b></dt>
<dd>

<p>Obtain the password from the environment variable <b>var</b>. Since the environment of other processes is visible on certain platforms (e.g. ps under certain Unix OSes) this option should be used with caution.</p>

</dd>
<dt id="file:pathname"><b>file:pathname</b></dt>
<dd>

<p>The first line of <b>pathname</b> is the password. If the same <b>pathname</b> argument is supplied to <b>-passin</b> and <b>-passout</b> arguments then the first line will be used for the input password and the next line for the output password. <b>pathname</b> need not refer to a regular file: it could for example refer to a device or named pipe.</p>

</dd>
<dt id="fd:number"><b>fd:number</b></dt>
<dd>

<p>Read the password from the file descriptor <b>number</b>. This can be used to send the data via a pipe for example.</p>

</dd>
<dt id="stdin"><b>stdin</b></dt>
<dd>

<p>Read the password from standard input.</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/asn1parse.html">asn1parse(1)</a>, <a href="../man1/ca.html">ca(1)</a>, <a href="../man1/ciphers.html">ciphers(1)</a>, <a href="../man1/cms.html">cms(1)</a>, <a href="../man5/config.html">config(5)</a>, <a href="../man1/crl.html">crl(1)</a>, <a href="../man1/crl2pkcs7.html">crl2pkcs7(1)</a>, <a href="../man1/dgst.html">dgst(1)</a>, <a href="../man1/dhparam.html">dhparam(1)</a>, <a href="../man1/dsa.html">dsa(1)</a>, <a href="../man1/dsaparam.html">dsaparam(1)</a>, <a href="../man1/ec.html">ec(1)</a>, <a href="../man1/ecparam.html">ecparam(1)</a>, <a href="../man1/enc.html">enc(1)</a>, <a href="../man1/engine.html">engine(1)</a>, <a href="../man1/errstr.html">errstr(1)</a>, <a href="../man1/gendsa.html">gendsa(1)</a>, <a href="../man1/genpkey.html">genpkey(1)</a>, <a href="../man1/genrsa.html">genrsa(1)</a>, <a href="../man1/nseq.html">nseq(1)</a>, <a href="../man1/ocsp.html">ocsp(1)</a>, <a href="../man1/passwd.html">passwd(1)</a>, <a href="../man1/pkcs12.html">pkcs12(1)</a>, <a href="../man1/pkcs7.html">pkcs7(1)</a>, <a href="../man1/pkcs8.html">pkcs8(1)</a>, <a href="../man1/pkey.html">pkey(1)</a>, <a href="../man1/pkeyparam.html">pkeyparam(1)</a>, <a href="../man1/pkeyutl.html">pkeyutl(1)</a>, <a href="../man1/prime.html">prime(1)</a>, <a href="../man1/rand.html">rand(1)</a>, <a href="../man1/rehash.html">rehash(1)</a>, <a href="../man1/req.html">req(1)</a>, <a href="../man1/rsa.html">rsa(1)</a>, <a href="../man1/rsautl.html">rsautl(1)</a>, <a href="../man1/s_client.html">s_client(1)</a>, <a href="../man1/s_server.html">s_server(1)</a>, <a href="../man1/s_time.html">s_time(1)</a>, <a href="../man1/sess_id.html">sess_id(1)</a>, <a href="../man1/smime.html">smime(1)</a>, <a href="../man1/speed.html">speed(1)</a>, <a href="../man1/spkac.html">spkac(1)</a>, <a href="../man1/srp.html">srp(1)</a>, <a href="../man1/storeutl.html">storeutl(1)</a>, <a href="../man1/ts.html">ts(1)</a>, <a href="../man1/verify.html">verify(1)</a>, <a href="../man1/version.html">version(1)</a>, <a href="../man1/x509.html">x509(1)</a>, <a href="../man7/crypto.html">crypto(7)</a>, <a href="../man7/ssl.html">ssl(7)</a>, <a href="../man5/x509v3_config.html">x509v3_config(5)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The <b>list-</b><i>XXX</i><b>-algorithms</b> pseudo-commands were added in OpenSSL 1.0.0; For notes on the availability of other commands, see their individual manual pages.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


