import { IjkMediaPlayer } from "@ohos/ijkplayer";
import type { OnPreparedListener } from "@ohos/ijkplayer";
import type { OnVideoSizeChangedListener } from "@ohos/ijkplayer";
import type { OnCompletionListener } from "@ohos/ijkplayer";
import type { OnErrorListener } from "@ohos/ijkplayer";
import { LogUtils } from "@ohos/ijkplayer";
import fs from '@ohos.file.fs';
import { BusinessError } from '@ohos.base';
import util from '@ohos.util';
import buffer from '@ohos.buffer';
import resourceManager from '@ohos.resourceManager';

@Entry
@Component
struct Index {
  @State message: string = 'Stream Player';
  @State isPlaying: boolean = false;
  @State aspRatio: number = 16 / 9; // 默认宽高比
  @State hasStreamData: boolean = false;
  @State currentVideoIndex: number = 0; // 当前视频索引
  private mContext: object | null = null;
  private mIjkMediaPlayer: IjkMediaPlayer | null = null;
  private tempFilePath: string = '';
  private streamBuffer: ArrayBuffer | null = null;
  private videoFiles: string[] = ['trailer.mp4', 'videoTest.mp4']; // 视频文件列表
  private videoNames: string[] = ['视频1', '视频2']; // 视频显示名称
  build() {
    Column() {
      Text(this.message)
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 20 })

      // 视频播放区域
      XComponent({
        id: 'xcomponentId',
        type: 'surface',
        libraryname: 'ijkplayer_napi'
      })
        .onLoad((context) => {
          this.mContext = context || null;
          this.initPlayer();
        })
        .onDestroy(() => {
          this.releasePlayer();
        })
        .width(200)
        .height(200)
        .margin({top:20})
        .aspectRatio(this.aspRatio)
        .backgroundColor(Color.Black)
      // 控制按钮
      Column() {
        Row() {
          Button('加载流数据')
            .onClick(() => {
              this.loadStreamData();
            })
            .margin({ right: 10 })
            .enabled(!this.hasStreamData)

          Button('清除数据')
            .onClick(() => {
              this.clearStreamData();
            })
            .enabled(this.hasStreamData)
        }
        .margin({ bottom: 10 })

        // 视频切换按钮
        Row() {
          Button(`切换到${this.videoNames[1 - this.currentVideoIndex]}`)
            .onClick(() => {
              this.switchVideo();
            })
            .margin({ right: 10 })
            .enabled(this.hasStreamData)
        }
        .margin({ bottom: 10 })

        Row() {
          Button(this.isPlaying ? '暂停' : '播放')
            .onClick(() => {
              if (this.isPlaying) {
                this.pausePlay();
              } else {
                this.startPlayWithStream();
              }
            })
            .margin({ right: 10 })
            .enabled(this.hasStreamData)

          Button('停止')
            .onClick(() => {
              this.stopPlay();
            })
            .enabled(this.hasStreamData)
        }
      }
      .margin({ top: 20 })
    }
    .width('100%')
    .height('100%')
    .padding(20)
  }

  private initPlayer() {
    if (!this.mContext) {
      LogUtils.getInstance().LOGI("Context is null, cannot initialize player");
      return;
    }

    // 创建播放器实例
    this.mIjkMediaPlayer = IjkMediaPlayer.getInstance();

    // 设置XComponent上下文
    this.mIjkMediaPlayer.setContext(this.mContext, "xcomponentId");

    // 设置调试模式
    this.mIjkMediaPlayer.setDebug(true);

    // 初始化配置
    this.mIjkMediaPlayer.native_setup();

    // 设置播放器选项
    this.setupPlayerOptions();

    // 设置监听器
    this.setupListeners();

    LogUtils.getInstance().LOGI("Player initialized successfully");
  }



  private setupPlayerOptions() {
    if (!this.mIjkMediaPlayer) return;

    // 使用精确寻帧
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "enable-accurate-seek", "1");

    // 预读数据的缓冲区大小
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-buffer-size", "102400");

    // 停止预读的最小帧数
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "min-frames", "100");

    // 启动预加载
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", "1");

    // 设置无缓冲
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "packet-buffering", "0");

    // 跳帧处理
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "framedrop", "5");

    // 最大缓冲时间3秒
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max_cached_duration", "3000");

    // 无限制收流
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "infbuf", "1");

    // 屏幕常亮
    this.mIjkMediaPlayer.setScreenOnWhilePlaying(true);

    // 设置超时
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "timeout", "10000000");
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "connect_timeout", "10000000");
  }

  private setupListeners() {
    if (!this.mIjkMediaPlayer) return;

    const that = this;

    // 视频尺寸变化监听
    let mOnVideoSizeChangedListener: OnVideoSizeChangedListener = {
      onVideoSizeChanged(width: number, height: number, sar_num: number, sar_den: number) {
        that.aspRatio = width / height;
        LogUtils.getInstance().LOGI(`Video size changed: ${width}x${height}, aspect ratio: ${that.aspRatio}`);
      }
    };
    this.mIjkMediaPlayer.setOnVideoSizeChangedListener(mOnVideoSizeChangedListener);

    // 准备完成监听
    let mOnPreparedListener: OnPreparedListener = {
      onPrepared() {
        LogUtils.getInstance().LOGI("Player prepared, ready to play");
        that.mIjkMediaPlayer?.start();
        that.isPlaying = true;
      }
    };
    this.mIjkMediaPlayer.setOnPreparedListener(mOnPreparedListener);

    // 播放完成监听
    let mOnCompletionListener: OnCompletionListener = {
      onCompletion() {
        LogUtils.getInstance().LOGI("Playback completed");
        that.isPlaying = false;
      }
    };
    this.mIjkMediaPlayer.setOnCompletionListener(mOnCompletionListener);

    // 错误监听
    let mOnErrorListener: OnErrorListener = {
      onError(what: number, extra: number) {
        LogUtils.getInstance().LOGI(`Player error: what=${what}, extra=${extra}`);
        that.isPlaying = false;
      }
    };
    this.mIjkMediaPlayer.setOnErrorListener(mOnErrorListener);

    // 设置消息监听器
    this.mIjkMediaPlayer.setMessageListener();
  }

  private startPlay() {
    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    if (this.isPlaying) {
      LogUtils.getInstance().LOGI("Already playing");
      return;
    }

    this.mIjkMediaPlayer.start();
    this.isPlaying = true;
    LogUtils.getInstance().LOGI("Started playback");
  }

  private pausePlay() {
    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    if (!this.isPlaying) {
      LogUtils.getInstance().LOGI("Already paused");
      return;
    }

    this.mIjkMediaPlayer.pause();
    this.isPlaying = false;
    LogUtils.getInstance().LOGI("Paused playback");
  }

  private stopPlay() {
    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    this.mIjkMediaPlayer.stop();
    this.isPlaying = false;
    LogUtils.getInstance().LOGI("Stopped playback");
  }

  private releasePlayer() {
    if (this.mIjkMediaPlayer) {
      this.mIjkMediaPlayer.release();
      this.mIjkMediaPlayer = null;
      this.isPlaying = false;
      LogUtils.getInstance().LOGI("Released player resources");
    }
  }

  // 模拟加载ArrayBuffer流数据
  private loadStreamData() {
    try {
      // 这里模拟创建一个简单的测试视频数据
      // 在实际应用中，这里应该是从网络或其他来源获取的ArrayBuffer
      this.createTestStreamData();

      LogUtils.getInstance().LOGI("Stream data loaded successfully");
      this.message = `Stream Player - 当前：${this.videoNames[this.currentVideoIndex]}`;
      this.hasStreamData = true;
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to load stream data: ${error}`);
      this.message = 'Stream Player - 数据加载失败';
    }
  }

  // 创建测试流数据（实际应用中替换为真实的ArrayBuffer数据）
  private createTestStreamData() {
    // 创建一个测试用的ArrayBuffer
    // 注意：这里只是示例，实际应用中应该使用真实的视频流数据
    const testData = "Test video stream data";
    // const encoder = new TextEncoder();
    this.streamBuffer = buffer.from(testData).buffer

    // 为了演示，我们使用一个测试视频URL
    // 在实际应用中，您需要将ArrayBuffer写入临时文件或使用其他方式
    this.setupTestVideoSource();
  }

  // 处理真实的ArrayBuffer流数据的方法
  public async setStreamData(buffer: ArrayBuffer): Promise<boolean> {
    try {
      if (!buffer || buffer.byteLength === 0) {
        LogUtils.getInstance().LOGI("Invalid ArrayBuffer data");
        return false;
      }

      this.streamBuffer = buffer;

      // 将ArrayBuffer写入临时文件
      const filePath = await this.writeArrayBufferToFile(buffer);

      // 设置数据源
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setDataSource(filePath);
        LogUtils.getInstance().LOGI(`Set stream data source: ${filePath}`);
      }

      this.hasStreamData = true;
      this.message = `Stream Player - 已加载 ${buffer.byteLength} 字节数据`;

      return true;
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to set stream data: ${error}`);
      return false;
    }
  }

  // 设置测试视频源（用于演示）
  private async setupTestVideoSource() {
    try {
      await this.copyRawFileToTemp();
      // // // 方法1：获取 rawfile 的文件描述符
      // const rawFd = await getContext(this).resourceManager.getRawFd('videoTest.mp4');
      // console.log('rawFd', rawFd);
      //
      // // 构造文件描述符路径
      // const fdPath = `fd://${rawFd.fd}:${rawFd.offset}:${rawFd.length}`;
      //
      // if (this.mIjkMediaPlayer) {
      //   this.mIjkMediaPlayer.setDataSource(fdPath);
      //   LogUtils.getInstance().LOGI(`Set test video source: ${fdPath}`);
      // }
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to setup video source: ${error}`);
      // 备用方案：复制到临时文件
      // await this.copyRawFileToTemp();
    }
  }

  // 备用方案：将 rawfile 复制到临时文件
  private async copyRawFileToTemp(): Promise<void> {
    try {
      // 获取当前视频文件
      const currentVideoFile = this.videoFiles[this.currentVideoIndex];
      this.message = `Stream Player - 读取视频文件: ${currentVideoFile}`;
      LogUtils.getInstance().LOGI(`Loading video file: ${currentVideoFile}`);

      const rawFileData = await getContext(this).resourceManager.getRawFileContent(currentVideoFile);

      // 获取应用缓存目录
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      // 使用时间戳和视频索引创建唯一的临时文件名
      const timestamp = Date.now();
      const tempFilePath = `${cacheDir}/temp_video_${this.currentVideoIndex}_${timestamp}.mp4`;

      this.message = `Stream Player - 创建临时文件...`;
      LogUtils.getInstance().LOGI(`Creating temp file: ${tempFilePath}`);

      // 写入临时文件
      const file = fs.openSync(tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      fs.writeSync(file.fd, rawFileData.buffer);
      fs.closeSync(file);
      console.log('tempFilePath',tempFilePath)

      // 保存临时文件路径，用于清理
      this.tempFilePath = tempFilePath;

      // 设置数据源前显示状态信息
      this.message = `Stream Player - 设置数据源...`;
      LogUtils.getInstance().LOGI(`About to set data source: ${tempFilePath}`);

      // 设置数据源
      if (this.mIjkMediaPlayer) {
        // 记录设置数据源的详细信息
        LogUtils.getInstance().LOGI(`Setting data source for video ${this.currentVideoIndex}: ${tempFilePath}`);
        LogUtils.getInstance().LOGI(`Current player state before setDataSource: attempting to get state info`);

        this.mIjkMediaPlayer.setDataSource(tempFilePath);

        this.message = `Stream Player - 数据源设置完成`;
        LogUtils.getInstance().LOGI(`Set test video source (temp file): ${tempFilePath}`);
      }

    } catch (error) {
      this.message = `Stream Player - 文件处理失败: ${error}`;
      LogUtils.getInstance().LOGI(`Failed to copy rawfile to temp: ${error}`);
      throw new Error(error); // 重新抛出错误，让调用方知道失败了
    }
  }

  // 将ArrayBuffer写入临时文件的方法（实际应用中使用）
  private async writeArrayBufferToFile(buffer: ArrayBuffer): Promise<string> {
    try {
      // 获取应用缓存目录
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      const fileName = `stream_${Date.now()}.mp4`;
      this.tempFilePath = `${cacheDir}/${fileName}`;

      // 将ArrayBuffer写入文件
      const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      const writeLen = fs.writeSync(file.fd, buffer);
      fs.closeSync(file);

      LogUtils.getInstance().LOGI(`Written ${writeLen} bytes to ${this.tempFilePath}`);
      return this.tempFilePath;
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to write ArrayBuffer to file: ${error}`);
      throw new Error(`Failed to write ArrayBuffer to file: ${error}`);
    }
  }

  // 使用流数据开始播放
  private startPlayWithStream() {
    if (!this.hasStreamData) {
      LogUtils.getInstance().LOGI("No stream data available");
      return;
    }

    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    try {
      // 准备播放
      this.mIjkMediaPlayer.prepareAsync();
      LogUtils.getInstance().LOGI("Started preparing stream for playback");
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to start playback: ${error}`);
    }
  }

  // 获取播放器状态字符串（用于日志显示）
  private getPlayerStateString(state: number): string {
    switch (state) {
      case 0:
        return 'IDLE';
      case 1:
        return 'INITIALIZED';
      case 2:
        return 'ASYNC_PREPARING';
      case 3:
        return 'PREPARED';
      case 4:
        return 'STARTED';
      case 5:
        return 'PAUSED';
      case 6:
        return 'COMPLETED';
      case 7:
        return 'STOPPED';
      case 8:
        return 'ERROR';
      case 9:
        return 'END';
      default:
        return `UNKNOWN(${state})`;
    }
  }

  // 安全停止播放器（强制终止所有线程）
  private async safeStopPlayer(): Promise<void> {
    if (!this.mIjkMediaPlayer) {
      return;
    }

    try {
      // 如果正在播放，先停止
      if (this.isPlaying) {
        this.message = `Stream Player - 停止播放...`;
        LogUtils.getInstance().LOGI("Stopping playback");
        this.mIjkMediaPlayer.stop();
        this.isPlaying = false;

        // 等待停止操作完成
        await new Promise<void>((resolve) => setTimeout(resolve, 500));
        LogUtils.getInstance().LOGI("Stop operation completed");
      }

      // 强制终止所有解码线程
      this.message = `Stream Player - 强制终止解码线程...`;
      LogUtils.getInstance().LOGI("Force terminating all decoder threads");
      this.mIjkMediaPlayer.forceTerminateThreads();

      // 等待线程终止完成
      await new Promise<void>((resolve) => setTimeout(resolve, 1000));
      LogUtils.getInstance().LOGI("Force terminate operation completed");

    } catch (error) {
      LogUtils.getInstance().LOGI(`Error in safe stop: ${error}`);
      // 即使出错也继续，因为我们需要释放资源
    }
  }

  // 重新创建播放器实例（支持播放中切换）
  private async recreatePlayer(): Promise<boolean> {
    try {
      this.message = `Stream Player - 准备重建播放器...`;
      LogUtils.getInstance().LOGI("Starting player recreation");

      // 记录当前播放状态
      const wasPlaying = this.isPlaying;
      LogUtils.getInstance().LOGI(`Current playing state: ${wasPlaying}`);

      // 安全停止当前播放器
      await this.safeStopPlayer();

      // 释放旧的播放器实例
      if (this.mIjkMediaPlayer) {
        this.message = `Stream Player - 释放播放器资源...`;
        LogUtils.getInstance().LOGI("Releasing old player instance");
        try {
          this.mIjkMediaPlayer.release();
        } catch (error) {
          LogUtils.getInstance().LOGI(`Error releasing player: ${error}`);
        }
        this.mIjkMediaPlayer = null;
        this.isPlaying = false;
        LogUtils.getInstance().LOGI("Old player instance released");
      }

      // 等待更长时间确保所有线程和资源完全释放
      this.message = `Stream Player - 等待资源完全释放...`;
      await new Promise<void>((resolve) => setTimeout(resolve, 3000));

      // 重新创建播放器实例
      this.message = `Stream Player - 创建新播放器实例...`;
      LogUtils.getInstance().LOGI("Creating new player instance");

      this.mIjkMediaPlayer = IjkMediaPlayer.getInstance();

      if (!this.mContext) {
        throw new Error("XComponent context is null");
      }

      // 设置XComponent上下文
      this.mIjkMediaPlayer.setContext(this.mContext, "xcomponentId");

      // 设置调试模式
      this.mIjkMediaPlayer.setDebug(true);

      // 初始化配置
      this.mIjkMediaPlayer.native_setup();

      // 设置播放器选项
      this.setupPlayerOptions();

      // 设置监听器
      this.setupListeners();

      this.message = `Stream Player - 新播放器实例创建完成`;
      LogUtils.getInstance().LOGI("New player instance created successfully");

      return true;
    } catch (error) {
      this.message = `Stream Player - 播放器重建失败: ${error}`;
      LogUtils.getInstance().LOGI(`Failed to recreate player: ${error}`);
      return false;
    }
  }

  // 切换视频（支持播放中切换）
  private async switchVideo() {
    if (!this.hasStreamData) {
      LogUtils.getInstance().LOGI("No stream data available");
      return;
    }

    const maxRetries = 3;
    let retryCount = 0;

    // 记录切换前的播放状态
    const wasPlaying = this.isPlaying;
    LogUtils.getInstance().LOGI(`Video switch started, was playing: ${wasPlaying}`);

    while (retryCount < maxRetries) {
      try {
        this.message = `Stream Player - 正在切换视频... (${retryCount + 1}/${maxRetries})`;
        LogUtils.getInstance().LOGI(`Starting video switch attempt ${retryCount + 1}/${maxRetries}`);

        // 清理旧的临时文件
        if (this.tempFilePath) {
          try {
            if (fs.accessSync(this.tempFilePath)) {
              fs.unlinkSync(this.tempFilePath);
              LogUtils.getInstance().LOGI(`Deleted old temp file: ${this.tempFilePath}`);
            }
          } catch (error) {
            LogUtils.getInstance().LOGI(`Failed to delete old temp file: ${error}`);
          }
        }

        // 重新创建播放器实例（会自动处理停止播放）
        const recreateSuccess = await this.recreatePlayer();

        if (!recreateSuccess) {
          throw new Error(`播放器重建失败`);
        }

        // 切换到下一个视频
        this.currentVideoIndex = 1 - this.currentVideoIndex;
        this.message = `Stream Player - 正在加载${this.videoNames[this.currentVideoIndex]}...`;

        // 重新加载新视频
        await this.copyRawFileToTemp();

        // 如果之前在播放，自动开始播放新视频
        if (wasPlaying) {
          this.message = `Stream Player - 自动开始播放新视频...`;
          LogUtils.getInstance().LOGI("Auto-starting playback for new video");
          try {
            await new Promise<void>((resolve) => setTimeout(resolve, 500)); // 等待数据源设置完成
            this.startPlayWithStream();
          } catch (error) {
            LogUtils.getInstance().LOGI(`Failed to auto-start playback: ${error}`);
            this.message = `Stream Player - 切换完成，请手动播放`;
          }
        } else {
          this.message = `Stream Player - 当前：${this.videoNames[this.currentVideoIndex]}`;
        }

        LogUtils.getInstance().LOGI(`Successfully switched to video: ${this.videoFiles[this.currentVideoIndex]}`);
        return;

      } catch (error) {
        retryCount++;
        LogUtils.getInstance().LOGI(`Video switch attempt ${retryCount} failed: ${error}`);

        if (retryCount >= maxRetries) {
          this.message = `Stream Player - 切换失败，已重试${maxRetries}次`;
          LogUtils.getInstance().LOGI(`Failed to switch video after ${maxRetries} attempts`);
        } else {
          // 等待一段时间后重试
          this.message = `Stream Player - 切换失败，${2}秒后重试...`;
          await new Promise<void>((resolve) => setTimeout(resolve, 2000));
        }
      }
    }
  }

  // 清除流数据
  private clearStreamData() {
    this.streamBuffer = null;
    this.hasStreamData = false;
    this.message = 'Stream Player';
    this.currentVideoIndex = 0; // 重置视频索引

    // 清理临时文件
    if (this.tempFilePath) {
      try {
        if (fs.accessSync(this.tempFilePath)) {
          fs.unlinkSync(this.tempFilePath);
          LogUtils.getInstance().LOGI(`Deleted temp file: ${this.tempFilePath}`);
        }
      } catch (error) {
        LogUtils.getInstance().LOGI(`Failed to delete temp file: ${error}`);
      }
      this.tempFilePath = '';
    }

    // 重置播放器
    if (this.mIjkMediaPlayer) {
      this.mIjkMediaPlayer.reset();
    }

    LogUtils.getInstance().LOGI("Stream data cleared");
  }
}