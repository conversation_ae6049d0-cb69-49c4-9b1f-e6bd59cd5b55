<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BN_CTX_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#REMOVED-FUNCTIONALITY">REMOVED FUNCTIONALITY</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BN_CTX_new, BN_CTX_secure_new, BN_CTX_free - allocate and free BN_CTX structures</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bn.h&gt;

BN_CTX *BN_CTX_new(void);

BN_CTX *BN_CTX_secure_new(void);

void BN_CTX_free(BN_CTX *c);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>A <b>BN_CTX</b> is a structure that holds <b>BIGNUM</b> temporary variables used by library functions. Since dynamic memory allocation to create <b>BIGNUM</b>s is rather expensive when used in conjunction with repeated subroutine calls, the <b>BN_CTX</b> structure is used.</p>

<p>BN_CTX_new() allocates and initializes a <b>BN_CTX</b> structure. BN_CTX_secure_new() allocates and initializes a <b>BN_CTX</b> structure but uses the secure heap (see <a href="../man3/CRYPTO_secure_malloc.html">CRYPTO_secure_malloc(3)</a>) to hold the <b>BIGNUM</b>s.</p>

<p>BN_CTX_free() frees the components of the <b>BN_CTX</b> and the structure itself. Since BN_CTX_start() is required in order to obtain <b>BIGNUM</b>s from the <b>BN_CTX</b>, in most cases BN_CTX_end() must be called before the <b>BN_CTX</b> may be freed by BN_CTX_free(). If <b>c</b> is NULL, nothing is done.</p>

<p>A given <b>BN_CTX</b> must only be used by a single thread of execution. No locking is performed, and the internal pool allocator will not properly handle multiple threads of execution.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BN_CTX_new() and BN_CTX_secure_new() return a pointer to the <b>BN_CTX</b>. If the allocation fails, they return <b>NULL</b> and sets an error code that can be obtained by <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>.</p>

<p>BN_CTX_free() has no return values.</p>

<h1 id="REMOVED-FUNCTIONALITY">REMOVED FUNCTIONALITY</h1>

<pre><code>void BN_CTX_init(BN_CTX *c);</code></pre>

<p>BN_CTX_init() is no longer available as of OpenSSL 1.1.0. Applications should replace use of BN_CTX_init with BN_CTX_new instead:</p>

<pre><code>BN_CTX *ctx;
ctx = BN_CTX_new();
if (!ctx)
    /* error */
...
BN_CTX_free(ctx);</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/BN_add.html">BN_add(3)</a>, <a href="../man3/BN_CTX_start.html">BN_CTX_start(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>BN_CTX_init() was removed in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2017 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


