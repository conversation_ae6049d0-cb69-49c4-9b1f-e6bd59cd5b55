.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL 1"
.TH OPENSSL 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl \- OpenSSL command line tool
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR
\&\fIcommand\fR
[ \fIcommand_opts\fR ]
[ \fIcommand_args\fR ]
.PP
\&\fBopenssl\fR \fBlist\fR [ \fBstandard-commands\fR | \fBdigest-commands\fR | \fBcipher-commands\fR | \fBcipher-algorithms\fR | \fBdigest-algorithms\fR | \fBpublic-key-algorithms\fR]
.PP
\&\fBopenssl\fR \fBno\-\fR\fIXXX\fR [ \fIarbitrary options\fR ]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
OpenSSL is a cryptography toolkit implementing the Secure Sockets Layer (SSL
v2/v3) and Transport Layer Security (TLS v1) network protocols and related
cryptography standards required by them.
.PP
The \fBopenssl\fR program is a command line tool for using the various
cryptography functions of OpenSSL's \fBcrypto\fR library from the shell.
It can be used for
.PP
.Vb 8
\& o  Creation and management of private keys, public keys and parameters
\& o  Public key cryptographic operations
\& o  Creation of X.509 certificates, CSRs and CRLs
\& o  Calculation of Message Digests
\& o  Encryption and Decryption with Ciphers
\& o  SSL/TLS Client and Server Tests
\& o  Handling of S/MIME signed or encrypted mail
\& o  Time Stamp requests, generation and verification
.Ve
.SH "COMMAND SUMMARY"
.IX Header "COMMAND SUMMARY"
The \fBopenssl\fR program provides a rich variety of commands (\fIcommand\fR in the
SYNOPSIS above), each of which often has a wealth of options and arguments
(\fIcommand_opts\fR and \fIcommand_args\fR in the SYNOPSIS).
.PP
Detailed documentation and use cases for most standard subcommands are available
(e.g., \fBx509\fR\|(1) or \fBopenssl\-x509\fR\|(1)).
.PP
Many commands use an external configuration file for some or all of their
arguments and have a \fB\-config\fR option to specify that file.
The environment variable \fBOPENSSL_CONF\fR can be used to specify
the location of the file.
If the environment variable is not specified, then the file is named
\&\fBopenssl.cnf\fR in the default certificate storage area, whose value
depends on the configuration flags specified when the OpenSSL
was built.
.PP
The list parameters \fBstandard-commands\fR, \fBdigest-commands\fR,
and \fBcipher-commands\fR output a list (one entry per line) of the names
of all standard commands, message digest commands, or cipher commands,
respectively, that are available in the present \fBopenssl\fR utility.
.PP
The list parameters \fBcipher-algorithms\fR and
\&\fBdigest-algorithms\fR list all cipher and message digest names, one entry per line. Aliases are listed as:
.PP
.Vb 1
\& from => to
.Ve
.PP
The list parameter \fBpublic-key-algorithms\fR lists all supported public
key algorithms.
.PP
The command \fBno\-\fR\fIXXX\fR tests whether a command of the
specified name is available.  If no command named \fIXXX\fR exists, it
returns 0 (success) and prints \fBno\-\fR\fIXXX\fR; otherwise it returns 1
and prints \fIXXX\fR.  In both cases, the output goes to \fBstdout\fR and
nothing is printed to \fBstderr\fR.  Additional command line arguments
are always ignored.  Since for each cipher there is a command of the
same name, this provides an easy way for shell scripts to test for the
availability of ciphers in the \fBopenssl\fR program.  (\fBno\-\fR\fIXXX\fR is
not able to detect pseudo-commands such as \fBquit\fR,
\&\fBlist\fR, or \fBno\-\fR\fIXXX\fR itself.)
.SS "Standard Commands"
.IX Subsection "Standard Commands"
.IP \fBasn1parse\fR 4
.IX Item "asn1parse"
Parse an ASN.1 sequence.
.IP \fBca\fR 4
.IX Item "ca"
Certificate Authority (CA) Management.
.IP \fBciphers\fR 4
.IX Item "ciphers"
Cipher Suite Description Determination.
.IP \fBcms\fR 4
.IX Item "cms"
CMS (Cryptographic Message Syntax) utility.
.IP \fBcrl\fR 4
.IX Item "crl"
Certificate Revocation List (CRL) Management.
.IP \fBcrl2pkcs7\fR 4
.IX Item "crl2pkcs7"
CRL to PKCS#7 Conversion.
.IP \fBdgst\fR 4
.IX Item "dgst"
Message Digest Calculation.
.IP \fBdh\fR 4
.IX Item "dh"
Diffie-Hellman Parameter Management.
Obsoleted by \fBdhparam\fR\|(1).
.IP \fBdhparam\fR 4
.IX Item "dhparam"
Generation and Management of Diffie-Hellman Parameters. Superseded by
\&\fBgenpkey\fR\|(1) and \fBpkeyparam\fR\|(1).
.IP \fBdsa\fR 4
.IX Item "dsa"
DSA Data Management.
.IP \fBdsaparam\fR 4
.IX Item "dsaparam"
DSA Parameter Generation and Management. Superseded by
\&\fBgenpkey\fR\|(1) and \fBpkeyparam\fR\|(1).
.IP \fBec\fR 4
.IX Item "ec"
EC (Elliptic curve) key processing.
.IP \fBecparam\fR 4
.IX Item "ecparam"
EC parameter manipulation and generation.
.IP \fBenc\fR 4
.IX Item "enc"
Encoding with Ciphers.
.IP \fBengine\fR 4
.IX Item "engine"
Engine (loadable module) information and manipulation.
.IP \fBerrstr\fR 4
.IX Item "errstr"
Error Number to Error String Conversion.
.IP \fBgendh\fR 4
.IX Item "gendh"
Generation of Diffie-Hellman Parameters.
Obsoleted by \fBdhparam\fR\|(1).
.IP \fBgendsa\fR 4
.IX Item "gendsa"
Generation of DSA Private Key from Parameters. Superseded by
\&\fBgenpkey\fR\|(1) and \fBpkey\fR\|(1).
.IP \fBgenpkey\fR 4
.IX Item "genpkey"
Generation of Private Key or Parameters.
.IP \fBgenrsa\fR 4
.IX Item "genrsa"
Generation of RSA Private Key. Superseded by \fBgenpkey\fR\|(1).
.IP \fBnseq\fR 4
.IX Item "nseq"
Create or examine a Netscape certificate sequence.
.IP \fBocsp\fR 4
.IX Item "ocsp"
Online Certificate Status Protocol utility.
.IP \fBpasswd\fR 4
.IX Item "passwd"
Generation of hashed passwords.
.IP \fBpkcs12\fR 4
.IX Item "pkcs12"
PKCS#12 Data Management.
.IP \fBpkcs7\fR 4
.IX Item "pkcs7"
PKCS#7 Data Management.
.IP \fBpkcs8\fR 4
.IX Item "pkcs8"
PKCS#8 format private key conversion tool.
.IP \fBpkey\fR 4
.IX Item "pkey"
Public and private key management.
.IP \fBpkeyparam\fR 4
.IX Item "pkeyparam"
Public key algorithm parameter management.
.IP \fBpkeyutl\fR 4
.IX Item "pkeyutl"
Public key algorithm cryptographic operation utility.
.IP \fBprime\fR 4
.IX Item "prime"
Compute prime numbers.
.IP \fBrand\fR 4
.IX Item "rand"
Generate pseudo-random bytes.
.IP \fBrehash\fR 4
.IX Item "rehash"
Create symbolic links to certificate and CRL files named by the hash values.
.IP \fBreq\fR 4
.IX Item "req"
PKCS#10 X.509 Certificate Signing Request (CSR) Management.
.IP \fBrsa\fR 4
.IX Item "rsa"
RSA key management.
.IP \fBrsautl\fR 4
.IX Item "rsautl"
RSA utility for signing, verification, encryption, and decryption. Superseded
by  \fBpkeyutl\fR\|(1).
.IP \fBs_client\fR 4
.IX Item "s_client"
This implements a generic SSL/TLS client which can establish a transparent
connection to a remote server speaking SSL/TLS. It's intended for testing
purposes only and provides only rudimentary interface functionality but
internally uses mostly all functionality of the OpenSSL \fBssl\fR library.
.IP \fBs_server\fR 4
.IX Item "s_server"
This implements a generic SSL/TLS server which accepts connections from remote
clients speaking SSL/TLS. It's intended for testing purposes only and provides
only rudimentary interface functionality but internally uses mostly all
functionality of the OpenSSL \fBssl\fR library.  It provides both an own command
line oriented protocol for testing SSL functions and a simple HTTP response
facility to emulate an SSL/TLS\-aware webserver.
.IP \fBs_time\fR 4
.IX Item "s_time"
SSL Connection Timer.
.IP \fBsess_id\fR 4
.IX Item "sess_id"
SSL Session Data Management.
.IP \fBsmime\fR 4
.IX Item "smime"
S/MIME mail processing.
.IP \fBspeed\fR 4
.IX Item "speed"
Algorithm Speed Measurement.
.IP \fBspkac\fR 4
.IX Item "spkac"
SPKAC printing and generating utility.
.IP \fBsrp\fR 4
.IX Item "srp"
Maintain SRP password file.
.IP \fBstoreutl\fR 4
.IX Item "storeutl"
Utility to list and display certificates, keys, CRLs, etc.
.IP \fBts\fR 4
.IX Item "ts"
Time Stamping Authority tool (client/server).
.IP \fBverify\fR 4
.IX Item "verify"
X.509 Certificate Verification.
.IP \fBversion\fR 4
.IX Item "version"
OpenSSL Version Information.
.IP \fBx509\fR 4
.IX Item "x509"
X.509 Certificate Data Management.
.SS "Message Digest Commands"
.IX Subsection "Message Digest Commands"
.IP \fBblake2b512\fR 4
.IX Item "blake2b512"
BLAKE2b\-512 Digest
.IP \fBblake2s256\fR 4
.IX Item "blake2s256"
BLAKE2s\-256 Digest
.IP \fBmd2\fR 4
.IX Item "md2"
MD2 Digest
.IP \fBmd4\fR 4
.IX Item "md4"
MD4 Digest
.IP \fBmd5\fR 4
.IX Item "md5"
MD5 Digest
.IP \fBmdc2\fR 4
.IX Item "mdc2"
MDC2 Digest
.IP \fBrmd160\fR 4
.IX Item "rmd160"
RMD\-160 Digest
.IP \fBsha1\fR 4
.IX Item "sha1"
SHA\-1 Digest
.IP \fBsha224\fR 4
.IX Item "sha224"
SHA\-2 224 Digest
.IP \fBsha256\fR 4
.IX Item "sha256"
SHA\-2 256 Digest
.IP \fBsha384\fR 4
.IX Item "sha384"
SHA\-2 384 Digest
.IP \fBsha512\fR 4
.IX Item "sha512"
SHA\-2 512 Digest
.IP \fBsha3\-224\fR 4
.IX Item "sha3-224"
SHA\-3 224 Digest
.IP \fBsha3\-256\fR 4
.IX Item "sha3-256"
SHA\-3 256 Digest
.IP \fBsha3\-384\fR 4
.IX Item "sha3-384"
SHA\-3 384 Digest
.IP \fBsha3\-512\fR 4
.IX Item "sha3-512"
SHA\-3 512 Digest
.IP \fBshake128\fR 4
.IX Item "shake128"
SHA\-3 SHAKE128 Digest
.IP \fBshake256\fR 4
.IX Item "shake256"
SHA\-3 SHAKE256 Digest
.IP \fBsm3\fR 4
.IX Item "sm3"
SM3 Digest
.SS "Encoding and Cipher Commands"
.IX Subsection "Encoding and Cipher Commands"
The following aliases provide convenient access to the most used encodings
and ciphers.
.PP
Depending on how OpenSSL was configured and built, not all ciphers listed
here may be present. See \fBenc\fR\|(1) for more information and command usage.
.IP "\fBaes128\fR, \fBaes\-128\-cbc\fR, \fBaes\-128\-cfb\fR, \fBaes\-128\-ctr\fR, \fBaes\-128\-ecb\fR, \fBaes\-128\-ofb\fR" 4
.IX Item "aes128, aes-128-cbc, aes-128-cfb, aes-128-ctr, aes-128-ecb, aes-128-ofb"
AES\-128 Cipher
.IP "\fBaes192\fR, \fBaes\-192\-cbc\fR, \fBaes\-192\-cfb\fR, \fBaes\-192\-ctr\fR, \fBaes\-192\-ecb\fR, \fBaes\-192\-ofb\fR" 4
.IX Item "aes192, aes-192-cbc, aes-192-cfb, aes-192-ctr, aes-192-ecb, aes-192-ofb"
AES\-192 Cipher
.IP "\fBaes256\fR, \fBaes\-256\-cbc\fR, \fBaes\-256\-cfb\fR, \fBaes\-256\-ctr\fR, \fBaes\-256\-ecb\fR, \fBaes\-256\-ofb\fR" 4
.IX Item "aes256, aes-256-cbc, aes-256-cfb, aes-256-ctr, aes-256-ecb, aes-256-ofb"
AES\-256 Cipher
.IP "\fBaria128\fR, \fBaria\-128\-cbc\fR, \fBaria\-128\-cfb\fR, \fBaria\-128\-ctr\fR, \fBaria\-128\-ecb\fR, \fBaria\-128\-ofb\fR" 4
.IX Item "aria128, aria-128-cbc, aria-128-cfb, aria-128-ctr, aria-128-ecb, aria-128-ofb"
Aria\-128 Cipher
.IP "\fBaria192\fR, \fBaria\-192\-cbc\fR, \fBaria\-192\-cfb\fR, \fBaria\-192\-ctr\fR, \fBaria\-192\-ecb\fR, \fBaria\-192\-ofb\fR" 4
.IX Item "aria192, aria-192-cbc, aria-192-cfb, aria-192-ctr, aria-192-ecb, aria-192-ofb"
Aria\-192 Cipher
.IP "\fBaria256\fR, \fBaria\-256\-cbc\fR, \fBaria\-256\-cfb\fR, \fBaria\-256\-ctr\fR, \fBaria\-256\-ecb\fR, \fBaria\-256\-ofb\fR" 4
.IX Item "aria256, aria-256-cbc, aria-256-cfb, aria-256-ctr, aria-256-ecb, aria-256-ofb"
Aria\-256 Cipher
.IP \fBbase64\fR 4
.IX Item "base64"
Base64 Encoding
.IP "\fBbf\fR, \fBbf-cbc\fR, \fBbf-cfb\fR, \fBbf-ecb\fR, \fBbf-ofb\fR" 4
.IX Item "bf, bf-cbc, bf-cfb, bf-ecb, bf-ofb"
Blowfish Cipher
.IP "\fBcamellia128\fR, \fBcamellia\-128\-cbc\fR, \fBcamellia\-128\-cfb\fR, \fBcamellia\-128\-ctr\fR, \fBcamellia\-128\-ecb\fR, \fBcamellia\-128\-ofb\fR" 4
.IX Item "camellia128, camellia-128-cbc, camellia-128-cfb, camellia-128-ctr, camellia-128-ecb, camellia-128-ofb"
Camellia\-128 Cipher
.IP "\fBcamellia192\fR, \fBcamellia\-192\-cbc\fR, \fBcamellia\-192\-cfb\fR, \fBcamellia\-192\-ctr\fR, \fBcamellia\-192\-ecb\fR, \fBcamellia\-192\-ofb\fR" 4
.IX Item "camellia192, camellia-192-cbc, camellia-192-cfb, camellia-192-ctr, camellia-192-ecb, camellia-192-ofb"
Camellia\-192 Cipher
.IP "\fBcamellia256\fR, \fBcamellia\-256\-cbc\fR, \fBcamellia\-256\-cfb\fR, \fBcamellia\-256\-ctr\fR, \fBcamellia\-256\-ecb\fR, \fBcamellia\-256\-ofb\fR" 4
.IX Item "camellia256, camellia-256-cbc, camellia-256-cfb, camellia-256-ctr, camellia-256-ecb, camellia-256-ofb"
Camellia\-256 Cipher
.IP "\fBcast\fR, \fBcast-cbc\fR" 4
.IX Item "cast, cast-cbc"
CAST Cipher
.IP "\fBcast5\-cbc\fR, \fBcast5\-cfb\fR, \fBcast5\-ecb\fR, \fBcast5\-ofb\fR" 4
.IX Item "cast5-cbc, cast5-cfb, cast5-ecb, cast5-ofb"
CAST5 Cipher
.IP \fBchacha20\fR 4
.IX Item "chacha20"
Chacha20 Cipher
.IP "\fBdes\fR, \fBdes-cbc\fR, \fBdes-cfb\fR, \fBdes-ecb\fR, \fBdes-ede\fR, \fBdes-ede-cbc\fR, \fBdes-ede-cfb\fR, \fBdes-ede-ofb\fR, \fBdes-ofb\fR" 4
.IX Item "des, des-cbc, des-cfb, des-ecb, des-ede, des-ede-cbc, des-ede-cfb, des-ede-ofb, des-ofb"
DES Cipher
.IP "\fBdes3\fR, \fBdesx\fR, \fBdes\-ede3\fR, \fBdes\-ede3\-cbc\fR, \fBdes\-ede3\-cfb\fR, \fBdes\-ede3\-ofb\fR" 4
.IX Item "des3, desx, des-ede3, des-ede3-cbc, des-ede3-cfb, des-ede3-ofb"
Triple-DES Cipher
.IP "\fBidea\fR, \fBidea-cbc\fR, \fBidea-cfb\fR, \fBidea-ecb\fR, \fBidea-ofb\fR" 4
.IX Item "idea, idea-cbc, idea-cfb, idea-ecb, idea-ofb"
IDEA Cipher
.IP "\fBrc2\fR, \fBrc2\-cbc\fR, \fBrc2\-cfb\fR, \fBrc2\-ecb\fR, \fBrc2\-ofb\fR" 4
.IX Item "rc2, rc2-cbc, rc2-cfb, rc2-ecb, rc2-ofb"
RC2 Cipher
.IP \fBrc4\fR 4
.IX Item "rc4"
RC4 Cipher
.IP "\fBrc5\fR, \fBrc5\-cbc\fR, \fBrc5\-cfb\fR, \fBrc5\-ecb\fR, \fBrc5\-ofb\fR" 4
.IX Item "rc5, rc5-cbc, rc5-cfb, rc5-ecb, rc5-ofb"
RC5 Cipher
.IP "\fBseed\fR, \fBseed-cbc\fR, \fBseed-cfb\fR, \fBseed-ecb\fR, \fBseed-ofb\fR" 4
.IX Item "seed, seed-cbc, seed-cfb, seed-ecb, seed-ofb"
SEED Cipher
.IP "\fBsm4\fR, \fBsm4\-cbc\fR, \fBsm4\-cfb\fR, \fBsm4\-ctr\fR, \fBsm4\-ecb\fR, \fBsm4\-ofb\fR" 4
.IX Item "sm4, sm4-cbc, sm4-cfb, sm4-ctr, sm4-ecb, sm4-ofb"
SM4 Cipher
.SH OPTIONS
.IX Header "OPTIONS"
Details of which options are available depend on the specific command.
This section describes some common options with common behavior.
.SS "Common Options"
.IX Subsection "Common Options"
.IP \fB\-help\fR 4
.IX Item "-help"
Provides a terse summary of all options.
.SS "Pass Phrase Options"
.IX Subsection "Pass Phrase Options"
Several commands accept password arguments, typically using \fB\-passin\fR
and \fB\-passout\fR for input and output passwords respectively. These allow
the password to be obtained from a variety of sources. Both of these
options take a single argument whose format is described below. If no
password argument is given and a password is required then the user is
prompted to enter one: this will typically be read from the current
terminal with echoing turned off.
.PP
Note that character encoding may be relevant, please see
\&\fBpassphrase\-encoding\fR\|(7).
.IP \fBpass:password\fR 4
.IX Item "pass:password"
The actual password is \fBpassword\fR. Since the password is visible
to utilities (like 'ps' under Unix) this form should only be used
where security is not important.
.IP \fBenv:var\fR 4
.IX Item "env:var"
Obtain the password from the environment variable \fBvar\fR. Since
the environment of other processes is visible on certain platforms
(e.g. ps under certain Unix OSes) this option should be used with caution.
.IP \fBfile:pathname\fR 4
.IX Item "file:pathname"
The first line of \fBpathname\fR is the password. If the same \fBpathname\fR
argument is supplied to \fB\-passin\fR and \fB\-passout\fR arguments then the first
line will be used for the input password and the next line for the output
password. \fBpathname\fR need not refer to a regular file: it could for example
refer to a device or named pipe.
.IP \fBfd:number\fR 4
.IX Item "fd:number"
Read the password from the file descriptor \fBnumber\fR. This can be used to
send the data via a pipe for example.
.IP \fBstdin\fR 4
.IX Item "stdin"
Read the password from standard input.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBasn1parse\fR\|(1), \fBca\fR\|(1), \fBciphers\fR\|(1), \fBcms\fR\|(1), \fBconfig\fR\|(5),
\&\fBcrl\fR\|(1), \fBcrl2pkcs7\fR\|(1), \fBdgst\fR\|(1),
\&\fBdhparam\fR\|(1), \fBdsa\fR\|(1), \fBdsaparam\fR\|(1),
\&\fBec\fR\|(1), \fBecparam\fR\|(1),
\&\fBenc\fR\|(1), \fBengine\fR\|(1), \fBerrstr\fR\|(1), \fBgendsa\fR\|(1), \fBgenpkey\fR\|(1),
\&\fBgenrsa\fR\|(1), \fBnseq\fR\|(1), \fBocsp\fR\|(1),
\&\fBpasswd\fR\|(1),
\&\fBpkcs12\fR\|(1), \fBpkcs7\fR\|(1), \fBpkcs8\fR\|(1),
\&\fBpkey\fR\|(1), \fBpkeyparam\fR\|(1), \fBpkeyutl\fR\|(1), \fBprime\fR\|(1),
\&\fBrand\fR\|(1), \fBrehash\fR\|(1), \fBreq\fR\|(1), \fBrsa\fR\|(1),
\&\fBrsautl\fR\|(1), \fBs_client\fR\|(1),
\&\fBs_server\fR\|(1), \fBs_time\fR\|(1), \fBsess_id\fR\|(1),
\&\fBsmime\fR\|(1), \fBspeed\fR\|(1), \fBspkac\fR\|(1), \fBsrp\fR\|(1), \fBstoreutl\fR\|(1),
\&\fBts\fR\|(1),
\&\fBverify\fR\|(1), \fBversion\fR\|(1), \fBx509\fR\|(1),
\&\fBcrypto\fR\|(7), \fBssl\fR\|(7), \fBx509v3_config\fR\|(5)
.SH HISTORY
.IX Header "HISTORY"
The \fBlist\-\fR\fIXXX\fR\fB\-algorithms\fR pseudo-commands were added in OpenSSL 1.0.0;
For notes on the availability of other commands, see their individual
manual pages.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
