.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_INTEGER_GET_INT64 3"
.TH ASN1_INTEGER_GET_INT64 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_INTEGER_get_uint64, ASN1_INTEGER_set_uint64,
ASN1_INTEGER_get_int64, ASN1_INTEGER_get, ASN1_INTEGER_set_int64, ASN1_INTEGER_set, BN_to_ASN1_INTEGER, ASN1_INTEGER_to_BN, ASN1_ENUMERATED_get_int64, ASN1_ENUMERATED_get, ASN1_ENUMERATED_set_int64, ASN1_ENUMERATED_set, BN_to_ASN1_ENUMERATED, ASN1_ENUMERATED_to_BN
\&\- ASN.1 INTEGER and ENUMERATED utilities
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1.h>
\&
\& int ASN1_INTEGER_get_int64(int64_t *pr, const ASN1_INTEGER *a);
\& long ASN1_INTEGER_get(const ASN1_INTEGER *a);
\&
\& int ASN1_INTEGER_set_int64(ASN1_INTEGER *a, int64_t r);
\& int ASN1_INTEGER_set(const ASN1_INTEGER *a, long v);
\&
\& int ASN1_INTEGER_get_uint64(uint64_t *pr, const ASN1_INTEGER *a);
\& int ASN1_INTEGER_set_uint64(ASN1_INTEGER *a, uint64_t r);
\&
\& ASN1_INTEGER *BN_to_ASN1_INTEGER(const BIGNUM *bn, ASN1_INTEGER *ai);
\& BIGNUM *ASN1_INTEGER_to_BN(const ASN1_INTEGER *ai, BIGNUM *bn);
\&
\& int ASN1_ENUMERATED_get_int64(int64_t *pr, const ASN1_ENUMERATED *a);
\& long ASN1_ENUMERATED_get(const ASN1_ENUMERATED *a);
\&
\& int ASN1_ENUMERATED_set_int64(ASN1_ENUMERATED *a, int64_t r);
\& int ASN1_ENUMERATED_set(ASN1_ENUMERATED *a, long v);
\&
\& ASN1_ENUMERATED *BN_to_ASN1_ENUMERATED(BIGNUM *bn, ASN1_ENUMERATED *ai);
\& BIGNUM *ASN1_ENUMERATED_to_BN(ASN1_ENUMERATED *ai, BIGNUM *bn);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions convert to and from \fBASN1_INTEGER\fR and \fBASN1_ENUMERATED\fR
structures.
.PP
\&\fBASN1_INTEGER_get_int64()\fR converts an \fBASN1_INTEGER\fR into an \fBint64_t\fR type
If successful it returns 1 and sets \fB*pr\fR to the value of \fBa\fR. If it fails
(due to invalid type or the value being too big to fit into an \fBint64_t\fR type)
it returns 0.
.PP
\&\fBASN1_INTEGER_get_uint64()\fR is similar to \fBASN1_INTEGER_get_int64_t()\fR except it
converts to a \fBuint64_t\fR type and an error is returned if the passed integer
is negative.
.PP
\&\fBASN1_INTEGER_get()\fR also returns the value of \fBa\fR but it returns 0 if \fBa\fR is
NULL and \-1 on error (which is ambiguous because \-1 is a legitimate value for
an \fBASN1_INTEGER\fR). New applications should use \fBASN1_INTEGER_get_int64()\fR
instead.
.PP
\&\fBASN1_INTEGER_set_int64()\fR sets the value of \fBASN1_INTEGER\fR \fBa\fR to the
\&\fBint64_t\fR value \fBr\fR.
.PP
\&\fBASN1_INTEGER_set_uint64()\fR sets the value of \fBASN1_INTEGER\fR \fBa\fR to the
\&\fBuint64_t\fR value \fBr\fR.
.PP
\&\fBASN1_INTEGER_set()\fR sets the value of \fBASN1_INTEGER\fR \fBa\fR to the \fBlong\fR value
\&\fBv\fR.
.PP
\&\fBBN_to_ASN1_INTEGER()\fR converts \fBBIGNUM\fR \fBbn\fR to an \fBASN1_INTEGER\fR. If \fBai\fR
is NULL a new \fBASN1_INTEGER\fR structure is returned. If \fBai\fR is not NULL then
the existing structure will be used instead.
.PP
\&\fBASN1_INTEGER_to_BN()\fR converts ASN1_INTEGER \fBai\fR into a \fBBIGNUM\fR. If \fBbn\fR is
NULL a new \fBBIGNUM\fR structure is returned. If \fBbn\fR is not NULL then the
existing structure will be used instead.
.PP
\&\fBASN1_ENUMERATED_get_int64()\fR, \fBASN1_ENUMERATED_set_int64()\fR,
\&\fBASN1_ENUMERATED_set()\fR, \fBBN_to_ASN1_ENUMERATED()\fR and \fBASN1_ENUMERATED_to_BN()\fR
behave in an identical way to their ASN1_INTEGER counterparts except they
operate on an \fBASN1_ENUMERATED\fR value.
.PP
\&\fBASN1_ENUMERATED_get()\fR returns the value of \fBa\fR in a similar way to
\&\fBASN1_INTEGER_get()\fR but it returns \fB0xffffffffL\fR if the value of \fBa\fR will not
fit in a long type. New applications should use \fBASN1_ENUMERATED_get_int64()\fR
instead.
.SH NOTES
.IX Header "NOTES"
In general an \fBASN1_INTEGER\fR or \fBASN1_ENUMERATED\fR type can contain an
integer of almost arbitrary size and so cannot always be represented by a C
\&\fBint64_t\fR type. However, in many cases (for example version numbers) they
represent small integers which can be more easily manipulated if converted to
an appropriate C integer type.
.SH BUGS
.IX Header "BUGS"
The ambiguous return values of \fBASN1_INTEGER_get()\fR and \fBASN1_ENUMERATED_get()\fR
mean these functions should be avoided if possible. They are retained for
compatibility. Normally the ambiguous return values are not legitimate
values for the fields they represent.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASN1_INTEGER_set_int64()\fR, \fBASN1_INTEGER_set()\fR, \fBASN1_ENUMERATED_set_int64()\fR and
\&\fBASN1_ENUMERATED_set()\fR return 1 for success and 0 for failure. They will only
fail if a memory allocation error occurs.
.PP
\&\fBASN1_INTEGER_get_int64()\fR and \fBASN1_ENUMERATED_get_int64()\fR return 1 for success
and 0 for failure. They will fail if the passed type is incorrect (this will
only happen if there is a programming error) or if the value exceeds the range
of an \fBint64_t\fR type.
.PP
\&\fBBN_to_ASN1_INTEGER()\fR and \fBBN_to_ASN1_ENUMERATED()\fR return an \fBASN1_INTEGER\fR or
\&\fBASN1_ENUMERATED\fR structure respectively or NULL if an error occurs. They will
only fail due to a memory allocation error.
.PP
\&\fBASN1_INTEGER_to_BN()\fR and \fBASN1_ENUMERATED_to_BN()\fR return a \fBBIGNUM\fR structure
of NULL if an error occurs. They can fail if the passed type is incorrect
(due to programming error) or due to a memory allocation failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBASN1_INTEGER_set_int64()\fR, \fBASN1_INTEGER_get_int64()\fR,
\&\fBASN1_ENUMERATED_set_int64()\fR and \fBASN1_ENUMERATED_get_int64()\fR
were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
