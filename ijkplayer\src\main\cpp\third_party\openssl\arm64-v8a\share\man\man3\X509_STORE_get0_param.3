.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_STORE_GET0_PARAM 3"
.TH X509_STORE_GET0_PARAM 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_STORE_get0_param, X509_STORE_set1_param,
X509_STORE_get0_objects \- X509_STORE setter and getter functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509_vfy.h>
\&
\& X509_VERIFY_PARAM *X509_STORE_get0_param(X509_STORE *ctx);
\& int X509_STORE_set1_param(X509_STORE *ctx, X509_VERIFY_PARAM *pm);
\& STACK_OF(X509_OBJECT) *X509_STORE_get0_objects(X509_STORE *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_STORE_set1_param()\fR sets the verification parameters
to \fBpm\fR for \fBctx\fR.
.PP
\&\fBX509_STORE_get0_param()\fR retrieves an internal pointer to the verification
parameters for \fBctx\fR. The returned pointer must not be freed by the
calling application
.PP
\&\fBX509_STORE_get0_objects()\fR retrieve an internal pointer to the store's
X509 object cache. The cache contains \fBX509\fR and \fBX509_CRL\fR objects. The
returned pointer must not be freed by the calling application.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_STORE_get0_param()\fR returns a pointer to an
\&\fBX509_VERIFY_PARAM\fR structure.
.PP
\&\fBX509_STORE_set1_param()\fR returns 1 for success and 0 for failure.
.PP
\&\fBX509_STORE_get0_objects()\fR returns a pointer to a stack of \fBX509_OBJECT\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_STORE_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBX509_STORE_get0_param\fR and \fBX509_STORE_get0_objects\fR were added in
OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
