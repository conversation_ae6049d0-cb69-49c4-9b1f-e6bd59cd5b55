.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ERR_PUT_ERROR 3"
.TH ERR_PUT_ERROR 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ERR_put_error, ERR_add_error_data, ERR_add_error_vdata \- record an error
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/err.h>
\&
\& void ERR_put_error(int lib, int func, int reason, const char *file, int line);
\&
\& void ERR_add_error_data(int num, ...);
\& void ERR_add_error_vdata(int num, va_list arg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBERR_put_error()\fR adds an error code to the thread's error queue. It
signals that the error of reason code \fBreason\fR occurred in function
\&\fBfunc\fR of library \fBlib\fR, in line number \fBline\fR of \fBfile\fR.
This function is usually called by a macro.
.PP
\&\fBERR_add_error_data()\fR associates the concatenation of its \fBnum\fR string
arguments with the error code added last.
\&\fBERR_add_error_vdata()\fR is similar except the argument is a \fBva_list\fR.
.PP
\&\fBERR_load_strings\fR\|(3) can be used to register
error strings so that the application can a generate human-readable
error messages for the error code.
.SS "Reporting errors"
.IX Subsection "Reporting errors"
Each sub-library has a specific macro \fBXXXerr()\fR that is used to report
errors. Its first argument is a function code \fBXXX_F_...\fR, the second
argument is a reason code \fBXXX_R_...\fR. Function codes are derived
from the function names; reason codes consist of textual error
descriptions. For example, the function \fBssl3_read_bytes()\fR reports a
"handshake failure" as follows:
.PP
.Vb 1
\& SSLerr(SSL_F_SSL3_READ_BYTES, SSL_R_SSL_HANDSHAKE_FAILURE);
.Ve
.PP
Function and reason codes should consist of uppercase characters,
numbers and underscores only. The error file generation script translates
function codes into function names by looking in the header files
for an appropriate function name, if none is found it just uses
the capitalized form such as "SSL3_READ_BYTES" in the above example.
.PP
The trailing section of a reason code (after the "_R_") is translated
into lowercase and underscores changed to spaces.
.PP
Although a library will normally report errors using its own specific
XXXerr macro, another library's macro can be used. This is normally
only done when a library wants to include ASN1 code which must use
the \fBASN1err()\fR macro.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBERR_put_error()\fR and \fBERR_add_error_data()\fR return
no values.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_load_strings\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
