.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CA 1"
.TH CA 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-ca,
ca \- sample minimal CA application
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBca\fR
[\fB\-help\fR]
[\fB\-verbose\fR]
[\fB\-config filename\fR]
[\fB\-name section\fR]
[\fB\-gencrl\fR]
[\fB\-revoke file\fR]
[\fB\-valid file\fR]
[\fB\-status serial\fR]
[\fB\-updatedb\fR]
[\fB\-crl_reason reason\fR]
[\fB\-crl_hold instruction\fR]
[\fB\-crl_compromise time\fR]
[\fB\-crl_CA_compromise time\fR]
[\fB\-crldays days\fR]
[\fB\-crlhours hours\fR]
[\fB\-crlexts section\fR]
[\fB\-startdate date\fR]
[\fB\-enddate date\fR]
[\fB\-days arg\fR]
[\fB\-md arg\fR]
[\fB\-policy arg\fR]
[\fB\-keyfile arg\fR]
[\fB\-keyform PEM|DER\fR]
[\fB\-key arg\fR]
[\fB\-passin arg\fR]
[\fB\-cert file\fR]
[\fB\-selfsign\fR]
[\fB\-in file\fR]
[\fB\-out file\fR]
[\fB\-notext\fR]
[\fB\-outdir dir\fR]
[\fB\-infiles\fR]
[\fB\-spkac file\fR]
[\fB\-ss_cert file\fR]
[\fB\-preserveDN\fR]
[\fB\-noemailDN\fR]
[\fB\-batch\fR]
[\fB\-msie_hack\fR]
[\fB\-extensions section\fR]
[\fB\-extfile section\fR]
[\fB\-engine id\fR]
[\fB\-subj arg\fR]
[\fB\-utf8\fR]
[\fB\-sigopt nm:v\fR]
[\fB\-create_serial\fR]
[\fB\-rand_serial\fR]
[\fB\-multivalue\-rdn\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBca\fR command is a minimal CA application. It can be used
to sign certificate requests in a variety of forms and generate
CRLs it also maintains a text database of issued certificates
and their status.
.PP
The options descriptions will be divided into each purpose.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP \fB\-verbose\fR 4
.IX Item "-verbose"
This prints extra details about the operations being performed.
.IP "\fB\-config filename\fR" 4
.IX Item "-config filename"
Specifies the configuration file to use.
Optional; for a description of the default value,
see "COMMAND SUMMARY" in \fBopenssl\fR\|(1).
.IP "\fB\-name section\fR" 4
.IX Item "-name section"
Specifies the configuration file section to use (overrides
\&\fBdefault_ca\fR in the \fBca\fR section).
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
An input filename containing a single certificate request to be
signed by the CA.
.IP "\fB\-ss_cert filename\fR" 4
.IX Item "-ss_cert filename"
A single self-signed certificate to be signed by the CA.
.IP "\fB\-spkac filename\fR" 4
.IX Item "-spkac filename"
A file containing a single Netscape signed public key and challenge
and additional field values to be signed by the CA. See the \fBSPKAC FORMAT\fR
section for information on the required input and output format.
.IP \fB\-infiles\fR 4
.IX Item "-infiles"
If present this should be the last option, all subsequent arguments
are taken as the names of files containing certificate requests.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
The output file to output certificates to. The default is standard
output. The certificate details will also be printed out to this
file in PEM format (except that \fB\-spkac\fR outputs DER format).
.IP "\fB\-outdir directory\fR" 4
.IX Item "-outdir directory"
The directory to output certificates to. The certificate will be
written to a filename consisting of the serial number in hex with
".pem" appended.
.IP \fB\-cert\fR 4
.IX Item "-cert"
The CA certificate file.
.IP "\fB\-keyfile filename\fR" 4
.IX Item "-keyfile filename"
The private key to sign requests with.
.IP "\fB\-keyform PEM|DER\fR" 4
.IX Item "-keyform PEM|DER"
The format of the data in the private key file.
The default is PEM.
.IP "\fB\-sigopt nm:v\fR" 4
.IX Item "-sigopt nm:v"
Pass options to the signature algorithm during sign or verify operations.
Names and values of these options are algorithm-specific.
.IP "\fB\-key password\fR" 4
.IX Item "-key password"
The password used to encrypt the private key. Since on some
systems the command line arguments are visible (e.g. Unix with
the 'ps' utility) this option should be used with caution.
.IP \fB\-selfsign\fR 4
.IX Item "-selfsign"
Indicates the issued certificates are to be signed with the key
the certificate requests were signed with (given with \fB\-keyfile\fR).
Certificate requests signed with a different key are ignored.  If
\&\fB\-spkac\fR, \fB\-ss_cert\fR or \fB\-gencrl\fR are given, \fB\-selfsign\fR is
ignored.
.Sp
A consequence of using \fB\-selfsign\fR is that the self-signed
certificate appears among the entries in the certificate database
(see the configuration option \fBdatabase\fR), and uses the same
serial number counter as all other certificates sign with the
self-signed certificate.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
The key password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP \fB\-notext\fR 4
.IX Item "-notext"
Don't output the text form of a certificate to the output file.
.IP "\fB\-startdate date\fR" 4
.IX Item "-startdate date"
This allows the start date to be explicitly set. The format of the
date is YYMMDDHHMMSSZ (the same as an ASN1 UTCTime structure), or
YYYYMMDDHHMMSSZ (the same as an ASN1 GeneralizedTime structure). In
both formats, seconds SS and timezone Z must be present.
.IP "\fB\-enddate date\fR" 4
.IX Item "-enddate date"
This allows the expiry date to be explicitly set. The format of the
date is YYMMDDHHMMSSZ (the same as an ASN1 UTCTime structure), or
YYYYMMDDHHMMSSZ (the same as an ASN1 GeneralizedTime structure). In
both formats, seconds SS and timezone Z must be present.
.IP "\fB\-days arg\fR" 4
.IX Item "-days arg"
The number of days to certify the certificate for.
.IP "\fB\-md alg\fR" 4
.IX Item "-md alg"
The message digest to use.
Any digest supported by the OpenSSL \fBdgst\fR command can be used. For signing
algorithms that do not support a digest (i.e. Ed25519 and Ed448) any message
digest that is set is ignored. This option also applies to CRLs.
.IP "\fB\-policy arg\fR" 4
.IX Item "-policy arg"
This option defines the CA "policy" to use. This is a section in
the configuration file which decides which fields should be mandatory
or match the CA certificate. Check out the \fBPOLICY FORMAT\fR section
for more information.
.IP \fB\-msie_hack\fR 4
.IX Item "-msie_hack"
This is a deprecated option to make \fBca\fR work with very old versions of
the IE certificate enrollment control "certenr3". It used UniversalStrings
for almost everything. Since the old control has various security bugs
its use is strongly discouraged.
.IP \fB\-preserveDN\fR 4
.IX Item "-preserveDN"
Normally the DN order of a certificate is the same as the order of the
fields in the relevant policy section. When this option is set the order
is the same as the request. This is largely for compatibility with the
older IE enrollment control which would only accept certificates if their
DNs match the order of the request. This is not needed for Xenroll.
.IP \fB\-noemailDN\fR 4
.IX Item "-noemailDN"
The DN of a certificate can contain the EMAIL field if present in the
request DN, however, it is good policy just having the e\-mail set into
the altName extension of the certificate. When this option is set the
EMAIL field is removed from the certificate' subject and set only in
the, eventually present, extensions. The \fBemail_in_dn\fR keyword can be
used in the configuration file to enable this behaviour.
.IP \fB\-batch\fR 4
.IX Item "-batch"
This sets the batch mode. In this mode no questions will be asked
and all certificates will be certified automatically.
.IP "\fB\-extensions section\fR" 4
.IX Item "-extensions section"
The section of the configuration file containing certificate extensions
to be added when a certificate is issued (defaults to \fBx509_extensions\fR
unless the \fB\-extfile\fR option is used). If no extension section is
present then, a V1 certificate is created. If the extension section
is present (even if it is empty), then a V3 certificate is created. See the
\&\fBx509v3_config\fR\|(5) manual page for details of the
extension section format.
.IP "\fB\-extfile file\fR" 4
.IX Item "-extfile file"
An additional configuration file to read certificate extensions from
(using the default section unless the \fB\-extensions\fR option is also
used).
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBca\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.IP "\fB\-subj arg\fR" 4
.IX Item "-subj arg"
Supersedes subject name given in the request.
The arg must be formatted as \fI/type0=value0/type1=value1/type2=...\fR.
Keyword characters may be escaped by \e (backslash), and whitespace is retained.
Empty values are permitted, but the corresponding type will not be included
in the resulting certificate.
.IP \fB\-utf8\fR 4
.IX Item "-utf8"
This option causes field values to be interpreted as UTF8 strings, by
default they are interpreted as ASCII. This means that the field
values, whether prompted from a terminal or obtained from a
configuration file, must be valid UTF8 strings.
.IP \fB\-create_serial\fR 4
.IX Item "-create_serial"
If reading serial from the text file as specified in the configuration
fails, specifying this option creates a new random serial to be used as next
serial number.
To get random serial numbers, use the \fB\-rand_serial\fR flag instead; this
should only be used for simple error-recovery.
.IP \fB\-rand_serial\fR 4
.IX Item "-rand_serial"
Generate a large random number to use as the serial number.
This overrides any option or configuration to use a serial number file.
.IP \fB\-multivalue\-rdn\fR 4
.IX Item "-multivalue-rdn"
This option causes the \-subj argument to be interpreted with full
support for multivalued RDNs. Example:
.Sp
\&\fI/DC=org/DC=OpenSSL/DC=users/UID=123456+CN=John Doe\fR
.Sp
If \-multi\-rdn is not used then the UID value is \fI123456+CN=John Doe\fR.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.SH "CRL OPTIONS"
.IX Header "CRL OPTIONS"
.IP \fB\-gencrl\fR 4
.IX Item "-gencrl"
This option generates a CRL based on information in the index file.
.IP "\fB\-crldays num\fR" 4
.IX Item "-crldays num"
The number of days before the next CRL is due. That is the days from
now to place in the CRL nextUpdate field.
.IP "\fB\-crlhours num\fR" 4
.IX Item "-crlhours num"
The number of hours before the next CRL is due.
.IP "\fB\-revoke filename\fR" 4
.IX Item "-revoke filename"
A filename containing a certificate to revoke.
.IP "\fB\-valid filename\fR" 4
.IX Item "-valid filename"
A filename containing a certificate to add a Valid certificate entry.
.IP "\fB\-status serial\fR" 4
.IX Item "-status serial"
Displays the revocation status of the certificate with the specified
serial number and exits.
.IP \fB\-updatedb\fR 4
.IX Item "-updatedb"
Updates the database index to purge expired certificates.
.IP "\fB\-crl_reason reason\fR" 4
.IX Item "-crl_reason reason"
Revocation reason, where \fBreason\fR is one of: \fBunspecified\fR, \fBkeyCompromise\fR,
\&\fBCACompromise\fR, \fBaffiliationChanged\fR, \fBsuperseded\fR, \fBcessationOfOperation\fR,
\&\fBcertificateHold\fR or \fBremoveFromCRL\fR. The matching of \fBreason\fR is case
insensitive. Setting any revocation reason will make the CRL v2.
.Sp
In practice \fBremoveFromCRL\fR is not particularly useful because it is only used
in delta CRLs which are not currently implemented.
.IP "\fB\-crl_hold instruction\fR" 4
.IX Item "-crl_hold instruction"
This sets the CRL revocation reason code to \fBcertificateHold\fR and the hold
instruction to \fBinstruction\fR which must be an OID. Although any OID can be
used only \fBholdInstructionNone\fR (the use of which is discouraged by RFC2459)
\&\fBholdInstructionCallIssuer\fR or \fBholdInstructionReject\fR will normally be used.
.IP "\fB\-crl_compromise time\fR" 4
.IX Item "-crl_compromise time"
This sets the revocation reason to \fBkeyCompromise\fR and the compromise time to
\&\fBtime\fR. \fBtime\fR should be in GeneralizedTime format that is \fBYYYYMMDDHHMMSSZ\fR.
.IP "\fB\-crl_CA_compromise time\fR" 4
.IX Item "-crl_CA_compromise time"
This is the same as \fBcrl_compromise\fR except the revocation reason is set to
\&\fBCACompromise\fR.
.IP "\fB\-crlexts section\fR" 4
.IX Item "-crlexts section"
The section of the configuration file containing CRL extensions to
include. If no CRL extension section is present then a V1 CRL is
created, if the CRL extension section is present (even if it is
empty) then a V2 CRL is created. The CRL extensions specified are
CRL extensions and \fBnot\fR CRL entry extensions.  It should be noted
that some software (for example Netscape) can't handle V2 CRLs. See
\&\fBx509v3_config\fR\|(5) manual page for details of the
extension section format.
.SH "CONFIGURATION FILE OPTIONS"
.IX Header "CONFIGURATION FILE OPTIONS"
The section of the configuration file containing options for \fBca\fR
is found as follows: If the \fB\-name\fR command line option is used,
then it names the section to be used. Otherwise the section to
be used must be named in the \fBdefault_ca\fR option of the \fBca\fR section
of the configuration file (or in the default section of the
configuration file). Besides \fBdefault_ca\fR, the following options are
read directly from the \fBca\fR section:
 RANDFILE
 preserve
 msie_hack
With the exception of \fBRANDFILE\fR, this is probably a bug and may
change in future releases.
.PP
Many of the configuration file options are identical to command line
options. Where the option is present in the configuration file
and the command line the command line value is used. Where an
option is described as mandatory then it must be present in
the configuration file or the command line equivalent (if
any) used.
.IP \fBoid_file\fR 4
.IX Item "oid_file"
This specifies a file containing additional \fBOBJECT IDENTIFIERS\fR.
Each line of the file should consist of the numerical form of the
object identifier followed by white space then the short name followed
by white space and finally the long name.
.IP \fBoid_section\fR 4
.IX Item "oid_section"
This specifies a section in the configuration file containing extra
object identifiers. Each line should consist of the short name of the
object identifier followed by \fB=\fR and the numerical form. The short
and long names are the same when this option is used.
.IP \fBnew_certs_dir\fR 4
.IX Item "new_certs_dir"
The same as the \fB\-outdir\fR command line option. It specifies
the directory where new certificates will be placed. Mandatory.
.IP \fBcertificate\fR 4
.IX Item "certificate"
The same as \fB\-cert\fR. It gives the file containing the CA
certificate. Mandatory.
.IP \fBprivate_key\fR 4
.IX Item "private_key"
Same as the \fB\-keyfile\fR option. The file containing the
CA private key. Mandatory.
.IP \fBRANDFILE\fR 4
.IX Item "RANDFILE"
At startup the specified file is loaded into the random number generator,
and at exit 256 bytes will be written to it.
.IP \fBdefault_days\fR 4
.IX Item "default_days"
The same as the \fB\-days\fR option. The number of days to certify
a certificate for.
.IP \fBdefault_startdate\fR 4
.IX Item "default_startdate"
The same as the \fB\-startdate\fR option. The start date to certify
a certificate for. If not set the current time is used.
.IP \fBdefault_enddate\fR 4
.IX Item "default_enddate"
The same as the \fB\-enddate\fR option. Either this option or
\&\fBdefault_days\fR (or the command line equivalents) must be
present.
.IP "\fBdefault_crl_hours default_crl_days\fR" 4
.IX Item "default_crl_hours default_crl_days"
The same as the \fB\-crlhours\fR and the \fB\-crldays\fR options. These
will only be used if neither command line option is present. At
least one of these must be present to generate a CRL.
.IP \fBdefault_md\fR 4
.IX Item "default_md"
The same as the \fB\-md\fR option. Mandatory except where the signing algorithm does
not require a digest (i.e. Ed25519 and Ed448).
.IP \fBdatabase\fR 4
.IX Item "database"
The text database file to use. Mandatory. This file must be present
though initially it will be empty.
.IP \fBunique_subject\fR 4
.IX Item "unique_subject"
If the value \fByes\fR is given, the valid certificate entries in the
database must have unique subjects.  if the value \fBno\fR is given,
several valid certificate entries may have the exact same subject.
The default value is \fByes\fR, to be compatible with older (pre 0.9.8)
versions of OpenSSL.  However, to make CA certificate roll-over easier,
it's recommended to use the value \fBno\fR, especially if combined with
the \fB\-selfsign\fR command line option.
.Sp
Note that it is valid in some circumstances for certificates to be created
without any subject. In the case where there are multiple certificates without
subjects this does not count as a duplicate.
.IP \fBserial\fR 4
.IX Item "serial"
A text file containing the next serial number to use in hex. Mandatory.
This file must be present and contain a valid serial number.
.IP \fBcrlnumber\fR 4
.IX Item "crlnumber"
A text file containing the next CRL number to use in hex. The crl number
will be inserted in the CRLs only if this file exists. If this file is
present, it must contain a valid CRL number.
.IP \fBx509_extensions\fR 4
.IX Item "x509_extensions"
The same as \fB\-extensions\fR.
.IP \fBcrl_extensions\fR 4
.IX Item "crl_extensions"
The same as \fB\-crlexts\fR.
.IP \fBpreserve\fR 4
.IX Item "preserve"
The same as \fB\-preserveDN\fR
.IP \fBemail_in_dn\fR 4
.IX Item "email_in_dn"
The same as \fB\-noemailDN\fR. If you want the EMAIL field to be removed
from the DN of the certificate simply set this to 'no'. If not present
the default is to allow for the EMAIL filed in the certificate's DN.
.IP \fBmsie_hack\fR 4
.IX Item "msie_hack"
The same as \fB\-msie_hack\fR
.IP \fBpolicy\fR 4
.IX Item "policy"
The same as \fB\-policy\fR. Mandatory. See the \fBPOLICY FORMAT\fR section
for more information.
.IP "\fBname_opt\fR, \fBcert_opt\fR" 4
.IX Item "name_opt, cert_opt"
These options allow the format used to display the certificate details
when asking the user to confirm signing. All the options supported by
the \fBx509\fR utilities \fB\-nameopt\fR and \fB\-certopt\fR switches can be used
here, except the \fBno_signame\fR and \fBno_sigdump\fR are permanently set
and cannot be disabled (this is because the certificate signature cannot
be displayed because the certificate has not been signed at this point).
.Sp
For convenience the values \fBca_default\fR are accepted by both to produce
a reasonable output.
.Sp
If neither option is present the format used in earlier versions of
OpenSSL is used. Use of the old format is \fBstrongly\fR discouraged because
it only displays fields mentioned in the \fBpolicy\fR section, mishandles
multicharacter string types and does not display extensions.
.IP \fBcopy_extensions\fR 4
.IX Item "copy_extensions"
Determines how extensions in certificate requests should be handled.
If set to \fBnone\fR or this option is not present then extensions are
ignored and not copied to the certificate. If set to \fBcopy\fR then any
extensions present in the request that are not already present are copied
to the certificate. If set to \fBcopyall\fR then all extensions in the
request are copied to the certificate: if the extension is already present
in the certificate it is deleted first. See the \fBWARNINGS\fR section before
using this option.
.Sp
The main use of this option is to allow a certificate request to supply
values for certain extensions such as subjectAltName.
.SH "POLICY FORMAT"
.IX Header "POLICY FORMAT"
The policy section consists of a set of variables corresponding to
certificate DN fields. If the value is "match" then the field value
must match the same field in the CA certificate. If the value is
"supplied" then it must be present. If the value is "optional" then
it may be present. Any fields not mentioned in the policy section
are silently deleted, unless the \fB\-preserveDN\fR option is set but
this can be regarded more of a quirk than intended behaviour.
.SH "SPKAC FORMAT"
.IX Header "SPKAC FORMAT"
The input to the \fB\-spkac\fR command line option is a Netscape
signed public key and challenge. This will usually come from
the \fBKEYGEN\fR tag in an HTML form to create a new private key.
It is however possible to create SPKACs using the \fBspkac\fR utility.
.PP
The file should contain the variable SPKAC set to the value of
the SPKAC and also the required DN components as name value pairs.
If you need to include the same component twice then it can be
preceded by a number and a '.'.
.PP
When processing SPKAC format, the output is DER if the \fB\-out\fR
flag is used, but PEM format if sending to stdout or the \fB\-outdir\fR
flag is used.
.SH EXAMPLES
.IX Header "EXAMPLES"
Note: these examples assume that the \fBca\fR directory structure is
already set up and the relevant files already exist. This usually
involves creating a CA certificate and private key with \fBreq\fR, a
serial number file and an empty index file and placing them in
the relevant directories.
.PP
To use the sample configuration file below the directories demoCA,
demoCA/private and demoCA/newcerts would be created. The CA
certificate would be copied to demoCA/cacert.pem and its private
key to demoCA/private/cakey.pem. A file demoCA/serial would be
created containing for example "01" and the empty index file
demoCA/index.txt.
.PP
Sign a certificate request:
.PP
.Vb 1
\& openssl ca \-in req.pem \-out newcert.pem
.Ve
.PP
Sign a certificate request, using CA extensions:
.PP
.Vb 1
\& openssl ca \-in req.pem \-extensions v3_ca \-out newcert.pem
.Ve
.PP
Generate a CRL
.PP
.Vb 1
\& openssl ca \-gencrl \-out crl.pem
.Ve
.PP
Sign several requests:
.PP
.Vb 1
\& openssl ca \-infiles req1.pem req2.pem req3.pem
.Ve
.PP
Certify a Netscape SPKAC:
.PP
.Vb 1
\& openssl ca \-spkac spkac.txt
.Ve
.PP
A sample SPKAC file (the SPKAC line has been truncated for clarity):
.PP
.Vb 5
\& SPKAC=MIG0MGAwXDANBgkqhkiG9w0BAQEFAANLADBIAkEAn7PDhCeV/xIxUg8V70YRxK2A5
\& CN=Steve Test
\& emailAddress=<EMAIL>
\& 0.OU=OpenSSL Group
\& 1.OU=Another Group
.Ve
.PP
A sample configuration file with the relevant sections for \fBca\fR:
.PP
.Vb 2
\& [ ca ]
\& default_ca      = CA_default            # The default ca section
\&
\& [ CA_default ]
\&
\& dir            = ./demoCA              # top dir
\& database       = $dir/index.txt        # index file.
\& new_certs_dir  = $dir/newcerts         # new certs dir
\&
\& certificate    = $dir/cacert.pem       # The CA cert
\& serial         = $dir/serial           # serial no file
\& #rand_serial    = yes                  # for random serial#\*(Aqs
\& private_key    = $dir/private/cakey.pem# CA private key
\& RANDFILE       = $dir/private/.rand    # random number file
\&
\& default_days   = 365                   # how long to certify for
\& default_crl_days= 30                   # how long before next CRL
\& default_md     = md5                   # md to use
\&
\& policy         = policy_any            # default policy
\& email_in_dn    = no                    # Don\*(Aqt add the email into cert DN
\&
\& name_opt       = ca_default            # Subject name display option
\& cert_opt       = ca_default            # Certificate display option
\& copy_extensions = none                 # Don\*(Aqt copy extensions from request
\&
\& [ policy_any ]
\& countryName            = supplied
\& stateOrProvinceName    = optional
\& organizationName       = optional
\& organizationalUnitName = optional
\& commonName             = supplied
\& emailAddress           = optional
.Ve
.SH FILES
.IX Header "FILES"
Note: the location of all files can change either by compile time options,
configuration file entries, environment variables or command line options.
The values below reflect the default values.
.PP
.Vb 10
\& /usr/local/ssl/lib/openssl.cnf \- master configuration file
\& ./demoCA                       \- main CA directory
\& ./demoCA/cacert.pem            \- CA certificate
\& ./demoCA/private/cakey.pem     \- CA private key
\& ./demoCA/serial                \- CA serial number file
\& ./demoCA/serial.old            \- CA serial number backup file
\& ./demoCA/index.txt             \- CA text database file
\& ./demoCA/index.txt.old         \- CA text database backup file
\& ./demoCA/certs                 \- certificate output file
\& ./demoCA/.rnd                  \- CA random seed information
.Ve
.SH RESTRICTIONS
.IX Header "RESTRICTIONS"
The text database index file is a critical part of the process and
if corrupted it can be difficult to fix. It is theoretically possible
to rebuild the index file from all the issued certificates and a current
CRL: however there is no option to do this.
.PP
V2 CRL features like delta CRLs are not currently supported.
.PP
Although several requests can be input and handled at once it is only
possible to include one SPKAC or self-signed certificate.
.SH BUGS
.IX Header "BUGS"
The use of an in-memory text database can cause problems when large
numbers of certificates are present because, as the name implies
the database has to be kept in memory.
.PP
The \fBca\fR command really needs rewriting or the required functionality
exposed at either a command or interface level so a more friendly utility
(perl script or GUI) can handle things properly. The script
\&\fBCA.pl\fR helps a little but not very much.
.PP
Any fields in a request that are not present in a policy are silently
deleted. This does not happen if the \fB\-preserveDN\fR option is used. To
enforce the absence of the EMAIL field within the DN, as suggested by
RFCs, regardless the contents of the request' subject the \fB\-noemailDN\fR
option can be used. The behaviour should be more friendly and
configurable.
.PP
Canceling some commands by refusing to certify a certificate can
create an empty file.
.SH WARNINGS
.IX Header "WARNINGS"
The \fBca\fR command is quirky and at times downright unfriendly.
.PP
The \fBca\fR utility was originally meant as an example of how to do things
in a CA. It was not supposed to be used as a full blown CA itself:
nevertheless some people are using it for this purpose.
.PP
The \fBca\fR command is effectively a single user command: no locking is
done on the various files and attempts to run more than one \fBca\fR command
on the same database can have unpredictable results.
.PP
The \fBcopy_extensions\fR option should be used with caution. If care is
not taken then it can be a security risk. For example if a certificate
request contains a basicConstraints extension with CA:TRUE and the
\&\fBcopy_extensions\fR value is set to \fBcopyall\fR and the user does not spot
this when the certificate is displayed then this will hand the requester
a valid CA certificate.
.PP
This situation can be avoided by setting \fBcopy_extensions\fR to \fBcopy\fR
and including basicConstraints with CA:FALSE in the configuration file.
Then if the request contains a basicConstraints extension it will be
ignored.
.PP
It is advisable to also include values for other extensions such
as \fBkeyUsage\fR to prevent a request supplying its own values.
.PP
Additional restrictions can be placed on the CA certificate itself.
For example if the CA certificate has:
.PP
.Vb 1
\& basicConstraints = CA:TRUE, pathlen:0
.Ve
.PP
then even if a certificate is issued with CA:TRUE it will not be valid.
.SH HISTORY
.IX Header "HISTORY"
Since OpenSSL 1.1.1, the program follows RFC5280. Specifically,
certificate validity period (specified by any of \fB\-startdate\fR,
\&\fB\-enddate\fR and \fB\-days\fR) will be encoded as UTCTime if the dates are
earlier than year 2049 (included), and as GeneralizedTime if the dates
are in year 2050 or later.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBreq\fR\|(1), \fBspkac\fR\|(1), \fBx509\fR\|(1), \fBCA.pl\fR\|(1),
\&\fBconfig\fR\|(5), \fBx509v3_config\fR\|(5)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
