.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_FORK_PREPARE 3"
.TH OPENSSL_FORK_PREPARE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_fork_prepare,
OPENSSL_fork_parent,
OPENSSL_fork_child
\&\- OpenSSL fork handlers
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crypto.h>
\&
\& void OPENSSL_fork_prepare(void);
\& void OPENSSL_fork_parent(void);
\& void OPENSSL_fork_child(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
OpenSSL has state that should be reset when a process forks. For example,
the entropy pool used to generate random numbers (and therefore encryption
keys) should not be shared across multiple programs.
The \fBOPENSSL_fork_prepare()\fR, \fBOPENSSL_fork_parent()\fR, and \fBOPENSSL_fork_child()\fR
functions are used to reset this internal state.
.PP
Platforms without \fBfork\fR\|(2) will probably not need to use these functions.
Platforms with \fBfork\fR\|(2) but without \fBpthread_atfork\fR\|(3) will probably need
to call them manually, as described in the following paragraph.  Platforms
such as Linux that have both functions will normally not need to call these
functions as the OpenSSL library will do so automatically.
.PP
\&\fBOPENSSL_init_crypto\fR\|(3) will register these functions with the appropriate
handler, when the \fBOPENSSL_INIT_ATFORK\fR flag is used. For other
applications, these functions can be called directly. They should be used
according to the calling sequence described by the \fBpthread_atfork\fR\|(3)
documentation, which is summarized here.  \fBOPENSSL_fork_prepare()\fR should
be called before a \fBfork()\fR is done.  After the \fBfork()\fR returns, the parent
process should call \fBOPENSSL_fork_parent()\fR and the child process should
call \fBOPENSSL_fork_child()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOPENSSL_fork_prepare()\fR, \fBOPENSSL_fork_parent()\fR and \fBOPENSSL_fork_child()\fR do not
return values.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOPENSSL_init_crypto\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
