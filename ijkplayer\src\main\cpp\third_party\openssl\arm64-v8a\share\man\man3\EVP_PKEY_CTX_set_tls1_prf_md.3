.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_CTX_SET_TLS1_PRF_MD 3"
.TH EVP_PKEY_CTX_SET_TLS1_PRF_MD 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_CTX_set_tls1_prf_md,
EVP_PKEY_CTX_set1_tls1_prf_secret, EVP_PKEY_CTX_add1_tls1_prf_seed \-
TLS PRF key derivation algorithm
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/kdf.h>
\&
\& int EVP_PKEY_CTX_set_tls1_prf_md(EVP_PKEY_CTX *pctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_set1_tls1_prf_secret(EVP_PKEY_CTX *pctx,
\&                                       unsigned char *sec, int seclen);
\& int EVP_PKEY_CTX_add1_tls1_prf_seed(EVP_PKEY_CTX *pctx,
\&                                     unsigned char *seed, int seedlen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_TLS1_PRF\fR algorithm implements the PRF key derivation function for
TLS. It has no associated private key and only implements key derivation
using \fBEVP_PKEY_derive\fR\|(3).
.PP
\&\fBEVP_PKEY_set_tls1_prf_md()\fR sets the message digest associated with the
TLS PRF. \fBEVP_md5_sha1()\fR is treated as a special case which uses the PRF
algorithm using both \fBMD5\fR and \fBSHA1\fR as used in TLS 1.0 and 1.1.
.PP
\&\fBEVP_PKEY_CTX_set_tls1_prf_secret()\fR sets the secret value of the TLS PRF
to \fBseclen\fR bytes of the buffer \fBsec\fR. Any existing secret value is replaced
and any seed is reset.
.PP
\&\fBEVP_PKEY_CTX_add1_tls1_prf_seed()\fR sets the seed to \fBseedlen\fR bytes of \fBseed\fR.
If a seed is already set it is appended to the existing value.
.SH "STRING CTRLS"
.IX Header "STRING CTRLS"
The TLS PRF also supports string based control operations using
\&\fBEVP_PKEY_CTX_ctrl_str\fR\|(3).
The \fBtype\fR parameter "md" uses the supplied \fBvalue\fR as the name of the digest
algorithm to use.
The \fBtype\fR parameters "secret" and "seed" use the supplied \fBvalue\fR parameter
as a secret or seed value.
The names "hexsecret" and "hexseed" are similar except they take a hex string
which is converted to binary.
.SH NOTES
.IX Header "NOTES"
All these functions are implemented as macros.
.PP
A context for the TLS PRF can be obtained by calling:
.PP
.Vb 1
\& EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_TLS1_PRF, NULL);
.Ve
.PP
The digest, secret value and seed must be set before a key is derived or an
error occurs.
.PP
The total length of all seeds cannot exceed 1024 bytes in length: this should
be more than enough for any normal use of the TLS PRF.
.PP
The output length of the PRF is specified by the length parameter in the
\&\fBEVP_PKEY_derive()\fR function. Since the output length is variable, setting
the buffer to \fBNULL\fR is not meaningful for the TLS PRF.
.PP
Optimised versions of the TLS PRF can be implemented in an ENGINE.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All these functions return 1 for success and 0 or a negative value for failure.
In particular a return value of \-2 indicates the operation is not supported by
the public key algorithm.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example derives 10 bytes using SHA\-256 with the secret key "secret"
and seed value "seed":
.PP
.Vb 3
\& EVP_PKEY_CTX *pctx;
\& unsigned char out[10];
\& size_t outlen = sizeof(out);
\&
\& pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_TLS1_PRF, NULL);
\& if (EVP_PKEY_derive_init(pctx) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set_tls1_prf_md(pctx, EVP_sha256()) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set1_tls1_prf_secret(pctx, "secret", 6) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_add1_tls1_prf_seed(pctx, "seed", 4) <= 0)
\&     /* Error */
\& if (EVP_PKEY_derive(pctx, out, &outlen) <= 0)
\&     /* Error */
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_CTX_ctrl_str\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
