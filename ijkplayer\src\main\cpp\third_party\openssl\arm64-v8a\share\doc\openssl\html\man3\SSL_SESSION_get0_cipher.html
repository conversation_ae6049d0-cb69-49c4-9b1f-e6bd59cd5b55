<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_SESSION_get0_cipher</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_SESSION_get0_cipher, SSL_SESSION_set_cipher - set and retrieve the SSL cipher associated with a session</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

const SSL_CIPHER *SSL_SESSION_get0_cipher(const SSL_SESSION *s);
int SSL_SESSION_set_cipher(SSL_SESSION *s, const SSL_CIPHER *cipher);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_SESSION_get0_cipher() retrieves the cipher that was used by the connection when the session was created, or NULL if it cannot be determined.</p>

<p>The value returned is a pointer to an object maintained within <b>s</b> and should not be released.</p>

<p>SSL_SESSION_set_cipher() can be used to set the ciphersuite associated with the SSL_SESSION <b>s</b> to <b>cipher</b>. For example, this could be used to set up a session based PSK (see <a href="../man3/SSL_CTX_set_psk_use_session_callback.html">SSL_CTX_set_psk_use_session_callback(3)</a>).</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_SESSION_get0_cipher() returns the SSL_CIPHER associated with the SSL_SESSION or NULL if it cannot be determined.</p>

<p>SSL_SESSION_set_cipher() returns 1 on success or 0 on failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/d2i_SSL_SESSION.html">d2i_SSL_SESSION(3)</a>, <a href="../man3/SSL_SESSION_get_time.html">SSL_SESSION_get_time(3)</a>, <a href="../man3/SSL_SESSION_get0_hostname.html">SSL_SESSION_get0_hostname(3)</a>, <a href="../man3/SSL_SESSION_free.html">SSL_SESSION_free(3)</a>, <a href="../man3/SSL_CTX_set_psk_use_session_callback.html">SSL_CTX_set_psk_use_session_callback(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The SSL_SESSION_get0_cipher() function was added in OpenSSL 1.1.0. The SSL_SESSION_set_cipher() function was added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016-2017 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


