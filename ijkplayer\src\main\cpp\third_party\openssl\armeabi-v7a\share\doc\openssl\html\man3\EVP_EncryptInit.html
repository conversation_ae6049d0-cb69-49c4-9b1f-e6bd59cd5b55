<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_EncryptInit</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#CIPHER-LISTING">CIPHER LISTING</a></li>
  <li><a href="#AEAD-Interface">AEAD Interface</a>
    <ul>
      <li><a href="#GCM-and-OCB-Modes">GCM and OCB Modes</a></li>
      <li><a href="#CCM-Mode">CCM Mode</a></li>
      <li><a href="#ChaCha20-Poly1305">ChaCha20-Poly1305</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_CIPHER_CTX_new, EVP_CIPHER_CTX_reset, EVP_CIPHER_CTX_free, EVP_EncryptInit_ex, EVP_EncryptUpdate, EVP_EncryptFinal_ex, EVP_DecryptInit_ex, EVP_DecryptUpdate, EVP_DecryptFinal_ex, EVP_CipherInit_ex, EVP_CipherUpdate, EVP_CipherFinal_ex, EVP_CIPHER_CTX_set_key_length, EVP_CIPHER_CTX_ctrl, EVP_EncryptInit, EVP_EncryptFinal, EVP_DecryptInit, EVP_DecryptFinal, EVP_CipherInit, EVP_CipherFinal, EVP_get_cipherbyname, EVP_get_cipherbynid, EVP_get_cipherbyobj, EVP_CIPHER_nid, EVP_CIPHER_block_size, EVP_CIPHER_key_length, EVP_CIPHER_iv_length, EVP_CIPHER_flags, EVP_CIPHER_mode, EVP_CIPHER_type, EVP_CIPHER_CTX_cipher, EVP_CIPHER_CTX_nid, EVP_CIPHER_CTX_block_size, EVP_CIPHER_CTX_key_length, EVP_CIPHER_CTX_iv_length, EVP_CIPHER_CTX_get_app_data, EVP_CIPHER_CTX_set_app_data, EVP_CIPHER_CTX_type, EVP_CIPHER_CTX_flags, EVP_CIPHER_CTX_mode, EVP_CIPHER_param_to_asn1, EVP_CIPHER_asn1_to_param, EVP_CIPHER_CTX_set_padding, EVP_enc_null - EVP cipher routines</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

EVP_CIPHER_CTX *EVP_CIPHER_CTX_new(void);
int EVP_CIPHER_CTX_reset(EVP_CIPHER_CTX *ctx);
void EVP_CIPHER_CTX_free(EVP_CIPHER_CTX *ctx);

int EVP_EncryptInit_ex(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
                       ENGINE *impl, const unsigned char *key, const unsigned char *iv);
int EVP_EncryptUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
                      int *outl, const unsigned char *in, int inl);
int EVP_EncryptFinal_ex(EVP_CIPHER_CTX *ctx, unsigned char *out, int *outl);

int EVP_DecryptInit_ex(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
                       ENGINE *impl, const unsigned char *key, const unsigned char *iv);
int EVP_DecryptUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
                      int *outl, const unsigned char *in, int inl);
int EVP_DecryptFinal_ex(EVP_CIPHER_CTX *ctx, unsigned char *outm, int *outl);

int EVP_CipherInit_ex(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
                      ENGINE *impl, const unsigned char *key, const unsigned char *iv, int enc);
int EVP_CipherUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
                     int *outl, const unsigned char *in, int inl);
int EVP_CipherFinal_ex(EVP_CIPHER_CTX *ctx, unsigned char *outm, int *outl);

int EVP_EncryptInit(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
                    const unsigned char *key, const unsigned char *iv);
int EVP_EncryptFinal(EVP_CIPHER_CTX *ctx, unsigned char *out, int *outl);

int EVP_DecryptInit(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
                    const unsigned char *key, const unsigned char *iv);
int EVP_DecryptFinal(EVP_CIPHER_CTX *ctx, unsigned char *outm, int *outl);

int EVP_CipherInit(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
                   const unsigned char *key, const unsigned char *iv, int enc);
int EVP_CipherFinal(EVP_CIPHER_CTX *ctx, unsigned char *outm, int *outl);

int EVP_CIPHER_CTX_set_padding(EVP_CIPHER_CTX *x, int padding);
int EVP_CIPHER_CTX_set_key_length(EVP_CIPHER_CTX *x, int keylen);
int EVP_CIPHER_CTX_ctrl(EVP_CIPHER_CTX *ctx, int type, int arg, void *ptr);
int EVP_CIPHER_CTX_rand_key(EVP_CIPHER_CTX *ctx, unsigned char *key);

const EVP_CIPHER *EVP_get_cipherbyname(const char *name);
const EVP_CIPHER *EVP_get_cipherbynid(int nid);
const EVP_CIPHER *EVP_get_cipherbyobj(const ASN1_OBJECT *a);

int EVP_CIPHER_nid(const EVP_CIPHER *e);
int EVP_CIPHER_block_size(const EVP_CIPHER *e);
int EVP_CIPHER_key_length(const EVP_CIPHER *e);
int EVP_CIPHER_iv_length(const EVP_CIPHER *e);
unsigned long EVP_CIPHER_flags(const EVP_CIPHER *e);
unsigned long EVP_CIPHER_mode(const EVP_CIPHER *e);
int EVP_CIPHER_type(const EVP_CIPHER *ctx);

const EVP_CIPHER *EVP_CIPHER_CTX_cipher(const EVP_CIPHER_CTX *ctx);
int EVP_CIPHER_CTX_nid(const EVP_CIPHER_CTX *ctx);
int EVP_CIPHER_CTX_block_size(const EVP_CIPHER_CTX *ctx);
int EVP_CIPHER_CTX_key_length(const EVP_CIPHER_CTX *ctx);
int EVP_CIPHER_CTX_iv_length(const EVP_CIPHER_CTX *ctx);
void *EVP_CIPHER_CTX_get_app_data(const EVP_CIPHER_CTX *ctx);
void EVP_CIPHER_CTX_set_app_data(const EVP_CIPHER_CTX *ctx, void *data);
int EVP_CIPHER_CTX_type(const EVP_CIPHER_CTX *ctx);
int EVP_CIPHER_CTX_mode(const EVP_CIPHER_CTX *ctx);

int EVP_CIPHER_param_to_asn1(EVP_CIPHER_CTX *c, ASN1_TYPE *type);
int EVP_CIPHER_asn1_to_param(EVP_CIPHER_CTX *c, ASN1_TYPE *type);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The EVP cipher routines are a high-level interface to certain symmetric ciphers.</p>

<p>EVP_CIPHER_CTX_new() creates a cipher context.</p>

<p>EVP_CIPHER_CTX_free() clears all information from a cipher context and free up any allocated memory associate with it, including <b>ctx</b> itself. This function should be called after all operations using a cipher are complete so sensitive information does not remain in memory.</p>

<p>EVP_EncryptInit_ex() sets up cipher context <b>ctx</b> for encryption with cipher <b>type</b> from ENGINE <b>impl</b>. <b>ctx</b> must be created before calling this function. <b>type</b> is normally supplied by a function such as EVP_aes_256_cbc(). If <b>impl</b> is NULL then the default implementation is used. <b>key</b> is the symmetric key to use and <b>iv</b> is the IV to use (if necessary), the actual number of bytes used for the key and IV depends on the cipher. It is possible to set all parameters to NULL except <b>type</b> in an initial call and supply the remaining parameters in subsequent calls, all of which have <b>type</b> set to NULL. This is done when the default cipher parameters are not appropriate.</p>

<p>EVP_EncryptUpdate() encrypts <b>inl</b> bytes from the buffer <b>in</b> and writes the encrypted version to <b>out</b>. This function can be called multiple times to encrypt successive blocks of data. The amount of data written depends on the block alignment of the encrypted data. For most ciphers and modes, the amount of data written can be anything from zero bytes to (inl + cipher_block_size - 1) bytes. For wrap cipher modes, the amount of data written can be anything from zero bytes to (inl + cipher_block_size) bytes. For stream ciphers, the amount of data written can be anything from zero bytes to inl bytes. Thus, <b>out</b> should contain sufficient room for the operation being performed. The actual number of bytes written is placed in <b>outl</b>. It also checks if <b>in</b> and <b>out</b> are partially overlapping, and if they are 0 is returned to indicate failure.</p>

<p>If padding is enabled (the default) then EVP_EncryptFinal_ex() encrypts the &quot;final&quot; data, that is any data that remains in a partial block. It uses standard block padding (aka PKCS padding) as described in the NOTES section, below. The encrypted final data is written to <b>out</b> which should have sufficient space for one cipher block. The number of bytes written is placed in <b>outl</b>. After this function is called the encryption operation is finished and no further calls to EVP_EncryptUpdate() should be made.</p>

<p>If padding is disabled then EVP_EncryptFinal_ex() will not encrypt any more data and it will return an error if any data remains in a partial block: that is if the total data length is not a multiple of the block size.</p>

<p>EVP_DecryptInit_ex(), EVP_DecryptUpdate() and EVP_DecryptFinal_ex() are the corresponding decryption operations. EVP_DecryptFinal() will return an error code if padding is enabled and the final block is not correctly formatted. The parameters and restrictions are identical to the encryption operations except that if padding is enabled the decrypted data buffer <b>out</b> passed to EVP_DecryptUpdate() should have sufficient room for (<b>inl</b> + cipher_block_size) bytes unless the cipher block size is 1 in which case <b>inl</b> bytes is sufficient.</p>

<p>EVP_CipherInit_ex(), EVP_CipherUpdate() and EVP_CipherFinal_ex() are functions that can be used for decryption or encryption. The operation performed depends on the value of the <b>enc</b> parameter. It should be set to 1 for encryption, 0 for decryption and -1 to leave the value unchanged (the actual value of &#39;enc&#39; being supplied in a previous call).</p>

<p>EVP_CIPHER_CTX_reset() clears all information from a cipher context and free up any allocated memory associate with it, except the <b>ctx</b> itself. This function should be called anytime <b>ctx</b> is to be reused for another EVP_CipherInit() / EVP_CipherUpdate() / EVP_CipherFinal() series of calls.</p>

<p>EVP_EncryptInit(), EVP_DecryptInit() and EVP_CipherInit() behave in a similar way to EVP_EncryptInit_ex(), EVP_DecryptInit_ex() and EVP_CipherInit_ex() except they always use the default cipher implementation.</p>

<p>EVP_EncryptFinal(), EVP_DecryptFinal() and EVP_CipherFinal() are identical to EVP_EncryptFinal_ex(), EVP_DecryptFinal_ex() and EVP_CipherFinal_ex(). In previous releases they also cleaned up the <b>ctx</b>, but this is no longer done and EVP_CIPHER_CTX_clean() must be called to free any context resources.</p>

<p>EVP_get_cipherbyname(), EVP_get_cipherbynid() and EVP_get_cipherbyobj() return an EVP_CIPHER structure when passed a cipher name, a NID or an ASN1_OBJECT structure.</p>

<p>EVP_CIPHER_nid() and EVP_CIPHER_CTX_nid() return the NID of a cipher when passed an <b>EVP_CIPHER</b> or <b>EVP_CIPHER_CTX</b> structure. The actual NID value is an internal value which may not have a corresponding OBJECT IDENTIFIER.</p>

<p>EVP_CIPHER_CTX_set_padding() enables or disables padding. This function should be called after the context is set up for encryption or decryption with EVP_EncryptInit_ex(), EVP_DecryptInit_ex() or EVP_CipherInit_ex(). By default encryption operations are padded using standard block padding and the padding is checked and removed when decrypting. If the <b>pad</b> parameter is zero then no padding is performed, the total amount of data encrypted or decrypted must then be a multiple of the block size or an error will occur.</p>

<p>EVP_CIPHER_key_length() and EVP_CIPHER_CTX_key_length() return the key length of a cipher when passed an <b>EVP_CIPHER</b> or <b>EVP_CIPHER_CTX</b> structure. The constant <b>EVP_MAX_KEY_LENGTH</b> is the maximum key length for all ciphers. Note: although EVP_CIPHER_key_length() is fixed for a given cipher, the value of EVP_CIPHER_CTX_key_length() may be different for variable key length ciphers.</p>

<p>EVP_CIPHER_CTX_set_key_length() sets the key length of the cipher ctx. If the cipher is a fixed length cipher then attempting to set the key length to any value other than the fixed value is an error.</p>

<p>EVP_CIPHER_iv_length() and EVP_CIPHER_CTX_iv_length() return the IV length of a cipher when passed an <b>EVP_CIPHER</b> or <b>EVP_CIPHER_CTX</b>. It will return zero if the cipher does not use an IV. The constant <b>EVP_MAX_IV_LENGTH</b> is the maximum IV length for all ciphers.</p>

<p>EVP_CIPHER_block_size() and EVP_CIPHER_CTX_block_size() return the block size of a cipher when passed an <b>EVP_CIPHER</b> or <b>EVP_CIPHER_CTX</b> structure. The constant <b>EVP_MAX_BLOCK_LENGTH</b> is also the maximum block length for all ciphers.</p>

<p>EVP_CIPHER_type() and EVP_CIPHER_CTX_type() return the type of the passed cipher or context. This &quot;type&quot; is the actual NID of the cipher OBJECT IDENTIFIER as such it ignores the cipher parameters and 40 bit RC2 and 128 bit RC2 have the same NID. If the cipher does not have an object identifier or does not have ASN1 support this function will return <b>NID_undef</b>.</p>

<p>EVP_CIPHER_CTX_cipher() returns the <b>EVP_CIPHER</b> structure when passed an <b>EVP_CIPHER_CTX</b> structure.</p>

<p>EVP_CIPHER_mode() and EVP_CIPHER_CTX_mode() return the block cipher mode: EVP_CIPH_ECB_MODE, EVP_CIPH_CBC_MODE, EVP_CIPH_CFB_MODE, EVP_CIPH_OFB_MODE, EVP_CIPH_CTR_MODE, EVP_CIPH_GCM_MODE, EVP_CIPH_CCM_MODE, EVP_CIPH_XTS_MODE, EVP_CIPH_WRAP_MODE or EVP_CIPH_OCB_MODE. If the cipher is a stream cipher then EVP_CIPH_STREAM_CIPHER is returned.</p>

<p>EVP_CIPHER_param_to_asn1() sets the AlgorithmIdentifier &quot;parameter&quot; based on the passed cipher. This will typically include any parameters and an IV. The cipher IV (if any) must be set when this call is made. This call should be made before the cipher is actually &quot;used&quot; (before any EVP_EncryptUpdate(), EVP_DecryptUpdate() calls for example). This function may fail if the cipher does not have any ASN1 support.</p>

<p>EVP_CIPHER_asn1_to_param() sets the cipher parameters based on an ASN1 AlgorithmIdentifier &quot;parameter&quot;. The precise effect depends on the cipher In the case of RC2, for example, it will set the IV and effective key length. This function should be called after the base cipher type is set but before the key is set. For example EVP_CipherInit() will be called with the IV and key set to NULL, EVP_CIPHER_asn1_to_param() will be called and finally EVP_CipherInit() again with all parameters except the key set to NULL. It is possible for this function to fail if the cipher does not have any ASN1 support or the parameters cannot be set (for example the RC2 effective key length is not supported.</p>

<p>EVP_CIPHER_CTX_ctrl() allows various cipher specific parameters to be determined and set.</p>

<p>EVP_CIPHER_CTX_rand_key() generates a random key of the appropriate length based on the cipher context. The EVP_CIPHER can provide its own random key generation routine to support keys of a specific form. <b>Key</b> must point to a buffer at least as big as the value returned by EVP_CIPHER_CTX_key_length().</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_CIPHER_CTX_new() returns a pointer to a newly created <b>EVP_CIPHER_CTX</b> for success and <b>NULL</b> for failure.</p>

<p>EVP_EncryptInit_ex(), EVP_EncryptUpdate() and EVP_EncryptFinal_ex() return 1 for success and 0 for failure.</p>

<p>EVP_DecryptInit_ex() and EVP_DecryptUpdate() return 1 for success and 0 for failure. EVP_DecryptFinal_ex() returns 0 if the decrypt failed or 1 for success.</p>

<p>EVP_CipherInit_ex() and EVP_CipherUpdate() return 1 for success and 0 for failure. EVP_CipherFinal_ex() returns 0 for a decryption failure or 1 for success.</p>

<p>EVP_CIPHER_CTX_reset() returns 1 for success and 0 for failure.</p>

<p>EVP_get_cipherbyname(), EVP_get_cipherbynid() and EVP_get_cipherbyobj() return an <b>EVP_CIPHER</b> structure or NULL on error.</p>

<p>EVP_CIPHER_nid() and EVP_CIPHER_CTX_nid() return a NID.</p>

<p>EVP_CIPHER_block_size() and EVP_CIPHER_CTX_block_size() return the block size.</p>

<p>EVP_CIPHER_key_length() and EVP_CIPHER_CTX_key_length() return the key length.</p>

<p>EVP_CIPHER_CTX_set_padding() always returns 1.</p>

<p>EVP_CIPHER_iv_length() and EVP_CIPHER_CTX_iv_length() return the IV length, zero if the cipher does not use an IV and a negative value on error.</p>

<p>EVP_CIPHER_type() and EVP_CIPHER_CTX_type() return the NID of the cipher&#39;s OBJECT IDENTIFIER or NID_undef if it has no defined OBJECT IDENTIFIER.</p>

<p>EVP_CIPHER_CTX_cipher() returns an <b>EVP_CIPHER</b> structure.</p>

<p>EVP_CIPHER_param_to_asn1() and EVP_CIPHER_asn1_to_param() return greater than zero for success and zero or a negative number on failure.</p>

<p>EVP_CIPHER_CTX_rand_key() returns 1 for success.</p>

<h1 id="CIPHER-LISTING">CIPHER LISTING</h1>

<p>All algorithms have a fixed key length unless otherwise stated.</p>

<p>Refer to <a href="#SEE-ALSO">&quot;SEE ALSO&quot;</a> for the full list of ciphers available through the EVP interface.</p>

<dl>

<dt id="EVP_enc_null">EVP_enc_null()</dt>
<dd>

<p>Null cipher: does nothing.</p>

</dd>
</dl>

<h1 id="AEAD-Interface">AEAD Interface</h1>

<p>The EVP interface for Authenticated Encryption with Associated Data (AEAD) modes are subtly altered and several additional <i>ctrl</i> operations are supported depending on the mode specified.</p>

<p>To specify additional authenticated data (AAD), a call to EVP_CipherUpdate(), EVP_EncryptUpdate() or EVP_DecryptUpdate() should be made with the output parameter <b>out</b> set to <b>NULL</b>.</p>

<p>When decrypting, the return value of EVP_DecryptFinal() or EVP_CipherFinal() indicates whether the operation was successful. If it does not indicate success, the authentication operation has failed and any output data <b>MUST NOT</b> be used as it is corrupted.</p>

<h2 id="GCM-and-OCB-Modes">GCM and OCB Modes</h2>

<p>The following <i>ctrl</i>s are supported in GCM and OCB modes.</p>

<dl>

<dt id="EVP_CIPHER_CTX_ctrl-ctx-EVP_CTRL_AEAD_SET_IVLEN-ivlen-NULL">EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)</dt>
<dd>

<p>Sets the IV length. This call can only be made before specifying an IV. If not called a default IV length is used.</p>

<p>For GCM AES and OCB AES the default is 12 (i.e. 96 bits). For OCB mode the maximum is 15.</p>

</dd>
<dt id="EVP_CIPHER_CTX_ctrl-ctx-EVP_CTRL_AEAD_GET_TAG-taglen-tag">EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, taglen, tag)</dt>
<dd>

<p>Writes <code>taglen</code> bytes of the tag value to the buffer indicated by <code>tag</code>. This call can only be made when encrypting data and <b>after</b> all data has been processed (e.g. after an EVP_EncryptFinal() call).</p>

<p>For OCB, <code>taglen</code> must either be 16 or the value previously set via <b>EVP_CTRL_AEAD_SET_TAG</b>.</p>

</dd>
<dt id="EVP_CIPHER_CTX_ctrl-ctx-EVP_CTRL_AEAD_SET_TAG-taglen-tag">EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)</dt>
<dd>

<p>When decrypting, this call sets the expected tag to <code>taglen</code> bytes from <code>tag</code>. <code>taglen</code> must be between 1 and 16 inclusive. The tag must be set prior to any call to EVP_DecryptFinal() or EVP_DecryptFinal_ex().</p>

<p>For GCM, this call is only valid when decrypting data.</p>

<p>For OCB, this call is valid when decrypting data to set the expected tag, and when encrypting to set the desired tag length.</p>

<p>In OCB mode, calling this when encrypting with <code>tag</code> set to <code>NULL</code> sets the tag length. The tag length can only be set before specifying an IV. If this is not called prior to setting the IV during encryption, then a default tag length is used.</p>

<p>For OCB AES, the default tag length is 16 (i.e. 128 bits). It is also the maximum tag length for OCB.</p>

</dd>
</dl>

<h2 id="CCM-Mode">CCM Mode</h2>

<p>The EVP interface for CCM mode is similar to that of the GCM mode but with a few additional requirements and different <i>ctrl</i> values.</p>

<p>For CCM mode, the total plaintext or ciphertext length <b>MUST</b> be passed to EVP_CipherUpdate(), EVP_EncryptUpdate() or EVP_DecryptUpdate() with the output and input parameters (<b>in</b> and <b>out</b>) set to <b>NULL</b> and the length passed in the <b>inl</b> parameter.</p>

<p>The following <i>ctrl</i>s are supported in CCM mode.</p>

<dl>

<dt id="EVP_CIPHER_CTX_ctrl-ctx-EVP_CTRL_AEAD_SET_TAG-taglen-tag1">EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)</dt>
<dd>

<p>This call is made to set the expected <b>CCM</b> tag value when decrypting or the length of the tag (with the <code>tag</code> parameter set to NULL) when encrypting. The tag length is often referred to as <b>M</b>. If not set a default value is used (12 for AES). When decrypting, the tag needs to be set before passing in data to be decrypted, but as in GCM and OCB mode, it can be set after passing additional authenticated data (see <a href="#AEAD-Interface">&quot;AEAD Interface&quot;</a>).</p>

</dd>
<dt id="EVP_CIPHER_CTX_ctrl-ctx-EVP_CTRL_CCM_SET_L-ivlen-NULL">EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_CCM_SET_L, ivlen, NULL)</dt>
<dd>

<p>Sets the CCM <b>L</b> value. If not set a default is used (8 for AES).</p>

</dd>
<dt id="EVP_CIPHER_CTX_ctrl-ctx-EVP_CTRL_AEAD_SET_IVLEN-ivlen-NULL1">EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)</dt>
<dd>

<p>Sets the CCM nonce (IV) length. This call can only be made before specifying a nonce value. The nonce length is given by <b>15 - L</b> so it is 7 by default for AES.</p>

</dd>
</dl>

<h2 id="ChaCha20-Poly1305">ChaCha20-Poly1305</h2>

<p>The following <i>ctrl</i>s are supported for the ChaCha20-Poly1305 AEAD algorithm.</p>

<dl>

<dt id="EVP_CIPHER_CTX_ctrl-ctx-EVP_CTRL_AEAD_SET_IVLEN-ivlen-NULL2">EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)</dt>
<dd>

<p>Sets the nonce length. This call can only be made before specifying the nonce. If not called a default nonce length of 12 (i.e. 96 bits) is used. The maximum nonce length is 12 bytes (i.e. 96-bits). If a nonce of less than 12 bytes is set then the nonce is automatically padded with leading 0 bytes to make it 12 bytes in length.</p>

</dd>
<dt id="EVP_CIPHER_CTX_ctrl-ctx-EVP_CTRL_AEAD_GET_TAG-taglen-tag1">EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, taglen, tag)</dt>
<dd>

<p>Writes <code>taglen</code> bytes of the tag value to the buffer indicated by <code>tag</code>. This call can only be made when encrypting data and <b>after</b> all data has been processed (e.g. after an EVP_EncryptFinal() call).</p>

<p><code>taglen</code> specified here must be 16 (<b>POLY1305_BLOCK_SIZE</b>, i.e. 128-bits) or less.</p>

</dd>
<dt id="EVP_CIPHER_CTX_ctrl-ctx-EVP_CTRL_AEAD_SET_TAG-taglen-tag2">EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)</dt>
<dd>

<p>Sets the expected tag to <code>taglen</code> bytes from <code>tag</code>. The tag length can only be set before specifying an IV. <code>taglen</code> must be between 1 and 16 (<b>POLY1305_BLOCK_SIZE</b>) inclusive. This call is only valid when decrypting data.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>Where possible the <b>EVP</b> interface to symmetric ciphers should be used in preference to the low-level interfaces. This is because the code then becomes transparent to the cipher used and much more flexible. Additionally, the <b>EVP</b> interface will ensure the use of platform specific cryptographic acceleration such as AES-NI (the low-level interfaces do not provide the guarantee).</p>

<p>PKCS padding works by adding <b>n</b> padding bytes of value <b>n</b> to make the total length of the encrypted data a multiple of the block size. Padding is always added so if the data is already a multiple of the block size <b>n</b> will equal the block size. For example if the block size is 8 and 11 bytes are to be encrypted then 5 padding bytes of value 5 will be added.</p>

<p>When decrypting the final block is checked to see if it has the correct form.</p>

<p>Although the decryption operation can produce an error if padding is enabled, it is not a strong test that the input data or key is correct. A random block has better than 1 in 256 chance of being of the correct format and problems with the input data earlier on will not produce a final decrypt error.</p>

<p>If padding is disabled then the decryption operation will always succeed if the total amount of data decrypted is a multiple of the block size.</p>

<p>The functions EVP_EncryptInit(), EVP_EncryptFinal(), EVP_DecryptInit(), EVP_CipherInit() and EVP_CipherFinal() are obsolete but are retained for compatibility with existing code. New code should use EVP_EncryptInit_ex(), EVP_EncryptFinal_ex(), EVP_DecryptInit_ex(), EVP_DecryptFinal_ex(), EVP_CipherInit_ex() and EVP_CipherFinal_ex() because they can reuse an existing context without allocating and freeing it up on each call.</p>

<p>There are some differences between functions EVP_CipherInit() and EVP_CipherInit_ex(), significant in some circumstances. EVP_CipherInit() fills the passed context object with zeros. As a consequence, EVP_CipherInit() does not allow step-by-step initialization of the ctx when the <i>key</i> and <i>iv</i> are passed in separate calls. It also means that the flags set for the CTX are removed, and it is especially important for the <b>EVP_CIPHER_CTX_FLAG_WRAP_ALLOW</b> flag treated specially in EVP_CipherInit_ex().</p>

<p>EVP_get_cipherbynid(), and EVP_get_cipherbyobj() are implemented as macros.</p>

<h1 id="BUGS">BUGS</h1>

<p><b>EVP_MAX_KEY_LENGTH</b> and <b>EVP_MAX_IV_LENGTH</b> only refer to the internal ciphers with default key lengths. If custom ciphers exceed these values the results are unpredictable. This is because it has become standard practice to define a generic key as a fixed unsigned char array containing <b>EVP_MAX_KEY_LENGTH</b> bytes.</p>

<p>The ASN1 code is incomplete (and sometimes inaccurate) it has only been tested for certain common S/MIME ciphers (RC2, DES, triple DES) in CBC mode.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Encrypt a string using IDEA:</p>

<pre><code>int do_crypt(char *outfile)
{
    unsigned char outbuf[1024];
    int outlen, tmplen;
    /*
     * Bogus key and IV: we&#39;d normally set these from
     * another source.
     */
    unsigned char key[] = {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15};
    unsigned char iv[] = {1,2,3,4,5,6,7,8};
    char intext[] = &quot;Some Crypto Text&quot;;
    EVP_CIPHER_CTX *ctx;
    FILE *out;

    ctx = EVP_CIPHER_CTX_new();
    EVP_EncryptInit_ex(ctx, EVP_idea_cbc(), NULL, key, iv);

    if (!EVP_EncryptUpdate(ctx, outbuf, &amp;outlen, intext, strlen(intext))) {
        /* Error */
        EVP_CIPHER_CTX_free(ctx);
        return 0;
    }
    /*
     * Buffer passed to EVP_EncryptFinal() must be after data just
     * encrypted to avoid overwriting it.
     */
    if (!EVP_EncryptFinal_ex(ctx, outbuf + outlen, &amp;tmplen)) {
        /* Error */
        EVP_CIPHER_CTX_free(ctx);
        return 0;
    }
    outlen += tmplen;
    EVP_CIPHER_CTX_free(ctx);
    /*
     * Need binary mode for fopen because encrypted data is
     * binary data. Also cannot use strlen() on it because
     * it won&#39;t be NUL terminated and may contain embedded
     * NULs.
     */
    out = fopen(outfile, &quot;wb&quot;);
    if (out == NULL) {
        /* Error */
        return 0;
    }
    fwrite(outbuf, 1, outlen, out);
    fclose(out);
    return 1;
}</code></pre>

<p>The ciphertext from the above example can be decrypted using the <b>openssl</b> utility with the command line (shown on two lines for clarity):</p>

<pre><code>openssl idea -d \
    -K 000102030405060708090A0B0C0D0E0F -iv 0102030405060708 &lt;filename</code></pre>

<p>General encryption and decryption function example using FILE I/O and AES128 with a 128-bit key:</p>

<pre><code>int do_crypt(FILE *in, FILE *out, int do_encrypt)
{
    /* Allow enough space in output buffer for additional block */
    unsigned char inbuf[1024], outbuf[1024 + EVP_MAX_BLOCK_LENGTH];
    int inlen, outlen;
    EVP_CIPHER_CTX *ctx;
    /*
     * Bogus key and IV: we&#39;d normally set these from
     * another source.
     */
    unsigned char key[] = &quot;0123456789abcdeF&quot;;
    unsigned char iv[] = &quot;1234567887654321&quot;;

    /* Don&#39;t set key or IV right away; we want to check lengths */
    ctx = EVP_CIPHER_CTX_new();
    EVP_CipherInit_ex(ctx, EVP_aes_128_cbc(), NULL, NULL, NULL,
                      do_encrypt);
    OPENSSL_assert(EVP_CIPHER_CTX_key_length(ctx) == 16);
    OPENSSL_assert(EVP_CIPHER_CTX_iv_length(ctx) == 16);

    /* Now we can set key and IV */
    EVP_CipherInit_ex(ctx, NULL, NULL, key, iv, do_encrypt);

    for (;;) {
        inlen = fread(inbuf, 1, 1024, in);
        if (inlen &lt;= 0)
            break;
        if (!EVP_CipherUpdate(ctx, outbuf, &amp;outlen, inbuf, inlen)) {
            /* Error */
            EVP_CIPHER_CTX_free(ctx);
            return 0;
        }
        fwrite(outbuf, 1, outlen, out);
    }
    if (!EVP_CipherFinal_ex(ctx, outbuf, &amp;outlen)) {
        /* Error */
        EVP_CIPHER_CTX_free(ctx);
        return 0;
    }
    fwrite(outbuf, 1, outlen, out);

    EVP_CIPHER_CTX_free(ctx);
    return 1;
}</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/evp.html">evp(7)</a></p>

<p>Supported ciphers are listed in:</p>

<p><a href="../man3/EVP_aes.html">EVP_aes(3)</a>, <a href="../man3/EVP_aria.html">EVP_aria(3)</a>, <a href="../man3/EVP_bf.html">EVP_bf(3)</a>, <a href="../man3/EVP_camellia.html">EVP_camellia(3)</a>, <a href="../man3/EVP_cast5.html">EVP_cast5(3)</a>, <a href="../man3/EVP_chacha20.html">EVP_chacha20(3)</a>, <a href="../man3/EVP_des.html">EVP_des(3)</a>, <a href="../man3/EVP_desx.html">EVP_desx(3)</a>, <a href="../man3/EVP_idea.html">EVP_idea(3)</a>, <a href="../man3/EVP_rc2.html">EVP_rc2(3)</a>, <a href="../man3/EVP_rc4.html">EVP_rc4(3)</a>, <a href="../man3/EVP_rc5.html">EVP_rc5(3)</a>, <a href="../man3/EVP_seed.html">EVP_seed(3)</a>, <a href="../man3/EVP_sm4.html">EVP_sm4(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>Support for OCB mode was added in OpenSSL 1.1.0.</p>

<p><b>EVP_CIPHER_CTX</b> was made opaque in OpenSSL 1.1.0. As a result, EVP_CIPHER_CTX_reset() appeared and EVP_CIPHER_CTX_cleanup() disappeared. EVP_CIPHER_CTX_init() remains as an alias for EVP_CIPHER_CTX_reset().</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


