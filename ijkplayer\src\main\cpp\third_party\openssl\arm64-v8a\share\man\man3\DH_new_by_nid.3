.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DH_NEW_BY_NID 3"
.TH DH_NEW_BY_NID 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DH_new_by_nid, DH_get_nid \- get or find DH named parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 3
\& #include <openssl/dh.h>
\& DH *DH_new_by_nid(int nid);
\& int *DH_get_nid(const DH *dh);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBDH_new_by_nid()\fR creates and returns a DH structure containing named parameters
\&\fBnid\fR. Currently \fBnid\fR must be \fBNID_ffdhe2048\fR, \fBNID_ffdhe3072\fR,
\&\fBNID_ffdhe4096\fR, \fBNID_ffdhe6144\fR or \fBNID_ffdhe8192\fR.
.PP
\&\fBDH_get_nid()\fR determines if the parameters contained in \fBdh\fR match
any named set. It returns the NID corresponding to the matching parameters or
\&\fBNID_undef\fR if there is no match.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDH_new_by_nid()\fR returns a set of DH parameters or \fBNULL\fR if an error occurred.
.PP
\&\fBDH_get_nid()\fR returns the NID of the matching set of parameters or
\&\fBNID_undef\fR if there is no match.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
