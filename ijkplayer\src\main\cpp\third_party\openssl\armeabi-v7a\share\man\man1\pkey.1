.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKEY 1"
.TH PKEY 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-pkey,
pkey \- public or private key processing tool
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBpkey\fR
[\fB\-help\fR]
[\fB\-inform PEM|DER\fR]
[\fB\-outform PEM|DER\fR]
[\fB\-in filename\fR]
[\fB\-passin arg\fR]
[\fB\-out filename\fR]
[\fB\-passout arg\fR]
[\fB\-traditional\fR]
[\fB\-\fR\f(BIcipher\fR]
[\fB\-text\fR]
[\fB\-text_pub\fR]
[\fB\-noout\fR]
[\fB\-pubin\fR]
[\fB\-pubout\fR]
[\fB\-engine id\fR]
[\fB\-check\fR]
[\fB\-pubcheck\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBpkey\fR command processes public or private keys. They can be converted
between various forms and their components printed out.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format DER or PEM. The default format is PEM.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format, the options have the same meaning and default
as the \fB\-inform\fR option.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read a key from or standard input if this
option is not specified. If the key is encrypted a pass phrase will be
prompted for.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
The input file password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write a key to or standard output if this
option is not specified. If any encryption options are set then a pass phrase
will be prompted for. The output filename should \fBnot\fR be the same as the input
filename.
.IP "\fB\-passout password\fR" 4
.IX Item "-passout password"
The output file password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP \fB\-traditional\fR 4
.IX Item "-traditional"
Normally a private key is written using standard format: this is PKCS#8 form
with the appropriate encryption algorithm (if any). If the \fB\-traditional\fR
option is specified then the older "traditional" format is used instead.
.IP \fB\-\fR\f(BIcipher\fR 4
.IX Item "-cipher"
These options encrypt the private key with the supplied cipher. Any algorithm
name accepted by \fBEVP_get_cipherbyname()\fR is acceptable such as \fBdes3\fR.
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the various public or private key components in
plain text in addition to the encoded version.
.IP \fB\-text_pub\fR 4
.IX Item "-text_pub"
Print out only public key components even if a private key is being processed.
.IP \fB\-noout\fR 4
.IX Item "-noout"
Do not output the encoded version of the key.
.IP \fB\-pubin\fR 4
.IX Item "-pubin"
By default a private key is read from the input file: with this
option a public key is read instead.
.IP \fB\-pubout\fR 4
.IX Item "-pubout"
By default a private key is output: with this option a public
key will be output instead. This option is automatically set if
the input is a public key.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBpkey\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.IP \fB\-check\fR 4
.IX Item "-check"
This option checks the consistency of a key pair for both public and private
components.
.IP \fB\-pubcheck\fR 4
.IX Item "-pubcheck"
This option checks the correctness of either a public key or the public component
of a key pair.
.SH EXAMPLES
.IX Header "EXAMPLES"
To remove the pass phrase on an RSA private key:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-out keyout.pem
.Ve
.PP
To encrypt a private key using triple DES:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-des3 \-out keyout.pem
.Ve
.PP
To convert a private key from PEM to DER format:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-outform DER \-out keyout.der
.Ve
.PP
To print out the components of a private key to standard output:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-text \-noout
.Ve
.PP
To print out the public components of a private key to standard output:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-text_pub \-noout
.Ve
.PP
To just output the public part of a private key:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-pubout \-out pubkey.pem
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBgenpkey\fR\|(1), \fBrsa\fR\|(1), \fBpkcs8\fR\|(1),
\&\fBdsa\fR\|(1), \fBgenrsa\fR\|(1), \fBgendsa\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
