.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "VERIFY 1"
.TH VERIFY 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-verify,
verify \- Utility to verify certificates
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBverify\fR
[\fB\-help\fR]
[\fB\-CAfile file\fR]
[\fB\-CApath directory\fR]
[\fB\-no\-CAfile\fR]
[\fB\-no\-CApath\fR]
[\fB\-allow_proxy_certs\fR]
[\fB\-attime timestamp\fR]
[\fB\-check_ss_sig\fR]
[\fB\-CRLfile file\fR]
[\fB\-crl_download\fR]
[\fB\-crl_check\fR]
[\fB\-crl_check_all\fR]
[\fB\-engine id\fR]
[\fB\-explicit_policy\fR]
[\fB\-extended_crl\fR]
[\fB\-ignore_critical\fR]
[\fB\-inhibit_any\fR]
[\fB\-inhibit_map\fR]
[\fB\-nameopt option\fR]
[\fB\-no_check_time\fR]
[\fB\-partial_chain\fR]
[\fB\-policy arg\fR]
[\fB\-policy_check\fR]
[\fB\-policy_print\fR]
[\fB\-purpose purpose\fR]
[\fB\-suiteB_128\fR]
[\fB\-suiteB_128_only\fR]
[\fB\-suiteB_192\fR]
[\fB\-trusted_first\fR]
[\fB\-no_alt_chains\fR]
[\fB\-untrusted file\fR]
[\fB\-trusted file\fR]
[\fB\-use_deltas\fR]
[\fB\-verbose\fR]
[\fB\-auth_level level\fR]
[\fB\-verify_depth num\fR]
[\fB\-verify_email email\fR]
[\fB\-verify_hostname hostname\fR]
[\fB\-verify_ip ip\fR]
[\fB\-verify_name name\fR]
[\fB\-x509_strict\fR]
[\fB\-show_chain\fR]
[\fB\-\fR]
[certificates]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBverify\fR command verifies certificate chains.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-CAfile file\fR" 4
.IX Item "-CAfile file"
A \fBfile\fR of trusted certificates.
The file should contain one or more certificates in PEM format.
.IP "\fB\-CApath directory\fR" 4
.IX Item "-CApath directory"
A directory of trusted certificates. The certificates should have names
of the form: hash.0 or have symbolic links to them of this
form ("hash" is the hashed certificate subject name: see the \fB\-hash\fR option
of the \fBx509\fR utility). Under Unix the \fBc_rehash\fR script will automatically
create symbolic links to a directory of certificates.
.IP \fB\-no\-CAfile\fR 4
.IX Item "-no-CAfile"
Do not load the trusted CA certificates from the default file location.
.IP \fB\-no\-CApath\fR 4
.IX Item "-no-CApath"
Do not load the trusted CA certificates from the default directory location.
.IP \fB\-allow_proxy_certs\fR 4
.IX Item "-allow_proxy_certs"
Allow the verification of proxy certificates.
.IP "\fB\-attime timestamp\fR" 4
.IX Item "-attime timestamp"
Perform validation checks using time specified by \fBtimestamp\fR and not
current system time. \fBtimestamp\fR is the number of seconds since
01.01.1970 (UNIX time).
.IP \fB\-check_ss_sig\fR 4
.IX Item "-check_ss_sig"
Verify the signature of
the last certificate in a chain if the certificate is supposedly self-signed.
This is prohibited and will result in an error if it is a non-conforming CA
certificate with key usage restrictions not including the keyCertSign bit.
This verification is disabled by default because it doesn't add any security.
.IP "\fB\-CRLfile file\fR" 4
.IX Item "-CRLfile file"
The \fBfile\fR should contain one or more CRLs in PEM format.
This option can be specified more than once to include CRLs from multiple
\&\fBfiles\fR.
.IP \fB\-crl_download\fR 4
.IX Item "-crl_download"
Attempt to download CRL information for this certificate.
.IP \fB\-crl_check\fR 4
.IX Item "-crl_check"
Checks end entity certificate validity by attempting to look up a valid CRL.
If a valid CRL cannot be found an error occurs.
.IP \fB\-crl_check_all\fR 4
.IX Item "-crl_check_all"
Checks the validity of \fBall\fR certificates in the chain by attempting
to look up valid CRLs.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine \fBid\fR will cause \fBverify\fR\|(1) to attempt to load the
specified engine.
The engine will then be set as the default for all its supported algorithms.
If you want to load certificates or CRLs that require engine support via any of
the \fB\-trusted\fR, \fB\-untrusted\fR or \fB\-CRLfile\fR options, the \fB\-engine\fR option
must be specified before those options.
.IP \fB\-explicit_policy\fR 4
.IX Item "-explicit_policy"
Set policy variable require-explicit-policy (see RFC5280).
.IP \fB\-extended_crl\fR 4
.IX Item "-extended_crl"
Enable extended CRL features such as indirect CRLs and alternate CRL
signing keys.
.IP \fB\-ignore_critical\fR 4
.IX Item "-ignore_critical"
Normally if an unhandled critical extension is present which is not
supported by OpenSSL the certificate is rejected (as required by RFC5280).
If this option is set critical extensions are ignored.
.IP \fB\-inhibit_any\fR 4
.IX Item "-inhibit_any"
Set policy variable inhibit-any-policy (see RFC5280).
.IP \fB\-inhibit_map\fR 4
.IX Item "-inhibit_map"
Set policy variable inhibit-policy-mapping (see RFC5280).
.IP "\fB\-nameopt option\fR" 4
.IX Item "-nameopt option"
Option which determines how the subject or issuer names are displayed. The
\&\fBoption\fR argument can be a single option or multiple options separated by
commas.  Alternatively the \fB\-nameopt\fR switch may be used more than once to
set multiple options. See the \fBx509\fR\|(1) manual page for details.
.IP \fB\-no_check_time\fR 4
.IX Item "-no_check_time"
This option suppresses checking the validity period of certificates and CRLs
against the current time. If option \fB\-attime timestamp\fR is used to specify
a verification time, the check is not suppressed.
.IP \fB\-partial_chain\fR 4
.IX Item "-partial_chain"
Allow verification to succeed even if a \fIcomplete\fR chain cannot be built to a
self-signed trust-anchor, provided it is possible to construct a chain to a
trusted certificate that might not be self-signed.
.IP "\fB\-policy arg\fR" 4
.IX Item "-policy arg"
Enable policy processing and add \fBarg\fR to the user-initial-policy-set (see
RFC5280). The policy \fBarg\fR can be an object name an OID in numeric form.
This argument can appear more than once.
.IP \fB\-policy_check\fR 4
.IX Item "-policy_check"
Enables certificate policy processing.
.IP \fB\-policy_print\fR 4
.IX Item "-policy_print"
Print out diagnostics related to policy processing.
.IP "\fB\-purpose purpose\fR" 4
.IX Item "-purpose purpose"
The intended use for the certificate. If this option is not specified,
\&\fBverify\fR will not consider certificate purpose during chain verification.
Currently accepted uses are \fBsslclient\fR, \fBsslserver\fR, \fBnssslserver\fR,
\&\fBsmimesign\fR, \fBsmimeencrypt\fR. See the \fBVERIFY OPERATION\fR section for more
information.
.IP "\fB\-suiteB_128_only\fR, \fB\-suiteB_128\fR, \fB\-suiteB_192\fR" 4
.IX Item "-suiteB_128_only, -suiteB_128, -suiteB_192"
Enable the Suite B mode operation at 128 bit Level of Security, 128 bit or
192 bit, or only 192 bit Level of Security respectively.
See RFC6460 for details. In particular the supported signature algorithms are
reduced to support only ECDSA and SHA256 or SHA384 and only the elliptic curves
P\-256 and P\-384.
.IP \fB\-trusted_first\fR 4
.IX Item "-trusted_first"
When constructing the certificate chain, use the trusted certificates specified
via \fB\-CAfile\fR, \fB\-CApath\fR or \fB\-trusted\fR before any certificates specified via
\&\fB\-untrusted\fR.
This can be useful in environments with Bridge or Cross-Certified CAs.
As of OpenSSL 1.1.0 this option is on by default and cannot be disabled.
.IP \fB\-no_alt_chains\fR 4
.IX Item "-no_alt_chains"
By default, unless \fB\-trusted_first\fR is specified, when building a certificate
chain, if the first certificate chain found is not trusted, then OpenSSL will
attempt to replace untrusted issuer certificates with certificates from the
trust store to see if an alternative chain can be found that is trusted.
As of OpenSSL 1.1.0, with \fB\-trusted_first\fR always on, this option has no
effect.
.IP "\fB\-untrusted file\fR" 4
.IX Item "-untrusted file"
A \fBfile\fR of additional untrusted certificates (intermediate issuer CAs) used
to construct a certificate chain from the subject certificate to a trust-anchor.
The \fBfile\fR should contain one or more certificates in PEM format.
This option can be specified more than once to include untrusted certificates
from multiple \fBfiles\fR.
.IP "\fB\-trusted file\fR" 4
.IX Item "-trusted file"
A \fBfile\fR of trusted certificates, which must be self-signed, unless the
\&\fB\-partial_chain\fR option is specified.
The \fBfile\fR contains one or more certificates in PEM format.
With this option, no additional (e.g., default) certificate lists are
consulted.
That is, the only trust-anchors are those listed in \fBfile\fR.
This option can be specified more than once to include trusted certificates
from multiple \fBfiles\fR.
This option implies the \fB\-no\-CAfile\fR and \fB\-no\-CApath\fR options.
This option cannot be used in combination with either of the \fB\-CAfile\fR or
\&\fB\-CApath\fR options.
.IP \fB\-use_deltas\fR 4
.IX Item "-use_deltas"
Enable support for delta CRLs.
.IP \fB\-verbose\fR 4
.IX Item "-verbose"
Print extra information about the operations being performed.
.IP "\fB\-auth_level level\fR" 4
.IX Item "-auth_level level"
Set the certificate chain authentication security level to \fBlevel\fR.
The authentication security level determines the acceptable signature and
public key strength when verifying certificate chains.
For a certificate chain to validate, the public keys of all the certificates
must meet the specified security \fBlevel\fR.
The signature algorithm security level is enforced for all the certificates in
the chain except for the chain's \fItrust anchor\fR, which is either directly
trusted or validated by means other than its signature.
See \fBSSL_CTX_set_security_level\fR\|(3) for the definitions of the available
levels.
The default security level is \-1, or "not set".
At security level 0 or lower all algorithms are acceptable.
Security level 1 requires at least 80\-bit\-equivalent security and is broadly
interoperable, though it will, for example, reject MD5 signatures or RSA keys
shorter than 1024 bits.
.IP "\fB\-verify_depth num\fR" 4
.IX Item "-verify_depth num"
Limit the certificate chain to \fBnum\fR intermediate CA certificates.
A maximal depth chain can have up to \fBnum+2\fR certificates, since neither the
end-entity certificate nor the trust-anchor certificate count against the
\&\fB\-verify_depth\fR limit.
.IP "\fB\-verify_email email\fR" 4
.IX Item "-verify_email email"
Verify if the \fBemail\fR matches the email address in Subject Alternative Name or
the email in the subject Distinguished Name.
.IP "\fB\-verify_hostname hostname\fR" 4
.IX Item "-verify_hostname hostname"
Verify if the \fBhostname\fR matches DNS name in Subject Alternative Name or
Common Name in the subject certificate.
.IP "\fB\-verify_ip ip\fR" 4
.IX Item "-verify_ip ip"
Verify if the \fBip\fR matches the IP address in Subject Alternative Name of
the subject certificate.
.IP "\fB\-verify_name name\fR" 4
.IX Item "-verify_name name"
Use default verification policies like trust model and required certificate
policies identified by \fBname\fR.
The trust model determines which auxiliary trust or reject OIDs are applicable
to verifying the given certificate chain.
See the \fB\-addtrust\fR and \fB\-addreject\fR options of the \fBx509\fR\|(1) command-line
utility.
Supported policy names include: \fBdefault\fR, \fBpkcs7\fR, \fBsmime_sign\fR,
\&\fBssl_client\fR, \fBssl_server\fR.
These mimics the combinations of purpose and trust settings used in SSL, CMS
and S/MIME.
As of OpenSSL 1.1.0, the trust model is inferred from the purpose when not
specified, so the \fB\-verify_name\fR options are functionally equivalent to the
corresponding \fB\-purpose\fR settings.
.IP \fB\-x509_strict\fR 4
.IX Item "-x509_strict"
For strict X.509 compliance, disable non-compliant workarounds for broken
certificates.
.IP \fB\-show_chain\fR 4
.IX Item "-show_chain"
Display information about the certificate chain that has been built (if
successful). Certificates in the chain that came from the untrusted list will be
flagged as "untrusted".
.IP \fB\-\fR 4
.IX Item "-"
Indicates the last option. All arguments following this are assumed to be
certificate files. This is useful if the first certificate filename begins
with a \fB\-\fR.
.IP \fBcertificates\fR 4
.IX Item "certificates"
One or more certificates to verify. If no certificates are given, \fBverify\fR
will attempt to read a certificate from standard input. Certificates must be
in PEM format.
.SH "VERIFY OPERATION"
.IX Header "VERIFY OPERATION"
The \fBverify\fR program uses the same functions as the internal SSL and S/MIME
verification, therefore, this description applies to these verify operations
too.
.PP
There is one crucial difference between the verify operations performed
by the \fBverify\fR program: wherever possible an attempt is made to continue
after an error whereas normally the verify operation would halt on the
first error. This allows all the problems with a certificate chain to be
determined.
.PP
The verify operation consists of a number of separate steps.
.PP
Firstly a certificate chain is built up starting from the supplied certificate
and ending in the root CA.
It is an error if the whole chain cannot be built up.
The chain is built up by looking up the issuers certificate of the current
certificate.
If a certificate is found which is its own issuer it is assumed to be the root
CA.
.PP
The process of 'looking up the issuers certificate' itself involves a number of
steps.
After all certificates whose subject name matches the issuer name of the current
certificate are subject to further tests.
The relevant authority key identifier components of the current certificate (if
present) must match the subject key identifier (if present) and issuer and
serial number of the candidate issuer, in addition the keyUsage extension of
the candidate issuer (if present) must permit certificate signing.
.PP
The lookup first looks in the list of untrusted certificates and if no match
is found the remaining lookups are from the trusted certificates. The root CA
is always looked up in the trusted certificate list: if the certificate to
verify is a root certificate then an exact match must be found in the trusted
list.
.PP
The second operation is to check every untrusted certificate's extensions for
consistency with the supplied purpose. If the \fB\-purpose\fR option is not included
then no checks are done. The supplied or "leaf" certificate must have extensions
compatible with the supplied purpose and all other certificates must also be valid
CA certificates. The precise extensions required are described in more detail in
the \fBCERTIFICATE EXTENSIONS\fR section of the \fBx509\fR utility.
.PP
The third operation is to check the trust settings on the root CA. The root CA
should be trusted for the supplied purpose.
For compatibility with previous versions of OpenSSL, a certificate with no
trust settings is considered to be valid for all purposes.
.PP
The final operation is to check the validity of the certificate chain.
For each element in the chain, including the root CA certificate,
the validity period as specified by the \f(CW\*(C`notBefore\*(C'\fR and \f(CW\*(C`notAfter\*(C'\fR fields
is checked against the current system time.
The \fB\-attime\fR flag may be used to use a reference time other than "now."
The certificate signature is checked as well
(except for the signature of the typically self-signed root CA certificate,
which is verified only if the \fB\-check_ss_sig\fR option is given).
.PP
If all operations complete successfully then certificate is considered valid. If
any operation fails then the certificate is not valid.
.SH DIAGNOSTICS
.IX Header "DIAGNOSTICS"
When a verify operation fails the output messages can be somewhat cryptic. The
general form of the error message is:
.PP
.Vb 2
\& server.pem: /C=AU/ST=Queensland/O=CryptSoft Pty Ltd/CN=Test CA (1024 bit)
\& error 24 at 1 depth lookup:invalid CA certificate
.Ve
.PP
The first line contains the name of the certificate being verified followed by
the subject name of the certificate. The second line contains the error number
and the depth. The depth is number of the certificate being verified when a
problem was detected starting with zero for the certificate being verified itself
then 1 for the CA that signed the certificate and so on. Finally a text version
of the error number is presented.
.PP
A partial list of the error codes and messages is shown below, this also
includes the name of the error code as defined in the header file x509_vfy.h
Some of the error codes are defined but never returned: these are described
as "unused".
.IP \fBX509_V_OK\fR 4
.IX Item "X509_V_OK"
The operation was successful.
.IP \fBX509_V_ERR_UNSPECIFIED\fR 4
.IX Item "X509_V_ERR_UNSPECIFIED"
Unspecified error; should not happen.
.IP \fBX509_V_ERR_UNABLE_TO_GET_ISSUER_CERT\fR 4
.IX Item "X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT"
The issuer certificate of a looked up certificate could not be found. This
normally means the list of trusted certificates is not complete.
.IP \fBX509_V_ERR_UNABLE_TO_GET_CRL\fR 4
.IX Item "X509_V_ERR_UNABLE_TO_GET_CRL"
The CRL of a certificate could not be found.
.IP \fBX509_V_ERR_UNABLE_TO_DECRYPT_CERT_SIGNATURE\fR 4
.IX Item "X509_V_ERR_UNABLE_TO_DECRYPT_CERT_SIGNATURE"
The certificate signature could not be decrypted. This means that the
actual signature value could not be determined rather than it not matching
the expected value, this is only meaningful for RSA keys.
.IP \fBX509_V_ERR_UNABLE_TO_DECRYPT_CRL_SIGNATURE\fR 4
.IX Item "X509_V_ERR_UNABLE_TO_DECRYPT_CRL_SIGNATURE"
The CRL signature could not be decrypted: this means that the actual
signature value could not be determined rather than it not matching the
expected value. Unused.
.IP \fBX509_V_ERR_UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY\fR 4
.IX Item "X509_V_ERR_UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY"
The public key in the certificate SubjectPublicKeyInfo could not be read.
.IP \fBX509_V_ERR_CERT_SIGNATURE_FAILURE\fR 4
.IX Item "X509_V_ERR_CERT_SIGNATURE_FAILURE"
The signature of the certificate is invalid.
.IP \fBX509_V_ERR_CRL_SIGNATURE_FAILURE\fR 4
.IX Item "X509_V_ERR_CRL_SIGNATURE_FAILURE"
The signature of the certificate is invalid.
.IP \fBX509_V_ERR_CERT_NOT_YET_VALID\fR 4
.IX Item "X509_V_ERR_CERT_NOT_YET_VALID"
The certificate is not yet valid: the notBefore date is after the
current time.
.IP \fBX509_V_ERR_CERT_HAS_EXPIRED\fR 4
.IX Item "X509_V_ERR_CERT_HAS_EXPIRED"
The certificate has expired: that is the notAfter date is before the
current time.
.IP \fBX509_V_ERR_CRL_NOT_YET_VALID\fR 4
.IX Item "X509_V_ERR_CRL_NOT_YET_VALID"
The CRL is not yet valid.
.IP \fBX509_V_ERR_CRL_HAS_EXPIRED\fR 4
.IX Item "X509_V_ERR_CRL_HAS_EXPIRED"
The CRL has expired.
.IP \fBX509_V_ERR_ERROR_IN_CERT_NOT_BEFORE_FIELD\fR 4
.IX Item "X509_V_ERR_ERROR_IN_CERT_NOT_BEFORE_FIELD"
The certificate notBefore field contains an invalid time.
.IP \fBX509_V_ERR_ERROR_IN_CERT_NOT_AFTER_FIELD\fR 4
.IX Item "X509_V_ERR_ERROR_IN_CERT_NOT_AFTER_FIELD"
The certificate notAfter field contains an invalid time.
.IP \fBX509_V_ERR_ERROR_IN_CRL_LAST_UPDATE_FIELD\fR 4
.IX Item "X509_V_ERR_ERROR_IN_CRL_LAST_UPDATE_FIELD"
The CRL lastUpdate field contains an invalid time.
.IP \fBX509_V_ERR_ERROR_IN_CRL_NEXT_UPDATE_FIELD\fR 4
.IX Item "X509_V_ERR_ERROR_IN_CRL_NEXT_UPDATE_FIELD"
The CRL nextUpdate field contains an invalid time.
.IP \fBX509_V_ERR_OUT_OF_MEM\fR 4
.IX Item "X509_V_ERR_OUT_OF_MEM"
An error occurred trying to allocate memory. This should never happen.
.IP \fBX509_V_ERR_DEPTH_ZERO_SELF_SIGNED_CERT\fR 4
.IX Item "X509_V_ERR_DEPTH_ZERO_SELF_SIGNED_CERT"
The passed certificate is self-signed and the same certificate cannot
be found in the list of trusted certificates.
.IP \fBX509_V_ERR_SELF_SIGNED_CERT_IN_CHAIN\fR 4
.IX Item "X509_V_ERR_SELF_SIGNED_CERT_IN_CHAIN"
The certificate chain could be built up using the untrusted certificates
but the root could not be found locally.
.IP \fBX509_V_ERR_UNABLE_TO_GET_ISSUER_CERT_LOCALLY\fR 4
.IX Item "X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT_LOCALLY"
The issuer certificate could not be found: this occurs if the issuer
certificate of an untrusted certificate cannot be found.
.IP \fBX509_V_ERR_UNABLE_TO_VERIFY_LEAF_SIGNATURE\fR 4
.IX Item "X509_V_ERR_UNABLE_TO_VERIFY_LEAF_SIGNATURE"
No signatures could be verified because the chain contains only one
certificate and it is not self signed.
.IP \fBX509_V_ERR_CERT_CHAIN_TOO_LONG\fR 4
.IX Item "X509_V_ERR_CERT_CHAIN_TOO_LONG"
The certificate chain length is greater than the supplied maximum
depth. Unused.
.IP \fBX509_V_ERR_CERT_REVOKED\fR 4
.IX Item "X509_V_ERR_CERT_REVOKED"
The certificate has been revoked.
.IP \fBX509_V_ERR_INVALID_CA\fR 4
.IX Item "X509_V_ERR_INVALID_CA"
A CA certificate is invalid. Either it is not a CA or its extensions
are not consistent with the supplied purpose.
.IP \fBX509_V_ERR_PATH_LENGTH_EXCEEDED\fR 4
.IX Item "X509_V_ERR_PATH_LENGTH_EXCEEDED"
The basicConstraints pathlength parameter has been exceeded.
.IP \fBX509_V_ERR_INVALID_PURPOSE\fR 4
.IX Item "X509_V_ERR_INVALID_PURPOSE"
The supplied certificate cannot be used for the specified purpose.
.IP \fBX509_V_ERR_CERT_UNTRUSTED\fR 4
.IX Item "X509_V_ERR_CERT_UNTRUSTED"
The root CA is not marked as trusted for the specified purpose.
.IP \fBX509_V_ERR_CERT_REJECTED\fR 4
.IX Item "X509_V_ERR_CERT_REJECTED"
The root CA is marked to reject the specified purpose.
.IP \fBX509_V_ERR_SUBJECT_ISSUER_MISMATCH\fR 4
.IX Item "X509_V_ERR_SUBJECT_ISSUER_MISMATCH"
Not used as of OpenSSL 1.1.0 as a result of the deprecation of the
\&\fB\-issuer_checks\fR option.
.IP \fBX509_V_ERR_AKID_SKID_MISMATCH\fR 4
.IX Item "X509_V_ERR_AKID_SKID_MISMATCH"
Not used as of OpenSSL 1.1.0 as a result of the deprecation of the
\&\fB\-issuer_checks\fR option.
.IP \fBX509_V_ERR_AKID_ISSUER_SERIAL_MISMATCH\fR 4
.IX Item "X509_V_ERR_AKID_ISSUER_SERIAL_MISMATCH"
Not used as of OpenSSL 1.1.0 as a result of the deprecation of the
\&\fB\-issuer_checks\fR option.
.IP \fBX509_V_ERR_KEYUSAGE_NO_CERTSIGN\fR 4
.IX Item "X509_V_ERR_KEYUSAGE_NO_CERTSIGN"
Not used as of OpenSSL 1.1.0 as a result of the deprecation of the
\&\fB\-issuer_checks\fR option.
.IP \fBX509_V_ERR_UNABLE_TO_GET_CRL_ISSUER\fR 4
.IX Item "X509_V_ERR_UNABLE_TO_GET_CRL_ISSUER"
Unable to get CRL issuer certificate.
.IP \fBX509_V_ERR_UNHANDLED_CRITICAL_EXTENSION\fR 4
.IX Item "X509_V_ERR_UNHANDLED_CRITICAL_EXTENSION"
Unhandled critical extension.
.IP \fBX509_V_ERR_KEYUSAGE_NO_CRL_SIGN\fR 4
.IX Item "X509_V_ERR_KEYUSAGE_NO_CRL_SIGN"
Key usage does not include CRL signing.
.IP \fBX509_V_ERR_UNHANDLED_CRITICAL_CRL_EXTENSION\fR 4
.IX Item "X509_V_ERR_UNHANDLED_CRITICAL_CRL_EXTENSION"
Unhandled critical CRL extension.
.IP \fBX509_V_ERR_INVALID_NON_CA\fR 4
.IX Item "X509_V_ERR_INVALID_NON_CA"
Invalid non-CA certificate has CA markings.
.IP \fBX509_V_ERR_PROXY_PATH_LENGTH_EXCEEDED\fR 4
.IX Item "X509_V_ERR_PROXY_PATH_LENGTH_EXCEEDED"
Proxy path length constraint exceeded.
.IP \fBX509_V_ERR_PROXY_SUBJECT_INVALID\fR 4
.IX Item "X509_V_ERR_PROXY_SUBJECT_INVALID"
Proxy certificate subject is invalid.  It MUST be the same as the issuer
with a single CN component added.
.IP \fBX509_V_ERR_KEYUSAGE_NO_DIGITAL_SIGNATURE\fR 4
.IX Item "X509_V_ERR_KEYUSAGE_NO_DIGITAL_SIGNATURE"
Key usage does not include digital signature.
.IP \fBX509_V_ERR_PROXY_CERTIFICATES_NOT_ALLOWED\fR 4
.IX Item "X509_V_ERR_PROXY_CERTIFICATES_NOT_ALLOWED"
Proxy certificates not allowed, please use \fB\-allow_proxy_certs\fR.
.IP \fBX509_V_ERR_INVALID_EXTENSION\fR 4
.IX Item "X509_V_ERR_INVALID_EXTENSION"
Invalid or inconsistent certificate extension.
.IP \fBX509_V_ERR_INVALID_POLICY_EXTENSION\fR 4
.IX Item "X509_V_ERR_INVALID_POLICY_EXTENSION"
Invalid or inconsistent certificate policy extension.
.IP \fBX509_V_ERR_NO_EXPLICIT_POLICY\fR 4
.IX Item "X509_V_ERR_NO_EXPLICIT_POLICY"
No explicit policy.
.IP \fBX509_V_ERR_DIFFERENT_CRL_SCOPE\fR 4
.IX Item "X509_V_ERR_DIFFERENT_CRL_SCOPE"
Different CRL scope.
.IP \fBX509_V_ERR_UNSUPPORTED_EXTENSION_FEATURE\fR 4
.IX Item "X509_V_ERR_UNSUPPORTED_EXTENSION_FEATURE"
Unsupported extension feature.
.IP \fBX509_V_ERR_UNNESTED_RESOURCE\fR 4
.IX Item "X509_V_ERR_UNNESTED_RESOURCE"
RFC 3779 resource not subset of parent's resources.
.IP \fBX509_V_ERR_PERMITTED_VIOLATION\fR 4
.IX Item "X509_V_ERR_PERMITTED_VIOLATION"
Permitted subtree violation.
.IP \fBX509_V_ERR_EXCLUDED_VIOLATION\fR 4
.IX Item "X509_V_ERR_EXCLUDED_VIOLATION"
Excluded subtree violation.
.IP \fBX509_V_ERR_SUBTREE_MINMAX\fR 4
.IX Item "X509_V_ERR_SUBTREE_MINMAX"
Name constraints minimum and maximum not supported.
.IP \fBX509_V_ERR_APPLICATION_VERIFICATION\fR 4
.IX Item "X509_V_ERR_APPLICATION_VERIFICATION"
Application verification failure. Unused.
.IP \fBX509_V_ERR_UNSUPPORTED_CONSTRAINT_TYPE\fR 4
.IX Item "X509_V_ERR_UNSUPPORTED_CONSTRAINT_TYPE"
Unsupported name constraint type.
.IP \fBX509_V_ERR_UNSUPPORTED_CONSTRAINT_SYNTAX\fR 4
.IX Item "X509_V_ERR_UNSUPPORTED_CONSTRAINT_SYNTAX"
Unsupported or invalid name constraint syntax.
.IP \fBX509_V_ERR_UNSUPPORTED_NAME_SYNTAX\fR 4
.IX Item "X509_V_ERR_UNSUPPORTED_NAME_SYNTAX"
Unsupported or invalid name syntax.
.IP \fBX509_V_ERR_CRL_PATH_VALIDATION_ERROR\fR 4
.IX Item "X509_V_ERR_CRL_PATH_VALIDATION_ERROR"
CRL path validation error.
.IP \fBX509_V_ERR_PATH_LOOP\fR 4
.IX Item "X509_V_ERR_PATH_LOOP"
Path loop.
.IP \fBX509_V_ERR_SUITE_B_INVALID_VERSION\fR 4
.IX Item "X509_V_ERR_SUITE_B_INVALID_VERSION"
Suite B: certificate version invalid.
.IP \fBX509_V_ERR_SUITE_B_INVALID_ALGORITHM\fR 4
.IX Item "X509_V_ERR_SUITE_B_INVALID_ALGORITHM"
Suite B: invalid public key algorithm.
.IP \fBX509_V_ERR_SUITE_B_INVALID_CURVE\fR 4
.IX Item "X509_V_ERR_SUITE_B_INVALID_CURVE"
Suite B: invalid ECC curve.
.IP \fBX509_V_ERR_SUITE_B_INVALID_SIGNATURE_ALGORITHM\fR 4
.IX Item "X509_V_ERR_SUITE_B_INVALID_SIGNATURE_ALGORITHM"
Suite B: invalid signature algorithm.
.IP \fBX509_V_ERR_SUITE_B_LOS_NOT_ALLOWED\fR 4
.IX Item "X509_V_ERR_SUITE_B_LOS_NOT_ALLOWED"
Suite B: curve not allowed for this LOS.
.IP \fBX509_V_ERR_SUITE_B_CANNOT_SIGN_P_384_WITH_P_256\fR 4
.IX Item "X509_V_ERR_SUITE_B_CANNOT_SIGN_P_384_WITH_P_256"
Suite B: cannot sign P\-384 with P\-256.
.IP \fBX509_V_ERR_HOSTNAME_MISMATCH\fR 4
.IX Item "X509_V_ERR_HOSTNAME_MISMATCH"
Hostname mismatch.
.IP \fBX509_V_ERR_EMAIL_MISMATCH\fR 4
.IX Item "X509_V_ERR_EMAIL_MISMATCH"
Email address mismatch.
.IP \fBX509_V_ERR_IP_ADDRESS_MISMATCH\fR 4
.IX Item "X509_V_ERR_IP_ADDRESS_MISMATCH"
IP address mismatch.
.IP \fBX509_V_ERR_DANE_NO_MATCH\fR 4
.IX Item "X509_V_ERR_DANE_NO_MATCH"
DANE TLSA authentication is enabled, but no TLSA records matched the
certificate chain.
This error is only possible in \fBs_client\fR\|(1).
.IP \fBX509_V_ERR_EE_KEY_TOO_SMALL\fR 4
.IX Item "X509_V_ERR_EE_KEY_TOO_SMALL"
EE certificate key too weak.
.IP \fBX509_ERR_CA_KEY_TOO_SMALL\fR 4
.IX Item "X509_ERR_CA_KEY_TOO_SMALL"
CA certificate key too weak.
.IP \fBX509_ERR_CA_MD_TOO_WEAK\fR 4
.IX Item "X509_ERR_CA_MD_TOO_WEAK"
CA signature digest algorithm too weak.
.IP \fBX509_V_ERR_INVALID_CALL\fR 4
.IX Item "X509_V_ERR_INVALID_CALL"
nvalid certificate verification context.
.IP \fBX509_V_ERR_STORE_LOOKUP\fR 4
.IX Item "X509_V_ERR_STORE_LOOKUP"
Issuer certificate lookup error.
.IP \fBX509_V_ERR_NO_VALID_SCTS\fR 4
.IX Item "X509_V_ERR_NO_VALID_SCTS"
Certificate Transparency required, but no valid SCTs found.
.IP \fBX509_V_ERR_PROXY_SUBJECT_NAME_VIOLATION\fR 4
.IX Item "X509_V_ERR_PROXY_SUBJECT_NAME_VIOLATION"
Proxy subject name violation.
.IP \fBX509_V_ERR_OCSP_VERIFY_NEEDED\fR 4
.IX Item "X509_V_ERR_OCSP_VERIFY_NEEDED"
Returned by the verify callback to indicate an OCSP verification is needed.
.IP \fBX509_V_ERR_OCSP_VERIFY_FAILED\fR 4
.IX Item "X509_V_ERR_OCSP_VERIFY_FAILED"
Returned by the verify callback to indicate OCSP verification failed.
.IP \fBX509_V_ERR_OCSP_CERT_UNKNOWN\fR 4
.IX Item "X509_V_ERR_OCSP_CERT_UNKNOWN"
Returned by the verify callback to indicate that the certificate is not recognized
by the OCSP responder.
.SH BUGS
.IX Header "BUGS"
Although the issuer checks are a considerable improvement over the old
technique they still suffer from limitations in the underlying X509_LOOKUP
API. One consequence of this is that trusted certificates with matching
subject name must either appear in a file (as specified by the \fB\-CAfile\fR
option) or a directory (as specified by \fB\-CApath\fR). If they occur in
both then only the certificates in the file will be recognised.
.PP
Previous versions of OpenSSL assume certificates with matching subject
name are identical and mishandled them.
.PP
Previous versions of this documentation swapped the meaning of the
\&\fBX509_V_ERR_UNABLE_TO_GET_ISSUER_CERT\fR and
\&\fBX509_V_ERR_UNABLE_TO_GET_ISSUER_CERT_LOCALLY\fR error codes.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBx509\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The \fB\-show_chain\fR option was added in OpenSSL 1.1.0.
.PP
The \fB\-issuer_checks\fR option is deprecated as of OpenSSL 1.1.0 and
is silently ignored.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
