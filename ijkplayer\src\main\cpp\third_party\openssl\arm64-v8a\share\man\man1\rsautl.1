.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSAUTL 1"
.TH RSAUTL 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-rsautl,
rsautl \- RSA utility
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBrsautl\fR
[\fB\-help\fR]
[\fB\-in file\fR]
[\fB\-out file\fR]
[\fB\-inkey file\fR]
[\fB\-keyform PEM|DER|ENGINE\fR]
[\fB\-pubin\fR]
[\fB\-certin\fR]
[\fB\-sign\fR]
[\fB\-verify\fR]
[\fB\-encrypt\fR]
[\fB\-decrypt\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-pkcs\fR]
[\fB\-ssl\fR]
[\fB\-raw\fR]
[\fB\-hexdump\fR]
[\fB\-asn1parse\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBrsautl\fR command can be used to sign, verify, encrypt and decrypt
data using the RSA algorithm.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read data from or standard input
if this option is not specified.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
Specifies the output filename to write to or standard output by
default.
.IP "\fB\-inkey file\fR" 4
.IX Item "-inkey file"
The input key file, by default it should be an RSA private key.
.IP "\fB\-keyform PEM|DER|ENGINE\fR" 4
.IX Item "-keyform PEM|DER|ENGINE"
The key format PEM, DER or ENGINE.
.IP \fB\-pubin\fR 4
.IX Item "-pubin"
The input file is an RSA public key.
.IP \fB\-certin\fR 4
.IX Item "-certin"
The input is a certificate containing an RSA public key.
.IP \fB\-sign\fR 4
.IX Item "-sign"
Sign the input data and output the signed result. This requires
an RSA private key.
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verify the input data and output the recovered data.
.IP \fB\-encrypt\fR 4
.IX Item "-encrypt"
Encrypt the input data using an RSA public key.
.IP \fB\-decrypt\fR 4
.IX Item "-decrypt"
Decrypt the input data using an RSA private key.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP "\fB\-pkcs, \-oaep, \-ssl, \-raw\fR" 4
.IX Item "-pkcs, -oaep, -ssl, -raw"
The padding to use: PKCS#1 v1.5 (the default), PKCS#1 OAEP,
special padding used in SSL v2 backwards compatible handshakes,
or no padding, respectively.
For signatures, only \fB\-pkcs\fR and \fB\-raw\fR can be used.
.IP \fB\-hexdump\fR 4
.IX Item "-hexdump"
Hex dump the output data.
.IP \fB\-asn1parse\fR 4
.IX Item "-asn1parse"
Parse the ASN.1 output data, this is useful when combined with the
\&\fB\-verify\fR option.
.SH NOTES
.IX Header "NOTES"
\&\fBrsautl\fR because it uses the RSA algorithm directly can only be
used to sign or verify small pieces of data.
.SH EXAMPLES
.IX Header "EXAMPLES"
Sign some data using a private key:
.PP
.Vb 1
\& openssl rsautl \-sign \-in file \-inkey key.pem \-out sig
.Ve
.PP
Recover the signed data
.PP
.Vb 1
\& openssl rsautl \-verify \-in sig \-inkey key.pem
.Ve
.PP
Examine the raw signed data:
.PP
.Vb 1
\& openssl rsautl \-verify \-in sig \-inkey key.pem \-raw \-hexdump
\&
\& 0000 \- 00 01 ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0010 \- ff ff ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0020 \- ff ff ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0030 \- ff ff ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0040 \- ff ff ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0050 \- ff ff ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0060 \- ff ff ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0070 \- ff ff ff ff 00 68 65 6c\-6c 6f 20 77 6f 72 6c 64   .....hello world
.Ve
.PP
The PKCS#1 block formatting is evident from this. If this was done using
encrypt and decrypt the block would have been of type 2 (the second byte)
and random padding data visible instead of the 0xff bytes.
.PP
It is possible to analyse the signature of certificates using this
utility in conjunction with \fBasn1parse\fR. Consider the self signed
example in certs/pca\-cert.pem . Running \fBasn1parse\fR as follows yields:
.PP
.Vb 1
\& openssl asn1parse \-in pca\-cert.pem
\&
\&    0:d=0  hl=4 l= 742 cons: SEQUENCE
\&    4:d=1  hl=4 l= 591 cons:  SEQUENCE
\&    8:d=2  hl=2 l=   3 cons:   cont [ 0 ]
\&   10:d=3  hl=2 l=   1 prim:    INTEGER           :02
\&   13:d=2  hl=2 l=   1 prim:   INTEGER           :00
\&   16:d=2  hl=2 l=  13 cons:   SEQUENCE
\&   18:d=3  hl=2 l=   9 prim:    OBJECT            :md5WithRSAEncryption
\&   29:d=3  hl=2 l=   0 prim:    NULL
\&   31:d=2  hl=2 l=  92 cons:   SEQUENCE
\&   33:d=3  hl=2 l=  11 cons:    SET
\&   35:d=4  hl=2 l=   9 cons:     SEQUENCE
\&   37:d=5  hl=2 l=   3 prim:      OBJECT            :countryName
\&   42:d=5  hl=2 l=   2 prim:      PRINTABLESTRING   :AU
\&  ....
\&  599:d=1  hl=2 l=  13 cons:  SEQUENCE
\&  601:d=2  hl=2 l=   9 prim:   OBJECT            :md5WithRSAEncryption
\&  612:d=2  hl=2 l=   0 prim:   NULL
\&  614:d=1  hl=3 l= 129 prim:  BIT STRING
.Ve
.PP
The final BIT STRING contains the actual signature. It can be extracted with:
.PP
.Vb 1
\& openssl asn1parse \-in pca\-cert.pem \-out sig \-noout \-strparse 614
.Ve
.PP
The certificate public key can be extracted with:
.PP
.Vb 1
\& openssl x509 \-in test/testx509.pem \-pubkey \-noout >pubkey.pem
.Ve
.PP
The signature can be analysed with:
.PP
.Vb 1
\& openssl rsautl \-in sig \-verify \-asn1parse \-inkey pubkey.pem \-pubin
\&
\&    0:d=0  hl=2 l=  32 cons: SEQUENCE
\&    2:d=1  hl=2 l=  12 cons:  SEQUENCE
\&    4:d=2  hl=2 l=   8 prim:   OBJECT            :md5
\&   14:d=2  hl=2 l=   0 prim:   NULL
\&   16:d=1  hl=2 l=  16 prim:  OCTET STRING
\&      0000 \- f3 46 9e aa 1a 4a 73 c9\-37 ea 93 00 48 25 08 b5   .F...Js.7...H%..
.Ve
.PP
This is the parsed version of an ASN1 DigestInfo structure. It can be seen that
the digest used was md5. The actual part of the certificate that was signed can
be extracted with:
.PP
.Vb 1
\& openssl asn1parse \-in pca\-cert.pem \-out tbs \-noout \-strparse 4
.Ve
.PP
and its digest computed with:
.PP
.Vb 2
\& openssl md5 \-c tbs
\& MD5(tbs)= f3:46:9e:aa:1a:4a:73:c9:37:ea:93:00:48:25:08:b5
.Ve
.PP
which it can be seen agrees with the recovered value above.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBdgst\fR\|(1), \fBrsa\fR\|(1), \fBgenrsa\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
