.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_ASN1_METHOD 3"
.TH EVP_PKEY_ASN1_METHOD 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_ASN1_METHOD,
EVP_PKEY_asn1_new,
EVP_PKEY_asn1_copy,
EVP_PKEY_asn1_free,
EVP_PKEY_asn1_add0,
EVP_PKEY_asn1_add_alias,
EVP_PKEY_asn1_set_public,
EVP_PKEY_asn1_set_private,
EVP_PKEY_asn1_set_param,
EVP_PKEY_asn1_set_free,
EVP_PKEY_asn1_set_ctrl,
EVP_PKEY_asn1_set_item,
EVP_PKEY_asn1_set_siginf,
EVP_PKEY_asn1_set_check,
EVP_PKEY_asn1_set_public_check,
EVP_PKEY_asn1_set_param_check,
EVP_PKEY_asn1_set_security_bits,
EVP_PKEY_asn1_set_set_priv_key,
EVP_PKEY_asn1_set_set_pub_key,
EVP_PKEY_asn1_set_get_priv_key,
EVP_PKEY_asn1_set_get_pub_key,
EVP_PKEY_get0_asn1
\&\- manipulating and registering EVP_PKEY_ASN1_METHOD structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& typedef struct evp_pkey_asn1_method_st EVP_PKEY_ASN1_METHOD;
\&
\& EVP_PKEY_ASN1_METHOD *EVP_PKEY_asn1_new(int id, int flags,
\&                                         const char *pem_str,
\&                                         const char *info);
\& void EVP_PKEY_asn1_copy(EVP_PKEY_ASN1_METHOD *dst,
\&                         const EVP_PKEY_ASN1_METHOD *src);
\& void EVP_PKEY_asn1_free(EVP_PKEY_ASN1_METHOD *ameth);
\& int EVP_PKEY_asn1_add0(const EVP_PKEY_ASN1_METHOD *ameth);
\& int EVP_PKEY_asn1_add_alias(int to, int from);
\&
\& void EVP_PKEY_asn1_set_public(EVP_PKEY_ASN1_METHOD *ameth,
\&                               int (*pub_decode) (EVP_PKEY *pk,
\&                                                  X509_PUBKEY *pub),
\&                               int (*pub_encode) (X509_PUBKEY *pub,
\&                                                  const EVP_PKEY *pk),
\&                               int (*pub_cmp) (const EVP_PKEY *a,
\&                                               const EVP_PKEY *b),
\&                               int (*pub_print) (BIO *out,
\&                                                 const EVP_PKEY *pkey,
\&                                                 int indent, ASN1_PCTX *pctx),
\&                               int (*pkey_size) (const EVP_PKEY *pk),
\&                               int (*pkey_bits) (const EVP_PKEY *pk));
\& void EVP_PKEY_asn1_set_private(EVP_PKEY_ASN1_METHOD *ameth,
\&                                int (*priv_decode) (EVP_PKEY *pk,
\&                                                    const PKCS8_PRIV_KEY_INFO
\&                                                    *p8inf),
\&                                int (*priv_encode) (PKCS8_PRIV_KEY_INFO *p8,
\&                                                    const EVP_PKEY *pk),
\&                                int (*priv_print) (BIO *out,
\&                                                   const EVP_PKEY *pkey,
\&                                                   int indent,
\&                                                   ASN1_PCTX *pctx));
\& void EVP_PKEY_asn1_set_param(EVP_PKEY_ASN1_METHOD *ameth,
\&                              int (*param_decode) (EVP_PKEY *pkey,
\&                                                   const unsigned char **pder,
\&                                                   int derlen),
\&                              int (*param_encode) (const EVP_PKEY *pkey,
\&                                                   unsigned char **pder),
\&                              int (*param_missing) (const EVP_PKEY *pk),
\&                              int (*param_copy) (EVP_PKEY *to,
\&                                                 const EVP_PKEY *from),
\&                              int (*param_cmp) (const EVP_PKEY *a,
\&                                                const EVP_PKEY *b),
\&                              int (*param_print) (BIO *out,
\&                                                  const EVP_PKEY *pkey,
\&                                                  int indent,
\&                                                  ASN1_PCTX *pctx));
\&
\& void EVP_PKEY_asn1_set_free(EVP_PKEY_ASN1_METHOD *ameth,
\&                             void (*pkey_free) (EVP_PKEY *pkey));
\& void EVP_PKEY_asn1_set_ctrl(EVP_PKEY_ASN1_METHOD *ameth,
\&                             int (*pkey_ctrl) (EVP_PKEY *pkey, int op,
\&                                               long arg1, void *arg2));
\& void EVP_PKEY_asn1_set_item(EVP_PKEY_ASN1_METHOD *ameth,
\&                             int (*item_verify) (EVP_MD_CTX *ctx,
\&                                                 const ASN1_ITEM *it,
\&                                                 void *asn,
\&                                                 X509_ALGOR *a,
\&                                                 ASN1_BIT_STRING *sig,
\&                                                 EVP_PKEY *pkey),
\&                             int (*item_sign) (EVP_MD_CTX *ctx,
\&                                               const ASN1_ITEM *it,
\&                                               void *asn,
\&                                               X509_ALGOR *alg1,
\&                                               X509_ALGOR *alg2,
\&                                               ASN1_BIT_STRING *sig));
\&
\& void EVP_PKEY_asn1_set_siginf(EVP_PKEY_ASN1_METHOD *ameth,
\&                               int (*siginf_set) (X509_SIG_INFO *siginf,
\&                                                  const X509_ALGOR *alg,
\&                                                  const ASN1_STRING *sig));
\&
\& void EVP_PKEY_asn1_set_check(EVP_PKEY_ASN1_METHOD *ameth,
\&                              int (*pkey_check) (const EVP_PKEY *pk));
\&
\& void EVP_PKEY_asn1_set_public_check(EVP_PKEY_ASN1_METHOD *ameth,
\&                                     int (*pkey_pub_check) (const EVP_PKEY *pk));
\&
\& void EVP_PKEY_asn1_set_param_check(EVP_PKEY_ASN1_METHOD *ameth,
\&                                    int (*pkey_param_check) (const EVP_PKEY *pk));
\&
\& void EVP_PKEY_asn1_set_security_bits(EVP_PKEY_ASN1_METHOD *ameth,
\&                                      int (*pkey_security_bits) (const EVP_PKEY
\&                                                                 *pk));
\&
\& void EVP_PKEY_asn1_set_set_priv_key(EVP_PKEY_ASN1_METHOD *ameth,
\&                                     int (*set_priv_key) (EVP_PKEY *pk,
\&                                                          const unsigned char
\&                                                             *priv,
\&                                                          size_t len));
\&
\& void EVP_PKEY_asn1_set_set_pub_key(EVP_PKEY_ASN1_METHOD *ameth,
\&                                    int (*set_pub_key) (EVP_PKEY *pk,
\&                                                        const unsigned char *pub,
\&                                                        size_t len));
\&
\& void EVP_PKEY_asn1_set_get_priv_key(EVP_PKEY_ASN1_METHOD *ameth,
\&                                     int (*get_priv_key) (const EVP_PKEY *pk,
\&                                                          unsigned char *priv,
\&                                                          size_t *len));
\&
\& void EVP_PKEY_asn1_set_get_pub_key(EVP_PKEY_ASN1_METHOD *ameth,
\&                                    int (*get_pub_key) (const EVP_PKEY *pk,
\&                                                        unsigned char *pub,
\&                                                        size_t *len));
\&
\& const EVP_PKEY_ASN1_METHOD *EVP_PKEY_get0_asn1(const EVP_PKEY *pkey);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_ASN1_METHOD\fR is a structure which holds a set of ASN.1
conversion, printing and information methods for a specific public key
algorithm.
.PP
There are two places where the \fBEVP_PKEY_ASN1_METHOD\fR objects are
stored: one is a built-in array representing the standard methods for
different algorithms, and the other one is a stack of user-defined
application-specific methods, which can be manipulated by using
\&\fBEVP_PKEY_asn1_add0\fR\|(3).
.SS Methods
.IX Subsection "Methods"
The methods are the underlying implementations of a particular public
key algorithm present by the \fBEVP_PKEY\fR object.
.PP
.Vb 5
\& int (*pub_decode) (EVP_PKEY *pk, X509_PUBKEY *pub);
\& int (*pub_encode) (X509_PUBKEY *pub, const EVP_PKEY *pk);
\& int (*pub_cmp) (const EVP_PKEY *a, const EVP_PKEY *b);
\& int (*pub_print) (BIO *out, const EVP_PKEY *pkey, int indent,
\&                   ASN1_PCTX *pctx);
.Ve
.PP
The \fBpub_decode()\fR and \fBpub_encode()\fR methods are called to decode /
encode \fBX509_PUBKEY\fR ASN.1 parameters to / from \fBpk\fR.
They MUST return 0 on error, 1 on success.
They're called by \fBX509_PUBKEY_get0\fR\|(3) and \fBX509_PUBKEY_set\fR\|(3).
.PP
The \fBpub_cmp()\fR method is called when two public keys are to be
compared.
It MUST return 1 when the keys are equal, 0 otherwise.
It's called by \fBEVP_PKEY_cmp\fR\|(3).
.PP
The \fBpub_print()\fR method is called to print a public key in humanly
readable text to \fBout\fR, indented \fBindent\fR spaces.
It MUST return 0 on error, 1 on success.
It's called by \fBEVP_PKEY_print_public\fR\|(3).
.PP
.Vb 4
\& int (*priv_decode) (EVP_PKEY *pk, const PKCS8_PRIV_KEY_INFO *p8inf);
\& int (*priv_encode) (PKCS8_PRIV_KEY_INFO *p8, const EVP_PKEY *pk);
\& int (*priv_print) (BIO *out, const EVP_PKEY *pkey, int indent,
\&                    ASN1_PCTX *pctx);
.Ve
.PP
The \fBpriv_decode()\fR and \fBpriv_encode()\fR methods are called to decode /
encode \fBPKCS8_PRIV_KEY_INFO\fR form private key to / from \fBpk\fR.
They MUST return 0 on error, 1 on success.
They're called by \fBEVP_PKCS82PKEY\fR\|(3) and \fBEVP_PKEY2PKCS8\fR\|(3).
.PP
The \fBpriv_print()\fR method is called to print a private key in humanly
readable text to \fBout\fR, indented \fBindent\fR spaces.
It MUST return 0 on error, 1 on success.
It's called by \fBEVP_PKEY_print_private\fR\|(3).
.PP
.Vb 3
\& int (*pkey_size) (const EVP_PKEY *pk);
\& int (*pkey_bits) (const EVP_PKEY *pk);
\& int (*pkey_security_bits) (const EVP_PKEY *pk);
.Ve
.PP
The \fBpkey_size()\fR method returns the key size in bytes.
It's called by \fBEVP_PKEY_size\fR\|(3).
.PP
The \fBpkey_bits()\fR method returns the key size in bits.
It's called by \fBEVP_PKEY_bits\fR\|(3).
.PP
.Vb 8
\& int (*param_decode) (EVP_PKEY *pkey,
\&                      const unsigned char **pder, int derlen);
\& int (*param_encode) (const EVP_PKEY *pkey, unsigned char **pder);
\& int (*param_missing) (const EVP_PKEY *pk);
\& int (*param_copy) (EVP_PKEY *to, const EVP_PKEY *from);
\& int (*param_cmp) (const EVP_PKEY *a, const EVP_PKEY *b);
\& int (*param_print) (BIO *out, const EVP_PKEY *pkey, int indent,
\&                     ASN1_PCTX *pctx);
.Ve
.PP
The \fBparam_decode()\fR and \fBparam_encode()\fR methods are called to decode /
encode DER formatted parameters to / from \fBpk\fR.
They MUST return 0 on error, 1 on success.
They're called by \fBPEM_read_bio_Parameters\fR\|(3) and the \fBfile:\fR
\&\fBOSSL_STORE_LOADER\fR\|(3).
.PP
The \fBparam_missing()\fR method returns 0 if a key parameter is missing,
otherwise 1.
It's called by \fBEVP_PKEY_missing_parameters\fR\|(3).
.PP
The \fBparam_copy()\fR method copies key parameters from \fBfrom\fR to \fBto\fR.
It MUST return 0 on error, 1 on success.
It's called by \fBEVP_PKEY_copy_parameters\fR\|(3).
.PP
The \fBparam_cmp()\fR method compares the parameters of keys \fBa\fR and \fBb\fR.
It MUST return 1 when the keys are equal, 0 when not equal, or a
negative number on error.
It's called by \fBEVP_PKEY_cmp_parameters\fR\|(3).
.PP
The \fBparam_print()\fR method prints the private key parameters in humanly
readable text to \fBout\fR, indented \fBindent\fR spaces.
It MUST return 0 on error, 1 on success.
It's called by \fBEVP_PKEY_print_params\fR\|(3).
.PP
.Vb 3
\& int (*sig_print) (BIO *out,
\&                   const X509_ALGOR *sigalg, const ASN1_STRING *sig,
\&                   int indent, ASN1_PCTX *pctx);
.Ve
.PP
The \fBsig_print()\fR method prints a signature in humanly readable text to
\&\fBout\fR, indented \fBindent\fR spaces.
\&\fBsigalg\fR contains the exact signature algorithm.
If the signature in \fBsig\fR doesn't correspond to what this method
expects, \fBX509_signature_dump()\fR must be used as a last resort.
It MUST return 0 on error, 1 on success.
It's called by \fBX509_signature_print\fR\|(3).
.PP
.Vb 1
\& void (*pkey_free) (EVP_PKEY *pkey);
.Ve
.PP
The \fBpkey_free()\fR method helps freeing the internals of \fBpkey\fR.
It's called by \fBEVP_PKEY_free\fR\|(3), \fBEVP_PKEY_set_type\fR\|(3),
\&\fBEVP_PKEY_set_type_str\fR\|(3), and \fBEVP_PKEY_assign\fR\|(3).
.PP
.Vb 1
\& int (*pkey_ctrl) (EVP_PKEY *pkey, int op, long arg1, void *arg2);
.Ve
.PP
The \fBpkey_ctrl()\fR method adds extra algorithm specific control.
It's called by \fBEVP_PKEY_get_default_digest_nid\fR\|(3),
\&\fBEVP_PKEY_set1_tls_encodedpoint\fR\|(3),
\&\fBEVP_PKEY_get1_tls_encodedpoint\fR\|(3), \fBPKCS7_SIGNER_INFO_set\fR\|(3),
\&\fBPKCS7_RECIP_INFO_set\fR\|(3), ...
.PP
.Vb 3
\& int (*old_priv_decode) (EVP_PKEY *pkey,
\&                         const unsigned char **pder, int derlen);
\& int (*old_priv_encode) (const EVP_PKEY *pkey, unsigned char **pder);
.Ve
.PP
The \fBold_priv_decode()\fR and \fBold_priv_encode()\fR methods decode / encode
they private key \fBpkey\fR from / to a DER formatted array.
These are exclusively used to help decoding / encoding older (pre
PKCS#8) PEM formatted encrypted private keys.
\&\fBold_priv_decode()\fR MUST return 0 on error, 1 on success.
\&\fBold_priv_encode()\fR MUST the return same kind of values as
\&\fBi2d_PrivateKey()\fR.
They're called by \fBd2i_PrivateKey\fR\|(3) and \fBi2d_PrivateKey\fR\|(3).
.PP
.Vb 5
\& int (*item_verify) (EVP_MD_CTX *ctx, const ASN1_ITEM *it, void *asn,
\&                     X509_ALGOR *a, ASN1_BIT_STRING *sig, EVP_PKEY *pkey);
\& int (*item_sign) (EVP_MD_CTX *ctx, const ASN1_ITEM *it, void *asn,
\&                   X509_ALGOR *alg1, X509_ALGOR *alg2,
\&                   ASN1_BIT_STRING *sig);
.Ve
.PP
The \fBitem_sign()\fR and  \fBitem_verify()\fR methods make it possible to have
algorithm specific signatures and verification of them.
.PP
\&\fBitem_sign()\fR MUST return one of:
.IP <=0 4
.IX Item "<=0"
error
.IP 1 4
.IX Item "1"
\&\fBitem_sign()\fR did everything, OpenSSL internals just needs to pass the
signature length back.
.IP 2 4
.IX Item "2"
\&\fBitem_sign()\fR did nothing, OpenSSL internal standard routines are
expected to continue with the default signature production.
.IP 3 4
.IX Item "3"
\&\fBitem_sign()\fR set the algorithm identifier \fBalgor1\fR and \fBalgor2\fR,
OpenSSL internals should just sign using those algorithms.
.PP
\&\fBitem_verify()\fR MUST return one of:
.IP <=0 4
.IX Item "<=0"
error
.IP 1 4
.IX Item "1"
\&\fBitem_sign()\fR did everything, OpenSSL internals just needs to pass the
signature length back.
.IP 2 4
.IX Item "2"
\&\fBitem_sign()\fR did nothing, OpenSSL internal standard routines are
expected to continue with the default signature production.
.PP
\&\fBitem_verify()\fR and \fBitem_sign()\fR are called by \fBASN1_item_verify\fR\|(3) and
\&\fBASN1_item_sign\fR\|(3), and by extension, \fBX509_verify\fR\|(3),
\&\fBX509_REQ_verify\fR\|(3), \fBX509_sign\fR\|(3), \fBX509_REQ_sign\fR\|(3), ...
.PP
.Vb 2
\& int (*siginf_set) (X509_SIG_INFO *siginf, const X509_ALGOR *alg,
\&                    const ASN1_STRING *sig);
.Ve
.PP
The \fBsiginf_set()\fR method is used to set custom \fBX509_SIG_INFO\fR
parameters.
It MUST return 0 on error, or 1 on success.
It's called as part of \fBX509_check_purpose\fR\|(3), \fBX509_check_ca\fR\|(3)
and \fBX509_check_issued\fR\|(3).
.PP
.Vb 3
\& int (*pkey_check) (const EVP_PKEY *pk);
\& int (*pkey_public_check) (const EVP_PKEY *pk);
\& int (*pkey_param_check) (const EVP_PKEY *pk);
.Ve
.PP
The \fBpkey_check()\fR, \fBpkey_public_check()\fR and \fBpkey_param_check()\fR methods are used
to check the validity of \fBpk\fR for key-pair, public component and parameters,
respectively.
They MUST return 0 for an invalid key, or 1 for a valid key.
They are called by \fBEVP_PKEY_check\fR\|(3), \fBEVP_PKEY_public_check\fR\|(3) and
\&\fBEVP_PKEY_param_check\fR\|(3) respectively.
.PP
.Vb 2
\& int (*set_priv_key) (EVP_PKEY *pk, const unsigned char *priv, size_t len);
\& int (*set_pub_key) (EVP_PKEY *pk, const unsigned char *pub, size_t len);
.Ve
.PP
The \fBset_priv_key()\fR and \fBset_pub_key()\fR methods are used to set the raw private and
public key data for an EVP_PKEY. They MUST return 0 on error, or 1 on success.
They are called by \fBEVP_PKEY_new_raw_private_key\fR\|(3), and
\&\fBEVP_PKEY_new_raw_public_key\fR\|(3) respectively.
.SS Functions
.IX Subsection "Functions"
\&\fBEVP_PKEY_asn1_new()\fR creates and returns a new \fBEVP_PKEY_ASN1_METHOD\fR
object, and associates the given \fBid\fR, \fBflags\fR, \fBpem_str\fR and
\&\fBinfo\fR.
\&\fBid\fR is a NID, \fBpem_str\fR is the PEM type string, \fBinfo\fR is a
descriptive string.
The following \fBflags\fR are supported:
.PP
.Vb 1
\& ASN1_PKEY_SIGPARAM_NULL
.Ve
.PP
If \fBASN1_PKEY_SIGPARAM_NULL\fR is set, then the signature algorithm
parameters are given the type \fBV_ASN1_NULL\fR by default, otherwise
they will be given the type \fBV_ASN1_UNDEF\fR (i.e. the parameter is
omitted).
See \fBX509_ALGOR_set0\fR\|(3) for more information.
.PP
\&\fBEVP_PKEY_asn1_copy()\fR copies an \fBEVP_PKEY_ASN1_METHOD\fR object from
\&\fBsrc\fR to \fBdst\fR.
This function is not thread safe, it's recommended to only use this
when initializing the application.
.PP
\&\fBEVP_PKEY_asn1_free()\fR frees an existing \fBEVP_PKEY_ASN1_METHOD\fR pointed
by \fBameth\fR.
.PP
\&\fBEVP_PKEY_asn1_add0()\fR adds \fBameth\fR to the user defined stack of
methods unless another \fBEVP_PKEY_ASN1_METHOD\fR with the same NID is
already there.
This function is not thread safe, it's recommended to only use this
when initializing the application.
.PP
\&\fBEVP_PKEY_asn1_add_alias()\fR creates an alias with the NID \fBto\fR for the
\&\fBEVP_PKEY_ASN1_METHOD\fR with NID \fBfrom\fR unless another
\&\fBEVP_PKEY_ASN1_METHOD\fR with the same NID is already added.
This function is not thread safe, it's recommended to only use this
when initializing the application.
.PP
\&\fBEVP_PKEY_asn1_set_public()\fR, \fBEVP_PKEY_asn1_set_private()\fR,
\&\fBEVP_PKEY_asn1_set_param()\fR, \fBEVP_PKEY_asn1_set_free()\fR,
\&\fBEVP_PKEY_asn1_set_ctrl()\fR, \fBEVP_PKEY_asn1_set_item()\fR,
\&\fBEVP_PKEY_asn1_set_siginf()\fR, \fBEVP_PKEY_asn1_set_check()\fR,
\&\fBEVP_PKEY_asn1_set_public_check()\fR, \fBEVP_PKEY_asn1_set_param_check()\fR,
\&\fBEVP_PKEY_asn1_set_security_bits()\fR, \fBEVP_PKEY_asn1_set_set_priv_key()\fR,
\&\fBEVP_PKEY_asn1_set_set_pub_key()\fR, \fBEVP_PKEY_asn1_set_get_priv_key()\fR and
\&\fBEVP_PKEY_asn1_set_get_pub_key()\fR set the diverse methods of the given
\&\fBEVP_PKEY_ASN1_METHOD\fR object.
.PP
\&\fBEVP_PKEY_get0_asn1()\fR finds the \fBEVP_PKEY_ASN1_METHOD\fR associated
with the key \fBpkey\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_asn1_new()\fR returns NULL on error, or a pointer to an
\&\fBEVP_PKEY_ASN1_METHOD\fR object otherwise.
.PP
\&\fBEVP_PKEY_asn1_add0()\fR and \fBEVP_PKEY_asn1_add_alias()\fR return 0 on error,
or 1 on success.
.PP
\&\fBEVP_PKEY_get0_asn1()\fR returns NULL on error, or a pointer to a constant
\&\fBEVP_PKEY_ASN1_METHOD\fR object otherwise.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
