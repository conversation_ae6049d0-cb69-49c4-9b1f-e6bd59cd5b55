.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_STORE_NEW 3"
.TH X509_STORE_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_STORE_new, X509_STORE_up_ref, X509_STORE_free, X509_STORE_lock,
X509_STORE_unlock \- X509_STORE allocation, freeing and locking functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509_vfy.h>
\&
\& X509_STORE *X509_STORE_new(void);
\& void X509_STORE_free(X509_STORE *v);
\& int X509_STORE_lock(X509_STORE *v);
\& int X509_STORE_unlock(X509_STORE *v);
\& int X509_STORE_up_ref(X509_STORE *v);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBX509_STORE_new()\fR function returns a new X509_STORE.
.PP
\&\fBX509_STORE_up_ref()\fR increments the reference count associated with the
X509_STORE object.
.PP
\&\fBX509_STORE_lock()\fR locks the store from modification by other threads,
\&\fBX509_STORE_unlock()\fR unlocks it.
.PP
\&\fBX509_STORE_free()\fR frees up a single X509_STORE object.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_STORE_new()\fR returns a newly created X509_STORE or NULL if the call fails.
.PP
\&\fBX509_STORE_up_ref()\fR, \fBX509_STORE_lock()\fR and \fBX509_STORE_unlock()\fR return
1 for success and 0 for failure.
.PP
\&\fBX509_STORE_free()\fR does not return values.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_STORE_set_verify_cb_func\fR\|(3)
\&\fBX509_STORE_get0_param\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBX509_STORE_up_ref()\fR, \fBX509_STORE_lock()\fR and \fBX509_STORE_unlock()\fR
functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
