.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_SHARED_SIGALGS 3"
.TH SSL_GET_SHARED_SIGALGS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_shared_sigalgs, SSL_get_sigalgs \- get supported signature algorithms
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_get_shared_sigalgs(SSL *s, int idx,
\&                            int *psign, int *phash, int *psignhash,
\&                            unsigned char *rsig, unsigned char *rhash);
\&
\& int SSL_get_sigalgs(SSL *s, int idx,
\&                     int *psign, int *phash, int *psignhash,
\&                     unsigned char *rsig, unsigned char *rhash);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_shared_sigalgs()\fR returns information about the shared signature
algorithms supported by peer \fBs\fR. The parameter \fBidx\fR indicates the index
of the shared signature algorithm to return starting from zero. The signature
algorithm NID is written to \fB*psign\fR, the hash NID to \fB*phash\fR and the
sign and hash NID to \fB*psignhash\fR. The raw signature and hash values
are written to \fB*rsig\fR and \fB*rhash\fR.
.PP
\&\fBSSL_get_sigalgs()\fR is similar to \fBSSL_get_shared_sigalgs()\fR except it returns
information about all signature algorithms supported by \fBs\fR in the order
they were sent by the peer.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_get_shared_sigalgs()\fR and \fBSSL_get_sigalgs()\fR return the number of
signature algorithms or \fB0\fR if the \fBidx\fR parameter is out of range.
.SH NOTES
.IX Header "NOTES"
These functions are typically called for debugging purposes (to report
the peer's preferences) or where an application wants finer control over
certificate selection. Most applications will rely on internal handling
and will not need to call them.
.PP
If an application is only interested in the highest preference shared
signature algorithm it can just set \fBidx\fR to zero.
.PP
Any or all of the parameters \fBpsign\fR, \fBphash\fR, \fBpsignhash\fR, \fBrsig\fR or
\&\fBrhash\fR can be set to \fBNULL\fR if the value is not required. By setting
them all to \fBNULL\fR and setting \fBidx\fR to zero the total number of
signature algorithms can be determined: which can be zero.
.PP
These functions must be called after the peer has sent a list of supported
signature algorithms: after a client hello (for servers) or a certificate
request (for clients). They can (for example) be called in the certificate
callback.
.PP
Only TLS 1.2, TLS 1.3 and DTLS 1.2 currently support signature algorithms.
If these
functions are called on an earlier version of TLS or DTLS zero is returned.
.PP
The shared signature algorithms returned by \fBSSL_get_shared_sigalgs()\fR are
ordered according to configuration and peer preferences.
.PP
The raw values correspond to the on the wire form as defined by RFC5246 et al.
The NIDs are OpenSSL equivalents. For example if the peer sent \fBsha256\fR\|(4) and
\&\fBrsa\fR\|(1) then \fB*rhash\fR would be 4, \fB*rsign\fR 1, \fB*phash\fR NID_sha256, \fB*psig\fR
NID_rsaEncryption and \fB*psighash\fR NID_sha256WithRSAEncryption.
.PP
If a signature algorithm is not recognised the corresponding NIDs
will be set to \fBNID_undef\fR. This may be because the value is not supported,
is not an appropriate combination (for example MD5 and DSA) or the
signature algorithm does not use a hash (for example Ed25519).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CTX_set_cert_cb\fR\|(3),
\&\fBssl\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
