.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_DRBG_RESEED 3"
.TH RAND_DRBG_RESEED 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_DRBG_reseed,
RAND_DRBG_set_reseed_interval,
RAND_DRBG_set_reseed_time_interval,
RAND_DRBG_set_reseed_defaults
\&\- reseed a RAND_DRBG instance
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand_drbg.h>
\&
\& int RAND_DRBG_reseed(RAND_DRBG *drbg,
\&                      const unsigned char *adin, size_t adinlen,
\&                      int prediction_resistance);
\&
\& int RAND_DRBG_set_reseed_interval(RAND_DRBG *drbg,
\&                                   unsigned int interval);
\&
\& int RAND_DRBG_set_reseed_time_interval(RAND_DRBG *drbg,
\&                                        time_t interval);
\&
\& int RAND_DRBG_set_reseed_defaults(
\&                                   unsigned int master_reseed_interval,
\&                                   unsigned int slave_reseed_interval,
\&                                   time_t master_reseed_time_interval,
\&                                   time_t slave_reseed_time_interval
\&                                   );
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBRAND_DRBG_reseed()\fR
reseeds the given \fBdrbg\fR, obtaining entropy input from its entropy source
and mixing in the specified additional data provided in the buffer \fBadin\fR
of length \fBadinlen\fR.
The additional data can be omitted by setting \fBadin\fR to NULL and \fBadinlen\fR
to 0.
An immediate reseeding from a live entropy source can be requested by setting
the \fBprediction_resistance\fR flag to 1.
This feature is not implemented yet, so reseeding with prediction resistance
requested will always fail.
.PP
\&\fBRAND_DRBG_set_reseed_interval()\fR
sets the reseed interval of the \fBdrbg\fR, which is the maximum allowed number
of generate requests between consecutive reseedings.
If \fBinterval\fR > 0, then the \fBdrbg\fR will reseed automatically whenever the
number of generate requests since its last seeding exceeds the given reseed
interval.
If \fBinterval\fR == 0, then this feature is disabled.
.PP
\&\fBRAND_DRBG_set_reseed_time_interval()\fR
sets the reseed time interval of the \fBdrbg\fR, which is the maximum allowed
number of seconds between consecutive reseedings.
If \fBinterval\fR > 0, then the \fBdrbg\fR will reseed automatically whenever the
elapsed time since its last reseeding exceeds the given reseed time interval.
If \fBinterval\fR == 0, then this feature is disabled.
.PP
\&\fBRAND_DRBG_set_reseed_defaults()\fR sets the default values for the reseed interval
(\fBmaster_reseed_interval\fR and \fBslave_reseed_interval\fR)
and the reseed time interval
(\fBmaster_reseed_time_interval\fR and \fBslave_reseed_tme_interval\fR)
of DRBG instances.
The default values are set independently for master DRBG instances (which don't
have a parent) and slave DRBG instances (which are chained to a parent DRBG).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_DRBG_reseed()\fR,
\&\fBRAND_DRBG_set_reseed_interval()\fR, and
\&\fBRAND_DRBG_set_reseed_time_interval()\fR,
return 1 on success, 0 on failure.
.SH NOTES
.IX Header "NOTES"
The default OpenSSL random generator is already set up for automatic reseeding,
so in general it is not necessary to reseed it explicitly, or to modify
its reseeding thresholds.
.PP
Normally, the entropy input for seeding a DRBG is either obtained from a
trusted os entropy source or from a parent DRBG instance, which was seeded
(directly or indirectly) from a trusted os entropy source.
In exceptional cases it is possible to replace the reseeding mechanism entirely
by providing application defined callbacks using \fBRAND_DRBG_set_callbacks()\fR.
.PP
The reseeding default values are applied only during creation of a DRBG instance.
To ensure that they are applied to the global and thread-local DRBG instances
(<master>, resp. <public> and <private>), it is necessary to call
\&\fBRAND_DRBG_set_reseed_defaults()\fR before creating any thread and before calling any
 cryptographic routines that obtain random data directly or indirectly.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND_DRBG_generate\fR\|(3),
\&\fBRAND_DRBG_bytes\fR\|(3),
\&\fBRAND_DRBG_set_callbacks\fR\|(3).
\&\fBRAND_DRBG\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The RAND_DRBG functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
