.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_LIBRARY_INIT 3"
.TH SSL_LIBRARY_INIT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_library_init, OpenSSL_add_ssl_algorithms
\&\- initialize SSL library by registering algorithms
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_library_init(void);
\&
\& int OpenSSL_add_ssl_algorithms(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_library_init()\fR registers the available SSL/TLS ciphers and digests.
.PP
\&\fBOpenSSL_add_ssl_algorithms()\fR is a synonym for \fBSSL_library_init()\fR and is
implemented as a macro.
.SH NOTES
.IX Header "NOTES"
\&\fBSSL_library_init()\fR must be called before any other action takes place.
\&\fBSSL_library_init()\fR is not reentrant.
.SH WARNINGS
.IX Header "WARNINGS"
\&\fBSSL_library_init()\fR adds ciphers and digests used directly and indirectly by
SSL/TLS.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_library_init()\fR always returns "1", so it is safe to discard the return
value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBRAND_add\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_library_init()\fR and \fBOpenSSL_add_ssl_algorithms()\fR functions were
deprecated in OpenSSL 1.1.0 by \fBOPENSSL_init_ssl()\fR.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
