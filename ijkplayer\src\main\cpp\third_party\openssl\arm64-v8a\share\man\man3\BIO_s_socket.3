.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_S_SOCKET 3"
.TH BIO_S_SOCKET 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_s_socket, BIO_new_socket \- socket BIO
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& const BIO_METHOD *BIO_s_socket(void);
\&
\& BIO *BIO_new_socket(int sock, int close_flag);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_s_socket()\fR returns the socket BIO method. This is a wrapper
round the platform's socket routines.
.PP
\&\fBBIO_read_ex()\fR and \fBBIO_write_ex()\fR read or write the underlying socket.
\&\fBBIO_puts()\fR is supported but \fBBIO_gets()\fR is not.
.PP
If the close flag is set then the socket is shut down and closed
when the BIO is freed.
.PP
\&\fBBIO_new_socket()\fR returns a socket BIO using \fBsock\fR and \fBclose_flag\fR.
.SH NOTES
.IX Header "NOTES"
Socket BIOs also support any relevant functionality of file descriptor
BIOs.
.PP
The reason for having separate file descriptor and socket BIOs is that on some
platforms sockets are not file descriptors and use distinct I/O routines,
Windows is one such platform. Any code mixing the two will not work on
all platforms.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_s_socket()\fR returns the socket BIO method.
.PP
\&\fBBIO_new_socket()\fR returns the newly allocated BIO or NULL is an error
occurred.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
