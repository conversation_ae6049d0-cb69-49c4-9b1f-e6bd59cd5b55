.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_OPENINIT 3"
.TH EVP_OPENINIT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_OpenInit, EVP_OpenUpdate, EVP_OpenFinal \- EVP envelope decryption
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_OpenInit(EVP_CIPHER_CTX *ctx, EVP_CIPHER *type, unsigned char *ek,
\&                  int ekl, unsigned char *iv, EVP_PKEY *priv);
\& int EVP_OpenUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
\&                    int *outl, unsigned char *in, int inl);
\& int EVP_OpenFinal(EVP_CIPHER_CTX *ctx, unsigned char *out, int *outl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP envelope routines are a high-level interface to envelope
decryption. They decrypt a public key encrypted symmetric key and
then decrypt data using it.
.PP
\&\fBEVP_OpenInit()\fR initializes a cipher context \fBctx\fR for decryption
with cipher \fBtype\fR. It decrypts the encrypted symmetric key of length
\&\fBekl\fR bytes passed in the \fBek\fR parameter using the private key \fBpriv\fR.
The IV is supplied in the \fBiv\fR parameter.
.PP
\&\fBEVP_OpenUpdate()\fR and \fBEVP_OpenFinal()\fR have exactly the same properties
as the \fBEVP_DecryptUpdate()\fR and \fBEVP_DecryptFinal()\fR routines, as
documented on the \fBEVP_EncryptInit\fR\|(3) manual
page.
.SH NOTES
.IX Header "NOTES"
It is possible to call \fBEVP_OpenInit()\fR twice in the same way as
\&\fBEVP_DecryptInit()\fR. The first call should have \fBpriv\fR set to NULL
and (after setting any cipher parameters) it should be called again
with \fBtype\fR set to NULL.
.PP
If the cipher passed in the \fBtype\fR parameter is a variable length
cipher then the key length will be set to the value of the recovered
key length. If the cipher is a fixed length cipher then the recovered
key length must match the fixed cipher length.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_OpenInit()\fR returns 0 on error or a non zero integer (actually the
recovered secret key size) if successful.
.PP
\&\fBEVP_OpenUpdate()\fR returns 1 for success or 0 for failure.
.PP
\&\fBEVP_OpenFinal()\fR returns 0 if the decrypt failed or 1 for success.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7), \fBRAND_bytes\fR\|(3),
\&\fBEVP_EncryptInit\fR\|(3),
\&\fBEVP_SealInit\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
