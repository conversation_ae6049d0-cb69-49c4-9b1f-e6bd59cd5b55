.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_RSTATE_STRING 3"
.TH SSL_RSTATE_STRING 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_rstate_string, SSL_rstate_string_long \- get textual description of state of an SSL object during read operation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& const char *SSL_rstate_string(SSL *ssl);
\& const char *SSL_rstate_string_long(SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_rstate_string()\fR returns a 2 letter string indicating the current read state
of the SSL object \fBssl\fR.
.PP
\&\fBSSL_rstate_string_long()\fR returns a string indicating the current read state of
the SSL object \fBssl\fR.
.SH NOTES
.IX Header "NOTES"
When performing a read operation, the SSL/TLS engine must parse the record,
consisting of header and body. When working in a blocking environment,
SSL_rstate_string[_long]() should always return "RD"/"read done".
.PP
This function should only seldom be needed in applications.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_rstate_string()\fR and \fBSSL_rstate_string_long()\fR can return the following
values:
.IP """RH""/""read header""" 4
.IX Item """RH""/""read header"""
The header of the record is being evaluated.
.IP """RB""/""read body""" 4
.IX Item """RB""/""read body"""
The body of the record is being evaluated.
.IP """RD""/""read done""" 4
.IX Item """RD""/""read done"""
The record has been completely processed.
.IP """unknown""/""unknown""" 4
.IX Item """unknown""/""unknown"""
The read state is unknown. This should never happen.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
