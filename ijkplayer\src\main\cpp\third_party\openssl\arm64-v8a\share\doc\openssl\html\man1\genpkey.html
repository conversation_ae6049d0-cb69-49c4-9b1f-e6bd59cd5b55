<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>genpkey</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#KEY-GENERATION-OPTIONS">KEY GENERATION OPTIONS</a>
    <ul>
      <li><a href="#RSA-Key-Generation-Options">RSA Key Generation Options</a></li>
      <li><a href="#RSA-PSS-Key-Generation-Options">RSA-PSS Key Generation Options</a></li>
      <li><a href="#EC-Key-Generation-Options">EC Key Generation Options</a></li>
    </ul>
  </li>
  <li><a href="#PARAMETER-GENERATION-OPTIONS">PARAMETER GENERATION OPTIONS</a>
    <ul>
      <li><a href="#DSA-Parameter-Generation-Options">DSA Parameter Generation Options</a></li>
      <li><a href="#DH-Parameter-Generation-Options">DH Parameter Generation Options</a></li>
      <li><a href="#EC-Parameter-Generation-Options">EC Parameter Generation Options</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-genpkey, genpkey - generate a private key</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>genpkey</b> [<b>-help</b>] [<b>-out filename</b>] [<b>-outform PEM|DER</b>] [<b>-pass arg</b>] [<b>-<i>cipher</i></b>] [<b>-engine id</b>] [<b>-paramfile file</b>] [<b>-algorithm alg</b>] [<b>-pkeyopt opt:value</b>] [<b>-genparam</b>] [<b>-text</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>genpkey</b> command generates a private key.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="out-filename"><b>-out filename</b></dt>
<dd>

<p>Output the key to the specified file. If this argument is not specified then standard output is used.</p>

</dd>
<dt id="outform-DER-PEM"><b>-outform DER|PEM</b></dt>
<dd>

<p>This specifies the output format DER or PEM. The default format is PEM.</p>

</dd>
<dt id="pass-arg"><b>-pass arg</b></dt>
<dd>

<p>The output file password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="cipher"><b>-<i>cipher</i></b></dt>
<dd>

<p>This option encrypts the private key with the supplied cipher. Any algorithm name accepted by EVP_get_cipherbyname() is acceptable such as <b>des3</b>.</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Specifying an engine (by its unique <b>id</b> string) will cause <b>genpkey</b> to attempt to obtain a functional reference to the specified engine, thus initialising it if needed. The engine will then be set as the default for all available algorithms. If used this option should precede all other options.</p>

</dd>
<dt id="algorithm-alg"><b>-algorithm alg</b></dt>
<dd>

<p>Public key algorithm to use such as RSA, DSA or DH. If used this option must precede any <b>-pkeyopt</b> options. The options <b>-paramfile</b> and <b>-algorithm</b> are mutually exclusive. Engines may add algorithms in addition to the standard built-in ones.</p>

<p>Valid built-in algorithm names for private key generation are RSA, RSA-PSS, EC, X25519, X448, ED25519 and ED448.</p>

<p>Valid built-in algorithm names for parameter generation (see the <b>-genparam</b> option) are DH, DSA and EC.</p>

<p>Note that the algorithm name X9.42 DH may be used as a synonym for the DH algorithm. These are identical and do not indicate the type of parameters that will be generated. Use the <b>dh_paramgen_type</b> option to indicate whether PKCS#3 or X9.42 DH parameters are required. See <a href="#DH-Parameter-Generation-Options">&quot;DH Parameter Generation Options&quot;</a> below for more details.</p>

</dd>
<dt id="pkeyopt-opt:value"><b>-pkeyopt opt:value</b></dt>
<dd>

<p>Set the public key algorithm option <b>opt</b> to <b>value</b>. The precise set of options supported depends on the public key algorithm used and its implementation. See <a href="#KEY-GENERATION-OPTIONS">&quot;KEY GENERATION OPTIONS&quot;</a> and <a href="#PARAMETER-GENERATION-OPTIONS">&quot;PARAMETER GENERATION OPTIONS&quot;</a> below for more details.</p>

</dd>
<dt id="genparam"><b>-genparam</b></dt>
<dd>

<p>Generate a set of parameters instead of a private key. If used this option must precede any <b>-algorithm</b>, <b>-paramfile</b> or <b>-pkeyopt</b> options.</p>

</dd>
<dt id="paramfile-filename"><b>-paramfile filename</b></dt>
<dd>

<p>Some public key algorithms generate a private key based on a set of parameters. They can be supplied using this option. If this option is used the public key algorithm used is determined by the parameters. If used this option must precede any <b>-pkeyopt</b> options. The options <b>-paramfile</b> and <b>-algorithm</b> are mutually exclusive.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>Print an (unencrypted) text representation of private and public keys and parameters along with the PEM or DER structure.</p>

</dd>
</dl>

<h1 id="KEY-GENERATION-OPTIONS">KEY GENERATION OPTIONS</h1>

<p>The options supported by each algorithm and indeed each implementation of an algorithm can vary. The options for the OpenSSL implementations are detailed below. There are no key generation options defined for the X25519, X448, ED25519 or ED448 algorithms.</p>

<h2 id="RSA-Key-Generation-Options">RSA Key Generation Options</h2>

<dl>

<dt id="rsa_keygen_bits:numbits"><b>rsa_keygen_bits:numbits</b></dt>
<dd>

<p>The number of bits in the generated key. If not specified 2048 is used.</p>

</dd>
<dt id="rsa_keygen_primes:numprimes"><b>rsa_keygen_primes:numprimes</b></dt>
<dd>

<p>The number of primes in the generated key. If not specified 2 is used.</p>

</dd>
<dt id="rsa_keygen_pubexp:value"><b>rsa_keygen_pubexp:value</b></dt>
<dd>

<p>The RSA public exponent value. This can be a large decimal or hexadecimal value if preceded by <b>0x</b>. Default value is 65537.</p>

</dd>
</dl>

<h2 id="RSA-PSS-Key-Generation-Options">RSA-PSS Key Generation Options</h2>

<p>Note: by default an <b>RSA-PSS</b> key has no parameter restrictions.</p>

<dl>

<dt id="rsa_keygen_bits:numbits-rsa_keygen_primes:numprimes-rsa_keygen_pubexp:value"><b>rsa_keygen_bits:numbits</b>, <b>rsa_keygen_primes:numprimes</b>, <b>rsa_keygen_pubexp:value</b></dt>
<dd>

<p>These options have the same meaning as the <b>RSA</b> algorithm.</p>

</dd>
<dt id="rsa_pss_keygen_md:digest"><b>rsa_pss_keygen_md:digest</b></dt>
<dd>

<p>If set the key is restricted and can only use <b>digest</b> for signing.</p>

</dd>
<dt id="rsa_pss_keygen_mgf1_md:digest"><b>rsa_pss_keygen_mgf1_md:digest</b></dt>
<dd>

<p>If set the key is restricted and can only use <b>digest</b> as it&#39;s MGF1 parameter.</p>

</dd>
<dt id="rsa_pss_keygen_saltlen:len"><b>rsa_pss_keygen_saltlen:len</b></dt>
<dd>

<p>If set the key is restricted and <b>len</b> specifies the minimum salt length.</p>

</dd>
</dl>

<h2 id="EC-Key-Generation-Options">EC Key Generation Options</h2>

<p>The EC key generation options can also be used for parameter generation.</p>

<dl>

<dt id="ec_paramgen_curve:curve"><b>ec_paramgen_curve:curve</b></dt>
<dd>

<p>The EC curve to use. OpenSSL supports NIST curve names such as &quot;P-256&quot;.</p>

</dd>
<dt id="ec_param_enc:encoding"><b>ec_param_enc:encoding</b></dt>
<dd>

<p>The encoding to use for parameters. The &quot;encoding&quot; parameter must be either &quot;named_curve&quot; or &quot;explicit&quot;. The default value is &quot;named_curve&quot;.</p>

</dd>
</dl>

<h1 id="PARAMETER-GENERATION-OPTIONS">PARAMETER GENERATION OPTIONS</h1>

<p>The options supported by each algorithm and indeed each implementation of an algorithm can vary. The options for the OpenSSL implementations are detailed below.</p>

<h2 id="DSA-Parameter-Generation-Options">DSA Parameter Generation Options</h2>

<dl>

<dt id="dsa_paramgen_bits:numbits"><b>dsa_paramgen_bits:numbits</b></dt>
<dd>

<p>The number of bits in the generated prime. If not specified 2048 is used.</p>

</dd>
<dt id="dsa_paramgen_q_bits:numbits"><b>dsa_paramgen_q_bits:numbits</b></dt>
<dd>

<p>The number of bits in the q parameter. Must be one of 160, 224 or 256. If not specified 224 is used.</p>

</dd>
<dt id="dsa_paramgen_md:digest"><b>dsa_paramgen_md:digest</b></dt>
<dd>

<p>The digest to use during parameter generation. Must be one of <b>sha1</b>, <b>sha224</b> or <b>sha256</b>. If set, then the number of bits in <b>q</b> will match the output size of the specified digest and the <b>dsa_paramgen_q_bits</b> parameter will be ignored. If not set, then a digest will be used that gives an output matching the number of bits in <b>q</b>, i.e. <b>sha1</b> if q length is 160, <b>sha224</b> if it 224 or <b>sha256</b> if it is 256.</p>

</dd>
</dl>

<h2 id="DH-Parameter-Generation-Options">DH Parameter Generation Options</h2>

<dl>

<dt id="dh_paramgen_prime_len:numbits"><b>dh_paramgen_prime_len:numbits</b></dt>
<dd>

<p>The number of bits in the prime parameter <b>p</b>. The default is 2048.</p>

</dd>
<dt id="dh_paramgen_subprime_len:numbits"><b>dh_paramgen_subprime_len:numbits</b></dt>
<dd>

<p>The number of bits in the sub prime parameter <b>q</b>. The default is 256 if the prime is at least 2048 bits long or 160 otherwise. Only relevant if used in conjunction with the <b>dh_paramgen_type</b> option to generate X9.42 DH parameters.</p>

</dd>
<dt id="dh_paramgen_generator:value"><b>dh_paramgen_generator:value</b></dt>
<dd>

<p>The value to use for the generator <b>g</b>. The default is 2.</p>

</dd>
<dt id="dh_paramgen_type:value"><b>dh_paramgen_type:value</b></dt>
<dd>

<p>The type of DH parameters to generate. Use 0 for PKCS#3 DH and 1 for X9.42 DH. The default is 0.</p>

</dd>
<dt id="dh_rfc5114:num"><b>dh_rfc5114:num</b></dt>
<dd>

<p>If this option is set, then the appropriate RFC5114 parameters are used instead of generating new parameters. The value <b>num</b> can take the values 1, 2 or 3 corresponding to RFC5114 DH parameters consisting of 1024 bit group with 160 bit subgroup, 2048 bit group with 224 bit subgroup and 2048 bit group with 256 bit subgroup as mentioned in RFC5114 sections 2.1, 2.2 and 2.3 respectively. If present this overrides all other DH parameter options.</p>

</dd>
</dl>

<h2 id="EC-Parameter-Generation-Options">EC Parameter Generation Options</h2>

<p>The EC parameter generation options are the same as for key generation. See <a href="#EC-Key-Generation-Options">&quot;EC Key Generation Options&quot;</a> above.</p>

<h1 id="NOTES">NOTES</h1>

<p>The use of the genpkey program is encouraged over the algorithm specific utilities because additional algorithm options and ENGINE provided algorithms can be used.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Generate an RSA private key using default parameters:</p>

<pre><code>openssl genpkey -algorithm RSA -out key.pem</code></pre>

<p>Encrypt output private key using 128 bit AES and the passphrase &quot;hello&quot;:</p>

<pre><code>openssl genpkey -algorithm RSA -out key.pem -aes-128-cbc -pass pass:hello</code></pre>

<p>Generate a 2048 bit RSA key using 3 as the public exponent:</p>

<pre><code>openssl genpkey -algorithm RSA -out key.pem \
    -pkeyopt rsa_keygen_bits:2048 -pkeyopt rsa_keygen_pubexp:3</code></pre>

<p>Generate 2048 bit DSA parameters:</p>

<pre><code>openssl genpkey -genparam -algorithm DSA -out dsap.pem \
    -pkeyopt dsa_paramgen_bits:2048</code></pre>

<p>Generate DSA key from parameters:</p>

<pre><code>openssl genpkey -paramfile dsap.pem -out dsakey.pem</code></pre>

<p>Generate 2048 bit DH parameters:</p>

<pre><code>openssl genpkey -genparam -algorithm DH -out dhp.pem \
    -pkeyopt dh_paramgen_prime_len:2048</code></pre>

<p>Generate 2048 bit X9.42 DH parameters:</p>

<pre><code>openssl genpkey -genparam -algorithm DH -out dhpx.pem \
    -pkeyopt dh_paramgen_prime_len:2048 \
    -pkeyopt dh_paramgen_type:1</code></pre>

<p>Output RFC5114 2048 bit DH parameters with 224 bit subgroup:</p>

<pre><code>openssl genpkey -genparam -algorithm DH -out dhp.pem -pkeyopt dh_rfc5114:2</code></pre>

<p>Generate DH key from parameters:</p>

<pre><code>openssl genpkey -paramfile dhp.pem -out dhkey.pem</code></pre>

<p>Generate EC parameters:</p>

<pre><code>openssl genpkey -genparam -algorithm EC -out ecp.pem \
       -pkeyopt ec_paramgen_curve:secp384r1 \
       -pkeyopt ec_param_enc:named_curve</code></pre>

<p>Generate EC key from parameters:</p>

<pre><code>openssl genpkey -paramfile ecp.pem -out eckey.pem</code></pre>

<p>Generate EC key directly:</p>

<pre><code>openssl genpkey -algorithm EC -out eckey.pem \
       -pkeyopt ec_paramgen_curve:P-384 \
       -pkeyopt ec_param_enc:named_curve</code></pre>

<p>Generate an X25519 private key:</p>

<pre><code>openssl genpkey -algorithm X25519 -out xkey.pem</code></pre>

<p>Generate an ED448 private key:</p>

<pre><code>openssl genpkey -algorithm ED448 -out xkey.pem</code></pre>

<h1 id="HISTORY">HISTORY</h1>

<p>The ability to use NIST curve names, and to generate an EC key directly, were added in OpenSSL 1.0.2. The ability to generate X25519 keys was added in OpenSSL 1.1.0. The ability to generate X448, ED25519 and ED448 keys was added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2006-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


