<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ecparam</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-ecparam, ecparam - EC parameter manipulation and generation</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl ecparam</b> [<b>-help</b>] [<b>-inform DER|PEM</b>] [<b>-outform DER|PEM</b>] [<b>-in filename</b>] [<b>-out filename</b>] [<b>-noout</b>] [<b>-text</b>] [<b>-C</b>] [<b>-check</b>] [<b>-name arg</b>] [<b>-list_curves</b>] [<b>-conv_form arg</b>] [<b>-param_enc arg</b>] [<b>-no_seed</b>] [<b>-rand file...</b>] [<b>-writerand file</b>] [<b>-genkey</b>] [<b>-engine id</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command is used to manipulate or generate EC parameter files.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="inform-DER-PEM"><b>-inform DER|PEM</b></dt>
<dd>

<p>This specifies the input format. The <b>DER</b> option uses an ASN.1 DER encoded form compatible with RFC 3279 EcpkParameters. The PEM form is the default format: it consists of the <b>DER</b> format base64 encoded with additional header and footer lines.</p>

</dd>
<dt id="outform-DER-PEM"><b>-outform DER|PEM</b></dt>
<dd>

<p>This specifies the output format, the options have the same meaning and default as the <b>-inform</b> option.</p>

</dd>
<dt id="in-filename"><b>-in filename</b></dt>
<dd>

<p>This specifies the input filename to read parameters from or standard input if this option is not specified.</p>

</dd>
<dt id="out-filename"><b>-out filename</b></dt>
<dd>

<p>This specifies the output filename parameters to. Standard output is used if this option is not present. The output filename should <b>not</b> be the same as the input filename.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>This option inhibits the output of the encoded version of the parameters.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>This option prints out the EC parameters in human readable form.</p>

</dd>
<dt id="C"><b>-C</b></dt>
<dd>

<p>This option converts the EC parameters into C code. The parameters can then be loaded by calling the get_ec_group_XXX() function.</p>

</dd>
<dt id="check"><b>-check</b></dt>
<dd>

<p>Validate the elliptic curve parameters.</p>

</dd>
<dt id="name-arg"><b>-name arg</b></dt>
<dd>

<p>Use the EC parameters with the specified &#39;short&#39; name. Use <b>-list_curves</b> to get a list of all currently implemented EC parameters.</p>

</dd>
<dt id="list_curves"><b>-list_curves</b></dt>
<dd>

<p>If this options is specified <b>ecparam</b> will print out a list of all currently implemented EC parameters names and exit.</p>

</dd>
<dt id="conv_form"><b>-conv_form</b></dt>
<dd>

<p>This specifies how the points on the elliptic curve are converted into octet strings. Possible values are: <b>compressed</b>, <b>uncompressed</b> (the default value) and <b>hybrid</b>. For more information regarding the point conversion forms please read the X9.62 standard. <b>Note</b> Due to patent issues the <b>compressed</b> option is disabled by default for binary curves and can be enabled by defining the preprocessor macro <b>OPENSSL_EC_BIN_PT_COMP</b> at compile time.</p>

</dd>
<dt id="param_enc-arg"><b>-param_enc arg</b></dt>
<dd>

<p>This specifies how the elliptic curve parameters are encoded. Possible value are: <b>named_curve</b>, i.e. the ec parameters are specified by an OID, or <b>explicit</b> where the ec parameters are explicitly given (see RFC 3279 for the definition of the EC parameters structures). The default value is <b>named_curve</b>. <b>Note</b> the <b>implicitlyCA</b> alternative, as specified in RFC 3279, is currently not implemented in OpenSSL.</p>

</dd>
<dt id="no_seed"><b>-no_seed</b></dt>
<dd>

<p>This option inhibits that the &#39;seed&#39; for the parameter generation is included in the ECParameters structure (see RFC 3279).</p>

</dd>
<dt id="genkey"><b>-genkey</b></dt>
<dd>

<p>This option will generate an EC private key using the specified parameters.</p>

</dd>
<dt id="rand-file"><b>-rand file...</b></dt>
<dd>

<p>A file or files containing random data used to seed the random number generator. Multiple files can be specified separated by an OS-dependent character. The separator is <b>;</b> for MS-Windows, <b>,</b> for OpenVMS, and <b>:</b> for all others.</p>

</dd>
<dt id="writerand-file">[<b>-writerand file</b>]</dt>
<dd>

<p>Writes random data to the specified <i>file</i> upon exit. This can be used with a subsequent <b>-rand</b> flag.</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Specifying an engine (by its unique <b>id</b> string) will cause <b>ecparam</b> to attempt to obtain a functional reference to the specified engine, thus initialising it if needed. The engine will then be set as the default for all available algorithms.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>PEM format EC parameters use the header and footer lines:</p>

<pre><code>-----BEGIN EC PARAMETERS-----
-----END EC PARAMETERS-----</code></pre>

<p>OpenSSL is currently not able to generate new groups and therefore <b>ecparam</b> can only create EC parameters from known (named) curves.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>To create EC parameters with the group &#39;prime192v1&#39;:</p>

<pre><code>openssl ecparam -out ec_param.pem -name prime192v1</code></pre>

<p>To create EC parameters with explicit parameters:</p>

<pre><code>openssl ecparam -out ec_param.pem -name prime192v1 -param_enc explicit</code></pre>

<p>To validate given EC parameters:</p>

<pre><code>openssl ecparam -in ec_param.pem -check</code></pre>

<p>To create EC parameters and a private key:</p>

<pre><code>openssl ecparam -out ec_key.pem -name prime192v1 -genkey</code></pre>

<p>To change the point encoding to &#39;compressed&#39;:</p>

<pre><code>openssl ecparam -in ec_in.pem -out ec_out.pem -conv_form compressed</code></pre>

<p>To print out the EC parameters to standard output:</p>

<pre><code>openssl ecparam -in ec_param.pem -noout -text</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/ec.html">ec(1)</a>, <a href="../man1/dsaparam.html">dsaparam(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2003-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


