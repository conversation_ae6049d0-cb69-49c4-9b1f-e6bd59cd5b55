.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SESS_ID 1"
.TH SESS_ID 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-sess_id,
sess_id \- SSL/TLS session handling utility
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBsess_id\fR
[\fB\-help\fR]
[\fB\-inform PEM|DER\fR]
[\fB\-outform PEM|DER|NSS\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-text\fR]
[\fB\-noout\fR]
[\fB\-context ID\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBsess_id\fR process the encoded version of the SSL session structure
and optionally prints out SSL session details (for example the SSL session
master key) in human readable format. Since this is a diagnostic tool that
needs some knowledge of the SSL protocol to use properly, most users will
not need to use it.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format. The \fBDER\fR option uses an ASN1 DER encoded
format containing session details. The precise format can vary from one version
to the next.  The \fBPEM\fR form is the default format: it consists of the \fBDER\fR
format base64 encoded with additional header and footer lines.
.IP "\fB\-outform DER|PEM|NSS\fR" 4
.IX Item "-outform DER|PEM|NSS"
This specifies the output format. The \fBPEM\fR and \fBDER\fR options have the same meaning
and default as the \fB\-inform\fR option. The \fBNSS\fR option outputs the session id and
the master key in NSS keylog format.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read session information from or standard
input by default.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write session information to or standard
output if this option is not specified.
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the various public or private key components in
plain text in addition to the encoded version.
.IP \fB\-cert\fR 4
.IX Item "-cert"
If a certificate is present in the session it will be output using this option,
if the \fB\-text\fR option is also present then it will be printed out in text form.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option prevents output of the encoded version of the session.
.IP "\fB\-context ID\fR" 4
.IX Item "-context ID"
This option can set the session id so the output session information uses the
supplied ID. The ID can be any string of characters. This option won't normally
be used.
.SH OUTPUT
.IX Header "OUTPUT"
Typical output:
.PP
.Vb 10
\& SSL\-Session:
\&     Protocol  : TLSv1
\&     Cipher    : 0016
\&     Session\-ID: 871E62626C554CE95488823752CBD5F3673A3EF3DCE9C67BD916C809914B40ED
\&     Session\-ID\-ctx: 01000000
\&     Master\-Key: A7CEFC571974BE02CAC305269DC59F76EA9F0B180CB6642697A68251F2D2BB57E51DBBB4C7885573192AE9AEE220FACD
\&     Key\-Arg   : None
\&     Start Time: 948459261
\&     Timeout   : 300 (sec)
\&     Verify return code 0 (ok)
.Ve
.PP
These are described below in more detail.
.IP \fBProtocol\fR 4
.IX Item "Protocol"
This is the protocol in use TLSv1.3, TLSv1.2, TLSv1.1, TLSv1 or SSLv3.
.IP \fBCipher\fR 4
.IX Item "Cipher"
The cipher used this is the actual raw SSL or TLS cipher code, see the SSL
or TLS specifications for more information.
.IP \fBSession-ID\fR 4
.IX Item "Session-ID"
The SSL session ID in hex format.
.IP \fBSession-ID-ctx\fR 4
.IX Item "Session-ID-ctx"
The session ID context in hex format.
.IP \fBMaster-Key\fR 4
.IX Item "Master-Key"
This is the SSL session master key.
.IP "\fBStart Time\fR" 4
.IX Item "Start Time"
This is the session start time represented as an integer in standard
Unix format.
.IP \fBTimeout\fR 4
.IX Item "Timeout"
The timeout in seconds.
.IP "\fBVerify return code\fR" 4
.IX Item "Verify return code"
This is the return code when an SSL client certificate is verified.
.SH NOTES
.IX Header "NOTES"
The PEM encoded session format uses the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN SSL SESSION PARAMETERS\-\-\-\-\-
\& \-\-\-\-\-END SSL SESSION PARAMETERS\-\-\-\-\-
.Ve
.PP
Since the SSL session output contains the master key it is
possible to read the contents of an encrypted session using this
information. Therefore, appropriate security precautions should be taken if
the information is being output by a "real" application. This is however
strongly discouraged and should only be used for debugging purposes.
.SH BUGS
.IX Header "BUGS"
The cipher and start time should be printed out in human readable form.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBciphers\fR\|(1), \fBs_server\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
