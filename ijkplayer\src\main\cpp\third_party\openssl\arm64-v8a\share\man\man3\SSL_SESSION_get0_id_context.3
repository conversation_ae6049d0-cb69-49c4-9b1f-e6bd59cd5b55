.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SESSION_GET0_ID_CONTEXT 3"
.TH SSL_SESSION_GET0_ID_CONTEXT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_SESSION_get0_id_context,
SSL_SESSION_set1_id_context
\&\- get and set the SSL ID context associated with a session
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& const unsigned char *SSL_SESSION_get0_id_context(const SSL_SESSION *s,
\&                                                  unsigned int *len)
\& int SSL_SESSION_set1_id_context(SSL_SESSION *s, const unsigned char *sid_ctx,
\&                                unsigned int sid_ctx_len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
See \fBSSL_CTX_set_session_id_context\fR\|(3) for further details on session ID
contexts.
.PP
\&\fBSSL_SESSION_get0_id_context()\fR returns the ID context associated with
the SSL/TLS session \fBs\fR. The length of the ID context is written to
\&\fB*len\fR if \fBlen\fR is not NULL.
.PP
The value returned is a pointer to an object maintained within \fBs\fR and
should not be released.
.PP
\&\fBSSL_SESSION_set1_id_context()\fR takes a copy of the provided ID context given in
\&\fBsid_ctx\fR and associates it with the session \fBs\fR. The length of the ID context
is given by \fBsid_ctx_len\fR which must not exceed SSL_MAX_SID_CTX_LENGTH bytes.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_SESSION_set1_id_context()\fR returns 1 on success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_set_session_id_context\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_SESSION_get0_id_context()\fR function was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
