<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BIO_s_socket</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BIO_s_socket, BIO_new_socket - socket BIO</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bio.h&gt;

const BIO_METHOD *BIO_s_socket(void);

BIO *BIO_new_socket(int sock, int close_flag);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>BIO_s_socket() returns the socket BIO method. This is a wrapper round the platform&#39;s socket routines.</p>

<p>BIO_read_ex() and BIO_write_ex() read or write the underlying socket. BIO_puts() is supported but BIO_gets() is not.</p>

<p>If the close flag is set then the socket is shut down and closed when the BIO is freed.</p>

<p>BIO_new_socket() returns a socket BIO using <b>sock</b> and <b>close_flag</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>Socket BIOs also support any relevant functionality of file descriptor BIOs.</p>

<p>The reason for having separate file descriptor and socket BIOs is that on some platforms sockets are not file descriptors and use distinct I/O routines, Windows is one such platform. Any code mixing the two will not work on all platforms.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BIO_s_socket() returns the socket BIO method.</p>

<p>BIO_new_socket() returns the newly allocated BIO or NULL is an error occurred.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


