.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ERR_LOAD_STRINGS 3"
.TH ERR_LOAD_STRINGS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ERR_load_strings, ERR_PACK, ERR_get_next_error_library \- load
arbitrary error strings
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/err.h>
\&
\& int ERR_load_strings(int lib, ERR_STRING_DATA *str);
\&
\& int ERR_get_next_error_library(void);
\&
\& unsigned long ERR_PACK(int lib, int func, int reason);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBERR_load_strings()\fR registers error strings for library number \fBlib\fR.
.PP
\&\fBstr\fR is an array of error string data:
.PP
.Vb 5
\& typedef struct ERR_string_data_st
\& {
\&     unsigned long error;
\&     char *string;
\& } ERR_STRING_DATA;
.Ve
.PP
The error code is generated from the library number and a function and
reason code: \fBerror\fR = ERR_PACK(\fBlib\fR, \fBfunc\fR, \fBreason\fR).
\&\fBERR_PACK()\fR is a macro.
.PP
The last entry in the array is {0,0}.
.PP
\&\fBERR_get_next_error_library()\fR can be used to assign library numbers
to user libraries at runtime.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBERR_load_strings()\fR returns 1 for success and 0 for failure. \fBERR_PACK()\fR returns the error code.
\&\fBERR_get_next_error_library()\fR returns zero on failure, otherwise a new
library number.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_load_strings\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
