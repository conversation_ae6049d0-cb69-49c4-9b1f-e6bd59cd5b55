.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASYNC_WAIT_CTX_NEW 3"
.TH ASYNC_WAIT_CTX_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASYNC_WAIT_CTX_new, ASYNC_WAIT_CTX_free, ASYNC_WAIT_CTX_set_wait_fd,
ASYNC_WAIT_CTX_get_fd, ASYNC_WAIT_CTX_get_all_fds,
ASYNC_WAIT_CTX_get_changed_fds, ASYNC_WAIT_CTX_clear_fd \- functions to manage
waiting for asynchronous jobs to complete
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/async.h>
\&
\& ASYNC_WAIT_CTX *ASYNC_WAIT_CTX_new(void);
\& void ASYNC_WAIT_CTX_free(ASYNC_WAIT_CTX *ctx);
\& int ASYNC_WAIT_CTX_set_wait_fd(ASYNC_WAIT_CTX *ctx, const void *key,
\&                                OSSL_ASYNC_FD fd,
\&                                void *custom_data,
\&                                void (*cleanup)(ASYNC_WAIT_CTX *, const void *,
\&                                                OSSL_ASYNC_FD, void *));
\& int ASYNC_WAIT_CTX_get_fd(ASYNC_WAIT_CTX *ctx, const void *key,
\&                           OSSL_ASYNC_FD *fd, void **custom_data);
\& int ASYNC_WAIT_CTX_get_all_fds(ASYNC_WAIT_CTX *ctx, OSSL_ASYNC_FD *fd,
\&                                size_t *numfds);
\& int ASYNC_WAIT_CTX_get_changed_fds(ASYNC_WAIT_CTX *ctx, OSSL_ASYNC_FD *addfd,
\&                                    size_t *numaddfds, OSSL_ASYNC_FD *delfd,
\&                                    size_t *numdelfds);
\& int ASYNC_WAIT_CTX_clear_fd(ASYNC_WAIT_CTX *ctx, const void *key);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
For an overview of how asynchronous operations are implemented in OpenSSL see
\&\fBASYNC_start_job\fR\|(3). An ASYNC_WAIT_CTX object represents an asynchronous
"session", i.e. a related set of crypto operations. For example in SSL terms
this would have a one-to-one correspondence with an SSL connection.
.PP
Application code must create an ASYNC_WAIT_CTX using the \fBASYNC_WAIT_CTX_new()\fR
function prior to calling \fBASYNC_start_job()\fR (see \fBASYNC_start_job\fR\|(3)). When
the job is started it is associated with the ASYNC_WAIT_CTX for the duration of
that job. An ASYNC_WAIT_CTX should only be used for one ASYNC_JOB at any one
time, but can be reused after an ASYNC_JOB has finished for a subsequent
ASYNC_JOB. When the session is complete (e.g. the SSL connection is closed),
application code cleans up with \fBASYNC_WAIT_CTX_free()\fR.
.PP
ASYNC_WAIT_CTXs can have "wait" file descriptors associated with them. Calling
\&\fBASYNC_WAIT_CTX_get_all_fds()\fR and passing in a pointer to an ASYNC_WAIT_CTX in
the \fBctx\fR parameter will return the wait file descriptors associated with that
job in \fB*fd\fR. The number of file descriptors returned will be stored in
\&\fB*numfds\fR. It is the caller's responsibility to ensure that sufficient memory
has been allocated in \fB*fd\fR to receive all the file descriptors. Calling
\&\fBASYNC_WAIT_CTX_get_all_fds()\fR with a NULL \fBfd\fR value will return no file
descriptors but will still populate \fB*numfds\fR. Therefore, application code is
typically expected to call this function twice: once to get the number of fds,
and then again when sufficient memory has been allocated. If only one
asynchronous engine is being used then normally this call will only ever return
one fd. If multiple asynchronous engines are being used then more could be
returned.
.PP
The function \fBASYNC_WAIT_CTX_get_changed_fds()\fR can be used to detect if any fds
have changed since the last call time \fBASYNC_start_job()\fR returned an ASYNC_PAUSE
result (or since the ASYNC_WAIT_CTX was created if no ASYNC_PAUSE result has
been received). The \fBnumaddfds\fR and \fBnumdelfds\fR parameters will be populated
with the number of fds added or deleted respectively. \fB*addfd\fR and \fB*delfd\fR
will be populated with the list of added and deleted fds respectively. Similarly
to \fBASYNC_WAIT_CTX_get_all_fds()\fR either of these can be NULL, but if they are not
NULL then the caller is responsible for ensuring sufficient memory is allocated.
.PP
Implementors of async aware code (e.g. engines) are encouraged to return a
stable fd for the lifetime of the ASYNC_WAIT_CTX in order to reduce the "churn"
of regularly changing fds \- although no guarantees of this are provided to
applications.
.PP
Applications can wait for the file descriptor to be ready for "read" using a
system function call such as select or poll (being ready for "read" indicates
that the job should be resumed). If no file descriptor is made available then an
application will have to periodically "poll" the job by attempting to restart it
to see if it is ready to continue.
.PP
Async aware code (e.g. engines) can get the current ASYNC_WAIT_CTX from the job
via \fBASYNC_get_wait_ctx\fR\|(3) and provide a file descriptor to use for waiting
on by calling \fBASYNC_WAIT_CTX_set_wait_fd()\fR. Typically this would be done by an
engine immediately prior to calling \fBASYNC_pause_job()\fR and not by end user code.
An existing association with a file descriptor can be obtained using
\&\fBASYNC_WAIT_CTX_get_fd()\fR and cleared using \fBASYNC_WAIT_CTX_clear_fd()\fR. Both of
these functions requires a \fBkey\fR value which is unique to the async aware
code.  This could be any unique value but a good candidate might be the
\&\fBENGINE *\fR for the engine. The \fBcustom_data\fR parameter can be any value, and
will be returned in a subsequent call to \fBASYNC_WAIT_CTX_get_fd()\fR. The
\&\fBASYNC_WAIT_CTX_set_wait_fd()\fR function also expects a pointer to a "cleanup"
routine. This can be NULL but if provided will automatically get called when
the ASYNC_WAIT_CTX is freed, and gives the engine the opportunity to close the
fd or any other resources. Note: The "cleanup" routine does not get called if
the fd is cleared directly via a call to \fBASYNC_WAIT_CTX_clear_fd()\fR.
.PP
An example of typical usage might be an async capable engine. User code would
initiate cryptographic operations. The engine would initiate those operations
asynchronously and then call \fBASYNC_WAIT_CTX_set_wait_fd()\fR followed by
\&\fBASYNC_pause_job()\fR to return control to the user code. The user code can then
perform other tasks or wait for the job to be ready by calling "select" or other
similar function on the wait file descriptor. The engine can signal to the user
code that the job should be resumed by making the wait file descriptor
"readable". Once resumed the engine should clear the wake signal on the wait
file descriptor.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASYNC_WAIT_CTX_new()\fR returns a pointer to the newly allocated ASYNC_WAIT_CTX or
NULL on error.
.PP
ASYNC_WAIT_CTX_set_wait_fd, ASYNC_WAIT_CTX_get_fd, ASYNC_WAIT_CTX_get_all_fds,
ASYNC_WAIT_CTX_get_changed_fds and ASYNC_WAIT_CTX_clear_fd all return 1 on
success or 0 on error.
.SH NOTES
.IX Header "NOTES"
On Windows platforms the openssl/async.h header is dependent on some
of the types customarily made available by including windows.h. The
application developer is likely to require control over when the latter
is included, commonly as one of the first included headers. Therefore,
it is defined as an application developer's responsibility to include
windows.h prior to async.h.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBASYNC_start_job\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBASYNC_WAIT_CTX_new()\fR, \fBASYNC_WAIT_CTX_free()\fR, \fBASYNC_WAIT_CTX_set_wait_fd()\fR,
\&\fBASYNC_WAIT_CTX_get_fd()\fR, \fBASYNC_WAIT_CTX_get_all_fds()\fR,
\&\fBASYNC_WAIT_CTX_get_changed_fds()\fR and \fBASYNC_WAIT_CTX_clear_fd()\fR
were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
