.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_VERSION 3"
.TH SSL_GET_VERSION 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_client_version, SSL_get_version, SSL_is_dtls, SSL_version \- get the
protocol information of a connection
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_client_version(const SSL *s);
\&
\& const char *SSL_get_version(const SSL *ssl);
\&
\& int SSL_is_dtls(const SSL *ssl);
\&
\& int SSL_version(const SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_client_version()\fR returns the numeric protocol version advertised by the
client in the legacy_version field of the ClientHello when initiating the
connection. Note that, for TLS, this value will never indicate a version greater
than TLSv1.2 even if TLSv1.3 is subsequently negotiated. \fBSSL_get_version()\fR
returns the name of the protocol used for the connection. \fBSSL_version()\fR returns
the numeric protocol version used for the connection. They should only be called
after the initial handshake has been completed. Prior to that the results
returned from these functions may be unreliable.
.PP
\&\fBSSL_is_dtls()\fR returns one if the connection is using DTLS, zero if not.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_get_version()\fR returns one of the following strings:
.IP SSLv3 4
.IX Item "SSLv3"
The connection uses the SSLv3 protocol.
.IP TLSv1 4
.IX Item "TLSv1"
The connection uses the TLSv1.0 protocol.
.IP TLSv1.1 4
.IX Item "TLSv1.1"
The connection uses the TLSv1.1 protocol.
.IP TLSv1.2 4
.IX Item "TLSv1.2"
The connection uses the TLSv1.2 protocol.
.IP TLSv1.3 4
.IX Item "TLSv1.3"
The connection uses the TLSv1.3 protocol.
.IP unknown 4
.IX Item "unknown"
This indicates an unknown protocol version.
.PP
\&\fBSSL_version()\fR and \fBSSL_client_version()\fR return an integer which could include any
of the following:
.IP SSL3_VERSION 4
.IX Item "SSL3_VERSION"
The connection uses the SSLv3 protocol.
.IP TLS1_VERSION 4
.IX Item "TLS1_VERSION"
The connection uses the TLSv1.0 protocol.
.IP TLS1_1_VERSION 4
.IX Item "TLS1_1_VERSION"
The connection uses the TLSv1.1 protocol.
.IP TLS1_2_VERSION 4
.IX Item "TLS1_2_VERSION"
The connection uses the TLSv1.2 protocol.
.IP TLS1_3_VERSION 4
.IX Item "TLS1_3_VERSION"
The connection uses the TLSv1.3 protocol (never returned for
\&\fBSSL_client_version()\fR).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_is_dtls()\fR function was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
