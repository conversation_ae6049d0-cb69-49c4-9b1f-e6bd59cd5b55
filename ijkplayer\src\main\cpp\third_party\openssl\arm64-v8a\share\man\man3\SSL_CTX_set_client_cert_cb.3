.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_CLIENT_CERT_CB 3"
.TH SSL_CTX_SET_CLIENT_CERT_CB 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_client_cert_cb, SSL_CTX_get_client_cert_cb \- handle client certificate callback function
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_client_cert_cb(SSL_CTX *ctx,
\&                                 int (*client_cert_cb)(SSL *ssl, X509 **x509,
\&                                                       EVP_PKEY **pkey));
\& int (*SSL_CTX_get_client_cert_cb(SSL_CTX *ctx))(SSL *ssl, X509 **x509,
\&                                                 EVP_PKEY **pkey);
\& int (*client_cert_cb)(SSL *ssl, X509 **x509, EVP_PKEY **pkey);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_client_cert_cb()\fR sets the \fBclient_cert_cb()\fR callback, that is
called when a client certificate is requested by a server and no certificate
was yet set for the SSL object.
.PP
When \fBclient_cert_cb()\fR is NULL, no callback function is used.
.PP
\&\fBSSL_CTX_get_client_cert_cb()\fR returns a pointer to the currently set callback
function.
.PP
\&\fBclient_cert_cb()\fR is the application defined callback. If it wants to
set a certificate, a certificate/private key combination must be set
using the \fBx509\fR and \fBpkey\fR arguments and "1" must be returned. The
certificate will be installed into \fBssl\fR, see the NOTES and BUGS sections.
If no certificate should be set, "0" has to be returned and no certificate
will be sent. A negative return value will suspend the handshake and the
handshake function will return immediately. \fBSSL_get_error\fR\|(3)
will return SSL_ERROR_WANT_X509_LOOKUP to indicate, that the handshake was
suspended. The next call to the handshake function will again lead to the call
of \fBclient_cert_cb()\fR. It is the job of the \fBclient_cert_cb()\fR to store information
about the state of the last call, if required to continue.
.SH NOTES
.IX Header "NOTES"
During a handshake (or renegotiation) a server may request a certificate
from the client. A client certificate must only be sent, when the server
did send the request.
.PP
When a certificate was set using the
\&\fBSSL_CTX_use_certificate\fR\|(3) family of functions,
it will be sent to the server. The TLS standard requires that only a
certificate is sent, if it matches the list of acceptable CAs sent by the
server. This constraint is violated by the default behavior of the OpenSSL
library. Using the callback function it is possible to implement a proper
selection routine or to allow a user interaction to choose the certificate to
be sent.
.PP
If a callback function is defined and no certificate was yet defined for the
SSL object, the callback function will be called.
If the callback function returns a certificate, the OpenSSL library
will try to load the private key and certificate data into the SSL
object using the \fBSSL_use_certificate()\fR and \fBSSL_use_private_key()\fR functions.
Thus it will permanently install the certificate and key for this SSL
object. It will not be reset by calling \fBSSL_clear\fR\|(3).
If the callback returns no certificate, the OpenSSL library will not send
a certificate.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_get_client_cert_cb()\fR returns function pointer of \fBclient_cert_cb()\fR or
NULL if the callback is not set.
.SH BUGS
.IX Header "BUGS"
The \fBclient_cert_cb()\fR cannot return a complete certificate chain, it can
only return one client certificate. If the chain only has a length of 2,
the root CA certificate may be omitted according to the TLS standard and
thus a standard conforming answer can be sent to the server. For a
longer chain, the client must send the complete chain (with the option
to leave out the root CA certificate). This can only be accomplished by
either adding the intermediate CA certificates into the trusted
certificate store for the SSL_CTX object (resulting in having to add
CA certificates that otherwise maybe would not be trusted), or by adding
the chain certificates using the
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3)
function, which is only available for the SSL_CTX object as a whole and that
therefore probably can only apply for one client certificate, making
the concept of the callback function (to allow the choice from several
certificates) questionable.
.PP
Once the SSL object has been used in conjunction with the callback function,
the certificate will be set for the SSL object and will not be cleared
even when \fBSSL_clear\fR\|(3) is being called. It is therefore
mandatory to destroy the SSL object using \fBSSL_free\fR\|(3)
and create a new one to return to the previous state.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_use_certificate\fR\|(3),
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3),
\&\fBSSL_get_client_CA_list\fR\|(3),
\&\fBSSL_clear\fR\|(3), \fBSSL_free\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
