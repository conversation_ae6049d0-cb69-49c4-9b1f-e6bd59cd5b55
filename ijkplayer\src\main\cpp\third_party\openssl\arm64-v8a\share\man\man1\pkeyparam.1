.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKEYPARAM 1"
.TH PKEYPARAM 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-pkeyparam,
pkeyparam \- public key algorithm parameter processing tool
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBpkeyparam\fR
[\fB\-help\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-text\fR]
[\fB\-noout\fR]
[\fB\-engine id\fR]
[\fB\-check\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBpkeyparam\fR command processes public key algorithm parameters.
They can be checked for correctness and their components printed out.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read parameters from or standard input if
this option is not specified.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write parameters to or standard output if
this option is not specified.
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the parameters in plain text in addition to the encoded version.
.IP \fB\-noout\fR 4
.IX Item "-noout"
Do not output the encoded version of the parameters.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBpkeyparam\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.IP \fB\-check\fR 4
.IX Item "-check"
This option checks the correctness of parameters.
.SH EXAMPLES
.IX Header "EXAMPLES"
Print out text version of parameters:
.PP
.Vb 1
\& openssl pkeyparam \-in param.pem \-text
.Ve
.SH NOTES
.IX Header "NOTES"
There are no \fB\-inform\fR or \fB\-outform\fR options for this command because only
PEM format is supported because the key type is determined by the PEM headers.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBgenpkey\fR\|(1), \fBrsa\fR\|(1), \fBpkcs8\fR\|(1),
\&\fBdsa\fR\|(1), \fBgenrsa\fR\|(1), \fBgendsa\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
