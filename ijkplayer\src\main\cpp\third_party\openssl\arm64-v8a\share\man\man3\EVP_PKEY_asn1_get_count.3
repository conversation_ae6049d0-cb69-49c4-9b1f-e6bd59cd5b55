.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_ASN1_GET_COUNT 3"
.TH EVP_PKEY_ASN1_GET_COUNT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_asn1_find,
EVP_PKEY_asn1_find_str,
EVP_PKEY_asn1_get_count,
EVP_PKEY_asn1_get0,
EVP_PKEY_asn1_get0_info
\&\- enumerate public key ASN.1 methods
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_asn1_get_count(void);
\& const EVP_PKEY_ASN1_METHOD *EVP_PKEY_asn1_get0(int idx);
\& const EVP_PKEY_ASN1_METHOD *EVP_PKEY_asn1_find(ENGINE **pe, int type);
\& const EVP_PKEY_ASN1_METHOD *EVP_PKEY_asn1_find_str(ENGINE **pe,
\&                                                    const char *str, int len);
\& int EVP_PKEY_asn1_get0_info(int *ppkey_id, int *pkey_base_id,
\&                             int *ppkey_flags, const char **pinfo,
\&                             const char **ppem_str,
\&                             const EVP_PKEY_ASN1_METHOD *ameth);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_asn1_count()\fR returns a count of the number of public key
ASN.1 methods available: it includes standard methods and any methods
added by the application.
.PP
\&\fBEVP_PKEY_asn1_get0()\fR returns the public key ASN.1 method \fBidx\fR.
The value of \fBidx\fR must be between zero and \fBEVP_PKEY_asn1_get_count()\fR
\&\- 1.
.PP
\&\fBEVP_PKEY_asn1_find()\fR looks up the \fBEVP_PKEY_ASN1_METHOD\fR with NID
\&\fBtype\fR.
If \fBpe\fR isn't \fBNULL\fR, then it will look up an engine implementing a
\&\fBEVP_PKEY_ASN1_METHOD\fR for the NID \fBtype\fR and return that instead,
and also set \fB*pe\fR to point at the engine that implements it.
.PP
\&\fBEVP_PKEY_asn1_find_str()\fR looks up the \fBEVP_PKEY_ASN1_METHOD\fR with PEM
type string \fBstr\fR.
Just like \fBEVP_PKEY_asn1_find()\fR, if \fBpe\fR isn't \fBNULL\fR, then it will
look up an engine implementing a \fBEVP_PKEY_ASN1_METHOD\fR for the NID
\&\fBtype\fR and return that instead, and also set \fB*pe\fR to point at the
engine that implements it.
.PP
\&\fBEVP_PKEY_asn1_get0_info()\fR returns the public key ID, base public key
ID (both NIDs), any flags, the method description and PEM type string
associated with the public key ASN.1 method \fB*ameth\fR.
.PP
\&\fBEVP_PKEY_asn1_count()\fR, \fBEVP_PKEY_asn1_get0()\fR, \fBEVP_PKEY_asn1_find()\fR and
\&\fBEVP_PKEY_asn1_find_str()\fR are not thread safe, but as long as all
\&\fBEVP_PKEY_ASN1_METHOD\fR objects are added before the application gets
threaded, using them is safe.  See \fBEVP_PKEY_asn1_add0\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_asn1_count()\fR returns the number of available public key methods.
.PP
\&\fBEVP_PKEY_asn1_get0()\fR return a public key method or \fBNULL\fR if \fBidx\fR is
out of range.
.PP
\&\fBEVP_PKEY_asn1_get0_info()\fR returns 0 on failure, 1 on success.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_asn1_new\fR\|(3), \fBEVP_PKEY_asn1_add0\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
