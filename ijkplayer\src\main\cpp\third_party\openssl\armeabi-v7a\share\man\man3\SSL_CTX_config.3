.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_CONFIG 3"
.TH SSL_CTX_CONFIG 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_config, SSL_config \- configure SSL_CTX or SSL structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_config(SSL_CTX *ctx, const char *name);
\& int SSL_config(SSL *s, const char *name);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The functions \fBSSL_CTX_config()\fR and \fBSSL_config()\fR configure an \fBSSL_CTX\fR or
\&\fBSSL\fR structure using the configuration \fBname\fR.
.SH NOTES
.IX Header "NOTES"
By calling \fBSSL_CTX_config()\fR or \fBSSL_config()\fR an application can perform many
complex tasks based on the contents of the configuration file: greatly
simplifying application configuration code. A degree of future proofing
can also be achieved: an application can support configuration features
in newer versions of OpenSSL automatically.
.PP
A configuration file must have been previously loaded, for example using
\&\fBCONF_modules_load_file()\fR. See \fBconfig\fR\|(5) for details of the configuration
file syntax.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_config()\fR and \fBSSL_config()\fR return 1 for success or 0 if an error
occurred.
.SH EXAMPLES
.IX Header "EXAMPLES"
If the file "config.cnf" contains the following:
.PP
.Vb 1
\& testapp = test_sect
\&
\& [test_sect]
\& # list of configuration modules
\&
\& ssl_conf = ssl_sect
\&
\& [ssl_sect]
\& server = server_section
\&
\& [server_section]
\& RSA.Certificate = server\-rsa.pem
\& ECDSA.Certificate = server\-ecdsa.pem
\& Ciphers = ALL:!RC4
.Ve
.PP
An application could call:
.PP
.Vb 4
\& if (CONF_modules_load_file("config.cnf", "testapp", 0) <= 0) {
\&     fprintf(stderr, "Error processing config file\en");
\&     goto err;
\& }
\&
\& ctx = SSL_CTX_new(TLS_server_method());
\&
\& if (SSL_CTX_config(ctx, "server") == 0) {
\&     fprintf(stderr, "Error configuring server.\en");
\&     goto err;
\& }
.Ve
.PP
In this example two certificates and the cipher list are configured without
the need for any additional application code.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBconfig\fR\|(5),
\&\fBSSL_CONF_cmd\fR\|(3),
\&\fBCONF_modules_load_file\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_CTX_config()\fR and \fBSSL_config()\fR functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
