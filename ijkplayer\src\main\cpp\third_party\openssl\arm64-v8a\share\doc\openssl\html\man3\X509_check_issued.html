<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_check_issued</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_check_issued - checks if certificate is apparently issued by another certificate</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509v3.h&gt;

int X509_check_issued(X509 *issuer, X509 *subject);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>X509_check_issued() checks if certificate <i>subject</i> was apparently issued using (CA) certificate <i>issuer</i>. This function takes into account not only matching of the issuer field of <i>subject</i> with the subject field of <i>issuer</i>, but also compares all sub-fields of the <b>authorityKeyIdentifier</b> extension of <i>subject</i>, as far as present, with the respective <b>subjectKeyIdentifier</b>, serial number, and issuer fields of <i>issuer</i>, as far as present. It also checks if the <b>keyUsage</b> field (if present) of <i>issuer</i> allows certificate signing. It does not check the certificate signature.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>Function return <b>X509_V_OK</b> if certificate <i>subject</i> is issued by <i>issuer</i> or some <b>X509_V_ERR*</b> constant to indicate an error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/X509_verify_cert.html">X509_verify_cert(3)</a>, <a href="../man3/X509_check_ca.html">X509_check_ca(3)</a>, <a href="../man1/verify.html">verify(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


