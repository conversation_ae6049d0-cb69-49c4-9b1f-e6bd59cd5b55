.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_PUBKEY_NEW 3"
.TH X509_PUBKEY_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_PUBKEY_new, X509_PUBKEY_free, X509_PUBKEY_set, X509_PUBKEY_get0,
X509_PUBKEY_get, d2i_PUBKEY, i2d_PUBKEY, d2i_PUBKEY_bio, d2i_PUBKEY_fp,
i2d_PUBKEY_fp, i2d_PUBKEY_bio, X509_PUBKEY_set0_param,
X509_PUBKEY_get0_param \- SubjectPublicKeyInfo public key functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& X509_PUBKEY *X509_PUBKEY_new(void);
\& void X509_PUBKEY_free(X509_PUBKEY *a);
\&
\& int X509_PUBKEY_set(X509_PUBKEY **x, EVP_PKEY *pkey);
\& EVP_PKEY *X509_PUBKEY_get0(X509_PUBKEY *key);
\& EVP_PKEY *X509_PUBKEY_get(X509_PUBKEY *key);
\&
\& EVP_PKEY *d2i_PUBKEY(EVP_PKEY **a, const unsigned char **pp, long length);
\& int i2d_PUBKEY(EVP_PKEY *a, unsigned char **pp);
\&
\& EVP_PKEY *d2i_PUBKEY_bio(BIO *bp, EVP_PKEY **a);
\& EVP_PKEY *d2i_PUBKEY_fp(FILE *fp, EVP_PKEY **a);
\&
\& int i2d_PUBKEY_fp(FILE *fp, EVP_PKEY *pkey);
\& int i2d_PUBKEY_bio(BIO *bp, EVP_PKEY *pkey);
\&
\& int X509_PUBKEY_set0_param(X509_PUBKEY *pub, ASN1_OBJECT *aobj,
\&                            int ptype, void *pval,
\&                            unsigned char *penc, int penclen);
\& int X509_PUBKEY_get0_param(ASN1_OBJECT **ppkalg,
\&                            const unsigned char **pk, int *ppklen,
\&                            X509_ALGOR **pa, X509_PUBKEY *pub);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBX509_PUBKEY\fR structure represents the ASN.1 \fBSubjectPublicKeyInfo\fR
structure defined in RFC5280 and used in certificates and certificate requests.
.PP
\&\fBX509_PUBKEY_new()\fR allocates and initializes an \fBX509_PUBKEY\fR structure.
.PP
\&\fBX509_PUBKEY_free()\fR frees up \fBX509_PUBKEY\fR structure \fBa\fR. If \fBa\fR is NULL
nothing is done.
.PP
\&\fBX509_PUBKEY_set()\fR sets the public key in \fB*x\fR to the public key contained
in the \fBEVP_PKEY\fR structure \fBpkey\fR. If \fB*x\fR is not NULL any existing
public key structure will be freed.
.PP
\&\fBX509_PUBKEY_get0()\fR returns the public key contained in \fBkey\fR. The returned
value is an internal pointer which \fBMUST NOT\fR be freed after use.
.PP
\&\fBX509_PUBKEY_get()\fR is similar to \fBX509_PUBKEY_get0()\fR except the reference
count on the returned key is incremented so it \fBMUST\fR be freed using
\&\fBEVP_PKEY_free()\fR after use.
.PP
\&\fBd2i_PUBKEY()\fR and \fBi2d_PUBKEY()\fR decode and encode an \fBEVP_PKEY\fR structure
using \fBSubjectPublicKeyInfo\fR format. They otherwise follow the conventions of
other ASN.1 functions such as \fBd2i_X509()\fR.
.PP
\&\fBd2i_PUBKEY_bio()\fR, \fBd2i_PUBKEY_fp()\fR, \fBi2d_PUBKEY_bio()\fR and \fBi2d_PUBKEY_fp()\fR are
similar to \fBd2i_PUBKEY()\fR and \fBi2d_PUBKEY()\fR except they decode or encode using a
\&\fBBIO\fR or \fBFILE\fR pointer.
.PP
\&\fBX509_PUBKEY_set0_param()\fR sets the public key parameters of \fBpub\fR. The
OID associated with the algorithm is set to \fBaobj\fR. The type of the
algorithm parameters is set to \fBtype\fR using the structure \fBpval\fR.
The encoding of the public key itself is set to the \fBpenclen\fR
bytes contained in buffer \fBpenc\fR. On success ownership of all the supplied
parameters is passed to \fBpub\fR so they must not be freed after the
call.
.PP
\&\fBX509_PUBKEY_get0_param()\fR retrieves the public key parameters from \fBpub\fR,
\&\fB*ppkalg\fR is set to the associated OID and the encoding consists of
\&\fB*ppklen\fR bytes at \fB*pk\fR, \fB*pa\fR is set to the associated
AlgorithmIdentifier for the public key. If the value of any of these
parameters is not required it can be set to \fBNULL\fR. All of the
retrieved pointers are internal and must not be freed after the
call.
.SH NOTES
.IX Header "NOTES"
The \fBX509_PUBKEY\fR functions can be used to encode and decode public keys
in a standard format.
.PP
In many cases applications will not call the \fBX509_PUBKEY\fR functions
directly: they will instead call wrapper functions such as \fBX509_get0_pubkey()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If the allocation fails, \fBX509_PUBKEY_new()\fR returns \fBNULL\fR and sets an error
code that can be obtained by \fBERR_get_error\fR\|(3).
.PP
Otherwise it returns a pointer to the newly allocated structure.
.PP
\&\fBX509_PUBKEY_free()\fR does not return a value.
.PP
\&\fBX509_PUBKEY_get0()\fR and \fBX509_PUBKEY_get()\fR return a pointer to an \fBEVP_PKEY\fR
structure or \fBNULL\fR if an error occurs.
.PP
\&\fBX509_PUBKEY_set()\fR, \fBX509_PUBKEY_set0_param()\fR and \fBX509_PUBKEY_get0_param()\fR
return 1 for success and 0 if an error occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3),
\&\fBERR_get_error\fR\|(3),
\&\fBX509_get_pubkey\fR\|(3),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
