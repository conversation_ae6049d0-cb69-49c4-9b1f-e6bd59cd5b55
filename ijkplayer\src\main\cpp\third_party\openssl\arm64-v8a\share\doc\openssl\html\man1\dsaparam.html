<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>dsaparam</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-dsaparam, dsaparam - DSA parameter manipulation and generation</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl dsaparam</b> [<b>-help</b>] [<b>-inform DER|PEM</b>] [<b>-outform DER|PEM</b>] [<b>-in filename</b>] [<b>-out filename</b>] [<b>-noout</b>] [<b>-text</b>] [<b>-C</b>] [<b>-rand file...</b>] [<b>-writerand file</b>] [<b>-genkey</b>] [<b>-engine id</b>] [<b>numbits</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command is used to manipulate or generate DSA parameter files.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="inform-DER-PEM"><b>-inform DER|PEM</b></dt>
<dd>

<p>This specifies the input format. The <b>DER</b> option uses an ASN1 DER encoded form compatible with RFC2459 (PKIX) DSS-Parms that is a SEQUENCE consisting of p, q and g respectively. The PEM form is the default format: it consists of the <b>DER</b> format base64 encoded with additional header and footer lines.</p>

</dd>
<dt id="outform-DER-PEM"><b>-outform DER|PEM</b></dt>
<dd>

<p>This specifies the output format, the options have the same meaning and default as the <b>-inform</b> option.</p>

</dd>
<dt id="in-filename"><b>-in filename</b></dt>
<dd>

<p>This specifies the input filename to read parameters from or standard input if this option is not specified. If the <b>numbits</b> parameter is included then this option will be ignored.</p>

</dd>
<dt id="out-filename"><b>-out filename</b></dt>
<dd>

<p>This specifies the output filename parameters to. Standard output is used if this option is not present. The output filename should <b>not</b> be the same as the input filename.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>This option inhibits the output of the encoded version of the parameters.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>This option prints out the DSA parameters in human readable form.</p>

</dd>
<dt id="C"><b>-C</b></dt>
<dd>

<p>This option converts the parameters into C code. The parameters can then be loaded by calling the get_dsaXXX() function.</p>

</dd>
<dt id="genkey"><b>-genkey</b></dt>
<dd>

<p>This option will generate a DSA either using the specified or generated parameters.</p>

</dd>
<dt id="rand-file"><b>-rand file...</b></dt>
<dd>

<p>A file or files containing random data used to seed the random number generator. Multiple files can be specified separated by an OS-dependent character. The separator is <b>;</b> for MS-Windows, <b>,</b> for OpenVMS, and <b>:</b> for all others.</p>

</dd>
<dt id="writerand-file">[<b>-writerand file</b>]</dt>
<dd>

<p>Writes random data to the specified <i>file</i> upon exit. This can be used with a subsequent <b>-rand</b> flag.</p>

</dd>
<dt id="numbits"><b>numbits</b></dt>
<dd>

<p>This option specifies that a parameter set should be generated of size <b>numbits</b>. It must be the last option. If this option is included then the input file (if any) is ignored.</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Specifying an engine (by its unique <b>id</b> string) will cause <b>dsaparam</b> to attempt to obtain a functional reference to the specified engine, thus initialising it if needed. The engine will then be set as the default for all available algorithms.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>PEM format DSA parameters use the header and footer lines:</p>

<pre><code>-----BEGIN DSA PARAMETERS-----
-----END DSA PARAMETERS-----</code></pre>

<p>DSA parameter generation is a slow process and as a result the same set of DSA parameters is often used to generate several distinct keys.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/gendsa.html">gendsa(1)</a>, <a href="../man1/dsa.html">dsa(1)</a>, <a href="../man1/genrsa.html">genrsa(1)</a>, <a href="../man1/rsa.html">rsa(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2017 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


