.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_RAND 3"
.TH BN_RAND 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_rand, BN_priv_rand, BN_pseudo_rand,
BN_rand_range, BN_priv_rand_range, BN_pseudo_rand_range
\&\- generate pseudo\-random number
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& int BN_rand(BIGNUM *rnd, int bits, int top, int bottom);
\&
\& int BN_priv_rand(BIGNUM *rnd, int bits, int top, int bottom);
\&
\& int BN_pseudo_rand(BIGNUM *rnd, int bits, int top, int bottom);
\&
\& int BN_rand_range(BIGNUM *rnd, BIGNUM *range);
\&
\& int BN_priv_rand_range(BIGNUM *rnd, BIGNUM *range);
\&
\& int BN_pseudo_rand_range(BIGNUM *rnd, BIGNUM *range);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_rand()\fR generates a cryptographically strong pseudo-random number of
\&\fBbits\fR in length and stores it in \fBrnd\fR.
If \fBbits\fR is less than zero, or too small to
accommodate the requirements specified by the \fBtop\fR and \fBbottom\fR
parameters, an error is returned.
The \fBtop\fR parameters specifies
requirements on the most significant bit of the generated number.
If it is \fBBN_RAND_TOP_ANY\fR, there is no constraint.
If it is \fBBN_RAND_TOP_ONE\fR, the top bit must be one.
If it is \fBBN_RAND_TOP_TWO\fR, the two most significant bits of
the number will be set to 1, so that the product of two such random
numbers will always have 2*\fBbits\fR length.
If \fBbottom\fR is \fBBN_RAND_BOTTOM_ODD\fR, the number will be odd; if it
is \fBBN_RAND_BOTTOM_ANY\fR it can be odd or even.
If \fBbits\fR is 1 then \fBtop\fR cannot also be \fBBN_RAND_TOP_TWO\fR.
.PP
\&\fBBN_rand_range()\fR generates a cryptographically strong pseudo-random
number \fBrnd\fR in the range 0 <= \fBrnd\fR < \fBrange\fR.
.PP
\&\fBBN_priv_rand()\fR and \fBBN_priv_rand_range()\fR have the same semantics as
\&\fBBN_rand()\fR and \fBBN_rand_range()\fR respectively.  They are intended to be
used for generating values that should remain private, and mirror the
same difference between \fBRAND_bytes\fR\|(3) and \fBRAND_priv_bytes\fR\|(3).
.SH NOTES
.IX Header "NOTES"
Always check the error return value of these functions and do not take
randomness for granted: an error occurs if the CSPRNG has not been
seeded with enough randomness to ensure an unpredictable byte sequence.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The functions return 1 on success, 0 on error.
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBRAND_add\fR\|(3),
\&\fBRAND_bytes\fR\|(3),
\&\fBRAND_priv_bytes\fR\|(3),
\&\fBRAND\fR\|(7),
\&\fBRAND_DRBG\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
.IP \(bu 2
Starting with OpenSSL release 1.1.0, \fBBN_pseudo_rand()\fR has been identical
to \fBBN_rand()\fR and \fBBN_pseudo_rand_range()\fR has been identical to
\&\fBBN_rand_range()\fR.
The "pseudo" functions should not be used and may be deprecated in
a future release.
.IP \(bu 2
The
\&\fBBN_priv_rand()\fR and \fBBN_priv_rand_range()\fR functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
