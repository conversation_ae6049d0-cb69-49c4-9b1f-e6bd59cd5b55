<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_clear</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#WARNINGS">WARNINGS</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_clear - reset SSL object to allow another connection</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

int SSL_clear(SSL *ssl);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Reset <b>ssl</b> to allow another connection. All settings (method, ciphers, BIOs) are kept.</p>

<h1 id="NOTES">NOTES</h1>

<p>SSL_clear is used to prepare an SSL object for a new connection. While all settings are kept, a side effect is the handling of the current SSL session. If a session is still <b>open</b>, it is considered bad and will be removed from the session cache, as required by RFC2246. A session is considered open, if <a href="../man3/SSL_shutdown.html">SSL_shutdown(3)</a> was not called for the connection or at least <a href="../man3/SSL_set_shutdown.html">SSL_set_shutdown(3)</a> was used to set the SSL_SENT_SHUTDOWN state.</p>

<p>If a session was closed cleanly, the session object will be kept and all settings corresponding. This explicitly means, that e.g. the special method used during the session will be kept for the next handshake. So if the session was a TLSv1 session, a SSL client object will use a TLSv1 client method for the next handshake and a SSL server object will use a TLSv1 server method, even if TLS_*_methods were chosen on startup. This will might lead to connection failures (see <a href="../man3/SSL_new.html">SSL_new(3)</a>) for a description of the method&#39;s properties.</p>

<h1 id="WARNINGS">WARNINGS</h1>

<p>SSL_clear() resets the SSL object to allow for another connection. The reset operation however keeps several settings of the last sessions (some of these settings were made automatically during the last handshake). It only makes sense for a new connection with the exact same peer that shares these settings, and may fail if that peer changes its settings between connections. Use the sequence <a href="../man3/SSL_get_session.html">SSL_get_session(3)</a>; <a href="../man3/SSL_new.html">SSL_new(3)</a>; <a href="../man3/SSL_set_session.html">SSL_set_session(3)</a>; <a href="../man3/SSL_free.html">SSL_free(3)</a> instead to avoid such failures (or simply <a href="../man3/SSL_free.html">SSL_free(3)</a>; <a href="../man3/SSL_new.html">SSL_new(3)</a> if session reuse is not desired).</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The following return values can occur:</p>

<dl>

<dt id="pod0">0</dt>
<dd>

<p>The SSL_clear() operation could not be performed. Check the error stack to find out the reason.</p>

</dd>
<dt id="pod1">1</dt>
<dd>

<p>The SSL_clear() operation was successful.</p>

</dd>
</dl>

<p><a href="../man3/SSL_new.html">SSL_new(3)</a>, <a href="../man3/SSL_free.html">SSL_free(3)</a>, <a href="../man3/SSL_shutdown.html">SSL_shutdown(3)</a>, <a href="../man3/SSL_set_shutdown.html">SSL_set_shutdown(3)</a>, <a href="../man3/SSL_CTX_set_options.html">SSL_CTX_set_options(3)</a>, <a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CTX_set_client_cert_cb.html">SSL_CTX_set_client_cert_cb(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


