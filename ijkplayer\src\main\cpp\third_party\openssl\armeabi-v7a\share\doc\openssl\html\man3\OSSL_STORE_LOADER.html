<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_STORE_LOADER</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Types">Types</a></li>
      <li><a href="#Functions">Functions</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_STORE_LOADER, OSSL_STORE_LOADER_CTX, OSSL_STORE_LOADER_new, OSSL_STORE_LOADER_get0_engine, OSSL_STORE_LOADER_get0_scheme, OSSL_STORE_LOADER_set_open, OSSL_STORE_LOADER_set_ctrl, OSSL_STORE_LOADER_set_expect, OSSL_STORE_LOADER_set_find, OSSL_STORE_LOADER_set_load, OSSL_STORE_LOADER_set_eof, OSSL_STORE_LOADER_set_error, OSSL_STORE_LOADER_set_close, OSSL_STORE_LOADER_free, OSSL_STORE_register_loader, OSSL_STORE_unregister_loader, OSSL_STORE_open_fn, OSSL_STORE_ctrl_fn, OSSL_STORE_expect_fn, OSSL_STORE_find_fn, OSSL_STORE_load_fn, OSSL_STORE_eof_fn, OSSL_STORE_error_fn, OSSL_STORE_close_fn - Types and functions to manipulate, register and unregister STORE loaders for different URI schemes</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/store.h&gt;

typedef struct ossl_store_loader_st OSSL_STORE_LOADER;

OSSL_STORE_LOADER *OSSL_STORE_LOADER_new(ENGINE *e, const char *scheme);
const ENGINE *OSSL_STORE_LOADER_get0_engine(const OSSL_STORE_LOADER
                                            *store_loader);
const char *OSSL_STORE_LOADER_get0_scheme(const OSSL_STORE_LOADER
                                          *store_loader);

/* struct ossl_store_loader_ctx_st is defined differently by each loader */
typedef struct ossl_store_loader_ctx_st OSSL_STORE_LOADER_CTX;

typedef OSSL_STORE_LOADER_CTX *(*OSSL_STORE_open_fn)(const char *uri,
                                                     const UI_METHOD *ui_method,
                                                     void *ui_data);
int OSSL_STORE_LOADER_set_open(OSSL_STORE_LOADER *store_loader,
                               OSSL_STORE_open_fn store_open_function);
typedef int (*OSSL_STORE_ctrl_fn)(OSSL_STORE_LOADER_CTX *ctx, int cmd,
                                  va_list args);
int OSSL_STORE_LOADER_set_ctrl(OSSL_STORE_LOADER *store_loader,
                               OSSL_STORE_ctrl_fn store_ctrl_function);
typedef int (*OSSL_STORE_expect_fn)(OSSL_STORE_LOADER_CTX *ctx, int expected);
int OSSL_STORE_LOADER_set_expect(OSSL_STORE_LOADER *loader,
                                 OSSL_STORE_expect_fn expect_function);
typedef int (*OSSL_STORE_find_fn)(OSSL_STORE_LOADER_CTX *ctx,
                                  OSSL_STORE_SEARCH *criteria);
int OSSL_STORE_LOADER_set_find(OSSL_STORE_LOADER *loader,
                               OSSL_STORE_find_fn find_function);
typedef OSSL_STORE_INFO *(*OSSL_STORE_load_fn)(OSSL_STORE_LOADER_CTX *ctx,
                                               UI_METHOD *ui_method,
                                               void *ui_data);
int OSSL_STORE_LOADER_set_load(OSSL_STORE_LOADER *store_loader,
                               OSSL_STORE_load_fn store_load_function);
typedef int (*OSSL_STORE_eof_fn)(OSSL_STORE_LOADER_CTX *ctx);
int OSSL_STORE_LOADER_set_eof(OSSL_STORE_LOADER *store_loader,
                              OSSL_STORE_eof_fn store_eof_function);
typedef int (*OSSL_STORE_error_fn)(OSSL_STORE_LOADER_CTX *ctx);
int OSSL_STORE_LOADER_set_error(OSSL_STORE_LOADER *store_loader,
                                OSSL_STORE_error_fn store_error_function);
typedef int (*OSSL_STORE_close_fn)(OSSL_STORE_LOADER_CTX *ctx);
int OSSL_STORE_LOADER_set_close(OSSL_STORE_LOADER *store_loader,
                                OSSL_STORE_close_fn store_close_function);
void OSSL_STORE_LOADER_free(OSSL_STORE_LOADER *store_loader);

int OSSL_STORE_register_loader(OSSL_STORE_LOADER *loader);
OSSL_STORE_LOADER *OSSL_STORE_unregister_loader(const char *scheme);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>These functions help applications and engines to create loaders for schemes they support.</p>

<h2 id="Types">Types</h2>

<p><b>OSSL_STORE_LOADER</b> is the type to hold a loader. It contains a scheme and the functions needed to implement OSSL_STORE_open(), OSSL_STORE_load(), OSSL_STORE_eof(), OSSL_STORE_error() and OSSL_STORE_close() for this scheme.</p>

<p><b>OSSL_STORE_LOADER_CTX</b> is a type template, to be defined by each loader using <b>struct ossl_store_loader_ctx_st { ... }</b>.</p>

<p><b>OSSL_STORE_open_fn</b>, <b>OSSL_STORE_ctrl_fn</b>, <b>OSSL_STORE_expect_fn</b>, <b>OSSL_STORE_find_fn</b>, <b>OSSL_STORE_load_fn</b>, <b>OSSL_STORE_eof_fn</b>, and <b>OSSL_STORE_close_fn</b> are the function pointer types used within a STORE loader. The functions pointed at define the functionality of the given loader.</p>

<dl>

<dt id="OSSL_STORE_open_fn"><b>OSSL_STORE_open_fn</b></dt>
<dd>

<p>This function takes a URI and is expected to interpret it in the best manner possible according to the scheme the loader implements, it also takes a <b>UI_METHOD</b> and associated data, to be used any time something needs to be prompted for. Furthermore, this function is expected to initialize what needs to be initialized, to create a private data store (<b>OSSL_STORE_LOADER_CTX</b>, see above), and to return it. If something goes wrong, this function is expected to return NULL.</p>

</dd>
<dt id="OSSL_STORE_ctrl_fn"><b>OSSL_STORE_ctrl_fn</b></dt>
<dd>

<p>This function takes a <b>OSSL_STORE_LOADER_CTX</b> pointer, a command number <b>cmd</b> and a <b>va_list</b> <b>args</b> and is used to manipulate loader specific parameters.</p>

<p>Loader specific command numbers must begin at <b>OSSL_STORE_C_CUSTOM_START</b>. Any number below that is reserved for future globally known command numbers.</p>

<p>This function is expected to return 1 on success, 0 on error.</p>

</dd>
<dt id="OSSL_STORE_expect_fn"><b>OSSL_STORE_expect_fn</b></dt>
<dd>

<p>This function takes a <b>OSSL_STORE_LOADER_CTX</b> pointer and a <b>OSSL_STORE_INFO</b> identity <b>expected</b>, and is used to tell the loader what object type is expected. <b>expected</b> may be zero to signify that no specific object type is expected.</p>

<p>This function is expected to return 1 on success, 0 on error.</p>

</dd>
<dt id="OSSL_STORE_find_fn"><b>OSSL_STORE_find_fn</b></dt>
<dd>

<p>This function takes a <b>OSSL_STORE_LOADER_CTX</b> pointer and a <b>OSSL_STORE_SEARCH</b> search criterion, and is used to tell the loader what to search for.</p>

<p>When called with the loader context being <b>NULL</b>, this function is expected to return 1 if the loader supports the criterion, otherwise 0.</p>

<p>When called with the loader context being something other than <b>NULL</b>, this function is expected to return 1 on success, 0 on error.</p>

</dd>
<dt id="OSSL_STORE_load_fn"><b>OSSL_STORE_load_fn</b></dt>
<dd>

<p>This function takes a <b>OSSL_STORE_LOADER_CTX</b> pointer and a <b>UI_METHOD</b> with associated data. It&#39;s expected to load the next available data, mold it into a data structure that can be wrapped in a <b>OSSL_STORE_INFO</b> using one of the <a href="../man3/OSSL_STORE_INFO.html">OSSL_STORE_INFO(3)</a> functions. If no more data is available or an error occurs, this function is expected to return NULL. The <b>OSSL_STORE_eof_fn</b> and <b>OSSL_STORE_error_fn</b> functions must indicate if it was in fact the end of data or if an error occurred.</p>

<p>Note that this function retrieves <i>one</i> data item only.</p>

</dd>
<dt id="OSSL_STORE_eof_fn"><b>OSSL_STORE_eof_fn</b></dt>
<dd>

<p>This function takes a <b>OSSL_STORE_LOADER_CTX</b> pointer and is expected to return 1 to indicate that the end of available data has been reached. It is otherwise expected to return 0.</p>

</dd>
<dt id="OSSL_STORE_error_fn"><b>OSSL_STORE_error_fn</b></dt>
<dd>

<p>This function takes a <b>OSSL_STORE_LOADER_CTX</b> pointer and is expected to return 1 to indicate that an error occurred in a previous call to the <b>OSSL_STORE_load_fn</b> function. It is otherwise expected to return 0.</p>

</dd>
<dt id="OSSL_STORE_close_fn"><b>OSSL_STORE_close_fn</b></dt>
<dd>

<p>This function takes a <b>OSSL_STORE_LOADER_CTX</b> pointer and is expected to close or shut down what needs to be closed, and finally free the contents of the <b>OSSL_STORE_LOADER_CTX</b> pointer. It returns 1 on success and 0 on error.</p>

</dd>
</dl>

<h2 id="Functions">Functions</h2>

<p>OSSL_STORE_LOADER_new() creates a new <b>OSSL_STORE_LOADER</b>. It takes an <b>ENGINE</b> <b>e</b> and a string <b>scheme</b>. <b>scheme</b> must <i>always</i> be set. Both <b>e</b> and <b>scheme</b> are used as is and must therefore be alive as long as the created loader is.</p>

<p>OSSL_STORE_LOADER_get0_engine() returns the engine of the <b>store_loader</b>. OSSL_STORE_LOADER_get0_scheme() returns the scheme of the <b>store_loader</b>.</p>

<p>OSSL_STORE_LOADER_set_open() sets the opener function for the <b>store_loader</b>.</p>

<p>OSSL_STORE_LOADER_set_ctrl() sets the control function for the <b>store_loader</b>.</p>

<p>OSSL_STORE_LOADER_set_expect() sets the expect function for the <b>store_loader</b>.</p>

<p>OSSL_STORE_LOADER_set_load() sets the loader function for the <b>store_loader</b>.</p>

<p>OSSL_STORE_LOADER_set_eof() sets the end of file checker function for the <b>store_loader</b>.</p>

<p>OSSL_STORE_LOADER_set_close() sets the closing function for the <b>store_loader</b>.</p>

<p>OSSL_STORE_LOADER_free() frees the given <b>store_loader</b>.</p>

<p>OSSL_STORE_register_loader() register the given <b>store_loader</b> and thereby makes it available for use with OSSL_STORE_open(), OSSL_STORE_load(), OSSL_STORE_eof() and OSSL_STORE_close().</p>

<p>OSSL_STORE_unregister_loader() unregister the store loader for the given <b>scheme</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>The <b>file:</b> scheme has built in support.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The functions with the types <b>OSSL_STORE_open_fn</b>, <b>OSSL_STORE_ctrl_fn</b>, <b>OSSL_STORE_expect_fn</b>, <b>OSSL_STORE_load_fn</b>, <b>OSSL_STORE_eof_fn</b> and <b>OSSL_STORE_close_fn</b> have the same return values as OSSL_STORE_open(), OSSL_STORE_ctrl(), OSSL_STORE_expect(), OSSL_STORE_load(), OSSL_STORE_eof() and OSSL_STORE_close(), respectively.</p>

<p>OSSL_STORE_LOADER_new() returns a pointer to a <b>OSSL_STORE_LOADER</b> on success, or <b>NULL</b> on failure.</p>

<p>OSSL_STORE_LOADER_set_open(), OSSL_STORE_LOADER_set_ctrl(), OSSL_STORE_LOADER_set_load(), OSSL_STORE_LOADER_set_eof() and OSSL_STORE_LOADER_set_close() return 1 on success, or 0 on failure.</p>

<p>OSSL_STORE_register_loader() returns 1 on success, or 0 on failure.</p>

<p>OSSL_STORE_unregister_loader() returns the unregistered loader on success, or <b>NULL</b> on failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ossl_store.html">ossl_store(7)</a>, <a href="../man3/OSSL_STORE_open.html">OSSL_STORE_open(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>OSSL_STORE_LOADER(), OSSL_STORE_LOADER_CTX(), OSSL_STORE_LOADER_new(), OSSL_STORE_LOADER_set0_scheme(), OSSL_STORE_LOADER_set_open(), OSSL_STORE_LOADER_set_ctrl(), OSSL_STORE_LOADER_set_load(), OSSL_STORE_LOADER_set_eof(), OSSL_STORE_LOADER_set_close(), OSSL_STORE_LOADER_free(), OSSL_STORE_register_loader(), OSSL_STORE_unregister_loader(), OSSL_STORE_open_fn(), OSSL_STORE_ctrl_fn(), OSSL_STORE_load_fn(), OSSL_STORE_eof_fn() and OSSL_STORE_close_fn() were added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016-2019 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


