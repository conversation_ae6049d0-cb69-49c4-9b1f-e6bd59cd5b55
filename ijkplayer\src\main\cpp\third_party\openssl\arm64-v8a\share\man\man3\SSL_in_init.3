.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_IN_INIT 3"
.TH SSL_IN_INIT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_in_before,
SSL_in_init,
SSL_is_init_finished,
SSL_in_connect_init,
SSL_in_accept_init,
SSL_get_state
\&\- retrieve information about the handshake state machine
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_in_init(const SSL *s);
\& int SSL_in_before(const SSL *s);
\& int SSL_is_init_finished(const SSL *s);
\&
\& int SSL_in_connect_init(SSL *s);
\& int SSL_in_accept_init(SSL *s);
\&
\& OSSL_HANDSHAKE_STATE SSL_get_state(const SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_in_init()\fR returns 1 if the SSL/TLS state machine is currently processing or
awaiting handshake messages, or 0 otherwise.
.PP
\&\fBSSL_in_before()\fR returns 1 if no SSL/TLS handshake has yet been initiated, or 0
otherwise.
.PP
\&\fBSSL_is_init_finished()\fR returns 1 if the SSL/TLS connection is in a state where
fully protected application data can be transferred or 0 otherwise.
.PP
Note that in some circumstances (such as when early data is being transferred)
\&\fBSSL_in_init()\fR, \fBSSL_in_before()\fR and \fBSSL_is_init_finished()\fR can all return 0.
.PP
\&\fBSSL_in_connect_init()\fR returns 1 if \fBs\fR is acting as a client and \fBSSL_in_init()\fR
would return 1, or 0 otherwise.
.PP
\&\fBSSL_in_accept_init()\fR returns 1 if \fBs\fR is acting as a server and \fBSSL_in_init()\fR
would return 1, or 0 otherwise.
.PP
\&\fBSSL_in_connect_init()\fR and \fBSSL_in_accept_init()\fR are implemented as macros.
.PP
\&\fBSSL_get_state()\fR returns a value indicating the current state of the handshake
state machine. OSSL_HANDSHAKE_STATE is an enumerated type where each value
indicates a discrete state machine state. Note that future versions of OpenSSL
may define more states so applications should expect to receive unrecognised
state values. The naming format is made up of a number of elements as follows:
.PP
\&\fBprotocol\fR_ST_\fBrole\fR_\fBmessage\fR
.PP
\&\fBprotocol\fR is one of TLS or DTLS. DTLS is used where a state is specific to the
DTLS protocol. Otherwise TLS is used.
.PP
\&\fBrole\fR is one of CR, CW, SR or SW to indicate "client reading",
"client writing", "server reading" or "server writing" respectively.
.PP
\&\fBmessage\fR is the name of a handshake message that is being or has been sent, or
is being or has been processed.
.PP
Additionally there are some special states that do not conform to the above
format. These are:
.IP TLS_ST_BEFORE 4
.IX Item "TLS_ST_BEFORE"
No handshake messages have yet been been sent or received.
.IP TLS_ST_OK 4
.IX Item "TLS_ST_OK"
Handshake message sending/processing has completed.
.IP TLS_ST_EARLY_DATA 4
.IX Item "TLS_ST_EARLY_DATA"
Early data is being processed
.IP TLS_ST_PENDING_EARLY_DATA_END 4
.IX Item "TLS_ST_PENDING_EARLY_DATA_END"
Awaiting the end of early data processing
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_in_init()\fR, \fBSSL_in_before()\fR, \fBSSL_is_init_finished()\fR, \fBSSL_in_connect_init()\fR
and \fBSSL_in_accept_init()\fR return values as indicated above.
.PP
\&\fBSSL_get_state()\fR returns the current handshake state.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_read_early_data\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
