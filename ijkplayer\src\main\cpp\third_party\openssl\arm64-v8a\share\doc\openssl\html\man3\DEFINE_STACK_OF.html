<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>DEFINE_STACK_OF</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>DEFINE_STACK_OF, DEFINE_STACK_OF_CONST, DEFINE_SPECIAL_STACK_OF, DEFINE_SPECIAL_STACK_OF_CONST, sk_TYPE_num, sk_TYPE_value, sk_TYPE_new, sk_TYPE_new_null, sk_TYPE_reserve, sk_TYPE_free, sk_TYPE_zero, sk_TYPE_delete, sk_TYPE_delete_ptr, sk_TYPE_push, sk_TYPE_unshift, sk_TYPE_pop, sk_TYPE_shift, sk_TYPE_pop_free, sk_TYPE_insert, sk_TYPE_set, sk_TYPE_find, sk_TYPE_find_ex, sk_TYPE_sort, sk_TYPE_is_sorted, sk_TYPE_dup, sk_TYPE_deep_copy, sk_TYPE_set_cmp_func, sk_TYPE_new_reserve - stack container</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/safestack.h&gt;

STACK_OF(TYPE)
DEFINE_STACK_OF(TYPE)
DEFINE_STACK_OF_CONST(TYPE)
DEFINE_SPECIAL_STACK_OF(FUNCTYPE, TYPE)
DEFINE_SPECIAL_STACK_OF_CONST(FUNCTYPE, TYPE)

typedef int (*sk_TYPE_compfunc)(const TYPE *const *a, const TYPE *const *b);
typedef TYPE * (*sk_TYPE_copyfunc)(const TYPE *a);
typedef void (*sk_TYPE_freefunc)(TYPE *a);

int sk_TYPE_num(const STACK_OF(TYPE) *sk);
TYPE *sk_TYPE_value(const STACK_OF(TYPE) *sk, int idx);
STACK_OF(TYPE) *sk_TYPE_new(sk_TYPE_compfunc compare);
STACK_OF(TYPE) *sk_TYPE_new_null(void);
int sk_TYPE_reserve(STACK_OF(TYPE) *sk, int n);
void sk_TYPE_free(const STACK_OF(TYPE) *sk);
void sk_TYPE_zero(const STACK_OF(TYPE) *sk);
TYPE *sk_TYPE_delete(STACK_OF(TYPE) *sk, int i);
TYPE *sk_TYPE_delete_ptr(STACK_OF(TYPE) *sk, TYPE *ptr);
int sk_TYPE_push(STACK_OF(TYPE) *sk, const TYPE *ptr);
int sk_TYPE_unshift(STACK_OF(TYPE) *sk, const TYPE *ptr);
TYPE *sk_TYPE_pop(STACK_OF(TYPE) *sk);
TYPE *sk_TYPE_shift(STACK_OF(TYPE) *sk);
void sk_TYPE_pop_free(STACK_OF(TYPE) *sk, sk_TYPE_freefunc freefunc);
int sk_TYPE_insert(STACK_OF(TYPE) *sk, TYPE *ptr, int idx);
TYPE *sk_TYPE_set(STACK_OF(TYPE) *sk, int idx, const TYPE *ptr);
int sk_TYPE_find(STACK_OF(TYPE) *sk, TYPE *ptr);
int sk_TYPE_find_ex(STACK_OF(TYPE) *sk, TYPE *ptr);
void sk_TYPE_sort(const STACK_OF(TYPE) *sk);
int sk_TYPE_is_sorted(const STACK_OF(TYPE) *sk);
STACK_OF(TYPE) *sk_TYPE_dup(const STACK_OF(TYPE) *sk);
STACK_OF(TYPE) *sk_TYPE_deep_copy(const STACK_OF(TYPE) *sk,
                                  sk_TYPE_copyfunc copyfunc,
                                  sk_TYPE_freefunc freefunc);
sk_TYPE_compfunc (*sk_TYPE_set_cmp_func(STACK_OF(TYPE) *sk,
                                        sk_TYPE_compfunc compare));
STACK_OF(TYPE) *sk_TYPE_new_reserve(sk_TYPE_compfunc compare, int n);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Applications can create and use their own stacks by placing any of the macros described below in a header file. These macros define typesafe inline functions that wrap around the utility <b>OPENSSL_sk_</b> API. In the description here, <i>TYPE</i> is used as a placeholder for any of the OpenSSL datatypes, such as <i>X509</i>.</p>

<p>STACK_OF() returns the name for a stack of the specified <b>TYPE</b>. DEFINE_STACK_OF() creates set of functions for a stack of <b>TYPE</b>. This will mean that type <b>TYPE</b> is stored in each stack, the type is referenced by STACK_OF(TYPE) and each function name begins with <i>sk_TYPE_</i>. For example:</p>

<pre><code>TYPE *sk_TYPE_value(STACK_OF(TYPE) *sk, int idx);</code></pre>

<p>DEFINE_STACK_OF_CONST() is identical to DEFINE_STACK_OF() except each element is constant. For example:</p>

<pre><code>const TYPE *sk_TYPE_value(STACK_OF(TYPE) *sk, int idx);</code></pre>

<p>DEFINE_SPECIAL_STACK_OF() defines a stack of <b>TYPE</b> but each function uses <b>FUNCNAME</b> in the function name. For example:</p>

<pre><code>TYPE *sk_FUNCNAME_value(STACK_OF(TYPE) *sk, int idx);</code></pre>

<p>DEFINE_SPECIAL_STACK_OF_CONST() is similar except that each element is constant:</p>

<pre><code>const TYPE *sk_FUNCNAME_value(STACK_OF(TYPE) *sk, int idx);</code></pre>

<p>sk_TYPE_num() returns the number of elements in <b>sk</b> or -1 if <b>sk</b> is <b>NULL</b>.</p>

<p>sk_TYPE_value() returns element <b>idx</b> in <b>sk</b>, where <b>idx</b> starts at zero. If <b>idx</b> is out of range then <b>NULL</b> is returned.</p>

<p>sk_TYPE_new() allocates a new empty stack using comparison function <b>compare</b>. If <b>compare</b> is <b>NULL</b> then no comparison function is used. This function is equivalent to sk_TYPE_new_reserve(compare, 0).</p>

<p>sk_TYPE_new_null() allocates a new empty stack with no comparison function. This function is equivalent to sk_TYPE_new_reserve(NULL, 0).</p>

<p>sk_TYPE_reserve() allocates additional memory in the <b>sk</b> structure such that the next <b>n</b> calls to sk_TYPE_insert(), sk_TYPE_push() or sk_TYPE_unshift() will not fail or cause memory to be allocated or reallocated. If <b>n</b> is zero, any excess space allocated in the <b>sk</b> structure is freed. On error <b>sk</b> is unchanged.</p>

<p>sk_TYPE_new_reserve() allocates a new stack. The new stack will have additional memory allocated to hold <b>n</b> elements if <b>n</b> is positive. The next <b>n</b> calls to sk_TYPE_insert(), sk_TYPE_push() or sk_TYPE_unshift() will not fail or cause memory to be allocated or reallocated. If <b>n</b> is zero or less than zero, no memory is allocated. sk_TYPE_new_reserve() also sets the comparison function <b>compare</b> to the newly created stack. If <b>compare</b> is <b>NULL</b> then no comparison function is used.</p>

<p>sk_TYPE_set_cmp_func() sets the comparison function of <b>sk</b> to <b>compare</b>. The previous comparison function is returned or <b>NULL</b> if there was no previous comparison function.</p>

<p>sk_TYPE_free() frees up the <b>sk</b> structure. It does <b>not</b> free up any elements of <b>sk</b>. After this call <b>sk</b> is no longer valid.</p>

<p>sk_TYPE_zero() sets the number of elements in <b>sk</b> to zero. It does not free <b>sk</b> so after this call <b>sk</b> is still valid.</p>

<p>sk_TYPE_pop_free() frees up all elements of <b>sk</b> and <b>sk</b> itself. The free function freefunc() is called on each element to free it.</p>

<p>sk_TYPE_delete() deletes element <b>i</b> from <b>sk</b>. It returns the deleted element or <b>NULL</b> if <b>i</b> is out of range.</p>

<p>sk_TYPE_delete_ptr() deletes element matching <b>ptr</b> from <b>sk</b>. It returns the deleted element or <b>NULL</b> if no element matching <b>ptr</b> was found.</p>

<p>sk_TYPE_insert() inserts <b>ptr</b> into <b>sk</b> at position <b>idx</b>. Any existing elements at or after <b>idx</b> are moved downwards. If <b>idx</b> is out of range the new element is appended to <b>sk</b>. sk_TYPE_insert() either returns the number of elements in <b>sk</b> after the new element is inserted or zero if an error (such as memory allocation failure) occurred.</p>

<p>sk_TYPE_push() appends <b>ptr</b> to <b>sk</b> it is equivalent to:</p>

<pre><code>sk_TYPE_insert(sk, ptr, -1);</code></pre>

<p>sk_TYPE_unshift() inserts <b>ptr</b> at the start of <b>sk</b> it is equivalent to:</p>

<pre><code>sk_TYPE_insert(sk, ptr, 0);</code></pre>

<p>sk_TYPE_pop() returns and removes the last element from <b>sk</b>.</p>

<p>sk_TYPE_shift() returns and removes the first element from <b>sk</b>.</p>

<p>sk_TYPE_set() sets element <b>idx</b> of <b>sk</b> to <b>ptr</b> replacing the current element. The new element value is returned or <b>NULL</b> if an error occurred: this will only happen if <b>sk</b> is <b>NULL</b> or <b>idx</b> is out of range.</p>

<p>sk_TYPE_find() searches <b>sk</b> for the element <b>ptr</b>. In the case where no comparison function has been specified, the function performs a linear search for a pointer equal to <b>ptr</b>. The index of the first matching element is returned or <b>-1</b> if there is no match. In the case where a comparison function has been specified, <b>sk</b> is sorted then sk_TYPE_find() returns the index of a matching element or <b>-1</b> if there is no match. Note that, in this case, the matching element returned is not guaranteed to be the first; the comparison function will usually compare the values pointed to rather than the pointers themselves and the order of elements in <b>sk</b> could change.</p>

<p>sk_TYPE_find_ex() operates like sk_TYPE_find() except when a comparison function has been specified and no matching element is found. Instead of returning <b>-1</b>, sk_TYPE_find_ex() returns the index of the element either before or after the location where <b>ptr</b> would be if it were present in <b>sk</b>.</p>

<p>sk_TYPE_sort() sorts <b>sk</b> using the supplied comparison function.</p>

<p>sk_TYPE_is_sorted() returns <b>1</b> if <b>sk</b> is sorted and <b>0</b> otherwise.</p>

<p>sk_TYPE_dup() returns a copy of <b>sk</b>. Note the pointers in the copy are identical to the original.</p>

<p>sk_TYPE_deep_copy() returns a new stack where each element has been copied. Copying is performed by the supplied copyfunc() and freeing by freefunc(). The function freefunc() is only called if an error occurs.</p>

<h1 id="NOTES">NOTES</h1>

<p>Care should be taken when accessing stacks in multi-threaded environments. Any operation which increases the size of a stack such as sk_TYPE_insert() or sk_push() can &quot;grow&quot; the size of an internal array and cause race conditions if the same stack is accessed in a different thread. Operations such as sk_find() and sk_sort() can also reorder the stack.</p>

<p>Any comparison function supplied should use a metric suitable for use in a binary search operation. That is it should return zero, a positive or negative value if <b>a</b> is equal to, greater than or less than <b>b</b> respectively.</p>

<p>Care should be taken when checking the return values of the functions sk_TYPE_find() and sk_TYPE_find_ex(). They return an index to the matching element. In particular <b>0</b> indicates a matching first element. A failed search is indicated by a <b>-1</b> return value.</p>

<p>STACK_OF(), DEFINE_STACK_OF(), DEFINE_STACK_OF_CONST(), and DEFINE_SPECIAL_STACK_OF() are implemented as macros.</p>

<p>The underlying utility <b>OPENSSL_sk_</b> API should not be used directly. It defines these functions: OPENSSL_sk_deep_copy(), OPENSSL_sk_delete(), OPENSSL_sk_delete_ptr(), OPENSSL_sk_dup(), OPENSSL_sk_find(), OPENSSL_sk_find_ex(), OPENSSL_sk_free(), OPENSSL_sk_insert(), OPENSSL_sk_is_sorted(), OPENSSL_sk_new(), OPENSSL_sk_new_null(), OPENSSL_sk_num(), OPENSSL_sk_pop(), OPENSSL_sk_pop_free(), OPENSSL_sk_push(), OPENSSL_sk_reserve(), OPENSSL_sk_set(), OPENSSL_sk_set_cmp_func(), OPENSSL_sk_shift(), OPENSSL_sk_sort(), OPENSSL_sk_unshift(), OPENSSL_sk_value(), OPENSSL_sk_zero().</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>sk_TYPE_num() returns the number of elements in the stack or <b>-1</b> if the passed stack is <b>NULL</b>.</p>

<p>sk_TYPE_value() returns a pointer to a stack element or <b>NULL</b> if the index is out of range.</p>

<p>sk_TYPE_new(), sk_TYPE_new_null() and sk_TYPE_new_reserve() return an empty stack or <b>NULL</b> if an error occurs.</p>

<p>sk_TYPE_reserve() returns <b>1</b> on successful allocation of the required memory or <b>0</b> on error.</p>

<p>sk_TYPE_set_cmp_func() returns the old comparison function or <b>NULL</b> if there was no old comparison function.</p>

<p>sk_TYPE_free(), sk_TYPE_zero(), sk_TYPE_pop_free() and sk_TYPE_sort() do not return values.</p>

<p>sk_TYPE_pop(), sk_TYPE_shift(), sk_TYPE_delete() and sk_TYPE_delete_ptr() return a pointer to the deleted element or <b>NULL</b> on error.</p>

<p>sk_TYPE_insert(), sk_TYPE_push() and sk_TYPE_unshift() return the total number of elements in the stack and 0 if an error occurred. sk_TYPE_push() further returns -1 if <b>sk</b> is <b>NULL</b>.</p>

<p>sk_TYPE_set() returns a pointer to the replacement element or <b>NULL</b> on error.</p>

<p>sk_TYPE_find() and sk_TYPE_find_ex() return an index to the found element or <b>-1</b> on error.</p>

<p>sk_TYPE_is_sorted() returns <b>1</b> if the stack is sorted and <b>0</b> if it is not.</p>

<p>sk_TYPE_dup() and sk_TYPE_deep_copy() return a pointer to the copy of the stack.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>Before OpenSSL 1.1.0, this was implemented via macros and not inline functions and was not a public API.</p>

<p>sk_TYPE_reserve() and sk_TYPE_new_reserve() were added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2017 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


