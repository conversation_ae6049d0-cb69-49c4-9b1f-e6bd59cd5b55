.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_STRING_PRINT_EX 3"
.TH ASN1_STRING_PRINT_EX 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_tag2str, ASN1_STRING_print_ex, ASN1_STRING_print_ex_fp, ASN1_STRING_print
\&\- ASN1_STRING output routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1.h>
\&
\& int ASN1_STRING_print_ex(BIO *out, const ASN1_STRING *str, unsigned long flags);
\& int ASN1_STRING_print_ex_fp(FILE *fp, const ASN1_STRING *str, unsigned long flags);
\& int ASN1_STRING_print(BIO *out, const ASN1_STRING *str);
\&
\& const char *ASN1_tag2str(int tag);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions output an \fBASN1_STRING\fR structure. \fBASN1_STRING\fR is used to
represent all the ASN1 string types.
.PP
\&\fBASN1_STRING_print_ex()\fR outputs \fBstr\fR to \fBout\fR, the format is determined by
the options \fBflags\fR. \fBASN1_STRING_print_ex_fp()\fR is identical except it outputs
to \fBfp\fR instead.
.PP
\&\fBASN1_STRING_print()\fR prints \fBstr\fR to \fBout\fR but using a different format to
\&\fBASN1_STRING_print_ex()\fR. It replaces unprintable characters (other than CR, LF)
with '.'.
.PP
\&\fBASN1_tag2str()\fR returns a human-readable name of the specified ASN.1 \fBtag\fR.
.SH NOTES
.IX Header "NOTES"
\&\fBASN1_STRING_print()\fR is a deprecated function which should be avoided; use
\&\fBASN1_STRING_print_ex()\fR instead.
.PP
Although there are a large number of options frequently \fBASN1_STRFLGS_RFC2253\fR is
suitable, or on UTF8 terminals \fBASN1_STRFLGS_RFC2253 & ~ASN1_STRFLGS_ESC_MSB\fR.
.PP
The complete set of supported options for \fBflags\fR is listed below.
.PP
Various characters can be escaped. If \fBASN1_STRFLGS_ESC_2253\fR is set the characters
determined by RFC2253 are escaped. If \fBASN1_STRFLGS_ESC_CTRL\fR is set control
characters are escaped. If \fBASN1_STRFLGS_ESC_MSB\fR is set characters with the
MSB set are escaped: this option should \fBnot\fR be used if the terminal correctly
interprets UTF8 sequences.
.PP
Escaping takes several forms.
.PP
If the character being escaped is a 16 bit character then the form "\eUXXXX" is used
using exactly four characters for the hex representation. If it is 32 bits then
"\eWXXXXXXXX" is used using eight characters of its hex representation. These forms
will only be used if UTF8 conversion is not set (see below).
.PP
Printable characters are normally escaped using the backslash '\e' character. If
\&\fBASN1_STRFLGS_ESC_QUOTE\fR is set then the whole string is instead surrounded by
double quote characters: this is arguably more readable than the backslash
notation. Other characters use the "\eXX" using exactly two characters of the hex
representation.
.PP
If \fBASN1_STRFLGS_UTF8_CONVERT\fR is set then characters are converted to UTF8
format first. If the terminal supports the display of UTF8 sequences then this
option will correctly display multi byte characters.
.PP
If \fBASN1_STRFLGS_IGNORE_TYPE\fR is set then the string type is not interpreted at
all: everything is assumed to be one byte per character. This is primarily for
debugging purposes and can result in confusing output in multi character strings.
.PP
If \fBASN1_STRFLGS_SHOW_TYPE\fR is set then the string type itself is printed out
before its value (for example "BMPSTRING"), this actually uses \fBASN1_tag2str()\fR.
.PP
The content of a string instead of being interpreted can be "dumped": this just
outputs the value of the string using the form #XXXX using hex format for each
octet.
.PP
If \fBASN1_STRFLGS_DUMP_ALL\fR is set then any type is dumped.
.PP
Normally non character string types (such as OCTET STRING) are assumed to be
one byte per character, if \fBASN1_STRFLGS_DUMP_UNKNOWN\fR is set then they will
be dumped instead.
.PP
When a type is dumped normally just the content octets are printed, if
\&\fBASN1_STRFLGS_DUMP_DER\fR is set then the complete encoding is dumped
instead (including tag and length octets).
.PP
\&\fBASN1_STRFLGS_RFC2253\fR includes all the flags required by RFC2253. It is
equivalent to:
 ASN1_STRFLGS_ESC_2253 | ASN1_STRFLGS_ESC_CTRL | ASN1_STRFLGS_ESC_MSB |
 ASN1_STRFLGS_UTF8_CONVERT | ASN1_STRFLGS_DUMP_UNKNOWN ASN1_STRFLGS_DUMP_DER
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASN1_STRING_print_ex()\fR and \fBASN1_STRING_print_ex_fp()\fR return the number of
characters written or \-1 if an error occurred.
.PP
\&\fBASN1_STRING_print()\fR returns 1 on success or 0 on error.
.PP
\&\fBASN1_tag2str()\fR returns a human-readable name of the specified ASN.1 \fBtag\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBASN1_tag2str\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
