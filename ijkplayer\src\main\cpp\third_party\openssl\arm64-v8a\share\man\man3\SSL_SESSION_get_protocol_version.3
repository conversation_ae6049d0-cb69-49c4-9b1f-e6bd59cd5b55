.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SESSION_GET_PROTOCOL_VERSION 3"
.TH SSL_SESSION_GET_PROTOCOL_VERSION 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_SESSION_get_protocol_version,
SSL_SESSION_set_protocol_version
\&\- get and set the session protocol version
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_SESSION_get_protocol_version(const SSL_SESSION *s);
\& int SSL_SESSION_set_protocol_version(SSL_SESSION *s, int version);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_SESSION_get_protocol_version()\fR returns the protocol version number used
by session \fBs\fR.
.PP
\&\fBSSL_SESSION_set_protocol_version()\fR sets the protocol version associated with the
SSL_SESSION object \fBs\fR to the value \fBversion\fR. This value should be a version
constant such as \fBTLS1_3_VERSION\fR etc. For example, this could be used to set
up a session based PSK (see \fBSSL_CTX_set_psk_use_session_callback\fR\|(3)).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_SESSION_get_protocol_version()\fR returns a number indicating the protocol
version used for the session; this number matches the constants \fIe.g.\fR
\&\fBTLS1_VERSION\fR, \fBTLS1_2_VERSION\fR or \fBTLS1_3_VERSION\fR.
.PP
Note that the \fBSSL_SESSION_get_protocol_version()\fR function
does \fBnot\fR perform a null check on the provided session \fBs\fR pointer.
.PP
\&\fBSSL_SESSION_set_protocol_version()\fR returns 1 on success or 0 on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_set_psk_use_session_callback\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_SESSION_get_protocol_version()\fR function was added in OpenSSL 1.1.0.
The \fBSSL_SESSION_set_protocol_version()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
