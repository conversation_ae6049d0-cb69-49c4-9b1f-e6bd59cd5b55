.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_TLSEXT_USE_SRTP 3"
.TH SSL_CTX_SET_TLSEXT_USE_SRTP 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_tlsext_use_srtp,
SSL_set_tlsext_use_srtp,
SSL_get_srtp_profiles,
SSL_get_selected_srtp_profile
\&\- Configure and query SRTP support
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/srtp.h>
\&
\& int SSL_CTX_set_tlsext_use_srtp(SSL_CTX *ctx, const char *profiles);
\& int SSL_set_tlsext_use_srtp(SSL *ssl, const char *profiles);
\&
\& STACK_OF(SRTP_PROTECTION_PROFILE) *SSL_get_srtp_profiles(SSL *ssl);
\& SRTP_PROTECTION_PROFILE *SSL_get_selected_srtp_profile(SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
SRTP is the Secure Real-Time Transport Protocol. OpenSSL implements support for
the "use_srtp" DTLS extension defined in RFC5764. This provides a mechanism for
establishing SRTP keying material, algorithms and parameters using DTLS. This
capability may be used as part of an implementation that conforms to RFC5763.
OpenSSL does not implement SRTP itself or RFC5763. Note that OpenSSL does not
support the use of SRTP Master Key Identifiers (MKIs). Also note that this
extension is only supported in DTLS. Any SRTP configuration will be ignored if a
TLS connection is attempted.
.PP
An OpenSSL client wishing to send the "use_srtp" extension should call
\&\fBSSL_CTX_set_tlsext_use_srtp()\fR to set its use for all SSL objects subsequently
created from an SSL_CTX. Alternatively a client may call
\&\fBSSL_set_tlsext_use_srtp()\fR to set its use for an individual SSL object. The
\&\fBprofiles\fR parameters should point to a NUL-terminated, colon delimited list of
SRTP protection profile names.
.PP
The currently supported protection profile names are:
.IP SRTP_AES128_CM_SHA1_80 4
.IX Item "SRTP_AES128_CM_SHA1_80"
This corresponds to SRTP_AES128_CM_HMAC_SHA1_80 defined in RFC5764.
.IP SRTP_AES128_CM_SHA1_32 4
.IX Item "SRTP_AES128_CM_SHA1_32"
This corresponds to SRTP_AES128_CM_HMAC_SHA1_32 defined in RFC5764.
.IP SRTP_AEAD_AES_128_GCM 4
.IX Item "SRTP_AEAD_AES_128_GCM"
This corresponds to the profile of the same name defined in RFC7714.
.IP SRTP_AEAD_AES_256_GCM 4
.IX Item "SRTP_AEAD_AES_256_GCM"
This corresponds to the profile of the same name defined in RFC7714.
.PP
Supplying an unrecognised protection profile name will result in an error.
.PP
An OpenSSL server wishing to support the "use_srtp" extension should also call
\&\fBSSL_CTX_set_tlsext_use_srtp()\fR or \fBSSL_set_tlsext_use_srtp()\fR to indicate the
protection profiles that it is willing to negotiate.
.PP
The currently configured list of protection profiles for either a client or a
server can be obtained by calling \fBSSL_get_srtp_profiles()\fR. This returns a stack
of SRTP_PROTECTION_PROFILE objects. The memory pointed to in the return value of
this function should not be freed by the caller.
.PP
After a handshake has been completed the negotiated SRTP protection profile (if
any) can be obtained (on the client or the server) by calling
\&\fBSSL_get_selected_srtp_profile()\fR. This function will return NULL if no SRTP
protection profile was negotiated. The memory returned from this function should
not be freed by the caller.
.PP
If an SRTP protection profile has been successfully negotiated then the SRTP
keying material (on both the client and server) should be obtained via a call to
\&\fBSSL_export_keying_material\fR\|(3). This call should provide a label value of
"EXTRACTOR\-dtls_srtp" and a NULL context value (use_context is 0). The total
length of keying material obtained should be equal to two times the sum of the
master key length and the salt length as defined for the protection profile in
use. This provides the client write master key, the server write master key, the
client write master salt and the server write master salt in that order.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_tlsext_use_srtp()\fR and \fBSSL_set_tlsext_use_srtp()\fR return 0 on success
or 1 on error.
.PP
\&\fBSSL_get_srtp_profiles()\fR returns a stack of SRTP_PROTECTION_PROFILE objects on
success or NULL on error or if no protection profiles have been configured.
.PP
\&\fBSSL_get_selected_srtp_profile()\fR returns a pointer to an SRTP_PROTECTION_PROFILE
object if one has been negotiated or NULL otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_export_keying_material\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
