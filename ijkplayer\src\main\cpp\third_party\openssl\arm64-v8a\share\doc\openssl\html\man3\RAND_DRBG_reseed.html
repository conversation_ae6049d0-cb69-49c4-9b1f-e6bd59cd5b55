<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>RAND_DRBG_reseed</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>RAND_DRBG_reseed, RAND_DRBG_set_reseed_interval, RAND_DRBG_set_reseed_time_interval, RAND_DRBG_set_reseed_defaults - reseed a RAND_DRBG instance</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/rand_drbg.h&gt;

int RAND_DRBG_reseed(RAND_DRBG *drbg,
                     const unsigned char *adin, size_t adinlen,
                     int prediction_resistance);

int RAND_DRBG_set_reseed_interval(RAND_DRBG *drbg,
                                  unsigned int interval);

int RAND_DRBG_set_reseed_time_interval(RAND_DRBG *drbg,
                                       time_t interval);

int RAND_DRBG_set_reseed_defaults(
                                  unsigned int master_reseed_interval,
                                  unsigned int slave_reseed_interval,
                                  time_t master_reseed_time_interval,
                                  time_t slave_reseed_time_interval
                                  );</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>RAND_DRBG_reseed() reseeds the given <b>drbg</b>, obtaining entropy input from its entropy source and mixing in the specified additional data provided in the buffer <b>adin</b> of length <b>adinlen</b>. The additional data can be omitted by setting <b>adin</b> to NULL and <b>adinlen</b> to 0. An immediate reseeding from a live entropy source can be requested by setting the <b>prediction_resistance</b> flag to 1. This feature is not implemented yet, so reseeding with prediction resistance requested will always fail.</p>

<p>RAND_DRBG_set_reseed_interval() sets the reseed interval of the <b>drbg</b>, which is the maximum allowed number of generate requests between consecutive reseedings. If <b>interval</b> &gt; 0, then the <b>drbg</b> will reseed automatically whenever the number of generate requests since its last seeding exceeds the given reseed interval. If <b>interval</b> == 0, then this feature is disabled.</p>

<p>RAND_DRBG_set_reseed_time_interval() sets the reseed time interval of the <b>drbg</b>, which is the maximum allowed number of seconds between consecutive reseedings. If <b>interval</b> &gt; 0, then the <b>drbg</b> will reseed automatically whenever the elapsed time since its last reseeding exceeds the given reseed time interval. If <b>interval</b> == 0, then this feature is disabled.</p>

<p>RAND_DRBG_set_reseed_defaults() sets the default values for the reseed interval (<b>master_reseed_interval</b> and <b>slave_reseed_interval</b>) and the reseed time interval (<b>master_reseed_time_interval</b> and <b>slave_reseed_tme_interval</b>) of DRBG instances. The default values are set independently for master DRBG instances (which don&#39;t have a parent) and slave DRBG instances (which are chained to a parent DRBG).</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>RAND_DRBG_reseed(), RAND_DRBG_set_reseed_interval(), and RAND_DRBG_set_reseed_time_interval(), return 1 on success, 0 on failure.</p>

<h1 id="NOTES">NOTES</h1>

<p>The default OpenSSL random generator is already set up for automatic reseeding, so in general it is not necessary to reseed it explicitly, or to modify its reseeding thresholds.</p>

<p>Normally, the entropy input for seeding a DRBG is either obtained from a trusted os entropy source or from a parent DRBG instance, which was seeded (directly or indirectly) from a trusted os entropy source. In exceptional cases it is possible to replace the reseeding mechanism entirely by providing application defined callbacks using RAND_DRBG_set_callbacks().</p>

<p>The reseeding default values are applied only during creation of a DRBG instance. To ensure that they are applied to the global and thread-local DRBG instances (&lt;master&gt;, resp. &lt;public&gt; and &lt;private&gt;), it is necessary to call RAND_DRBG_set_reseed_defaults() before creating any thread and before calling any cryptographic routines that obtain random data directly or indirectly.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/RAND_DRBG_generate.html">RAND_DRBG_generate(3)</a>, <a href="../man3/RAND_DRBG_bytes.html">RAND_DRBG_bytes(3)</a>, <a href="../man3/RAND_DRBG_set_callbacks.html">RAND_DRBG_set_callbacks(3)</a>. <a href="../man7/RAND_DRBG.html">RAND_DRBG(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The RAND_DRBG functions were added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2019 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


