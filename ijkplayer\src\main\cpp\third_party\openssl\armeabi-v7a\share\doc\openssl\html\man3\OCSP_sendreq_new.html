<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OCSP_sendreq_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OCSP_sendreq_new, OCSP_sendreq_nbio, OCSP_REQ_CTX_free, OCSP_set_max_response_length, OCSP_REQ_CTX_add1_header, OCSP_REQ_CTX_set1_req, OCSP_sendreq_bio, OCSP_REQ_CTX_i2d - OCSP responder query functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ocsp.h&gt;

OCSP_REQ_CTX *OCSP_sendreq_new(BIO *io, const char *path, OCSP_REQUEST *req,
                               int maxline);

int OCSP_sendreq_nbio(OCSP_RESPONSE **presp, OCSP_REQ_CTX *rctx);

void OCSP_REQ_CTX_free(OCSP_REQ_CTX *rctx);

void OCSP_set_max_response_length(OCSP_REQ_CTX *rctx, unsigned long len);

int OCSP_REQ_CTX_add1_header(OCSP_REQ_CTX *rctx,
                             const char *name, const char *value);

int OCSP_REQ_CTX_set1_req(OCSP_REQ_CTX *rctx, OCSP_REQUEST *req);

OCSP_RESPONSE *OCSP_sendreq_bio(BIO *io, const char *path, OCSP_REQUEST *req);

int OCSP_REQ_CTX_i2d(OCSP_REQ_CTX *rctx, const char *content_type,
                     const ASN1_ITEM *it, ASN1_VALUE *req);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The function OCSP_sendreq_new() returns an <b>OCSP_CTX</b> structure using the responder <b>io</b>, the URL path <b>path</b>, the OCSP request <b>req</b> and with a response header maximum line length of <b>maxline</b>. If <b>maxline</b> is zero a default value of 4k is used. The OCSP request <b>req</b> may be set to <b>NULL</b> and provided later if required.</p>

<p>OCSP_sendreq_nbio() performs nonblocking I/O on the OCSP request context <b>rctx</b>. When the operation is complete it returns the response in <b>*presp</b>.</p>

<p>OCSP_REQ_CTX_free() frees up the OCSP context <b>rctx</b>.</p>

<p>OCSP_set_max_response_length() sets the maximum response length for <b>rctx</b> to <b>len</b>. If the response exceeds this length an error occurs. If not set a default value of 100k is used.</p>

<p>OCSP_REQ_CTX_add1_header() adds header <b>name</b> with value <b>value</b> to the context <b>rctx</b>. It can be called more than once to add multiple headers. It <b>MUST</b> be called before any calls to OCSP_sendreq_nbio(). The <b>req</b> parameter in the initial to OCSP_sendreq_new() call MUST be set to <b>NULL</b> if additional headers are set.</p>

<p>OCSP_REQ_CTX_set1_req() sets the OCSP request in <b>rctx</b> to <b>req</b>. This function should be called after any calls to OCSP_REQ_CTX_add1_header(). OCSP_REQ_CTX_set1_req(rctx, req) is equivalent to the following:</p>

<pre><code>OCSP_REQ_CTX_i2d(rctx, &quot;application/ocsp-request&quot;,
                       ASN1_ITEM_rptr(OCSP_REQUEST), (ASN1_VALUE *)req)</code></pre>

<p>OCSP_REQ_CTX_i2d() sets the request context <b>rctx</b> to have the request <b>req</b>, which has the ASN.1 type <b>it</b>. The <b>content_type</b>, if not NULL, will be included in the HTTP request. The function should be called after all other headers have already been added.</p>

<p>OCSP_sendreq_bio() performs an OCSP request using the responder <b>io</b>, the URL path <b>path</b>, and the OCSP request <b>req</b> with a response header maximum line length 4k. It waits indefinitely on a response.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OCSP_sendreq_new() returns a valid <b>OCSP_REQ_CTX</b> structure or <b>NULL</b> if an error occurred.</p>

<p>OCSP_sendreq_nbio() returns <b>1</b> if the operation was completed successfully, <b>-1</b> if the operation should be retried and <b>0</b> if an error occurred.</p>

<p>OCSP_REQ_CTX_add1_header(), OCSP_REQ_CTX_set1_req(), and OCSP_REQ_CTX_i2d() return <b>1</b> for success and <b>0</b> for failure.</p>

<p>OCSP_sendreq_bio() returns the <b>OCSP_RESPONSE</b> structure sent by the responder or <b>NULL</b> if an error occurred.</p>

<p>OCSP_REQ_CTX_free() and OCSP_set_max_response_length() do not return values.</p>

<h1 id="NOTES">NOTES</h1>

<p>These functions only perform a minimal HTTP query to a responder. If an application wishes to support more advanced features it should use an alternative more complete HTTP library.</p>

<p>Currently only HTTP POST queries to responders are supported.</p>

<p>The arguments to OCSP_sendreq_new() correspond to the components of the URL. For example if the responder URL is <b>http://ocsp.com/ocspreq</b> the BIO <b>io</b> should be connected to host <b>ocsp.com</b> on port 80 and <b>path</b> should be set to <b>&quot;/ocspreq&quot;</b></p>

<p>The headers added with OCSP_REQ_CTX_add1_header() are of the form &quot;<b>name</b>: <b>value</b>&quot; or just &quot;<b>name</b>&quot; if <b>value</b> is <b>NULL</b>. So to add a Host header for <b>ocsp.com</b> you would call:</p>

<pre><code>OCSP_REQ_CTX_add1_header(ctx, &quot;Host&quot;, &quot;ocsp.com&quot;);</code></pre>

<p>If OCSP_sendreq_nbio() indicates an operation should be retried the corresponding BIO can be examined to determine which operation (read or write) should be retried and appropriate action taken (for example a select() call on the underlying socket).</p>

<p>OCSP_sendreq_bio() does not support retries and so cannot handle nonblocking I/O efficiently. It is retained for compatibility and its use in new applications is not recommended.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/crypto.html">crypto(7)</a>, <a href="../man3/OCSP_cert_to_id.html">OCSP_cert_to_id(3)</a>, <a href="../man3/OCSP_request_add1_nonce.html">OCSP_request_add1_nonce(3)</a>, <a href="../man3/OCSP_REQUEST_new.html">OCSP_REQUEST_new(3)</a>, <a href="../man3/OCSP_resp_find_status.html">OCSP_resp_find_status(3)</a>, <a href="../man3/OCSP_response_status.html">OCSP_response_status(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


