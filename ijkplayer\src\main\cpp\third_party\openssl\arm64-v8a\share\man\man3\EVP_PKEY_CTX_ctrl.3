.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_CTX_CTRL 3"
.TH EVP_PKEY_CTX_CTRL 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_CTX_ctrl,
EVP_PKEY_CTX_ctrl_str,
EVP_PKEY_CTX_ctrl_uint64,
EVP_PKEY_CTX_md,
EVP_PKEY_CTX_set_signature_md,
EVP_PKEY_CTX_get_signature_md,
EVP_PKEY_CTX_set_mac_key,
EVP_PKEY_CTX_set_rsa_padding,
EVP_PKEY_CTX_get_rsa_padding,
EVP_PKEY_CTX_set_rsa_pss_saltlen,
EVP_PKEY_CTX_get_rsa_pss_saltlen,
EVP_PKEY_CTX_set_rsa_keygen_bits,
EVP_PKEY_CTX_set_rsa_keygen_pubexp,
EVP_PKEY_CTX_set_rsa_keygen_primes,
EVP_PKEY_CTX_set_rsa_mgf1_md,
EVP_PKEY_CTX_get_rsa_mgf1_md,
EVP_PKEY_CTX_set_rsa_oaep_md,
EVP_PKEY_CTX_get_rsa_oaep_md,
EVP_PKEY_CTX_set0_rsa_oaep_label,
EVP_PKEY_CTX_get0_rsa_oaep_label,
EVP_PKEY_CTX_set_dsa_paramgen_bits,
EVP_PKEY_CTX_set_dsa_paramgen_q_bits,
EVP_PKEY_CTX_set_dsa_paramgen_md,
EVP_PKEY_CTX_set_dh_paramgen_prime_len,
EVP_PKEY_CTX_set_dh_paramgen_subprime_len,
EVP_PKEY_CTX_set_dh_paramgen_generator,
EVP_PKEY_CTX_set_dh_paramgen_type,
EVP_PKEY_CTX_set_dh_rfc5114,
EVP_PKEY_CTX_set_dhx_rfc5114,
EVP_PKEY_CTX_set_dh_pad,
EVP_PKEY_CTX_set_dh_nid,
EVP_PKEY_CTX_set_dh_kdf_type,
EVP_PKEY_CTX_get_dh_kdf_type,
EVP_PKEY_CTX_set0_dh_kdf_oid,
EVP_PKEY_CTX_get0_dh_kdf_oid,
EVP_PKEY_CTX_set_dh_kdf_md,
EVP_PKEY_CTX_get_dh_kdf_md,
EVP_PKEY_CTX_set_dh_kdf_outlen,
EVP_PKEY_CTX_get_dh_kdf_outlen,
EVP_PKEY_CTX_set0_dh_kdf_ukm,
EVP_PKEY_CTX_get0_dh_kdf_ukm,
EVP_PKEY_CTX_set_ec_paramgen_curve_nid,
EVP_PKEY_CTX_set_ec_param_enc,
EVP_PKEY_CTX_set_ecdh_cofactor_mode,
EVP_PKEY_CTX_get_ecdh_cofactor_mode,
EVP_PKEY_CTX_set_ecdh_kdf_type,
EVP_PKEY_CTX_get_ecdh_kdf_type,
EVP_PKEY_CTX_set_ecdh_kdf_md,
EVP_PKEY_CTX_get_ecdh_kdf_md,
EVP_PKEY_CTX_set_ecdh_kdf_outlen,
EVP_PKEY_CTX_get_ecdh_kdf_outlen,
EVP_PKEY_CTX_set0_ecdh_kdf_ukm,
EVP_PKEY_CTX_get0_ecdh_kdf_ukm,
EVP_PKEY_CTX_set1_id, EVP_PKEY_CTX_get1_id, EVP_PKEY_CTX_get1_id_len
\&\- algorithm specific control operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_CTX_ctrl(EVP_PKEY_CTX *ctx, int keytype, int optype,
\&                       int cmd, int p1, void *p2);
\& int EVP_PKEY_CTX_ctrl_uint64(EVP_PKEY_CTX *ctx, int keytype, int optype,
\&                              int cmd, uint64_t value);
\& int EVP_PKEY_CTX_ctrl_str(EVP_PKEY_CTX *ctx, const char *type,
\&                           const char *value);
\&
\& int EVP_PKEY_CTX_md(EVP_PKEY_CTX *ctx, int optype, int cmd, const char *md);
\&
\& int EVP_PKEY_CTX_set_signature_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_get_signature_md(EVP_PKEY_CTX *ctx, const EVP_MD **pmd);
\&
\& int EVP_PKEY_CTX_set_mac_key(EVP_PKEY_CTX *ctx, unsigned char *key, int len);
\&
\& #include <openssl/rsa.h>
\&
\& int EVP_PKEY_CTX_set_rsa_padding(EVP_PKEY_CTX *ctx, int pad);
\& int EVP_PKEY_CTX_get_rsa_padding(EVP_PKEY_CTX *ctx, int *pad);
\& int EVP_PKEY_CTX_set_rsa_pss_saltlen(EVP_PKEY_CTX *ctx, int len);
\& int EVP_PKEY_CTX_get_rsa_pss_saltlen(EVP_PKEY_CTX *ctx, int *len);
\& int EVP_PKEY_CTX_set_rsa_keygen_bits(EVP_PKEY_CTX *ctx, int mbits);
\& int EVP_PKEY_CTX_set_rsa_keygen_pubexp(EVP_PKEY_CTX *ctx, BIGNUM *pubexp);
\& int EVP_PKEY_CTX_set_rsa_keygen_primes(EVP_PKEY_CTX *ctx, int primes);
\& int EVP_PKEY_CTX_set_rsa_mgf1_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_get_rsa_mgf1_md(EVP_PKEY_CTX *ctx, const EVP_MD **md);
\& int EVP_PKEY_CTX_set_rsa_oaep_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_get_rsa_oaep_md(EVP_PKEY_CTX *ctx, const EVP_MD **md);
\& int EVP_PKEY_CTX_set0_rsa_oaep_label(EVP_PKEY_CTX *ctx, unsigned char *label, int len);
\& int EVP_PKEY_CTX_get0_rsa_oaep_label(EVP_PKEY_CTX *ctx, unsigned char **label);
\&
\& #include <openssl/dsa.h>
\&
\& int EVP_PKEY_CTX_set_dsa_paramgen_bits(EVP_PKEY_CTX *ctx, int nbits);
\& int EVP_PKEY_CTX_set_dsa_paramgen_q_bits(EVP_PKEY_CTX *ctx, int qbits);
\& int EVP_PKEY_CTX_set_dsa_paramgen_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
\&
\& #include <openssl/dh.h>
\&
\& int EVP_PKEY_CTX_set_dh_paramgen_prime_len(EVP_PKEY_CTX *ctx, int len);
\& int EVP_PKEY_CTX_set_dh_paramgen_subprime_len(EVP_PKEY_CTX *ctx, int len);
\& int EVP_PKEY_CTX_set_dh_paramgen_generator(EVP_PKEY_CTX *ctx, int gen);
\& int EVP_PKEY_CTX_set_dh_paramgen_type(EVP_PKEY_CTX *ctx, int type);
\& int EVP_PKEY_CTX_set_dh_pad(EVP_PKEY_CTX *ctx, int pad);
\& int EVP_PKEY_CTX_set_dh_nid(EVP_PKEY_CTX *ctx, int nid);
\& int EVP_PKEY_CTX_set_dh_rfc5114(EVP_PKEY_CTX *ctx, int rfc5114);
\& int EVP_PKEY_CTX_set_dhx_rfc5114(EVP_PKEY_CTX *ctx, int rfc5114);
\& int EVP_PKEY_CTX_set_dh_kdf_type(EVP_PKEY_CTX *ctx, int kdf);
\& int EVP_PKEY_CTX_get_dh_kdf_type(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_CTX_set0_dh_kdf_oid(EVP_PKEY_CTX *ctx, ASN1_OBJECT *oid);
\& int EVP_PKEY_CTX_get0_dh_kdf_oid(EVP_PKEY_CTX *ctx, ASN1_OBJECT **oid);
\& int EVP_PKEY_CTX_set_dh_kdf_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_get_dh_kdf_md(EVP_PKEY_CTX *ctx, const EVP_MD **md);
\& int EVP_PKEY_CTX_set_dh_kdf_outlen(EVP_PKEY_CTX *ctx, int len);
\& int EVP_PKEY_CTX_get_dh_kdf_outlen(EVP_PKEY_CTX *ctx, int *len);
\& int EVP_PKEY_CTX_set0_dh_kdf_ukm(EVP_PKEY_CTX *ctx, unsigned char *ukm, int len);
\& int EVP_PKEY_CTX_get0_dh_kdf_ukm(EVP_PKEY_CTX *ctx, unsigned char **ukm);
\&
\& #include <openssl/ec.h>
\&
\& int EVP_PKEY_CTX_set_ec_paramgen_curve_nid(EVP_PKEY_CTX *ctx, int nid);
\& int EVP_PKEY_CTX_set_ec_param_enc(EVP_PKEY_CTX *ctx, int param_enc);
\& int EVP_PKEY_CTX_set_ecdh_cofactor_mode(EVP_PKEY_CTX *ctx, int cofactor_mode);
\& int EVP_PKEY_CTX_get_ecdh_cofactor_mode(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_CTX_set_ecdh_kdf_type(EVP_PKEY_CTX *ctx, int kdf);
\& int EVP_PKEY_CTX_get_ecdh_kdf_type(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_CTX_set_ecdh_kdf_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_get_ecdh_kdf_md(EVP_PKEY_CTX *ctx, const EVP_MD **md);
\& int EVP_PKEY_CTX_set_ecdh_kdf_outlen(EVP_PKEY_CTX *ctx, int len);
\& int EVP_PKEY_CTX_get_ecdh_kdf_outlen(EVP_PKEY_CTX *ctx, int *len);
\& int EVP_PKEY_CTX_set0_ecdh_kdf_ukm(EVP_PKEY_CTX *ctx, unsigned char *ukm, int len);
\& int EVP_PKEY_CTX_get0_ecdh_kdf_ukm(EVP_PKEY_CTX *ctx, unsigned char **ukm);
\&
\& int EVP_PKEY_CTX_set1_id(EVP_PKEY_CTX *ctx, void *id, size_t id_len);
\& int EVP_PKEY_CTX_get1_id(EVP_PKEY_CTX *ctx, void *id);
\& int EVP_PKEY_CTX_get1_id_len(EVP_PKEY_CTX *ctx, size_t *id_len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The function \fBEVP_PKEY_CTX_ctrl()\fR sends a control operation to the context
\&\fBctx\fR. The key type used must match \fBkeytype\fR if it is not \-1. The parameter
\&\fBoptype\fR is a mask indicating which operations the control can be applied to.
The control command is indicated in \fBcmd\fR and any additional arguments in
\&\fBp1\fR and \fBp2\fR.
.PP
For \fBcmd\fR = \fBEVP_PKEY_CTRL_SET_MAC_KEY\fR, \fBp1\fR is the length of the MAC key,
and \fBp2\fR is MAC key. This is used by Poly1305, SipHash, HMAC and CMAC.
.PP
Applications will not normally call \fBEVP_PKEY_CTX_ctrl()\fR directly but will
instead call one of the algorithm specific macros below.
.PP
The function \fBEVP_PKEY_CTX_ctrl_uint64()\fR is a wrapper that directly passes a
uint64 value as \fBp2\fR to \fBEVP_PKEY_CTX_ctrl()\fR.
.PP
The function \fBEVP_PKEY_CTX_ctrl_str()\fR allows an application to send an algorithm
specific control operation to a context \fBctx\fR in string form. This is
intended to be used for options specified on the command line or in text
files. The commands supported are documented in the openssl utility
command line pages for the option \fB\-pkeyopt\fR which is supported by the
\&\fBpkeyutl\fR, \fBgenpkey\fR and \fBreq\fR commands.
.PP
The function \fBEVP_PKEY_CTX_md()\fR sends a message digest control operation
to the context \fBctx\fR. The message digest is specified by its name \fBmd\fR.
.PP
All the remaining "functions" are implemented as macros.
.PP
The \fBEVP_PKEY_CTX_set_signature_md()\fR macro sets the message digest type used
in a signature. It can be used in the RSA, DSA and ECDSA algorithms.
.PP
The \fBEVP_PKEY_CTX_get_signature_md()\fR macro gets the message digest type used in a
signature. It can be used in the RSA, DSA and ECDSA algorithms.
.PP
Key generation typically involves setting up parameters to be used and
generating the private and public key data. Some algorithm implementations
allow private key data to be set explicitly using the \fBEVP_PKEY_CTX_set_mac_key()\fR
macro. In this case key generation is simply the process of setting up the
parameters for the key and then setting the raw key data to the value explicitly
provided by that macro. Normally applications would call
\&\fBEVP_PKEY_new_raw_private_key\fR\|(3) or similar functions instead of this macro.
.PP
The \fBEVP_PKEY_CTX_set_mac_key()\fR macro can be used with any of the algorithms
supported by the \fBEVP_PKEY_new_raw_private_key\fR\|(3) function.
.SS "RSA parameters"
.IX Subsection "RSA parameters"
The \fBEVP_PKEY_CTX_set_rsa_padding()\fR macro sets the RSA padding mode for \fBctx\fR.
The \fBpad\fR parameter can take the value \fBRSA_PKCS1_PADDING\fR for PKCS#1
padding, \fBRSA_SSLV23_PADDING\fR for SSLv23 padding, \fBRSA_NO_PADDING\fR for
no padding, \fBRSA_PKCS1_OAEP_PADDING\fR for OAEP padding (encrypt and
decrypt only), \fBRSA_X931_PADDING\fR for X9.31 padding (signature operations
only) and \fBRSA_PKCS1_PSS_PADDING\fR (sign and verify only).
.PP
Two RSA padding modes behave differently if \fBEVP_PKEY_CTX_set_signature_md()\fR
is used. If this macro is called for PKCS#1 padding the plaintext buffer is
an actual digest value and is encapsulated in a DigestInfo structure according
to PKCS#1 when signing and this structure is expected (and stripped off) when
verifying. If this control is not used with RSA and PKCS#1 padding then the
supplied data is used directly and not encapsulated. In the case of X9.31
padding for RSA the algorithm identifier byte is added or checked and removed
if this control is called. If it is not called then the first byte of the plaintext
buffer is expected to be the algorithm identifier byte.
.PP
The \fBEVP_PKEY_CTX_get_rsa_padding()\fR macro gets the RSA padding mode for \fBctx\fR.
.PP
The \fBEVP_PKEY_CTX_set_rsa_pss_saltlen()\fR macro sets the RSA PSS salt length to
\&\fBlen\fR. As its name implies it is only supported for PSS padding. Three special
values are supported: \fBRSA_PSS_SALTLEN_DIGEST\fR sets the salt length to the
digest length, \fBRSA_PSS_SALTLEN_MAX\fR sets the salt length to the maximum
permissible value. When verifying \fBRSA_PSS_SALTLEN_AUTO\fR causes the salt length
to be automatically determined based on the \fBPSS\fR block structure. If this
macro is not called maximum salt length is used when signing and auto detection
when verifying is used by default.
.PP
The \fBEVP_PKEY_CTX_get_rsa_pss_saltlen()\fR macro gets the RSA PSS salt length
for \fBctx\fR. The padding mode must have been set to \fBRSA_PKCS1_PSS_PADDING\fR.
.PP
The \fBEVP_PKEY_CTX_set_rsa_keygen_bits()\fR macro sets the RSA key length for
RSA key generation to \fBbits\fR. If not specified 1024 bits is used.
.PP
The \fBEVP_PKEY_CTX_set_rsa_keygen_pubexp()\fR macro sets the public exponent value
for RSA key generation to \fBpubexp\fR. Currently it should be an odd integer. The
\&\fBpubexp\fR pointer is used internally by this function so it should not be
modified or freed after the call. If not specified 65537 is used.
.PP
The \fBEVP_PKEY_CTX_set_rsa_keygen_primes()\fR macro sets the number of primes for
RSA key generation to \fBprimes\fR. If not specified 2 is used.
.PP
The \fBEVP_PKEY_CTX_set_rsa_mgf1_md()\fR macro sets the MGF1 digest for RSA padding
schemes to \fBmd\fR. If not explicitly set the signing digest is used. The
padding mode must have been set to \fBRSA_PKCS1_OAEP_PADDING\fR
or \fBRSA_PKCS1_PSS_PADDING\fR.
.PP
The \fBEVP_PKEY_CTX_get_rsa_mgf1_md()\fR macro gets the MGF1 digest for \fBctx\fR.
If not explicitly set the signing digest is used. The padding mode must have
been set to \fBRSA_PKCS1_OAEP_PADDING\fR or \fBRSA_PKCS1_PSS_PADDING\fR.
.PP
The \fBEVP_PKEY_CTX_set_rsa_oaep_md()\fR macro sets the message digest type used
in RSA OAEP to \fBmd\fR. The padding mode must have been set to
\&\fBRSA_PKCS1_OAEP_PADDING\fR.
.PP
The \fBEVP_PKEY_CTX_get_rsa_oaep_md()\fR macro gets the message digest type used
in RSA OAEP to \fBmd\fR. The padding mode must have been set to
\&\fBRSA_PKCS1_OAEP_PADDING\fR.
.PP
The \fBEVP_PKEY_CTX_set0_rsa_oaep_label()\fR macro sets the RSA OAEP label to
\&\fBlabel\fR and its length to \fBlen\fR. If \fBlabel\fR is NULL or \fBlen\fR is 0,
the label is cleared. The library takes ownership of the label so the
caller should not free the original memory pointed to by \fBlabel\fR.
The padding mode must have been set to \fBRSA_PKCS1_OAEP_PADDING\fR.
.PP
The \fBEVP_PKEY_CTX_get0_rsa_oaep_label()\fR macro gets the RSA OAEP label to
\&\fBlabel\fR. The return value is the label length. The padding mode
must have been set to \fBRSA_PKCS1_OAEP_PADDING\fR. The resulting pointer is owned
by the library and should not be freed by the caller.
.SS "DSA parameters"
.IX Subsection "DSA parameters"
The \fBEVP_PKEY_CTX_set_dsa_paramgen_bits()\fR macro sets the number of bits used
for DSA parameter generation to \fBnbits\fR. If not specified, 1024 is used.
.PP
The \fBEVP_PKEY_CTX_set_dsa_paramgen_q_bits()\fR macro sets the number of bits in the
subprime parameter \fBq\fR for DSA parameter generation to \fBqbits\fR. If not
specified, 160 is used. If a digest function is specified below, this parameter
is ignored and instead, the number of bits in \fBq\fR matches the size of the
digest.
.PP
The \fBEVP_PKEY_CTX_set_dsa_paramgen_md()\fR macro sets the digest function used for
DSA parameter generation to \fBmd\fR. If not specified, one of SHA\-1, SHA\-224, or
SHA\-256 is selected to match the bit length of \fBq\fR above.
.SS "DH parameters"
.IX Subsection "DH parameters"
The \fBEVP_PKEY_CTX_set_dh_paramgen_prime_len()\fR macro sets the length of the DH
prime parameter \fBp\fR for DH parameter generation. If this macro is not called
then 1024 is used. Only accepts lengths greater than or equal to 256.
.PP
The \fBEVP_PKEY_CTX_set_dh_paramgen_subprime_len()\fR macro sets the length of the DH
optional subprime parameter \fBq\fR for DH parameter generation. The default is
256 if the prime is at least 2048 bits long or 160 otherwise. The DH
paramgen type must have been set to x9.42.
.PP
The \fBEVP_PKEY_CTX_set_dh_paramgen_generator()\fR macro sets DH generator to \fBgen\fR
for DH parameter generation. If not specified 2 is used.
.PP
The \fBEVP_PKEY_CTX_set_dh_paramgen_type()\fR macro sets the key type for DH
parameter generation. Use 0 for PKCS#3 DH and 1 for X9.42 DH.
The default is 0.
.PP
The \fBEVP_PKEY_CTX_set_dh_pad()\fR macro sets the DH padding mode. If \fBpad\fR is
1 the shared secret is padded with zeros up to the size of the DH prime \fBp\fR.
If \fBpad\fR is zero (the default) then no padding is performed.
.PP
\&\fBEVP_PKEY_CTX_set_dh_nid()\fR sets the DH parameters to values corresponding to
\&\fBnid\fR as defined in RFC7919. The \fBnid\fR parameter must be \fBNID_ffdhe2048\fR,
\&\fBNID_ffdhe3072\fR, \fBNID_ffdhe4096\fR, \fBNID_ffdhe6144\fR, \fBNID_ffdhe8192\fR
or \fBNID_undef\fR to clear the stored value. This macro can be called during
parameter or key generation.
The nid parameter and the rfc5114 parameter are mutually exclusive.
.PP
The \fBEVP_PKEY_CTX_set_dh_rfc5114()\fR and \fBEVP_PKEY_CTX_set_dhx_rfc5114()\fR macros are
synonymous. They set the DH parameters to the values defined in RFC5114. The
\&\fBrfc5114\fR parameter must be 1, 2 or 3 corresponding to RFC5114 sections
2.1, 2.2 and 2.3. or 0 to clear the stored value. This macro can be called
during parameter generation. The \fBctx\fR must have a key type of
\&\fBEVP_PKEY_DHX\fR.
The rfc5114 parameter and the nid parameter are mutually exclusive.
.SS "DH key derivation function parameters"
.IX Subsection "DH key derivation function parameters"
Note that all of the following functions require that the \fBctx\fR parameter has
a private key type of \fBEVP_PKEY_DHX\fR. When using key derivation, the output of
\&\fBEVP_PKEY_derive()\fR is the output of the KDF instead of the DH shared secret.
The KDF output is typically used as a Key Encryption Key (KEK) that in turn
encrypts a Content Encryption Key (CEK).
.PP
The \fBEVP_PKEY_CTX_set_dh_kdf_type()\fR macro sets the key derivation function type
to \fBkdf\fR for DH key derivation. Possible values are \fBEVP_PKEY_DH_KDF_NONE\fR
and \fBEVP_PKEY_DH_KDF_X9_42\fR which uses the key derivation specified in RFC2631
(based on the keying algorithm described in X9.42). When using key derivation,
the \fBkdf_oid\fR, \fBkdf_md\fR and \fBkdf_outlen\fR parameters must also be specified.
.PP
The \fBEVP_PKEY_CTX_get_dh_kdf_type()\fR macro gets the key derivation function type
for \fBctx\fR used for DH key derivation. Possible values are \fBEVP_PKEY_DH_KDF_NONE\fR
and \fBEVP_PKEY_DH_KDF_X9_42\fR.
.PP
The \fBEVP_PKEY_CTX_set0_dh_kdf_oid()\fR macro sets the key derivation function
object identifier to \fBoid\fR for DH key derivation. This OID should identify
the algorithm to be used with the Content Encryption Key.
The library takes ownership of the object identifier so the caller should not
free the original memory pointed to by \fBoid\fR.
.PP
The \fBEVP_PKEY_CTX_get0_dh_kdf_oid()\fR macro gets the key derivation function oid
for \fBctx\fR used for DH key derivation. The resulting pointer is owned by the
library and should not be freed by the caller.
.PP
The \fBEVP_PKEY_CTX_set_dh_kdf_md()\fR macro sets the key derivation function
message digest to \fBmd\fR for DH key derivation. Note that RFC2631 specifies
that this digest should be SHA1 but OpenSSL tolerates other digests.
.PP
The \fBEVP_PKEY_CTX_get_dh_kdf_md()\fR macro gets the key derivation function
message digest for \fBctx\fR used for DH key derivation.
.PP
The \fBEVP_PKEY_CTX_set_dh_kdf_outlen()\fR macro sets the key derivation function
output length to \fBlen\fR for DH key derivation.
.PP
The \fBEVP_PKEY_CTX_get_dh_kdf_outlen()\fR macro gets the key derivation function
output length for \fBctx\fR used for DH key derivation.
.PP
The \fBEVP_PKEY_CTX_set0_dh_kdf_ukm()\fR macro sets the user key material to
\&\fBukm\fR and its length to \fBlen\fR for DH key derivation. This parameter is optional
and corresponds to the partyAInfo field in RFC2631 terms. The specification
requires that it is 512 bits long but this is not enforced by OpenSSL.
The library takes ownership of the user key material so the caller should not
free the original memory pointed to by \fBukm\fR.
.PP
The \fBEVP_PKEY_CTX_get0_dh_kdf_ukm()\fR macro gets the user key material for \fBctx\fR.
The return value is the user key material length. The resulting pointer is owned
by the library and should not be freed by the caller.
.SS "EC parameters"
.IX Subsection "EC parameters"
The \fBEVP_PKEY_CTX_set_ec_paramgen_curve_nid()\fR sets the EC curve for EC parameter
generation to \fBnid\fR. For EC parameter generation this macro must be called
or an error occurs because there is no default curve.
This function can also be called to set the curve explicitly when
generating an EC key.
.PP
The \fBEVP_PKEY_CTX_set_ec_param_enc()\fR macro sets the EC parameter encoding to
\&\fBparam_enc\fR when generating EC parameters or an EC key. The encoding can be
\&\fBOPENSSL_EC_EXPLICIT_CURVE\fR for explicit parameters (the default in versions
of OpenSSL before 1.1.0) or \fBOPENSSL_EC_NAMED_CURVE\fR to use named curve form.
For maximum compatibility the named curve form should be used. Note: the
\&\fBOPENSSL_EC_NAMED_CURVE\fR value was added in OpenSSL 1.1.0; previous
versions should use 0 instead.
.SS "ECDH parameters"
.IX Subsection "ECDH parameters"
The \fBEVP_PKEY_CTX_set_ecdh_cofactor_mode()\fR macro sets the cofactor mode to
\&\fBcofactor_mode\fR for ECDH key derivation. Possible values are 1 to enable
cofactor key derivation, 0 to disable it and \-1 to clear the stored cofactor
mode and fallback to the private key cofactor mode.
.PP
The \fBEVP_PKEY_CTX_get_ecdh_cofactor_mode()\fR macro returns the cofactor mode for
\&\fBctx\fR used for ECDH key derivation. Possible values are 1 when cofactor key
derivation is enabled and 0 otherwise.
.SS "ECDH key derivation function parameters"
.IX Subsection "ECDH key derivation function parameters"
The \fBEVP_PKEY_CTX_set_ecdh_kdf_type()\fR macro sets the key derivation function type
to \fBkdf\fR for ECDH key derivation. Possible values are \fBEVP_PKEY_ECDH_KDF_NONE\fR
and \fBEVP_PKEY_ECDH_KDF_X9_63\fR which uses the key derivation specified in X9.63.
When using key derivation, the \fBkdf_md\fR and \fBkdf_outlen\fR parameters must
also be specified.
.PP
The \fBEVP_PKEY_CTX_get_ecdh_kdf_type()\fR macro returns the key derivation function
type for \fBctx\fR used for ECDH key derivation. Possible values are
\&\fBEVP_PKEY_ECDH_KDF_NONE\fR and \fBEVP_PKEY_ECDH_KDF_X9_63\fR.
.PP
The \fBEVP_PKEY_CTX_set_ecdh_kdf_md()\fR macro sets the key derivation function
message digest to \fBmd\fR for ECDH key derivation. Note that X9.63 specifies
that this digest should be SHA1 but OpenSSL tolerates other digests.
.PP
The \fBEVP_PKEY_CTX_get_ecdh_kdf_md()\fR macro gets the key derivation function
message digest for \fBctx\fR used for ECDH key derivation.
.PP
The \fBEVP_PKEY_CTX_set_ecdh_kdf_outlen()\fR macro sets the key derivation function
output length to \fBlen\fR for ECDH key derivation.
.PP
The \fBEVP_PKEY_CTX_get_ecdh_kdf_outlen()\fR macro gets the key derivation function
output length for \fBctx\fR used for ECDH key derivation.
.PP
The \fBEVP_PKEY_CTX_set0_ecdh_kdf_ukm()\fR macro sets the user key material to \fBukm\fR
for ECDH key derivation. This parameter is optional and corresponds to the
shared info in X9.63 terms. The library takes ownership of the user key material
so the caller should not free the original memory pointed to by \fBukm\fR.
.PP
The \fBEVP_PKEY_CTX_get0_ecdh_kdf_ukm()\fR macro gets the user key material for \fBctx\fR.
The return value is the user key material length. The resulting pointer is owned
by the library and should not be freed by the caller.
.SS "Other parameters"
.IX Subsection "Other parameters"
The \fBEVP_PKEY_CTX_set1_id()\fR, \fBEVP_PKEY_CTX_get1_id()\fR and \fBEVP_PKEY_CTX_get1_id_len()\fR
macros are used to manipulate the special identifier field for specific signature
algorithms such as SM2. The \fBEVP_PKEY_CTX_set1_id()\fR sets an ID pointed by \fBid\fR with
the length \fBid_len\fR to the library. The library takes a copy of the id so that
the caller can safely free the original memory pointed to by \fBid\fR. The
\&\fBEVP_PKEY_CTX_get1_id_len()\fR macro returns the length of the ID set via a previous
call to \fBEVP_PKEY_CTX_set1_id()\fR. The length is usually used to allocate adequate
memory for further calls to \fBEVP_PKEY_CTX_get1_id()\fR. The \fBEVP_PKEY_CTX_get1_id()\fR
macro returns the previously set ID value to caller in \fBid\fR. The caller should
allocate adequate memory space for the \fBid\fR before calling \fBEVP_PKEY_CTX_get1_id()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_CTX_ctrl()\fR and its macros return a positive value for success and 0
or a negative value for failure. In particular a return value of \-2
indicates the operation is not supported by the public key algorithm.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_decrypt\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3),
\&\fBEVP_PKEY_keygen\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The
\&\fBEVP_PKEY_CTX_set1_id()\fR, \fBEVP_PKEY_CTX_get1_id()\fR and \fBEVP_PKEY_CTX_get1_id_len()\fR
macros were added in 1.1.1, other functions were added in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
