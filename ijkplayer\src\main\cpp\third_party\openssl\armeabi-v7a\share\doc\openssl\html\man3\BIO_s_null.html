<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BIO_s_null</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BIO_s_null - null data sink</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bio.h&gt;

const BIO_METHOD *BIO_s_null(void);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>BIO_s_null() returns the null sink BIO method. Data written to the null sink is discarded, reads return EOF.</p>

<h1 id="NOTES">NOTES</h1>

<p>A null sink BIO behaves in a similar manner to the Unix /dev/null device.</p>

<p>A null bio can be placed on the end of a chain to discard any data passed through it.</p>

<p>A null sink is useful if, for example, an application wishes to digest some data by writing through a digest bio but not send the digested data anywhere. Since a BIO chain must normally include a source/sink BIO this can be achieved by adding a null sink BIO to the end of the chain</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BIO_s_null() returns the null sink BIO method.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


