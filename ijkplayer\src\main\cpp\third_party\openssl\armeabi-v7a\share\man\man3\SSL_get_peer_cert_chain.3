.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_PEER_CERT_CHAIN 3"
.TH SSL_GET_PEER_CERT_CHAIN 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_peer_cert_chain, SSL_get0_verified_chain \- get the X509 certificate
chain of the peer
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& STACK_OF(X509) *SSL_get_peer_cert_chain(const SSL *ssl);
\& STACK_OF(X509) *SSL_get0_verified_chain(const SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_peer_cert_chain()\fR returns a pointer to STACK_OF(X509) certificates
forming the certificate chain sent by the peer. If called on the client side,
the stack also contains the peer's certificate; if called on the server
side, the peer's certificate must be obtained separately using
\&\fBSSL_get_peer_certificate\fR\|(3).
If the peer did not present a certificate, NULL is returned.
.PP
NB: \fBSSL_get_peer_cert_chain()\fR returns the peer chain as sent by the peer: it
only consists of certificates the peer has sent (in the order the peer
has sent them) it is \fBnot\fR a verified chain.
.PP
\&\fBSSL_get0_verified_chain()\fR returns the \fBverified\fR certificate chain
of the peer including the peer's end entity certificate. It must be called
after a session has been successfully established. If peer verification was
not successful (as indicated by \fBSSL_get_verify_result()\fR not returning
X509_V_OK) the chain may be incomplete or invalid.
.SH NOTES
.IX Header "NOTES"
If the session is resumed peers do not send certificates so a NULL pointer
is returned by these functions. Applications can call \fBSSL_session_reused()\fR
to determine whether a session is resumed.
.PP
The reference count of each certificate in the returned STACK_OF(X509) object
is not incremented and the returned stack may be invalidated by renegotiation.
If applications wish to use any certificates in the returned chain
indefinitely they must increase the reference counts using \fBX509_up_ref()\fR or
obtain a copy of the whole chain with \fBX509_chain_up_ref()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur:
.IP NULL 4
.IX Item "NULL"
No certificate was presented by the peer or no connection was established
or the certificate chain is no longer available when a session is reused.
.IP "Pointer to a STACK_OF(X509)" 4
.IX Item "Pointer to a STACK_OF(X509)"
The return value points to the certificate chain presented by the peer.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_get_peer_certificate\fR\|(3), \fBX509_up_ref\fR\|(3),
\&\fBX509_chain_up_ref\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
