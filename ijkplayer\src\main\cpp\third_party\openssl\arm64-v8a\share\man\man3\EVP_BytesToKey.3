.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_BYTESTOKEY 3"
.TH EVP_BYTESTOKEY 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_BytesToKey \- password based encryption routine
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_BytesToKey(const EVP_CIPHER *type, const EVP_MD *md,
\&                    const unsigned char *salt,
\&                    const unsigned char *data, int datal, int count,
\&                    unsigned char *key, unsigned char *iv);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_BytesToKey()\fR derives a key and IV from various parameters. \fBtype\fR is
the cipher to derive the key and IV for. \fBmd\fR is the message digest to use.
The \fBsalt\fR parameter is used as a salt in the derivation: it should point to
an 8 byte buffer or NULL if no salt is used. \fBdata\fR is a buffer containing
\&\fBdatal\fR bytes which is used to derive the keying data. \fBcount\fR is the
iteration count to use. The derived key and IV will be written to \fBkey\fR
and \fBiv\fR respectively.
.SH NOTES
.IX Header "NOTES"
A typical application of this function is to derive keying material for an
encryption algorithm from a password in the \fBdata\fR parameter.
.PP
Increasing the \fBcount\fR parameter slows down the algorithm which makes it
harder for an attacker to perform a brute force attack using a large number
of candidate passwords.
.PP
If the total key and IV length is less than the digest length and
\&\fBMD5\fR is used then the derivation algorithm is compatible with PKCS#5 v1.5
otherwise a non standard extension is used to derive the extra data.
.PP
Newer applications should use a more modern algorithm such as PBKDF2 as
defined in PKCS#5v2.1 and provided by PKCS5_PBKDF2_HMAC.
.SH "KEY DERIVATION ALGORITHM"
.IX Header "KEY DERIVATION ALGORITHM"
The key and IV is derived by concatenating D_1, D_2, etc until
enough data is available for the key and IV. D_i is defined as:
.PP
.Vb 1
\&        D_i = HASH^count(D_(i\-1) || data || salt)
.Ve
.PP
where || denotes concatenation, D_0 is empty, HASH is the digest
algorithm in use, HASH^1(data) is simply HASH(data), HASH^2(data)
is HASH(HASH(data)) and so on.
.PP
The initial bytes are used for the key and the subsequent bytes for
the IV.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If \fBdata\fR is NULL, then \fBEVP_BytesToKey()\fR returns the number of bytes
needed to store the derived key.
Otherwise, \fBEVP_BytesToKey()\fR returns the size of the derived key in bytes,
or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7), \fBRAND_bytes\fR\|(3),
\&\fBPKCS5_PBKDF2_HMAC\fR\|(3),
\&\fBEVP_EncryptInit\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
