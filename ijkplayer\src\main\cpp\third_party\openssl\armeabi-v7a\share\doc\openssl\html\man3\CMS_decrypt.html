<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CMS_decrypt</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>CMS_decrypt - decrypt content from a CMS envelopedData structure</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/cms.h&gt;

int CMS_decrypt(CMS_ContentInfo *cms, EVP_PKEY *pkey, X509 *cert,
                BIO *dcont, BIO *out, unsigned int flags);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>CMS_decrypt() extracts and decrypts the content from a CMS EnvelopedData structure. <b>pkey</b> is the private key of the recipient, <b>cert</b> is the recipient&#39;s certificate, <b>out</b> is a BIO to write the content to and <b>flags</b> is an optional set of flags.</p>

<p>The <b>dcont</b> parameter is used in the rare case where the encrypted content is detached. It will normally be set to NULL.</p>

<h1 id="NOTES">NOTES</h1>

<p>Although the recipients certificate is not needed to decrypt the data it is needed to locate the appropriate (of possible several) recipients in the CMS structure.</p>

<p>If <b>cert</b> is set to NULL all possible recipients are tried. This case however is problematic. To thwart the MMA attack (Bleichenbacher&#39;s attack on PKCS #1 v1.5 RSA padding) all recipients are tried whether they succeed or not. If no recipient succeeds then a random symmetric key is used to decrypt the content: this will typically output garbage and may (but is not guaranteed to) ultimately return a padding error only. If CMS_decrypt() just returned an error when all recipient encrypted keys failed to decrypt an attacker could use this in a timing attack. If the special flag <b>CMS_DEBUG_DECRYPT</b> is set then the above behaviour is modified and an error <b>is</b> returned if no recipient encrypted key can be decrypted <b>without</b> generating a random content encryption key. Applications should use this flag with <b>extreme caution</b> especially in automated gateways as it can leave them open to attack.</p>

<p>It is possible to determine the correct recipient key by other means (for example looking them up in a database) and setting them in the CMS structure in advance using the CMS utility functions such as CMS_set1_pkey(). In this case both <b>cert</b> and <b>pkey</b> should be set to NULL.</p>

<p>To process KEKRecipientInfo types CMS_set1_key() or CMS_RecipientInfo_set0_key() and CMS_RecipientInfo_decrypt() should be called before CMS_decrypt() and <b>cert</b> and <b>pkey</b> set to NULL.</p>

<p>The following flags can be passed in the <b>flags</b> parameter.</p>

<p>If the <b>CMS_TEXT</b> flag is set MIME headers for type <b>text/plain</b> are deleted from the content. If the content is not of type <b>text/plain</b> then an error is returned.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>CMS_decrypt() returns either 1 for success or 0 for failure. The error can be obtained from ERR_get_error(3)</p>

<h1 id="BUGS">BUGS</h1>

<p>The lack of single pass processing and the need to hold all data in memory as mentioned in CMS_verify() also applies to CMS_decrypt().</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/CMS_encrypt.html">CMS_encrypt(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2008-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


