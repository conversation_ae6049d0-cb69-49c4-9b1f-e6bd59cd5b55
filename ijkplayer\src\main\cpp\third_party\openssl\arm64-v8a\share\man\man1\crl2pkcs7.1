.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CRL2PKCS7 1"
.TH CRL2PKCS7 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-crl2pkcs7,
crl2pkcs7 \- Create a PKCS#7 structure from a CRL and certificates
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBcrl2pkcs7\fR
[\fB\-help\fR]
[\fB\-inform PEM|DER\fR]
[\fB\-outform PEM|DER\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-certfile filename\fR]
[\fB\-nocrl\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBcrl2pkcs7\fR command takes an optional CRL and one or more
certificates and converts them into a PKCS#7 degenerate "certificates
only" structure.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the CRL input format. \fBDER\fR format is DER encoded CRL
structure.\fBPEM\fR (the default) is a base64 encoded version of
the DER form with header and footer lines. The default format is PEM.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the PKCS#7 structure output format. \fBDER\fR format is DER
encoded PKCS#7 structure.\fBPEM\fR (the default) is a base64 encoded version of
the DER form with header and footer lines. The default format is PEM.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read a CRL from or standard input if this
option is not specified.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
Specifies the output filename to write the PKCS#7 structure to or standard
output by default.
.IP "\fB\-certfile filename\fR" 4
.IX Item "-certfile filename"
Specifies a filename containing one or more certificates in \fBPEM\fR format.
All certificates in the file will be added to the PKCS#7 structure. This
option can be used more than once to read certificates from multiple
files.
.IP \fB\-nocrl\fR 4
.IX Item "-nocrl"
Normally a CRL is included in the output file. With this option no CRL is
included in the output file and a CRL is not read from the input file.
.SH EXAMPLES
.IX Header "EXAMPLES"
Create a PKCS#7 structure from a certificate and CRL:
.PP
.Vb 1
\& openssl crl2pkcs7 \-in crl.pem \-certfile cert.pem \-out p7.pem
.Ve
.PP
Creates a PKCS#7 structure in DER format with no CRL from several
different certificates:
.PP
.Vb 2
\& openssl crl2pkcs7 \-nocrl \-certfile newcert.pem
\&        \-certfile demoCA/cacert.pem \-outform DER \-out p7.der
.Ve
.SH NOTES
.IX Header "NOTES"
The output file is a PKCS#7 signed data structure containing no signers and
just certificates and an optional CRL.
.PP
This utility can be used to send certificates and CAs to Netscape as part of
the certificate enrollment process. This involves sending the DER encoded output
as MIME type application/x\-x509\-user\-cert.
.PP
The \fBPEM\fR encoded form with the header and footer lines removed can be used to
install user certificates and CAs in MSIE using the Xenroll control.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBpkcs7\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
