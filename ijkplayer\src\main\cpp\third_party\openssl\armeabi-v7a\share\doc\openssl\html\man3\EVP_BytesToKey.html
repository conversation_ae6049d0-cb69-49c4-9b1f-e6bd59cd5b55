<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_BytesToKey</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#KEY-DERIVATION-ALGORITHM">KEY DERIVATION ALGORITHM</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_BytesToKey - password based encryption routine</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

int EVP_BytesToKey(const EVP_CIPHER *type, const EVP_MD *md,
                   const unsigned char *salt,
                   const unsigned char *data, int datal, int count,
                   unsigned char *key, unsigned char *iv);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>EVP_BytesToKey() derives a key and IV from various parameters. <b>type</b> is the cipher to derive the key and IV for. <b>md</b> is the message digest to use. The <b>salt</b> parameter is used as a salt in the derivation: it should point to an 8 byte buffer or NULL if no salt is used. <b>data</b> is a buffer containing <b>datal</b> bytes which is used to derive the keying data. <b>count</b> is the iteration count to use. The derived key and IV will be written to <b>key</b> and <b>iv</b> respectively.</p>

<h1 id="NOTES">NOTES</h1>

<p>A typical application of this function is to derive keying material for an encryption algorithm from a password in the <b>data</b> parameter.</p>

<p>Increasing the <b>count</b> parameter slows down the algorithm which makes it harder for an attacker to perform a brute force attack using a large number of candidate passwords.</p>

<p>If the total key and IV length is less than the digest length and <b>MD5</b> is used then the derivation algorithm is compatible with PKCS#5 v1.5 otherwise a non standard extension is used to derive the extra data.</p>

<p>Newer applications should use a more modern algorithm such as PBKDF2 as defined in PKCS#5v2.1 and provided by PKCS5_PBKDF2_HMAC.</p>

<h1 id="KEY-DERIVATION-ALGORITHM">KEY DERIVATION ALGORITHM</h1>

<p>The key and IV is derived by concatenating D_1, D_2, etc until enough data is available for the key and IV. D_i is defined as:</p>

<pre><code>D_i = HASH^count(D_(i-1) || data || salt)</code></pre>

<p>where || denotes concatenation, D_0 is empty, HASH is the digest algorithm in use, HASH^1(data) is simply HASH(data), HASH^2(data) is HASH(HASH(data)) and so on.</p>

<p>The initial bytes are used for the key and the subsequent bytes for the IV.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>If <b>data</b> is NULL, then EVP_BytesToKey() returns the number of bytes needed to store the derived key. Otherwise, EVP_BytesToKey() returns the size of the derived key in bytes, or 0 on error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/evp.html">evp(7)</a>, <a href="../man3/RAND_bytes.html">RAND_bytes(3)</a>, <a href="../man3/PKCS5_PBKDF2_HMAC.html">PKCS5_PBKDF2_HMAC(3)</a>, <a href="../man3/EVP_EncryptInit.html">EVP_EncryptInit(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2001-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


