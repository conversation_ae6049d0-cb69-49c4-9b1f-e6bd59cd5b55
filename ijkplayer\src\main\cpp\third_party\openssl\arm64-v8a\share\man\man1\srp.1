.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SRP 1"
.TH SRP 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-srp,
srp \- maintain SRP password file
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl srp\fR
[\fB\-help\fR]
[\fB\-verbose\fR]
[\fB\-add\fR]
[\fB\-modify\fR]
[\fB\-delete\fR]
[\fB\-list\fR]
[\fB\-name section\fR]
[\fB\-config file\fR]
[\fB\-srpvfile file\fR]
[\fB\-gn identifier\fR]
[\fB\-userinfo text...\fR]
[\fB\-passin arg\fR]
[\fB\-passout arg\fR]
[\fIuser...\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBsrp\fR command is user to maintain an SRP (secure remote password)
file.
At most one of the \fB\-add\fR, \fB\-modify\fR, \fB\-delete\fR, and \fB\-list\fR options
can be specified.
These options take zero or more usernames as parameters and perform the
appropriate operation on the SRP file.
For \fB\-list\fR, if no \fBuser\fR is given then all users are displayed.
.PP
The configuration file to use, and the section within the file, can be
specified with the \fB\-config\fR and \fB\-name\fR flags, respectively.
If the config file is not specified, the \fB\-srpvfile\fR can be used to
just specify the file to operate on.
.PP
The \fB\-userinfo\fR option specifies additional information to add when
adding or modifying a user.
.PP
The \fB\-gn\fR flag specifies the \fBg\fR and \fBN\fR values, using one of
the strengths defined in IETF RFC 5054.
.PP
The \fB\-passin\fR and \fB\-passout\fR arguments are parsed as described in
the \fBopenssl\fR\|(1) command.
.SH OPTIONS
.IX Header "OPTIONS"
.IP [\fB\-help\fR] 4
.IX Item "[-help]"
Display an option summary.
.IP [\fB\-verbose\fR] 4
.IX Item "[-verbose]"
Generate verbose output while processing.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
