.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_MD_METH_NEW 3"
.TH EVP_MD_METH_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_MD_meth_dup,
EVP_MD_meth_new, EVP_MD_meth_free, EVP_MD_meth_set_input_blocksize,
EVP_MD_meth_set_result_size, EVP_MD_meth_set_app_datasize,
EVP_MD_meth_set_flags, EVP_MD_meth_set_init, EVP_MD_meth_set_update,
EVP_MD_meth_set_final, EVP_MD_meth_set_copy, EVP_MD_meth_set_cleanup,
EVP_MD_meth_set_ctrl, EVP_MD_meth_get_input_blocksize,
EVP_MD_meth_get_result_size, EVP_MD_meth_get_app_datasize,
EVP_MD_meth_get_flags, EVP_MD_meth_get_init, EVP_MD_meth_get_update,
EVP_MD_meth_get_final, EVP_MD_meth_get_copy, EVP_MD_meth_get_cleanup,
EVP_MD_meth_get_ctrl
\&\- Routines to build up EVP_MD methods
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_MD *EVP_MD_meth_new(int md_type, int pkey_type);
\& void EVP_MD_meth_free(EVP_MD *md);
\& EVP_MD *EVP_MD_meth_dup(const EVP_MD *md);
\&
\& int EVP_MD_meth_set_input_blocksize(EVP_MD *md, int blocksize);
\& int EVP_MD_meth_set_result_size(EVP_MD *md, int resultsize);
\& int EVP_MD_meth_set_app_datasize(EVP_MD *md, int datasize);
\& int EVP_MD_meth_set_flags(EVP_MD *md, unsigned long flags);
\& int EVP_MD_meth_set_init(EVP_MD *md, int (*init)(EVP_MD_CTX *ctx));
\& int EVP_MD_meth_set_update(EVP_MD *md, int (*update)(EVP_MD_CTX *ctx,
\&                                                      const void *data,
\&                                                      size_t count));
\& int EVP_MD_meth_set_final(EVP_MD *md, int (*final)(EVP_MD_CTX *ctx,
\&                                                    unsigned char *md));
\& int EVP_MD_meth_set_copy(EVP_MD *md, int (*copy)(EVP_MD_CTX *to,
\&                                                  const EVP_MD_CTX *from));
\& int EVP_MD_meth_set_cleanup(EVP_MD *md, int (*cleanup)(EVP_MD_CTX *ctx));
\& int EVP_MD_meth_set_ctrl(EVP_MD *md, int (*ctrl)(EVP_MD_CTX *ctx, int cmd,
\&                                                  int p1, void *p2));
\&
\& int EVP_MD_meth_get_input_blocksize(const EVP_MD *md);
\& int EVP_MD_meth_get_result_size(const EVP_MD *md);
\& int EVP_MD_meth_get_app_datasize(const EVP_MD *md);
\& unsigned long EVP_MD_meth_get_flags(const EVP_MD *md);
\& int (*EVP_MD_meth_get_init(const EVP_MD *md))(EVP_MD_CTX *ctx);
\& int (*EVP_MD_meth_get_update(const EVP_MD *md))(EVP_MD_CTX *ctx,
\&                                                 const void *data,
\&                                                 size_t count);
\& int (*EVP_MD_meth_get_final(const EVP_MD *md))(EVP_MD_CTX *ctx,
\&                                                unsigned char *md);
\& int (*EVP_MD_meth_get_copy(const EVP_MD *md))(EVP_MD_CTX *to,
\&                                               const EVP_MD_CTX *from);
\& int (*EVP_MD_meth_get_cleanup(const EVP_MD *md))(EVP_MD_CTX *ctx);
\& int (*EVP_MD_meth_get_ctrl(const EVP_MD *md))(EVP_MD_CTX *ctx, int cmd,
\&                                               int p1, void *p2);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_MD\fR type is a structure for digest method implementation.
It can also have associated public/private key signing and verifying
routines.
.PP
\&\fBEVP_MD_meth_new()\fR creates a new \fBEVP_MD\fR structure.
.PP
\&\fBEVP_MD_meth_dup()\fR creates a copy of \fBmd\fR.
.PP
\&\fBEVP_MD_meth_free()\fR destroys a \fBEVP_MD\fR structure.
.PP
\&\fBEVP_MD_meth_set_input_blocksize()\fR sets the internal input block size
for the method \fBmd\fR to \fBblocksize\fR bytes.
.PP
\&\fBEVP_MD_meth_set_result_size()\fR sets the size of the result that the
digest method in \fBmd\fR is expected to produce to \fBresultsize\fR bytes.
.PP
The digest method may have its own private data, which OpenSSL will
allocate for it.  \fBEVP_MD_meth_set_app_datasize()\fR should be used to
set the size for it to \fBdatasize\fR.
.PP
\&\fBEVP_MD_meth_set_flags()\fR sets the flags to describe optional
behaviours in the particular \fBmd\fR.  Several flags can be or'd
together.  The available flags are:
.IP EVP_MD_FLAG_ONESHOT 4
.IX Item "EVP_MD_FLAG_ONESHOT"
This digest method can only handle one block of input.
.IP EVP_MD_FLAG_XOF 4
.IX Item "EVP_MD_FLAG_XOF"
This digest method is an extensible-output function (XOF) and supports
the \fBEVP_MD_CTRL_XOF_LEN\fR control.
.IP EVP_MD_FLAG_DIGALGID_NULL 4
.IX Item "EVP_MD_FLAG_DIGALGID_NULL"
When setting up a DigestAlgorithmIdentifier, this flag will have the
parameter set to NULL by default.  Use this for PKCS#1.  \fINote: if
combined with EVP_MD_FLAG_DIGALGID_ABSENT, the latter will override.\fR
.IP EVP_MD_FLAG_DIGALGID_ABSENT 4
.IX Item "EVP_MD_FLAG_DIGALGID_ABSENT"
When setting up a DigestAlgorithmIdentifier, this flag will have the
parameter be left absent by default.  \fINote: if combined with
EVP_MD_FLAG_DIGALGID_NULL, the latter will be overridden.\fR
.IP EVP_MD_FLAG_DIGALGID_CUSTOM 4
.IX Item "EVP_MD_FLAG_DIGALGID_CUSTOM"
Custom DigestAlgorithmIdentifier handling via ctrl, with
\&\fBEVP_MD_FLAG_DIGALGID_ABSENT\fR as default.  \fINote: if combined with
EVP_MD_FLAG_DIGALGID_NULL, the latter will be overridden.\fR
Currently unused.
.IP EVP_MD_FLAG_FIPS 4
.IX Item "EVP_MD_FLAG_FIPS"
This digest method is suitable for use in FIPS mode.
Currently unused.
.PP
\&\fBEVP_MD_meth_set_init()\fR sets the digest init function for \fBmd\fR.
The digest init function is called by \fBEVP_Digest()\fR, \fBEVP_DigestInit()\fR,
\&\fBEVP_DigestInit_ex()\fR, EVP_SignInit, \fBEVP_SignInit_ex()\fR, \fBEVP_VerifyInit()\fR
and \fBEVP_VerifyInit_ex()\fR.
.PP
\&\fBEVP_MD_meth_set_update()\fR sets the digest update function for \fBmd\fR.
The digest update function is called by \fBEVP_Digest()\fR, \fBEVP_DigestUpdate()\fR and
\&\fBEVP_SignUpdate()\fR.
.PP
\&\fBEVP_MD_meth_set_final()\fR sets the digest final function for \fBmd\fR.
The digest final function is called by \fBEVP_Digest()\fR, \fBEVP_DigestFinal()\fR,
\&\fBEVP_DigestFinal_ex()\fR, \fBEVP_SignFinal()\fR and \fBEVP_VerifyFinal()\fR.
.PP
\&\fBEVP_MD_meth_set_copy()\fR sets the function for \fBmd\fR to do extra
computations after the method's private data structure has been copied
from one \fBEVP_MD_CTX\fR to another.  If all that's needed is to copy
the data, there is no need for this copy function.
Note that the copy function is passed two \fBEVP_MD_CTX *\fR, the private
data structure is then available with \fBEVP_MD_CTX_md_data()\fR.
This copy function is called by \fBEVP_MD_CTX_copy()\fR and
\&\fBEVP_MD_CTX_copy_ex()\fR.
.PP
\&\fBEVP_MD_meth_set_cleanup()\fR sets the function for \fBmd\fR to do extra
cleanup before the method's private data structure is cleaned out and
freed.
Note that the cleanup function is passed a \fBEVP_MD_CTX *\fR, the
private data structure is then available with \fBEVP_MD_CTX_md_data()\fR.
This cleanup function is called by \fBEVP_MD_CTX_reset()\fR and
\&\fBEVP_MD_CTX_free()\fR.
.PP
\&\fBEVP_MD_meth_set_ctrl()\fR sets the control function for \fBmd\fR.
See \fBEVP_MD_CTX_ctrl\fR\|(3) for the available controls.
.PP
\&\fBEVP_MD_meth_get_input_blocksize()\fR, \fBEVP_MD_meth_get_result_size()\fR,
\&\fBEVP_MD_meth_get_app_datasize()\fR, \fBEVP_MD_meth_get_flags()\fR,
\&\fBEVP_MD_meth_get_init()\fR, \fBEVP_MD_meth_get_update()\fR,
\&\fBEVP_MD_meth_get_final()\fR, \fBEVP_MD_meth_get_copy()\fR,
\&\fBEVP_MD_meth_get_cleanup()\fR and \fBEVP_MD_meth_get_ctrl()\fR are all used
to retrieve the method data given with the EVP_MD_meth_set_*()
functions above.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_MD_meth_new()\fR and \fBEVP_MD_meth_dup()\fR return a pointer to a newly
created \fBEVP_MD\fR, or NULL on failure.
All EVP_MD_meth_set_*() functions return 1.
\&\fBEVP_MD_get_input_blocksize()\fR, \fBEVP_MD_meth_get_result_size()\fR,
\&\fBEVP_MD_meth_get_app_datasize()\fR and \fBEVP_MD_meth_get_flags()\fR return the
indicated sizes or flags.
All other EVP_CIPHER_meth_get_*() functions return pointers to their
respective \fBmd\fR function.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_DigestInit\fR\|(3), \fBEVP_SignInit\fR\|(3), \fBEVP_VerifyInit\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBEVP_MD\fR structure was openly available in OpenSSL before version
1.1.  The functions described here were added in OpenSSL 1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
