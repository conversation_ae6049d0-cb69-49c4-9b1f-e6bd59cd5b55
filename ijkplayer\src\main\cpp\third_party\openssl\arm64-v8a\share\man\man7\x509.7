.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509 7"
.TH X509 7 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
x509 \- X.509 certificate handling
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
An X.509 certificate is a structured grouping of information about
an individual, a device, or anything one can imagine.  An X.509 CRL
(certificate revocation list) is a tool to help determine if a
certificate is still valid.  The exact definition of those can be
found in the X.509 document from ITU-T, or in RFC3280 from PKIX.
In OpenSSL, the type X509 is used to express such a certificate, and
the type X509_CRL is used to express a CRL.
.PP
A related structure is a certificate request, defined in PKCS#10 from
RSA Security, Inc, also reflected in RFC2896.  In OpenSSL, the type
X509_REQ is used to express such a certificate request.
.PP
To handle some complex parts of a certificate, there are the types
X509_NAME (to express a certificate name), X509_ATTRIBUTE (to express
a certificate attribute), X509_EXTENSION (to express a certificate
extension) and a few more.
.PP
Finally, there's the supertype X509_INFO, which can contain a CRL, a
certificate and a corresponding private key.
.PP
\&\fBX509_\fR\fIXXX\fR, \fBd2i_X509_\fR\fIXXX\fR, and \fBi2d_X509_\fR\fIXXX\fR functions
handle X.509 certificates, with some exceptions, shown below.
.PP
\&\fBX509_CRL_\fR\fIXXX\fR, \fBd2i_X509_CRL_\fR\fIXXX\fR, and \fBi2d_X509_CRL_\fR\fIXXX\fR
functions handle X.509 CRLs.
.PP
\&\fBX509_REQ_\fR\fIXXX\fR, \fBd2i_X509_REQ_\fR\fIXXX\fR, and \fBi2d_X509_REQ_\fR\fIXXX\fR
functions handle PKCS#10 certificate requests.
.PP
\&\fBX509_NAME_\fR\fIXXX\fR functions handle certificate names.
.PP
\&\fBX509_ATTRIBUTE_\fR\fIXXX\fR functions handle certificate attributes.
.PP
\&\fBX509_EXTENSION_\fR\fIXXX\fR functions handle certificate extensions.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_NAME_ENTRY_get_object\fR\|(3),
\&\fBX509_NAME_add_entry_by_txt\fR\|(3),
\&\fBX509_NAME_add_entry_by_NID\fR\|(3),
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBX509_NAME_new\fR\|(3),
\&\fBd2i_X509\fR\|(3),
\&\fBd2i_X509_ALGOR\fR\|(3),
\&\fBd2i_X509_CRL\fR\|(3),
\&\fBd2i_X509_NAME\fR\|(3),
\&\fBd2i_X509_REQ\fR\|(3),
\&\fBd2i_X509_SIG\fR\|(3),
\&\fBX509v3\fR\|(3),
\&\fBcrypto\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2003\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
