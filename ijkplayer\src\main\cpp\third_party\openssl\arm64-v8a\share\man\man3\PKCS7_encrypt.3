.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS7_ENCRYPT 3"
.TH PKCS7_ENCRYPT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS7_encrypt \- create a PKCS#7 envelopedData structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs7.h>
\&
\& PKCS7 *PKCS7_encrypt(STACK_OF(X509) *certs, BIO *in, const EVP_CIPHER *cipher,
\&                      int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS7_encrypt()\fR creates and returns a PKCS#7 envelopedData structure. \fBcerts\fR
is a list of recipient certificates. \fBin\fR is the content to be encrypted.
\&\fBcipher\fR is the symmetric cipher to use. \fBflags\fR is an optional set of flags.
.SH NOTES
.IX Header "NOTES"
Only RSA keys are supported in PKCS#7 and envelopedData so the recipient
certificates supplied to this function must all contain RSA public keys, though
they do not have to be signed using the RSA algorithm.
.PP
\&\fBEVP_des_ede3_cbc()\fR (triple DES) is the algorithm of choice for S/MIME use
because most clients will support it.
.PP
Some old "export grade" clients may only support weak encryption using 40 or 64
bit RC2. These can be used by passing \fBEVP_rc2_40_cbc()\fR and \fBEVP_rc2_64_cbc()\fR
respectively.
.PP
The algorithm passed in the \fBcipher\fR parameter must support ASN1 encoding of
its parameters.
.PP
Many browsers implement a "sign and encrypt" option which is simply an S/MIME
envelopedData containing an S/MIME signed message. This can be readily produced
by storing the S/MIME signed message in a memory BIO and passing it to
\&\fBPKCS7_encrypt()\fR.
.PP
The following flags can be passed in the \fBflags\fR parameter.
.PP
If the \fBPKCS7_TEXT\fR flag is set MIME headers for type \fBtext/plain\fR are
prepended to the data.
.PP
Normally the supplied content is translated into MIME canonical format (as
required by the S/MIME specifications) if \fBPKCS7_BINARY\fR is set no translation
occurs. This option should be used if the supplied data is in binary format
otherwise the translation will corrupt it. If \fBPKCS7_BINARY\fR is set then
\&\fBPKCS7_TEXT\fR is ignored.
.PP
If the \fBPKCS7_STREAM\fR flag is set a partial \fBPKCS7\fR structure is output
suitable for streaming I/O: no data is read from the BIO \fBin\fR.
.SH NOTES
.IX Header "NOTES"
If the flag \fBPKCS7_STREAM\fR is set the returned \fBPKCS7\fR structure is \fBnot\fR
complete and outputting its contents via a function that does not
properly finalize the \fBPKCS7\fR structure will give unpredictable
results.
.PP
Several functions including \fBSMIME_write_PKCS7()\fR, \fBi2d_PKCS7_bio_stream()\fR,
\&\fBPEM_write_bio_PKCS7_stream()\fR finalize the structure. Alternatively finalization
can be performed by obtaining the streaming ASN1 \fBBIO\fR directly using
\&\fBBIO_new_PKCS7()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS7_encrypt()\fR returns either a PKCS7 structure or NULL if an error occurred.
The error can be obtained from \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBPKCS7_decrypt\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBPKCS7_STREAM\fR flag was added in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
