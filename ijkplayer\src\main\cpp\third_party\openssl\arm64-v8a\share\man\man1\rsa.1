.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA 1"
.TH RSA 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-rsa,
rsa \- RSA key processing tool
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBrsa\fR
[\fB\-help\fR]
[\fB\-inform PEM|DER\fR]
[\fB\-outform PEM|DER\fR]
[\fB\-in filename\fR]
[\fB\-passin arg\fR]
[\fB\-out filename\fR]
[\fB\-passout arg\fR]
[\fB\-aes128\fR]
[\fB\-aes192\fR]
[\fB\-aes256\fR]
[\fB\-aria128\fR]
[\fB\-aria192\fR]
[\fB\-aria256\fR]
[\fB\-camellia128\fR]
[\fB\-camellia192\fR]
[\fB\-camellia256\fR]
[\fB\-des\fR]
[\fB\-des3\fR]
[\fB\-idea\fR]
[\fB\-text\fR]
[\fB\-noout\fR]
[\fB\-modulus\fR]
[\fB\-check\fR]
[\fB\-pubin\fR]
[\fB\-pubout\fR]
[\fB\-RSAPublicKey_in\fR]
[\fB\-RSAPublicKey_out\fR]
[\fB\-engine id\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBrsa\fR command processes RSA keys. They can be converted between various
forms and their components printed out. \fBNote\fR this command uses the
traditional SSLeay compatible format for private key encryption: newer
applications should use the more secure PKCS#8 format using the \fBpkcs8\fR
utility.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format. The \fBDER\fR option uses an ASN1 DER encoded
form compatible with the PKCS#1 RSAPrivateKey or SubjectPublicKeyInfo format.
The \fBPEM\fR form is the default format: it consists of the \fBDER\fR format base64
encoded with additional header and footer lines. On input PKCS#8 format private
keys are also accepted.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format, the options have the same meaning and default
as the \fB\-inform\fR option.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read a key from or standard input if this
option is not specified. If the key is encrypted a pass phrase will be
prompted for.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
The input file password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write a key to or standard output if this
option is not specified. If any encryption options are set then a pass phrase
will be prompted for. The output filename should \fBnot\fR be the same as the input
filename.
.IP "\fB\-passout password\fR" 4
.IX Item "-passout password"
The output file password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-aes128\fR, \fB\-aes192\fR, \fB\-aes256\fR, \fB\-aria128\fR, \fB\-aria192\fR, \fB\-aria256\fR, \fB\-camellia128\fR, \fB\-camellia192\fR, \fB\-camellia256\fR, \fB\-des\fR, \fB\-des3\fR, \fB\-idea\fR" 4
.IX Item "-aes128, -aes192, -aes256, -aria128, -aria192, -aria256, -camellia128, -camellia192, -camellia256, -des, -des3, -idea"
These options encrypt the private key with the specified
cipher before outputting it. A pass phrase is prompted for.
If none of these options is specified the key is written in plain text. This
means that using the \fBrsa\fR utility to read in an encrypted key with no
encryption option can be used to remove the pass phrase from a key, or by
setting the encryption options it can be use to add or change the pass phrase.
These options can only be used with PEM format output files.
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the various public or private key components in
plain text in addition to the encoded version.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option prevents output of the encoded version of the key.
.IP \fB\-modulus\fR 4
.IX Item "-modulus"
This option prints out the value of the modulus of the key.
.IP \fB\-check\fR 4
.IX Item "-check"
This option checks the consistency of an RSA private key.
.IP \fB\-pubin\fR 4
.IX Item "-pubin"
By default a private key is read from the input file: with this
option a public key is read instead.
.IP \fB\-pubout\fR 4
.IX Item "-pubout"
By default a private key is output: with this option a public
key will be output instead. This option is automatically set if
the input is a public key.
.IP "\fB\-RSAPublicKey_in\fR, \fB\-RSAPublicKey_out\fR" 4
.IX Item "-RSAPublicKey_in, -RSAPublicKey_out"
Like \fB\-pubin\fR and \fB\-pubout\fR except \fBRSAPublicKey\fR format is used instead.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBrsa\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.SH NOTES
.IX Header "NOTES"
The PEM private key format uses the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN RSA PRIVATE KEY\-\-\-\-\-
\& \-\-\-\-\-END RSA PRIVATE KEY\-\-\-\-\-
.Ve
.PP
The PEM public key format uses the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN PUBLIC KEY\-\-\-\-\-
\& \-\-\-\-\-END PUBLIC KEY\-\-\-\-\-
.Ve
.PP
The PEM \fBRSAPublicKey\fR format uses the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN RSA PUBLIC KEY\-\-\-\-\-
\& \-\-\-\-\-END RSA PUBLIC KEY\-\-\-\-\-
.Ve
.SH EXAMPLES
.IX Header "EXAMPLES"
To remove the pass phrase on an RSA private key:
.PP
.Vb 1
\& openssl rsa \-in key.pem \-out keyout.pem
.Ve
.PP
To encrypt a private key using triple DES:
.PP
.Vb 1
\& openssl rsa \-in key.pem \-des3 \-out keyout.pem
.Ve
.PP
To convert a private key from PEM to DER format:
.PP
.Vb 1
\& openssl rsa \-in key.pem \-outform DER \-out keyout.der
.Ve
.PP
To print out the components of a private key to standard output:
.PP
.Vb 1
\& openssl rsa \-in key.pem \-text \-noout
.Ve
.PP
To just output the public part of a private key:
.PP
.Vb 1
\& openssl rsa \-in key.pem \-pubout \-out pubkey.pem
.Ve
.PP
Output the public part of a private key in \fBRSAPublicKey\fR format:
.PP
.Vb 1
\& openssl rsa \-in key.pem \-RSAPublicKey_out \-out pubkey.pem
.Ve
.SH BUGS
.IX Header "BUGS"
There should be an option that automatically handles .key files,
without having to manually edit them.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBpkcs8\fR\|(1), \fBdsa\fR\|(1), \fBgenrsa\fR\|(1),
\&\fBgendsa\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
