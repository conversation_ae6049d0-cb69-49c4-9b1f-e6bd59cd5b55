<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BN_set_bit</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BN_set_bit, BN_clear_bit, BN_is_bit_set, BN_mask_bits, BN_lshift, BN_lshift1, BN_rshift, BN_rshift1 - bit operations on BIGNUMs</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bn.h&gt;

int BN_set_bit(BIGNUM *a, int n);
int BN_clear_bit(BIGNUM *a, int n);

int BN_is_bit_set(const BIGNUM *a, int n);

int BN_mask_bits(BIGNUM *a, int n);

int BN_lshift(BIGNUM *r, const BIGNUM *a, int n);
int BN_lshift1(BIGNUM *r, BIGNUM *a);

int BN_rshift(BIGNUM *r, BIGNUM *a, int n);
int BN_rshift1(BIGNUM *r, BIGNUM *a);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>BN_set_bit() sets bit <b>n</b> in <b>a</b> to 1 (<code>a|=(1&lt;&lt;n)</code>). The number is expanded if necessary.</p>

<p>BN_clear_bit() sets bit <b>n</b> in <b>a</b> to 0 (<code>a&amp;=~(1&lt;&lt;n)</code>). An error occurs if <b>a</b> is shorter than <b>n</b> bits.</p>

<p>BN_is_bit_set() tests if bit <b>n</b> in <b>a</b> is set.</p>

<p>BN_mask_bits() truncates <b>a</b> to an <b>n</b> bit number (<code>a&amp;=~((~0)&lt;&lt;n)</code>). An error occurs if <b>a</b> already is shorter than <b>n</b> bits.</p>

<p>BN_lshift() shifts <b>a</b> left by <b>n</b> bits and places the result in <b>r</b> (<code>r=a*2^n</code>). Note that <b>n</b> must be nonnegative. BN_lshift1() shifts <b>a</b> left by one and places the result in <b>r</b> (<code>r=2*a</code>).</p>

<p>BN_rshift() shifts <b>a</b> right by <b>n</b> bits and places the result in <b>r</b> (<code>r=a/2^n</code>). Note that <b>n</b> must be nonnegative. BN_rshift1() shifts <b>a</b> right by one and places the result in <b>r</b> (<code>r=a/2</code>).</p>

<p>For the shift functions, <b>r</b> and <b>a</b> may be the same variable.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BN_is_bit_set() returns 1 if the bit is set, 0 otherwise.</p>

<p>All other functions return 1 for success, 0 on error. The error codes can be obtained by <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/BN_num_bytes.html">BN_num_bytes(3)</a>, <a href="../man3/BN_add.html">BN_add(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


