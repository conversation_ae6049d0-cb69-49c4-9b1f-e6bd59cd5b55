.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_LOOKUP 3"
.TH X509_LOOKUP 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_LOOKUP, X509_LOOKUP_TYPE,
X509_LOOKUP_new, X509_LOOKUP_free, X509_LOOKUP_init,
X509_LOOKUP_shutdown,
X509_LOOKUP_set_method_data, X509_LOOKUP_get_method_data,
X509_LOOKUP_ctrl,
X509_LOOKUP_load_file, X509_LOOKUP_add_dir,
X509_LOOKUP_get_store, X509_LOOKUP_by_subject,
X509_LOOKUP_by_issuer_serial, X509_LOOKUP_by_fingerprint,
X509_LOOKUP_by_alias
\&\- OpenSSL certificate lookup mechanisms
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509_vfy.h>
\&
\& typedef x509_lookup_st X509_LOOKUP;
\&
\& typedef enum X509_LOOKUP_TYPE;
\&
\& X509_LOOKUP *X509_LOOKUP_new(X509_LOOKUP_METHOD *method);
\& int X509_LOOKUP_init(X509_LOOKUP *ctx);
\& int X509_LOOKUP_shutdown(X509_LOOKUP *ctx);
\& void X509_LOOKUP_free(X509_LOOKUP *ctx);
\&
\& int X509_LOOKUP_set_method_data(X509_LOOKUP *ctx, void *data);
\& void *X509_LOOKUP_get_method_data(const X509_LOOKUP *ctx);
\&
\& int X509_LOOKUP_ctrl(X509_LOOKUP *ctx, int cmd, const char *argc,
\&                      long argl, char **ret);
\& int X509_LOOKUP_load_file(X509_LOOKUP *ctx, char *name, long type);
\& int X509_LOOKUP_add_dir(X509_LOOKUP *ctx, char *name, long type);
\&
\& X509_STORE *X509_LOOKUP_get_store(const X509_LOOKUP *ctx);
\&
\& int X509_LOOKUP_by_subject(X509_LOOKUP *ctx, X509_LOOKUP_TYPE type,
\&                            X509_NAME *name, X509_OBJECT *ret);
\& int X509_LOOKUP_by_issuer_serial(X509_LOOKUP *ctx, X509_LOOKUP_TYPE type,
\&                                  X509_NAME *name, ASN1_INTEGER *serial,
\&                                  X509_OBJECT *ret);
\& int X509_LOOKUP_by_fingerprint(X509_LOOKUP *ctx, X509_LOOKUP_TYPE type,
\&                                const unsigned char *bytes, int len,
\&                                X509_OBJECT *ret);
\& int X509_LOOKUP_by_alias(X509_LOOKUP *ctx, X509_LOOKUP_TYPE type,
\&                          const char *str, int len, X509_OBJECT *ret);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBX509_LOOKUP\fR structure holds the information needed to look up
certificates and CRLs according to an associated \fBX509_LOOKUP_METHOD\fR\|(3).
Multiple \fBX509_LOOKUP\fR instances can be added to an \fBX509_STORE\fR\|(3)
to enable lookup in that store.
.PP
\&\fBX509_LOOKUP_new()\fR creates a new \fBX509_LOOKUP\fR using the given lookup
\&\fImethod\fR.
It can also be created by calling \fBX509_STORE_add_lookup\fR\|(3), which
will associate an \fBX509_STORE\fR with the lookup mechanism.
.PP
\&\fBX509_LOOKUP_init()\fR initializes the internal state and resources as
needed by the given \fBX509_LOOKUP\fR to do its work.
.PP
\&\fBX509_LOOKUP_shutdown()\fR tears down the internal state and resources of
the given \fBX509_LOOKUP\fR.
.PP
\&\fBX509_LOOKUP_free()\fR destructs the given \fBX509_LOOKUP\fR.
.PP
\&\fBX509_LOOKUP_set_method_data()\fR associates a pointer to application data
to the given \fBX509_LOOKUP\fR.
.PP
\&\fBX509_LOOKUP_get_method_data()\fR retrieves a pointer to application data
from the given \fBX509_LOOKUP\fR.
.PP
\&\fBX509_LOOKUP_ctrl()\fR is used to set or get additional data to or from an
\&\fBX509_LOOKUP\fR structure or its associated \fBX509_LOOKUP_METHOD\fR\|(3).
The arguments of the control command are passed via \fIargc\fR and \fIargl\fR,
its return value via \fI*ret\fR.
The meaning of the arguments depends on the \fIcmd\fR number of the
control command. In general, this function is not called directly, but
wrapped by a macro call, see below.
The control \fIcmd\fRs known to OpenSSL are discussed in more depth
in "Control Commands".
.PP
\&\fBX509_LOOKUP_load_file()\fR passes a filename to be loaded immediately
into the associated \fBX509_STORE\fR.
\&\fItype\fR indicates what type of object is expected.
This can only be used with a lookup using the implementation
\&\fBX509_LOOKUP_file\fR\|(3).
.PP
\&\fBX509_LOOKUP_add_dir()\fR passes a directory specification from which
certificates and CRLs are loaded on demand into the associated
\&\fBX509_STORE\fR.
\&\fItype\fR indicates what type of object is expected.
This can only be used with a lookup using the implementation
\&\fBX509_LOOKUP_hash_dir\fR\|(3).
.PP
\&\fBX509_LOOKUP_load_file()\fR, \fBX509_LOOKUP_add_dir()\fR,
\&\fBX509_LOOKUP_add_store()\fR, and \fBX509_LOOKUP_load_store()\fR are implemented
as macros that use \fBX509_LOOKUP_ctrl()\fR.
.PP
\&\fBX509_LOOKUP_by_subject()\fR, \fBX509_LOOKUP_by_issuer_serial()\fR,
\&\fBX509_LOOKUP_by_fingerprint()\fR, and \fBX509_LOOKUP_by_alias()\fR look up
certificates and CRLs in the \fBX509_STORE\fR\|(3) associated with the
\&\fBX509_LOOKUP\fR using different criteria, where the looked up object is
stored in \fIret\fR.
Some of the underlying \fBX509_LOOKUP_METHOD\fRs will also cache objects
matching the criteria in the associated \fBX509_STORE\fR, which makes it
possible to handle cases where the criteria have more than one hit.
.SS "File Types"
.IX Subsection "File Types"
\&\fBX509_LOOKUP_load_file()\fR and \fBX509_LOOKUP_add_dir()\fR take a \fItype\fR,
which can be one of the following:
.IP \fBX509_FILETYPE_PEM\fR 4
.IX Item "X509_FILETYPE_PEM"
The file or files that are loaded are expected to be in PEM format.
.IP \fBX509_FILETYPE_ASN1\fR 4
.IX Item "X509_FILETYPE_ASN1"
The file or files that are loaded are expected to be in raw DER format.
.IP \fBX509_FILETYPE_DEFAULT\fR 4
.IX Item "X509_FILETYPE_DEFAULT"
The default certificate file or directory is used.  In this case,
\&\fIname\fR is ignored.
.SS "Control Commands"
.IX Subsection "Control Commands"
The \fBX509_LOOKUP_METHOD\fRs built into OpenSSL recognise the following
\&\fBX509_LOOKUP_ctrl()\fR \fIcmd\fRs:
.IP \fBX509_L_FILE_LOAD\fR 4
.IX Item "X509_L_FILE_LOAD"
This is the command that \fBX509_LOOKUP_load_file()\fR uses.
The filename is passed in \fIargc\fR, and the type in \fIargl\fR.
.IP \fBX509_L_ADD_DIR\fR 4
.IX Item "X509_L_ADD_DIR"
This is the command that \fBX509_LOOKUP_add_dir()\fR uses.
The directory specification is passed in \fIargc\fR, and the type in
\&\fIargl\fR.
.IP \fBX509_L_ADD_STORE\fR 4
.IX Item "X509_L_ADD_STORE"
This is the command that \fBX509_LOOKUP_add_store()\fR uses.
The URI is passed in \fIargc\fR.
.IP \fBX509_L_LOAD_STORE\fR 4
.IX Item "X509_L_LOAD_STORE"
This is the command that \fBX509_LOOKUP_load_store()\fR uses.
The URI is passed in \fIargc\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_LOOKUP_new()\fR returns an \fBX509_LOOKUP\fR pointer when successful,
or NULL on error.
.PP
\&\fBX509_LOOKUP_init()\fR and \fBX509_LOOKUP_shutdown()\fR return 1 on success, or
0 on error.
.PP
\&\fBX509_LOOKUP_ctrl()\fR returns \-1 if the \fBX509_LOOKUP\fR doesn't have an
associated \fBX509_LOOKUP_METHOD\fR, or 1 if the 
doesn't have a control function.
Otherwise, it returns what the control function in the
\&\fBX509_LOOKUP_METHOD\fR returns, which is usually 1 on success and 0 in
error.
.IX Xref "509_LOOKUP_METHOD"
.PP
\&\fBX509_LOOKUP_get_store()\fR returns an \fBX509_STORE\fR pointer if there is
one, otherwise NULL.
.PP
\&\fBX509_LOOKUP_by_subject()\fR, \fBX509_LOOKUP_by_issuer_serial()\fR,
\&\fBX509_LOOKUP_by_fingerprint()\fR, and \fBX509_LOOKUP_by_alias()\fR all return 0
if there is no \fBX509_LOOKUP_METHOD\fR or that method doesn't implement
the corresponding function.
Otherwise, it returns what the corresponding function in the
\&\fBX509_LOOKUP_METHOD\fR returns, which is usually 1 on success and 0 in
error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_LOOKUP_METHOD\fR\|(3), \fBX509_STORE\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
