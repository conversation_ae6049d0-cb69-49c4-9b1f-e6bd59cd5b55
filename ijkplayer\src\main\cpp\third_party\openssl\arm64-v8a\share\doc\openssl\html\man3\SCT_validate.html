<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SCT_validate</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SCT_validate, SCT_LIST_validate, SCT_get_validation_status - checks Signed Certificate Timestamps (SCTs) are valid</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ct.h&gt;

typedef enum {
    SCT_VALIDATION_STATUS_NOT_SET,
    SCT_VALIDATION_STATUS_UNKNOWN_LOG,
    SCT_VALIDATION_STATUS_VALID,
    SCT_VALIDATION_STATUS_INVALID,
    SCT_VALIDATION_STATUS_UNVERIFIED,
    SCT_VALIDATION_STATUS_UNKNOWN_VERSION
} sct_validation_status_t;

int SCT_validate(SCT *sct, const CT_POLICY_EVAL_CTX *ctx);
int SCT_LIST_validate(const STACK_OF(SCT) *scts, CT_POLICY_EVAL_CTX *ctx);
sct_validation_status_t SCT_get_validation_status(const SCT *sct);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SCT_validate() will check that an SCT is valid and verify its signature. SCT_LIST_validate() performs the same checks on an entire stack of SCTs. The result of the validation checks can be obtained by passing the SCT to SCT_get_validation_status().</p>

<p>A CT_POLICY_EVAL_CTX must be provided that specifies:</p>

<ul>

<li><p>The certificate the SCT was issued for.</p>

<p>Failure to provide the certificate will result in the validation status being SCT_VALIDATION_STATUS_UNVERIFIED.</p>

</li>
<li><p>The issuer of that certificate.</p>

<p>This is only required if the SCT was issued for a pre-certificate (see RFC 6962). If it is required but not provided, the validation status will be SCT_VALIDATION_STATUS_UNVERIFIED.</p>

</li>
<li><p>A CTLOG_STORE that contains the CT log that issued this SCT.</p>

<p>If the SCT was issued by a log that is not in this CTLOG_STORE, the validation status will be SCT_VALIDATION_STATUS_UNKNOWN_LOG.</p>

</li>
</ul>

<p>If the SCT is of an unsupported version (only v1 is currently supported), the validation status will be SCT_VALIDATION_STATUS_UNKNOWN_VERSION.</p>

<p>If the SCT&#39;s signature is incorrect, its timestamp is in the future (relative to the time in CT_POLICY_EVAL_CTX), or if it is otherwise invalid, the validation status will be SCT_VALIDATION_STATUS_INVALID.</p>

<p>If all checks pass, the validation status will be SCT_VALIDATION_STATUS_VALID.</p>

<h1 id="NOTES">NOTES</h1>

<p>A return value of 0 from SCT_LIST_validate() should not be interpreted as a failure. At a minimum, only one valid SCT may provide sufficient confidence that a certificate has been publicly logged.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SCT_validate() returns a negative integer if an internal error occurs, 0 if the SCT fails validation, or 1 if the SCT passes validation.</p>

<p>SCT_LIST_validate() returns a negative integer if an internal error occurs, 0 if any of SCTs fails validation, or 1 if they all pass validation.</p>

<p>SCT_get_validation_status() returns the validation status of the SCT. If SCT_validate() or SCT_LIST_validate() have not been passed that SCT, the returned value will be SCT_VALIDATION_STATUS_NOT_SET.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ct.html">ct(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


