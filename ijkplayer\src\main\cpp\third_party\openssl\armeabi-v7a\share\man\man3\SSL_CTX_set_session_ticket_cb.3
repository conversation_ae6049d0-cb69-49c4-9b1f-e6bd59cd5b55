.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_SESSION_TICKET_CB 3"
.TH SSL_CTX_SET_SESSION_TICKET_CB 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_session_ticket_cb,
SSL_SESSION_get0_ticket_appdata,
SSL_SESSION_set1_ticket_appdata,
SSL_CTX_generate_session_ticket_fn,
SSL_CTX_decrypt_session_ticket_fn \- manage session ticket application data
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& typedef int (*SSL_CTX_generate_session_ticket_fn)(SSL *s, void *arg);
\& typedef SSL_TICKET_RETURN (*SSL_CTX_decrypt_session_ticket_fn)(SSL *s, SSL_SESSION *ss,
\&                                                                const unsigned char *keyname,
\&                                                                size_t keyname_len,
\&                                                                SSL_TICKET_STATUS status,
\&                                                                void *arg);
\& int SSL_CTX_set_session_ticket_cb(SSL_CTX *ctx,
\&                                   SSL_CTX_generate_session_ticket_fn gen_cb,
\&                                   SSL_CTX_decrypt_session_ticket_fn dec_cb,
\&                                   void *arg);
\& int SSL_SESSION_set1_ticket_appdata(SSL_SESSION *ss, const void *data, size_t len);
\& int SSL_SESSION_get0_ticket_appdata(SSL_SESSION *ss, void **data, size_t *len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_set_session_ticket_cb()\fR sets the application callbacks \fBgen_cb\fR
and \fBdec_cb\fR that are used by a server to set and get application data stored
with a session, and placed into a session ticket. Either callback function may
be set to NULL. The value of \fBarg\fR is passed to the callbacks.
.PP
\&\fBgen_cb\fR is the application defined callback invoked when a session ticket is
about to be created. The application can call \fBSSL_SESSION_set1_ticket_appdata()\fR
at this time to add application data to the session ticket. The value of \fBarg\fR
is the same as that given to \fBSSL_CTX_set_session_ticket_cb()\fR. The \fBgen_cb\fR
callback is defined as type \fBSSL_CTX_generate_session_ticket_fn\fR.
.PP
\&\fBdec_cb\fR is the application defined callback invoked after session ticket
decryption has been attempted and any session ticket application data is
available. If ticket decryption was successful then the \fBss\fR argument contains
the session data. The \fBkeyname\fR and \fBkeyname_len\fR arguments identify the key
used to decrypt the session ticket. The \fBstatus\fR argument is the result of the
ticket decryption. See the NOTES section below for further details. The value
of \fBarg\fR is the same as that given to \fBSSL_CTX_set_session_ticket_cb()\fR. The
\&\fBdec_cb\fR callback is defined as type \fBSSL_CTX_decrypt_session_ticket_fn\fR.
.PP
\&\fBSSL_SESSION_set1_ticket_appdata()\fR sets the application data specified by
\&\fBdata\fR and \fBlen\fR into \fBss\fR which is then placed into any generated session
tickets. It can be called at any time before a session ticket is created to
update the data placed into the session ticket. However, given that sessions
and tickets are created by the handshake, the \fBgen_cb\fR is provided to notify
the application that a session ticket is about to be generated.
.PP
\&\fBSSL_SESSION_get0_ticket_appdata()\fR assigns \fBdata\fR to the session ticket
application data and assigns \fBlen\fR to the length of the session ticket
application data from \fBss\fR. The application data can be set via
\&\fBSSL_SESSION_set1_ticket_appdata()\fR or by a session ticket. NULL will be assigned
to \fBdata\fR and 0 will be assigned to \fBlen\fR if there is no session ticket
application data. \fBSSL_SESSION_get0_ticket_appdata()\fR can be called any time
after a session has been created. The \fBdec_cb\fR is provided to notify the
application that a session ticket has just been decrypted.
.SH NOTES
.IX Header "NOTES"
When the \fBdec_cb\fR callback is invoked, the SSL_SESSION \fBss\fR has not yet been
assigned to the SSL \fBs\fR. The \fBstatus\fR indicates the result of the ticket
decryption. The callback must check the \fBstatus\fR value before performing any
action, as it is called even if ticket decryption fails.
.PP
The \fBkeyname\fR and \fBkeyname_len\fR arguments to \fBdec_cb\fR may be used to identify
the key that was used to encrypt the session ticket.
.PP
The \fBstatus\fR argument can be any of these values:
.IP SSL_TICKET_EMPTY 4
.IX Item "SSL_TICKET_EMPTY"
Empty ticket present. No ticket data will be used and a new ticket should be
sent to the client. This only occurs in TLSv1.2 or below. In TLSv1.3 it is not
valid for a client to send an empty ticket.
.IP SSL_TICKET_NO_DECRYPT 4
.IX Item "SSL_TICKET_NO_DECRYPT"
The ticket couldn't be decrypted. No ticket data will be used and a new ticket
should be sent to the client.
.IP SSL_TICKET_SUCCESS 4
.IX Item "SSL_TICKET_SUCCESS"
A ticket was successfully decrypted, any session ticket application data should
be available. A new ticket should not be sent to the client.
.IP SSL_TICKET_SUCCESS_RENEW 4
.IX Item "SSL_TICKET_SUCCESS_RENEW"
Same as \fBSSL_TICKET_SUCCESS\fR, but a new ticket should be sent to the client.
.PP
The return value can be any of these values:
.IP SSL_TICKET_RETURN_ABORT 4
.IX Item "SSL_TICKET_RETURN_ABORT"
The handshake should be aborted, either because of an error or because of some
policy. Note that in TLSv1.3 a client may send more than one ticket in a single
handshake. Therefore, just because one ticket is unacceptable it does not mean
that all of them are. For this reason this option should be used with caution.
.IP SSL_TICKET_RETURN_IGNORE 4
.IX Item "SSL_TICKET_RETURN_IGNORE"
Do not use a ticket (if one was available). Do not send a renewed ticket to the
client.
.IP SSL_TICKET_RETURN_IGNORE_RENEW 4
.IX Item "SSL_TICKET_RETURN_IGNORE_RENEW"
Do not use a ticket (if one was available). Send a renewed ticket to the client.
.Sp
If the callback does not wish to change the default ticket behaviour then it
should return this value if \fBstatus\fR is \fBSSL_TICKET_EMPTY\fR or
\&\fBSSL_TICKET_NO_DECRYPT\fR.
.IP SSL_TICKET_RETURN_USE 4
.IX Item "SSL_TICKET_RETURN_USE"
Use the ticket. Do not send a renewed ticket to the client. It is an error for
the callback to return this value if \fBstatus\fR has a value other than
\&\fBSSL_TICKET_SUCCESS\fR or \fBSSL_TICKET_SUCCESS_RENEW\fR.
.Sp
If the callback does not wish to change the default ticket behaviour then it
should return this value if \fBstatus\fR is \fBSSL_TICKET_SUCCESS\fR.
.IP SSL_TICKET_RETURN_USE_RENEW 4
.IX Item "SSL_TICKET_RETURN_USE_RENEW"
Use the ticket. Send a renewed ticket to the client. It is an error for the
callback to return this value if \fBstatus\fR has a value other than
\&\fBSSL_TICKET_SUCCESS\fR or \fBSSL_TICKET_SUCCESS_RENEW\fR.
.Sp
If the callback does not wish to change the default ticket behaviour then it
should return this value if \fBstatus\fR is \fBSSL_TICKET_SUCCESS_RENEW\fR.
.PP
If \fBstatus\fR has the value \fBSSL_TICKET_EMPTY\fR or \fBSSL_TICKET_NO_DECRYPT\fR then
no session data will be available and the callback must not use the \fBss\fR
argument. If \fBstatus\fR has the value \fBSSL_TICKET_SUCCESS\fR or
\&\fBSSL_TICKET_SUCCESS_RENEW\fR then the application can call
\&\fBSSL_SESSION_get0_ticket_appdata()\fR using the session provided in the \fBss\fR
argument to retrieve the application data.
.PP
When the \fBgen_cb\fR callback is invoked, the \fBSSL_get_session()\fR function can be
used to retrieve the SSL_SESSION for \fBSSL_SESSION_set1_ticket_appdata()\fR.
.PP
By default, in TLSv1.2 and below, a new session ticket is not issued on a
successful resumption and therefore \fBgen_cb\fR will not be called. In TLSv1.3 the
default behaviour is to always issue a new ticket on resumption. In both cases
this behaviour can be changed if a ticket key callback is in use (see
\&\fBSSL_CTX_set_tlsext_ticket_key_cb\fR\|(3)).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBSSL_CTX_set_session_ticket_cb()\fR, \fBSSL_SESSION_set1_ticket_appdata()\fR and
\&\fBSSL_SESSION_get0_ticket_appdata()\fR functions return 1 on success and 0 on
failure.
.PP
The \fBgen_cb\fR callback must return 1 to continue the connection. A return of 0
will terminate the connection with an INTERNAL_ERROR alert.
.PP
The \fBdec_cb\fR callback must return a value as described in NOTES above.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_get_session\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_CTX_set_session_ticket_cb()\fR, \fBSSL_SESSION_set1_ticket_appdata()\fR
and \fBSSL_SESSION_get_ticket_appdata()\fR functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
