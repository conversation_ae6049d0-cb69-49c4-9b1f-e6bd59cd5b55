.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "D2I_PRIVATEKEY 3"
.TH D2I_PRIVATEKEY 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
d2i_PrivateKey, d2i_PublicKey, d2i_AutoPrivateKey,
i2d_PrivateKey, i2d_PublicKey,
d2i_PrivateKey_bio, d2i_PrivateKey_fp
\&\- decode and encode functions for reading and saving EVP_PKEY structures
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_PKEY *d2i_PrivateKey(int type, EVP_PKEY **a, const unsigned char **pp,
\&                          long length);
\& EVP_PKEY *d2i_PublicKey(int type, EVP_PKEY **a, const unsigned char **pp,
\&                         long length);
\& EVP_PKEY *d2i_AutoPrivateKey(EVP_PKEY **a, const unsigned char **pp,
\&                              long length);
\& int i2d_PrivateKey(EVP_PKEY *a, unsigned char **pp);
\& int i2d_PublicKey(EVP_PKEY *a, unsigned char **pp);
\&
\& EVP_PKEY *d2i_PrivateKey_bio(BIO *bp, EVP_PKEY **a);
\& EVP_PKEY *d2i_PrivateKey_fp(FILE *fp, EVP_PKEY **a)
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBd2i_PrivateKey()\fR decodes a private key using algorithm \fBtype\fR. It attempts to
use any key specific format or PKCS#8 unencrypted PrivateKeyInfo format. The
\&\fBtype\fR parameter should be a public key algorithm constant such as
\&\fBEVP_PKEY_RSA\fR. An error occurs if the decoded key does not match \fBtype\fR.
\&\fBd2i_PublicKey()\fR does the same for public keys.
.PP
\&\fBd2i_AutoPrivateKey()\fR is similar to \fBd2i_PrivateKey()\fR except it attempts to
automatically detect the private key format.
.PP
\&\fBi2d_PrivateKey()\fR encodes \fBkey\fR. It uses a key specific format or, if none is
defined for that key type, PKCS#8 unencrypted PrivateKeyInfo format.
\&\fBi2d_PublicKey()\fR does the same for public keys.
.PP
These functions are similar to the \fBd2i_X509()\fR functions; see \fBd2i_X509\fR\|(3).
.SH NOTES
.IX Header "NOTES"
All the functions that operate on data in memory update the data pointer \fI*pp\fR
after a successful operation, just like the other d2i and i2d functions;
see \fBd2i_X509\fR\|(3).
.PP
All these functions use DER format and unencrypted keys. Applications wishing
to encrypt or decrypt private keys should use other functions such as
\&\fBd2i_PKCS8PrivateKey()\fR instead.
.PP
If the \fB*a\fR is not NULL when calling \fBd2i_PrivateKey()\fR or \fBd2i_AutoPrivateKey()\fR
(i.e. an existing structure is being reused) and the key format is PKCS#8
then \fB*a\fR will be freed and replaced on a successful call.
.PP
To decode a key with type \fBEVP_PKEY_EC\fR, \fBd2i_PublicKey()\fR requires \fB*a\fR to be
a non-NULL EVP_PKEY structure assigned an EC_KEY structure referencing the proper
EC_GROUP.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBd2i_PrivateKey()\fR, \fBd2i_AutoPrivateKey()\fR, \fBd2i_PrivateKey_bio()\fR, \fBd2i_PrivateKey_fp()\fR,
and \fBd2i_PublicKey()\fR functions return a valid \fBEVP_KEY\fR structure or \fBNULL\fR if an
error occurs. The error code can be obtained by calling \fBERR_get_error\fR\|(3).
.PP
\&\fBi2d_PrivateKey()\fR and \fBi2d_PublicKey()\fR return the number of bytes successfully
encoded or a negative value if an error occurs. The error code can be obtained
by calling \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7),
\&\fBd2i_PKCS8PrivateKey_bio\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
