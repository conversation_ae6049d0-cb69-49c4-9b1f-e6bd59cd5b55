.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_CHECK_PRIVATE_KEY 3"
.TH X509_CHECK_PRIVATE_KEY 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_check_private_key, X509_REQ_check_private_key \- check the consistency
of a private key with the public key in an X509 certificate or certificate
request
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_check_private_key(X509 *x, EVP_PKEY *k);
\&
\& int X509_REQ_check_private_key(X509_REQ *x, EVP_PKEY *k);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_check_private_key()\fR function checks the consistency of private
key \fBk\fR with the public key in \fBx\fR.
.PP
\&\fBX509_REQ_check_private_key()\fR is equivalent to \fBX509_check_private_key()\fR
except that \fBx\fR represents a certificate request of structure \fBX509_REQ\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_check_private_key()\fR and \fBX509_REQ_check_private_key()\fR return 1 if
the keys match each other, and 0 if not.
.PP
If the key is invalid or an error occurred, the reason code can be
obtained using \fBERR_get_error\fR\|(3).
.SH BUGS
.IX Header "BUGS"
The \fBcheck_private_key\fR functions don't check if \fBk\fR itself is indeed
a private key or not. It merely compares the public materials (e.g. exponent
and modulus of an RSA key) and/or key parameters (e.g. EC params of an EC key)
of a key pair. So if you pass a public key to these functions in \fBk\fR, it will
return success.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
