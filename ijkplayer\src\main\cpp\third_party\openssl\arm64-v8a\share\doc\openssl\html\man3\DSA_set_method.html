<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>DSA_set_method</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>DSA_set_default_method, DSA_get_default_method, DSA_set_method, DSA_new_method, DSA_OpenSSL - select DSA method</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/dsa.h&gt;

void DSA_set_default_method(const DSA_METHOD *meth);

const DSA_METHOD *DSA_get_default_method(void);

int DSA_set_method(DSA *dsa, const DSA_METHOD *meth);

DSA *DSA_new_method(ENGINE *engine);

DSA_METHOD *DSA_OpenSSL(void);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>A <b>DSA_METHOD</b> specifies the functions that OpenSSL uses for DSA operations. By modifying the method, alternative implementations such as hardware accelerators may be used. IMPORTANT: See the NOTES section for important information about how these DSA API functions are affected by the use of <b>ENGINE</b> API calls.</p>

<p>Initially, the default DSA_METHOD is the OpenSSL internal implementation, as returned by DSA_OpenSSL().</p>

<p>DSA_set_default_method() makes <b>meth</b> the default method for all DSA structures created later. <b>NB</b>: This is true only whilst no ENGINE has been set as a default for DSA, so this function is no longer recommended. This function is not thread-safe and should not be called at the same time as other OpenSSL functions.</p>

<p>DSA_get_default_method() returns a pointer to the current default DSA_METHOD. However, the meaningfulness of this result is dependent on whether the ENGINE API is being used, so this function is no longer recommended.</p>

<p>DSA_set_method() selects <b>meth</b> to perform all operations using the key <b>rsa</b>. This will replace the DSA_METHOD used by the DSA key and if the previous method was supplied by an ENGINE, the handle to that ENGINE will be released during the change. It is possible to have DSA keys that only work with certain DSA_METHOD implementations (e.g. from an ENGINE module that supports embedded hardware-protected keys), and in such cases attempting to change the DSA_METHOD for the key can have unexpected results. See <a href="/../doc/man3/DSA_meth_new.html">DSA_meth_new</a> for information on constructing custom DSA_METHOD objects;</p>

<p>DSA_new_method() allocates and initializes a DSA structure so that <b>engine</b> will be used for the DSA operations. If <b>engine</b> is NULL, the default engine for DSA operations is used, and if no default ENGINE is set, the DSA_METHOD controlled by DSA_set_default_method() is used.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>DSA_OpenSSL() and DSA_get_default_method() return pointers to the respective <b>DSA_METHOD</b>s.</p>

<p>DSA_set_default_method() returns no value.</p>

<p>DSA_set_method() returns nonzero if the provided <b>meth</b> was successfully set as the method for <b>dsa</b> (including unloading the ENGINE handle if the previous method was supplied by an ENGINE).</p>

<p>DSA_new_method() returns NULL and sets an error code that can be obtained by <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a> if the allocation fails. Otherwise it returns a pointer to the newly allocated structure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/DSA_new.html">DSA_new(3)</a>, <a href="../man3/DSA_new.html">DSA_new(3)</a>, <a href="../man3/DSA_meth_new.html">DSA_meth_new(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


