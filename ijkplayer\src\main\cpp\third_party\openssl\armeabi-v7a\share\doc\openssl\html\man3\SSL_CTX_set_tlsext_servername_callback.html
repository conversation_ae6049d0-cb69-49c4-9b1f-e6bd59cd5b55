<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CTX_set_tlsext_servername_callback</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CTX_set_tlsext_servername_callback, SSL_CTX_set_tlsext_servername_arg, SSL_get_servername_type, SSL_get_servername, SSL_set_tlsext_host_name - handle server name indication (SNI)</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

long SSL_CTX_set_tlsext_servername_callback(SSL_CTX *ctx,
                                  int (*cb)(SSL *s, int *al, void *arg));
long SSL_CTX_set_tlsext_servername_arg(SSL_CTX *ctx, void *arg);

const char *SSL_get_servername(const SSL *s, const int type);
int SSL_get_servername_type(const SSL *s);

int SSL_set_tlsext_host_name(const SSL *s, const char *name);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The functionality provided by the servername callback is mostly superseded by the ClientHello callback, which can be set using SSL_CTX_set_client_hello_cb(). However, even where the ClientHello callback is used, the servername callback is still necessary in order to acknowledge the servername requested by the client.</p>

<p>SSL_CTX_set_tlsext_servername_callback() sets the application callback <b>cb</b> used by a server to perform any actions or configuration required based on the servername extension received in the incoming connection. When <b>cb</b> is NULL, SNI is not used.</p>

<p>The servername callback should return one of the following values:</p>

<dl>

<dt id="SSL_TLSEXT_ERR_OK">SSL_TLSEXT_ERR_OK</dt>
<dd>

<p>This is used to indicate that the servername requested by the client has been accepted. Typically a server will call SSL_set_SSL_CTX() in the callback to set up a different configuration for the selected servername in this case.</p>

</dd>
<dt id="SSL_TLSEXT_ERR_ALERT_FATAL">SSL_TLSEXT_ERR_ALERT_FATAL</dt>
<dd>

<p>In this case the servername requested by the client is not accepted and the handshake will be aborted. The value of the alert to be used should be stored in the location pointed to by the <b>al</b> parameter to the callback. By default this value is initialised to SSL_AD_UNRECOGNIZED_NAME.</p>

</dd>
<dt id="SSL_TLSEXT_ERR_ALERT_WARNING">SSL_TLSEXT_ERR_ALERT_WARNING</dt>
<dd>

<p>If this value is returned then the servername is not accepted by the server. However, the handshake will continue and send a warning alert instead. The value of the alert should be stored in the location pointed to by the <b>al</b> parameter as for SSL_TLSEXT_ERR_ALERT_FATAL above. Note that TLSv1.3 does not support warning alerts, so if TLSv1.3 has been negotiated then this return value is treated the same way as SSL_TLSEXT_ERR_NOACK.</p>

</dd>
<dt id="SSL_TLSEXT_ERR_NOACK">SSL_TLSEXT_ERR_NOACK</dt>
<dd>

<p>This return value indicates that the servername is not accepted by the server. No alerts are sent and the server will not acknowledge the requested servername.</p>

</dd>
</dl>

<p>SSL_CTX_set_tlsext_servername_arg() sets a context-specific argument to be passed into the callback (via the <b>arg</b> parameter) for this <b>SSL_CTX</b>.</p>

<p>The behaviour of SSL_get_servername() depends on a number of different factors. In particular note that in TLSv1.3 the servername is negotiated in every handshake. In TLSv1.2 the servername is only negotiated on initial handshakes and not on resumption handshakes.</p>

<dl>

<dt id="On-the-client-before-the-handshake">On the client, before the handshake</dt>
<dd>

<p>If a servername has been set via a call to SSL_set_tlsext_host_name() then it will return that servername.</p>

<p>If one has not been set, but a TLSv1.2 resumption is being attempted and the session from the original handshake had a servername accepted by the server then it will return that servername.</p>

<p>Otherwise it returns NULL.</p>

</dd>
<dt id="On-the-client-during-or-after-the-handshake-and-a-TLSv1.2-or-below-resumption-occurred">On the client, during or after the handshake and a TLSv1.2 (or below) resumption occurred</dt>
<dd>

<p>If the session from the original handshake had a servername accepted by the server then it will return that servername.</p>

<p>Otherwise it returns the servername set via SSL_set_tlsext_host_name() or NULL if it was not called.</p>

</dd>
<dt id="On-the-client-during-or-after-the-handshake-and-a-TLSv1.2-or-below-resumption-did-not-occur">On the client, during or after the handshake and a TLSv1.2 (or below) resumption did not occur</dt>
<dd>

<p>It will return the servername set via SSL_set_tlsext_host_name() or NULL if it was not called.</p>

</dd>
<dt id="On-the-server-before-the-handshake">On the server, before the handshake</dt>
<dd>

<p>The function will always return NULL before the handshake</p>

</dd>
<dt id="On-the-server-after-the-servername-extension-has-been-processed-and-a-TLSv1.2-or-below-resumption-occurred">On the server, after the servername extension has been processed and a TLSv1.2 (or below) resumption occurred</dt>
<dd>

<p>If a servername was accepted by the server in the original handshake then it will return that servername, or NULL otherwise.</p>

</dd>
<dt id="On-the-server-after-the-servername-extension-has-been-processed-and-a-TLSv1.2-or-below-resumption-did-not-occur">On the server, after the servername extension has been processed and a TLSv1.2 (or below) resumption did not occur</dt>
<dd>

<p>The function will return the servername requested by the client in this handshake or NULL if none was requested.</p>

</dd>
</dl>

<p>Note that the ClientHello callback occurs before a servername extension from the client is processed. The servername, certificate and ALPN callbacks occur after a servername extension from the client is processed.</p>

<p>SSL_get_servername_type() returns the servername type or -1 if no servername is present. Currently the only supported type (defined in RFC3546) is <b>TLSEXT_NAMETYPE_host_name</b>.</p>

<p>SSL_set_tlsext_host_name() sets the server name indication ClientHello extension to contain the value <b>name</b>. The type of server name indication extension is set to <b>TLSEXT_NAMETYPE_host_name</b> (defined in RFC3546).</p>

<h1 id="NOTES">NOTES</h1>

<p>Several callbacks are executed during ClientHello processing, including the ClientHello, ALPN, and servername callbacks. The ClientHello callback is executed first, then the servername callback, followed by the ALPN callback.</p>

<p>The SSL_set_tlsext_host_name() function should only be called on SSL objects that will act as clients; otherwise the configured <b>name</b> will be ignored.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_CTX_set_tlsext_servername_callback() and SSL_CTX_set_tlsext_servername_arg() both always return 1 indicating success. SSL_set_tlsext_host_name() returns 1 on success, 0 in case of error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CTX_set_alpn_select_cb.html">SSL_CTX_set_alpn_select_cb(3)</a>, <a href="../man3/SSL_get0_alpn_selected.html">SSL_get0_alpn_selected(3)</a>, <a href="../man3/SSL_CTX_set_client_hello_cb.html">SSL_CTX_set_client_hello_cb(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>SSL_get_servername() historically provided some unexpected results in certain corner cases. This has been fixed from OpenSSL 1.1.1e.</p>

<p>Prior to 1.1.1e, when the client requested a servername in an initial TLSv1.2 handshake, the server accepted it, and then the client successfully resumed but set a different explicit servername in the second handshake then when called by the client it returned the servername from the second handshake. This has now been changed to return the servername requested in the original handshake.</p>

<p>Also prior to 1.1.1e, if the client sent a servername in the first handshake but the server did not accept it, and then a second handshake occurred where TLSv1.2 resumption was successful then when called by the server it returned the servername requested in the original handshake. This has now been changed to NULL.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


