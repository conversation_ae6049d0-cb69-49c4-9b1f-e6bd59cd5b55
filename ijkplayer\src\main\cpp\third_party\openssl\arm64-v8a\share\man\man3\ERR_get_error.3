.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ERR_GET_ERROR 3"
.TH ERR_GET_ERROR 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ERR_get_error, ERR_peek_error, ERR_peek_last_error,
ERR_get_error_line, ERR_peek_error_line, ERR_peek_last_error_line,
ERR_get_error_line_data, ERR_peek_error_line_data,
ERR_peek_last_error_line_data \- obtain error code and data
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/err.h>
\&
\& unsigned long ERR_get_error(void);
\& unsigned long ERR_peek_error(void);
\& unsigned long ERR_peek_last_error(void);
\&
\& unsigned long ERR_get_error_line(const char **file, int *line);
\& unsigned long ERR_peek_error_line(const char **file, int *line);
\& unsigned long ERR_peek_last_error_line(const char **file, int *line);
\&
\& unsigned long ERR_get_error_line_data(const char **file, int *line,
\&                                       const char **data, int *flags);
\& unsigned long ERR_peek_error_line_data(const char **file, int *line,
\&                                        const char **data, int *flags);
\& unsigned long ERR_peek_last_error_line_data(const char **file, int *line,
\&                                             const char **data, int *flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBERR_get_error()\fR returns the earliest error code from the thread's error
queue and removes the entry. This function can be called repeatedly
until there are no more error codes to return.
.PP
\&\fBERR_peek_error()\fR returns the earliest error code from the thread's
error queue without modifying it.
.PP
\&\fBERR_peek_last_error()\fR returns the latest error code from the thread's
error queue without modifying it.
.PP
See \fBERR_GET_LIB\fR\|(3) for obtaining information about
location and reason of the error, and
\&\fBERR_error_string\fR\|(3) for human-readable error
messages.
.PP
\&\fBERR_get_error_line()\fR, \fBERR_peek_error_line()\fR and
\&\fBERR_peek_last_error_line()\fR are the same as the above, but they
additionally store the filename and line number where
the error occurred in *\fBfile\fR and *\fBline\fR, unless these are \fBNULL\fR.
.PP
\&\fBERR_get_error_line_data()\fR, \fBERR_peek_error_line_data()\fR and
\&\fBERR_peek_last_error_line_data()\fR store additional data and flags
associated with the error code in *\fBdata\fR
and *\fBflags\fR, unless these are \fBNULL\fR. *\fBdata\fR contains a string
if *\fBflags\fR&\fBERR_TXT_STRING\fR is true.
.PP
An application \fBMUST NOT\fR free the *\fBdata\fR pointer (or any other pointers
returned by these functions) with \fBOPENSSL_free()\fR as freeing is handled
automatically by the error library.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The error code, or 0 if there is no error in the queue.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_error_string\fR\|(3),
\&\fBERR_GET_LIB\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
