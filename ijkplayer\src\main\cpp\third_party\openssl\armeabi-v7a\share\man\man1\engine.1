.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ENGINE 1"
.TH ENGINE 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-engine,
engine \- load and query engines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl engine\fR
[ \fIengine...\fR ]
[\fB\-v\fR]
[\fB\-vv\fR]
[\fB\-vvv\fR]
[\fB\-vvv\fR]
[\fB\-vvv\fR]
[\fB\-c\fR]
[\fB\-t\fR]
[\fB\-tt\fR]
[\fB\-pre\fR \fIcommand\fR]
[\fB\-post\fR \fIcommand\fR]
[ \fIengine...\fR ]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBengine\fR command is used to query the status and capabilities
of the specified \fBengine\fR's.
Engines may be specified before and after all other command-line flags.
Only those specified are queried.
.SH OPTIONS
.IX Header "OPTIONS"
.IP "\fB\-v\fR \fB\-vv\fR \fB\-vvv\fR \fB\-vvvv\fR" 4
.IX Item "-v -vv -vvv -vvvv"
Provides information about each specified engine. The first flag lists
all the possible run-time control commands; the second adds a
description of each command; the third adds the input flags, and the
final option adds the internal input flags.
.IP \fB\-c\fR 4
.IX Item "-c"
Lists the capabilities of each engine.
.IP \fB\-t\fR 4
.IX Item "-t"
Tests if each specified engine is available, and displays the answer.
.IP \fB\-tt\fR 4
.IX Item "-tt"
Displays an error trace for any unavailable engine.
.IP "\fB\-pre\fR \fIcommand\fR" 4
.IX Item "-pre command"
.PD 0
.IP "\fB\-post\fR \fIcommand\fR" 4
.IX Item "-post command"
.PD
Command-line configuration of engines.
The \fB\-pre\fR command is given to the engine before it is loaded and
the \fB\-post\fR command is given after the engine is loaded.
The \fIcommand\fR is of the form \fIcmd:val\fR where \fIcmd\fR is the command,
and \fIval\fR is the value for the command.
See the example below.
.SH EXAMPLES
.IX Header "EXAMPLES"
To list all the commands available to a dynamic engine:
.PP
.Vb 10
\& $ openssl engine \-t \-tt \-vvvv dynamic
\& (dynamic) Dynamic engine loading support
\&      [ unavailable ]
\&      SO_PATH: Specifies the path to the new ENGINE shared library
\&           (input flags): STRING
\&      NO_VCHECK: Specifies to continue even if version checking fails (boolean)
\&           (input flags): NUMERIC
\&      ID: Specifies an ENGINE id name for loading
\&           (input flags): STRING
\&      LIST_ADD: Whether to add a loaded ENGINE to the internal list (0=no,1=yes,2=mandatory)
\&           (input flags): NUMERIC
\&      DIR_LOAD: Specifies whether to load from \*(AqDIR_ADD\*(Aq directories (0=no,1=yes,2=mandatory)
\&           (input flags): NUMERIC
\&      DIR_ADD: Adds a directory from which ENGINEs can be loaded
\&           (input flags): STRING
\&      LOAD: Load up the ENGINE specified by other settings
\&           (input flags): NO_INPUT
.Ve
.PP
To list the capabilities of the \fIrsax\fR engine:
.PP
.Vb 4
\& $ openssl engine \-c
\& (rsax) RSAX engine support
\&  [RSA]
\& (dynamic) Dynamic engine loading support
.Ve
.SH ENVIRONMENT
.IX Header "ENVIRONMENT"
.IP \fBOPENSSL_ENGINES\fR 4
.IX Item "OPENSSL_ENGINES"
The path to the engines directory.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBconfig\fR\|(5)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
