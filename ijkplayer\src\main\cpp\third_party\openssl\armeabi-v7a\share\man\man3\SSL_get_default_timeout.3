.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_DEFAULT_TIMEOUT 3"
.TH SSL_GET_DEFAULT_TIMEOUT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_default_timeout \- get default session timeout value
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_get_default_timeout(const SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_default_timeout()\fR returns the default timeout value assigned to
SSL_SESSION objects negotiated for the protocol valid for \fBssl\fR.
.SH NOTES
.IX Header "NOTES"
Whenever a new session is negotiated, it is assigned a timeout value,
after which it will not be accepted for session reuse. If the timeout
value was not explicitly set using
\&\fBSSL_CTX_set_timeout\fR\|(3), the hardcoded default
timeout for the protocol will be used.
.PP
\&\fBSSL_get_default_timeout()\fR return this hardcoded value, which is 300 seconds
for all currently supported protocols.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
See description.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_set_session_cache_mode\fR\|(3),
\&\fBSSL_SESSION_get_time\fR\|(3),
\&\fBSSL_CTX_flush_sessions\fR\|(3),
\&\fBSSL_get_default_timeout\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
