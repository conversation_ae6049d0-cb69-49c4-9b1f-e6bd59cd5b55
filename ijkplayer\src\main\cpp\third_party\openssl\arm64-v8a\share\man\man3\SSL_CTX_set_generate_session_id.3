.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_GENERATE_SESSION_ID 3"
.TH SSL_CTX_SET_GENERATE_SESSION_ID 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_generate_session_id, SSL_set_generate_session_id,
SSL_has_matching_session_id, GEN_SESSION_CB
\&\- manipulate generation of SSL session IDs (server only)
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& typedef int (*GEN_SESSION_CB)(SSL *ssl, unsigned char *id,
\&                               unsigned int *id_len);
\&
\& int SSL_CTX_set_generate_session_id(SSL_CTX *ctx, GEN_SESSION_CB cb);
\& int SSL_set_generate_session_id(SSL *ssl, GEN_SESSION_CB, cb);
\& int SSL_has_matching_session_id(const SSL *ssl, const unsigned char *id,
\&                                 unsigned int id_len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_generate_session_id()\fR sets the callback function for generating
new session ids for SSL/TLS sessions for \fBctx\fR to be \fBcb\fR.
.PP
\&\fBSSL_set_generate_session_id()\fR sets the callback function for generating
new session ids for SSL/TLS sessions for \fBssl\fR to be \fBcb\fR.
.PP
\&\fBSSL_has_matching_session_id()\fR checks, whether a session with id \fBid\fR
(of length \fBid_len\fR) is already contained in the internal session cache
of the parent context of \fBssl\fR.
.SH NOTES
.IX Header "NOTES"
When a new session is established between client and server, the server
generates a session id. The session id is an arbitrary sequence of bytes.
The length of the session id is between 1 and 32 bytes.  The session id is not
security critical but must be unique for the server. Additionally, the session id is
transmitted in the clear when reusing the session so it must not contain
sensitive information.
.PP
Without a callback being set, an OpenSSL server will generate a unique
session id from pseudo random numbers of the maximum possible length.
Using the callback function, the session id can be changed to contain
additional information like e.g. a host id in order to improve load balancing
or external caching techniques.
.PP
The callback function receives a pointer to the memory location to put
\&\fBid\fR into and a pointer to the maximum allowed length \fBid_len\fR. The
buffer at location \fBid\fR is only guaranteed to have the size \fBid_len\fR.
The callback is only allowed to generate a shorter id and reduce \fBid_len\fR;
the callback \fBmust never\fR increase \fBid_len\fR or write to the location
\&\fBid\fR exceeding the given limit.
.PP
The location \fBid\fR is filled with 0x00 before the callback is called, so the
callback may only fill part of the possible length and leave \fBid_len\fR
untouched while maintaining reproducibility.
.PP
Since the sessions must be distinguished, session ids must be unique.
Without the callback a random number is used, so that the probability
of generating the same session id is extremely small (2^256 for SSLv3/TLSv1).
In order to assure the uniqueness of the generated session id, the callback must call
\&\fBSSL_has_matching_session_id()\fR and generate another id if a conflict occurs.
If an id conflict is not resolved, the handshake will fail.
If the application codes e.g. a unique host id, a unique process number, and
a unique sequence number into the session id, uniqueness could easily be
achieved without randomness added (it should however be taken care that
no confidential information is leaked this way). If the application can not
guarantee uniqueness, it is recommended to use the maximum \fBid_len\fR and
fill in the bytes not used to code special information with random data
to avoid collisions.
.PP
\&\fBSSL_has_matching_session_id()\fR will only query the internal session cache,
not the external one. Since the session id is generated before the
handshake is completed, it is not immediately added to the cache. If
another thread is using the same internal session cache, a race condition
can occur in that another thread generates the same session id.
Collisions can also occur when using an external session cache, since
the external cache is not tested with \fBSSL_has_matching_session_id()\fR
and the same race condition applies.
.PP
The callback must return 0 if it cannot generate a session id for whatever
reason and return 1 on success.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_generate_session_id()\fR and \fBSSL_set_generate_session_id()\fR
always return 1.
.PP
\&\fBSSL_has_matching_session_id()\fR returns 1 if another session with the
same id is already in the cache.
.SH EXAMPLES
.IX Header "EXAMPLES"
The callback function listed will generate a session id with the
server id given, and will fill the rest with pseudo random bytes:
.PP
.Vb 1
\& const char session_id_prefix = "www\-18";
\&
\& #define MAX_SESSION_ID_ATTEMPTS 10
\& static int generate_session_id(SSL *ssl, unsigned char *id,
\&                                unsigned int *id_len)
\& {
\&     unsigned int count = 0;
\&
\&     do {
\&         RAND_pseudo_bytes(id, *id_len);
\&         /*
\&          * Prefix the session_id with the required prefix. NB: If our
\&          * prefix is too long, clip it \- but there will be worse effects
\&          * anyway, e.g. the server could only possibly create 1 session
\&          * ID (i.e. the prefix!) so all future session negotiations will
\&          * fail due to conflicts.
\&          */
\&         memcpy(id, session_id_prefix, strlen(session_id_prefix) < *id_len ?
\&                                       strlen(session_id_prefix) : *id_len);
\&     } while (SSL_has_matching_session_id(ssl, id, *id_len)
\&               && ++count < MAX_SESSION_ID_ATTEMPTS);
\&     if (count >= MAX_SESSION_ID_ATTEMPTS)
\&         return 0;
\&     return 1;
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_get_version\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
