.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_MOD_MUL_RECIPROCAL 3"
.TH BN_MOD_MUL_RECIPROCAL 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_mod_mul_reciprocal, BN_div_recp, BN_RECP_CTX_new,
BN_RECP_CTX_free, BN_RECP_CTX_set \- modular multiplication using
reciprocal
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& BN_RECP_CTX *BN_RECP_CTX_new(void);
\& void BN_RECP_CTX_free(BN_RECP_CTX *recp);
\&
\& int BN_RECP_CTX_set(BN_RECP_CTX *recp, const BIGNUM *m, BN_CTX *ctx);
\&
\& int BN_div_recp(BIGNUM *dv, BIGNUM *rem, BIGNUM *a, BN_RECP_CTX *recp,
\&                 BN_CTX *ctx);
\&
\& int BN_mod_mul_reciprocal(BIGNUM *r, BIGNUM *a, BIGNUM *b,
\&                           BN_RECP_CTX *recp, BN_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_mod_mul_reciprocal()\fR can be used to perform an efficient
\&\fBBN_mod_mul\fR\|(3) operation when the operation will be performed
repeatedly with the same modulus. It computes \fBr\fR=(\fBa\fR*\fBb\fR)%\fBm\fR
using \fBrecp\fR=1/\fBm\fR, which is set as described below.  \fBctx\fR is a
previously allocated \fBBN_CTX\fR used for temporary variables.
.PP
\&\fBBN_RECP_CTX_new()\fR allocates and initializes a \fBBN_RECP\fR structure.
.PP
\&\fBBN_RECP_CTX_free()\fR frees the components of the \fBBN_RECP\fR, and, if it
was created by \fBBN_RECP_CTX_new()\fR, also the structure itself.
If \fBrecp\fR is NULL, nothing is done.
.PP
\&\fBBN_RECP_CTX_set()\fR stores \fBm\fR in \fBrecp\fR and sets it up for computing
1/\fBm\fR and shifting it left by BN_num_bits(\fBm\fR)+1 to make it an
integer. The result and the number of bits it was shifted left will
later be stored in \fBrecp\fR.
.PP
\&\fBBN_div_recp()\fR divides \fBa\fR by \fBm\fR using \fBrecp\fR. It places the quotient
in \fBdv\fR and the remainder in \fBrem\fR.
.PP
The \fBBN_RECP_CTX\fR structure cannot be shared between threads.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_RECP_CTX_new()\fR returns the newly allocated \fBBN_RECP_CTX\fR, and NULL
on error.
.PP
\&\fBBN_RECP_CTX_free()\fR has no return value.
.PP
For the other functions, 1 is returned for success, 0 on error.
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBBN_add\fR\|(3),
\&\fBBN_CTX_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBBN_RECP_CTX_init()\fR was removed in OpenSSL 1.1.0
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
