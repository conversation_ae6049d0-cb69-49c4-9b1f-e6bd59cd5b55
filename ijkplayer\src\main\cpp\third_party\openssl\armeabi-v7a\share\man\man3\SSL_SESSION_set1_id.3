.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SESSION_SET1_ID 3"
.TH SSL_SESSION_SET1_ID 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_SESSION_get_id,
SSL_SESSION_set1_id
\&\- get and set the SSL session ID
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& const unsigned char *SSL_SESSION_get_id(const SSL_SESSION *s,
\&                                         unsigned int *len)
\& int SSL_SESSION_set1_id(SSL_SESSION *s, const unsigned char *sid,
\&                         unsigned int sid_len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_SESSION_get_id()\fR returns a pointer to the internal session id value for the
session \fBs\fR. The length of the id in bytes is stored in \fB*len\fR. The length may
be 0. The caller should not free the returned pointer directly.
.PP
\&\fBSSL_SESSION_set1_id()\fR sets the session ID for the \fBssl\fR SSL/TLS session
to \fBsid\fR of length \fBsid_len\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_SESSION_get_id()\fR returns a pointer to the session id value.
\&\fBSSL_SESSION_set1_id()\fR returns 1 for success and 0 for failure, for example
if the supplied session ID length exceeds \fBSSL_MAX_SSL_SESSION_ID_LENGTH\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_SESSION_set1_id()\fR function was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
