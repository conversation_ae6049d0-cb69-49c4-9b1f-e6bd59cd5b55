.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CRYPTO 7"
.TH CRYPTO 7 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
crypto \- OpenSSL cryptographic library
.SH SYNOPSIS
.IX Header "SYNOPSIS"
See the individual manual pages for details.
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The OpenSSL \fBcrypto\fR library implements a wide range of cryptographic
algorithms used in various Internet standards. The services provided
by this library are used by the OpenSSL implementations of SSL, TLS
and S/MIME, and they have also been used to implement SSH, OpenPGP, and
other cryptographic standards.
.PP
\&\fBlibcrypto\fR consists of a number of sub-libraries that implement the
individual algorithms.
.PP
The functionality includes symmetric encryption, public key
cryptography and key agreement, certificate handling, cryptographic
hash functions, cryptographic pseudo-random number generator, and
various utilities.
.SH NOTES
.IX Header "NOTES"
Some of the newer functions follow a naming convention using the numbers
\&\fB0\fR and \fB1\fR. For example the functions:
.PP
.Vb 2
\& int X509_CRL_add0_revoked(X509_CRL *crl, X509_REVOKED *rev);
\& int X509_add1_trust_object(X509 *x, const ASN1_OBJECT *obj);
.Ve
.PP
The \fB0\fR version uses the supplied structure pointer directly
in the parent and it will be freed up when the parent is freed.
In the above example \fBcrl\fR would be freed but \fBrev\fR would not.
.PP
The \fB1\fR function uses a copy of the supplied structure pointer
(or in some cases increases its link count) in the parent and
so both (\fBx\fR and \fBobj\fR above) should be freed up.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
See the individual manual pages for details.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1), \fBssl\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
