.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_LOAD_BUILTIN_MODULES 3"
.TH OPENSSL_LOAD_BUILTIN_MODULES 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_load_builtin_modules, ASN1_add_oid_module, ENGINE_add_conf_module \- add standard configuration modules
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/conf.h>
\&
\& void OPENSSL_load_builtin_modules(void);
\& void ASN1_add_oid_module(void);
\& void ENGINE_add_conf_module(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The function \fBOPENSSL_load_builtin_modules()\fR adds all the standard OpenSSL
configuration modules to the internal list. They can then be used by the
OpenSSL configuration code.
.PP
\&\fBASN1_add_oid_module()\fR adds just the ASN1 OBJECT module.
.PP
\&\fBENGINE_add_conf_module()\fR adds just the ENGINE configuration module.
.SH NOTES
.IX Header "NOTES"
If the simple configuration function \fBOPENSSL_config()\fR is called then
\&\fBOPENSSL_load_builtin_modules()\fR is called automatically.
.PP
Applications which use the configuration functions directly will need to
call \fBOPENSSL_load_builtin_modules()\fR themselves \fIbefore\fR any other
configuration code.
.PP
Applications should call \fBOPENSSL_load_builtin_modules()\fR to load all
configuration modules instead of adding modules selectively: otherwise
functionality may be missing from the application if an when new
modules are added.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
None of the functions return a value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBconfig\fR\|(5), \fBOPENSSL_config\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2004\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
