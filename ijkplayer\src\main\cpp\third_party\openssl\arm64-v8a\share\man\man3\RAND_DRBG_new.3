.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_DRBG_NEW 3"
.TH RAND_DRBG_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_DRBG_new,
RAND_DRBG_secure_new,
RAND_DRBG_set,
RAND_DRBG_set_defaults,
RAND_DRBG_instantiate,
RAND_DRBG_uninstantiate,
RAND_DRBG_free
\&\- initialize and cleanup a RAND_DRBG instance
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand_drbg.h>
\&
\&
\& RAND_DRBG *RAND_DRBG_new(int type,
\&                          unsigned int flags,
\&                          RAND_DRBG *parent);
\&
\& RAND_DRBG *RAND_DRBG_secure_new(int type,
\&                                 unsigned int flags,
\&                                 RAND_DRBG *parent);
\&
\& int RAND_DRBG_set(RAND_DRBG *drbg,
\&                   int type, unsigned int flags);
\&
\& int RAND_DRBG_set_defaults(int type, unsigned int flags);
\&
\& int RAND_DRBG_instantiate(RAND_DRBG *drbg,
\&                           const unsigned char *pers, size_t perslen);
\&
\& int RAND_DRBG_uninstantiate(RAND_DRBG *drbg);
\&
\& void RAND_DRBG_free(RAND_DRBG *drbg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBRAND_DRBG_new()\fR and \fBRAND_DRBG_secure_new()\fR
create a new DRBG instance of the given \fBtype\fR, allocated from the heap resp.
the secure heap
(using \fBOPENSSL_zalloc()\fR resp. \fBOPENSSL_secure_zalloc()\fR).
.PP
\&\fBRAND_DRBG_set()\fR initializes the \fBdrbg\fR with the given \fBtype\fR and \fBflags\fR.
.PP
\&\fBRAND_DRBG_set_defaults()\fR sets the default \fBtype\fR and \fBflags\fR for new DRBG
instances.
.PP
Currently, all DRBG types are based on AES-CTR, so \fBtype\fR can be one of the
following values: NID_aes_128_ctr, NID_aes_192_ctr, NID_aes_256_ctr.
Before the DRBG can be used to generate random bits, it is necessary to set
its type and to instantiate it.
.PP
The optional \fBflags\fR argument specifies a set of bit flags which can be
joined using the | operator. Currently, the only flag is
RAND_DRBG_FLAG_CTR_NO_DF, which disables the use of the derivation function
ctr_df. For an explanation, see [NIST SP 800\-90A Rev. 1].
.PP
If a \fBparent\fR instance is specified then this will be used instead of
the default entropy source for reseeding the \fBdrbg\fR. It is said that the
\&\fBdrbg\fR is \fIchained\fR to its \fBparent\fR.
For more information, see the NOTES section.
.PP
\&\fBRAND_DRBG_instantiate()\fR
seeds the \fBdrbg\fR instance using random input from trusted entropy sources.
Optionally, a personalization string \fBpers\fR of length \fBperslen\fR can be
specified.
To omit the personalization string, set \fBpers\fR=NULL and \fBperslen\fR=0;
.PP
\&\fBRAND_DRBG_uninstantiate()\fR
clears the internal state of the \fBdrbg\fR and puts it back in the
uninstantiated state.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_DRBG_new()\fR and \fBRAND_DRBG_secure_new()\fR return a pointer to a DRBG
instance allocated on the heap, resp. secure heap.
.PP
\&\fBRAND_DRBG_set()\fR,
\&\fBRAND_DRBG_instantiate()\fR, and
\&\fBRAND_DRBG_uninstantiate()\fR
return 1 on success, and 0 on failure.
.PP
\&\fBRAND_DRBG_free()\fR does not return a value.
.SH NOTES
.IX Header "NOTES"
The DRBG design supports \fIchaining\fR, which means that a DRBG instance can
use another \fBparent\fR DRBG instance instead of the default entropy source
to obtain fresh random input for reseeding, provided that \fBparent\fR DRBG
instance was properly instantiated, either from a trusted entropy source,
or from yet another parent DRBG instance.
For a detailed description of the reseeding process, see \fBRAND_DRBG\fR\|(7).
.PP
The default DRBG type and flags are applied only during creation of a DRBG
instance.
To ensure that they are applied to the global and thread-local DRBG instances
(<master>, resp. <public> and <private>), it is necessary to call
\&\fBRAND_DRBG_set_defaults()\fR before creating any thread and before calling any
cryptographic routines that obtain random data directly or indirectly.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOPENSSL_zalloc\fR\|(3),
\&\fBOPENSSL_secure_zalloc\fR\|(3),
\&\fBRAND_DRBG_generate\fR\|(3),
\&\fBRAND_DRBG\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The RAND_DRBG functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
