.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_EX_DATA 3"
.TH SSL_CTX_SET_EX_DATA 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_get_ex_data, SSL_CTX_set_ex_data,
SSL_get_ex_data, SSL_set_ex_data
\&\- Store and retrieve extra data from the SSL_CTX, SSL or SSL_SESSION
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void *SSL_CTX_get_ex_data(const SSL_CTX *s, int idx);
\&
\& int SSL_CTX_set_ex_data(SSL_CTX *s, int idx, void *arg);
\&
\& void *SSL_get_ex_data(const SSL *s, int idx);
\&
\& int SSL_set_ex_data(SSL *s, int idx, void *arg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
SSL*\fB_set_ex_data()\fR functions can be used to store arbitrary user data into the
\&\fBSSL_CTX\fR, or \fBSSL\fR object. The user must supply a unique index
which they can subsequently use to retrieve the data using SSL*\fB_get_ex_data()\fR.
.PP
For more detailed information see \fBCRYPTO_get_ex_data\fR\|(3) and
\&\fBCRYPTO_set_ex_data\fR\|(3) which implement these functions and
\&\fBCRYPTO_get_ex_new_index\fR\|(3) for generating a unique index.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The SSL*\fB_set_ex_data()\fR functions return 1 if the item is successfully stored
and 0 if it is not.
The SSL*\fB_get_ex_data()\fR functions return the ex_data pointer if successful,
otherwise NULL.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBCRYPTO_get_ex_data\fR\|(3), \fBCRYPTO_set_ex_data\fR\|(3),
\&\fBCRYPTO_get_ex_new_index\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
