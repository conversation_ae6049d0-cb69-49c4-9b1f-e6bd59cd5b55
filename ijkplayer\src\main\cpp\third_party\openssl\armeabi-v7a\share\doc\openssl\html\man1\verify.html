<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>verify</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#VERIFY-OPERATION">VERIFY OPERATION</a></li>
  <li><a href="#DIAGNOSTICS">DIAGNOSTICS</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-verify, verify - Utility to verify certificates</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>verify</b> [<b>-help</b>] [<b>-CAfile file</b>] [<b>-CApath directory</b>] [<b>-no-CAfile</b>] [<b>-no-CApath</b>] [<b>-allow_proxy_certs</b>] [<b>-attime timestamp</b>] [<b>-check_ss_sig</b>] [<b>-CRLfile file</b>] [<b>-crl_download</b>] [<b>-crl_check</b>] [<b>-crl_check_all</b>] [<b>-engine id</b>] [<b>-explicit_policy</b>] [<b>-extended_crl</b>] [<b>-ignore_critical</b>] [<b>-inhibit_any</b>] [<b>-inhibit_map</b>] [<b>-nameopt option</b>] [<b>-no_check_time</b>] [<b>-partial_chain</b>] [<b>-policy arg</b>] [<b>-policy_check</b>] [<b>-policy_print</b>] [<b>-purpose purpose</b>] [<b>-suiteB_128</b>] [<b>-suiteB_128_only</b>] [<b>-suiteB_192</b>] [<b>-trusted_first</b>] [<b>-no_alt_chains</b>] [<b>-untrusted file</b>] [<b>-trusted file</b>] [<b>-use_deltas</b>] [<b>-verbose</b>] [<b>-auth_level level</b>] [<b>-verify_depth num</b>] [<b>-verify_email email</b>] [<b>-verify_hostname hostname</b>] [<b>-verify_ip ip</b>] [<b>-verify_name name</b>] [<b>-x509_strict</b>] [<b>-show_chain</b>] [<b>-</b>] [certificates]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>verify</b> command verifies certificate chains.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="CAfile-file"><b>-CAfile file</b></dt>
<dd>

<p>A <b>file</b> of trusted certificates. The file should contain one or more certificates in PEM format.</p>

</dd>
<dt id="CApath-directory"><b>-CApath directory</b></dt>
<dd>

<p>A directory of trusted certificates. The certificates should have names of the form: hash.0 or have symbolic links to them of this form (&quot;hash&quot; is the hashed certificate subject name: see the <b>-hash</b> option of the <b>x509</b> utility). Under Unix the <b>c_rehash</b> script will automatically create symbolic links to a directory of certificates.</p>

</dd>
<dt id="no-CAfile"><b>-no-CAfile</b></dt>
<dd>

<p>Do not load the trusted CA certificates from the default file location.</p>

</dd>
<dt id="no-CApath"><b>-no-CApath</b></dt>
<dd>

<p>Do not load the trusted CA certificates from the default directory location.</p>

</dd>
<dt id="allow_proxy_certs"><b>-allow_proxy_certs</b></dt>
<dd>

<p>Allow the verification of proxy certificates.</p>

</dd>
<dt id="attime-timestamp"><b>-attime timestamp</b></dt>
<dd>

<p>Perform validation checks using time specified by <b>timestamp</b> and not current system time. <b>timestamp</b> is the number of seconds since 01.01.1970 (UNIX time).</p>

</dd>
<dt id="check_ss_sig"><b>-check_ss_sig</b></dt>
<dd>

<p>Verify the signature of the last certificate in a chain if the certificate is supposedly self-signed. This is prohibited and will result in an error if it is a non-conforming CA certificate with key usage restrictions not including the keyCertSign bit. This verification is disabled by default because it doesn&#39;t add any security.</p>

</dd>
<dt id="CRLfile-file"><b>-CRLfile file</b></dt>
<dd>

<p>The <b>file</b> should contain one or more CRLs in PEM format. This option can be specified more than once to include CRLs from multiple <b>files</b>.</p>

</dd>
<dt id="crl_download"><b>-crl_download</b></dt>
<dd>

<p>Attempt to download CRL information for this certificate.</p>

</dd>
<dt id="crl_check"><b>-crl_check</b></dt>
<dd>

<p>Checks end entity certificate validity by attempting to look up a valid CRL. If a valid CRL cannot be found an error occurs.</p>

</dd>
<dt id="crl_check_all"><b>-crl_check_all</b></dt>
<dd>

<p>Checks the validity of <b>all</b> certificates in the chain by attempting to look up valid CRLs.</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Specifying an engine <b>id</b> will cause <a href="../man1/verify.html">verify(1)</a> to attempt to load the specified engine. The engine will then be set as the default for all its supported algorithms. If you want to load certificates or CRLs that require engine support via any of the <b>-trusted</b>, <b>-untrusted</b> or <b>-CRLfile</b> options, the <b>-engine</b> option must be specified before those options.</p>

</dd>
<dt id="explicit_policy"><b>-explicit_policy</b></dt>
<dd>

<p>Set policy variable require-explicit-policy (see RFC5280).</p>

</dd>
<dt id="extended_crl"><b>-extended_crl</b></dt>
<dd>

<p>Enable extended CRL features such as indirect CRLs and alternate CRL signing keys.</p>

</dd>
<dt id="ignore_critical"><b>-ignore_critical</b></dt>
<dd>

<p>Normally if an unhandled critical extension is present which is not supported by OpenSSL the certificate is rejected (as required by RFC5280). If this option is set critical extensions are ignored.</p>

</dd>
<dt id="inhibit_any"><b>-inhibit_any</b></dt>
<dd>

<p>Set policy variable inhibit-any-policy (see RFC5280).</p>

</dd>
<dt id="inhibit_map"><b>-inhibit_map</b></dt>
<dd>

<p>Set policy variable inhibit-policy-mapping (see RFC5280).</p>

</dd>
<dt id="nameopt-option"><b>-nameopt option</b></dt>
<dd>

<p>Option which determines how the subject or issuer names are displayed. The <b>option</b> argument can be a single option or multiple options separated by commas. Alternatively the <b>-nameopt</b> switch may be used more than once to set multiple options. See the <a href="../man1/x509.html">x509(1)</a> manual page for details.</p>

</dd>
<dt id="no_check_time"><b>-no_check_time</b></dt>
<dd>

<p>This option suppresses checking the validity period of certificates and CRLs against the current time. If option <b>-attime timestamp</b> is used to specify a verification time, the check is not suppressed.</p>

</dd>
<dt id="partial_chain"><b>-partial_chain</b></dt>
<dd>

<p>Allow verification to succeed even if a <i>complete</i> chain cannot be built to a self-signed trust-anchor, provided it is possible to construct a chain to a trusted certificate that might not be self-signed.</p>

</dd>
<dt id="policy-arg"><b>-policy arg</b></dt>
<dd>

<p>Enable policy processing and add <b>arg</b> to the user-initial-policy-set (see RFC5280). The policy <b>arg</b> can be an object name an OID in numeric form. This argument can appear more than once.</p>

</dd>
<dt id="policy_check"><b>-policy_check</b></dt>
<dd>

<p>Enables certificate policy processing.</p>

</dd>
<dt id="policy_print"><b>-policy_print</b></dt>
<dd>

<p>Print out diagnostics related to policy processing.</p>

</dd>
<dt id="purpose-purpose"><b>-purpose purpose</b></dt>
<dd>

<p>The intended use for the certificate. If this option is not specified, <b>verify</b> will not consider certificate purpose during chain verification. Currently accepted uses are <b>sslclient</b>, <b>sslserver</b>, <b>nssslserver</b>, <b>smimesign</b>, <b>smimeencrypt</b>. See the <b>VERIFY OPERATION</b> section for more information.</p>

</dd>
<dt id="suiteB_128_only--suiteB_128--suiteB_192"><b>-suiteB_128_only</b>, <b>-suiteB_128</b>, <b>-suiteB_192</b></dt>
<dd>

<p>Enable the Suite B mode operation at 128 bit Level of Security, 128 bit or 192 bit, or only 192 bit Level of Security respectively. See RFC6460 for details. In particular the supported signature algorithms are reduced to support only ECDSA and SHA256 or SHA384 and only the elliptic curves P-256 and P-384.</p>

</dd>
<dt id="trusted_first"><b>-trusted_first</b></dt>
<dd>

<p>When constructing the certificate chain, use the trusted certificates specified via <b>-CAfile</b>, <b>-CApath</b> or <b>-trusted</b> before any certificates specified via <b>-untrusted</b>. This can be useful in environments with Bridge or Cross-Certified CAs. As of OpenSSL 1.1.0 this option is on by default and cannot be disabled.</p>

</dd>
<dt id="no_alt_chains"><b>-no_alt_chains</b></dt>
<dd>

<p>By default, unless <b>-trusted_first</b> is specified, when building a certificate chain, if the first certificate chain found is not trusted, then OpenSSL will attempt to replace untrusted issuer certificates with certificates from the trust store to see if an alternative chain can be found that is trusted. As of OpenSSL 1.1.0, with <b>-trusted_first</b> always on, this option has no effect.</p>

</dd>
<dt id="untrusted-file"><b>-untrusted file</b></dt>
<dd>

<p>A <b>file</b> of additional untrusted certificates (intermediate issuer CAs) used to construct a certificate chain from the subject certificate to a trust-anchor. The <b>file</b> should contain one or more certificates in PEM format. This option can be specified more than once to include untrusted certificates from multiple <b>files</b>.</p>

</dd>
<dt id="trusted-file"><b>-trusted file</b></dt>
<dd>

<p>A <b>file</b> of trusted certificates, which must be self-signed, unless the <b>-partial_chain</b> option is specified. The <b>file</b> contains one or more certificates in PEM format. With this option, no additional (e.g., default) certificate lists are consulted. That is, the only trust-anchors are those listed in <b>file</b>. This option can be specified more than once to include trusted certificates from multiple <b>files</b>. This option implies the <b>-no-CAfile</b> and <b>-no-CApath</b> options. This option cannot be used in combination with either of the <b>-CAfile</b> or <b>-CApath</b> options.</p>

</dd>
<dt id="use_deltas"><b>-use_deltas</b></dt>
<dd>

<p>Enable support for delta CRLs.</p>

</dd>
<dt id="verbose"><b>-verbose</b></dt>
<dd>

<p>Print extra information about the operations being performed.</p>

</dd>
<dt id="auth_level-level"><b>-auth_level level</b></dt>
<dd>

<p>Set the certificate chain authentication security level to <b>level</b>. The authentication security level determines the acceptable signature and public key strength when verifying certificate chains. For a certificate chain to validate, the public keys of all the certificates must meet the specified security <b>level</b>. The signature algorithm security level is enforced for all the certificates in the chain except for the chain&#39;s <i>trust anchor</i>, which is either directly trusted or validated by means other than its signature. See <a href="../man3/SSL_CTX_set_security_level.html">SSL_CTX_set_security_level(3)</a> for the definitions of the available levels. The default security level is -1, or &quot;not set&quot;. At security level 0 or lower all algorithms are acceptable. Security level 1 requires at least 80-bit-equivalent security and is broadly interoperable, though it will, for example, reject MD5 signatures or RSA keys shorter than 1024 bits.</p>

</dd>
<dt id="verify_depth-num"><b>-verify_depth num</b></dt>
<dd>

<p>Limit the certificate chain to <b>num</b> intermediate CA certificates. A maximal depth chain can have up to <b>num+2</b> certificates, since neither the end-entity certificate nor the trust-anchor certificate count against the <b>-verify_depth</b> limit.</p>

</dd>
<dt id="verify_email-email"><b>-verify_email email</b></dt>
<dd>

<p>Verify if the <b>email</b> matches the email address in Subject Alternative Name or the email in the subject Distinguished Name.</p>

</dd>
<dt id="verify_hostname-hostname"><b>-verify_hostname hostname</b></dt>
<dd>

<p>Verify if the <b>hostname</b> matches DNS name in Subject Alternative Name or Common Name in the subject certificate.</p>

</dd>
<dt id="verify_ip-ip"><b>-verify_ip ip</b></dt>
<dd>

<p>Verify if the <b>ip</b> matches the IP address in Subject Alternative Name of the subject certificate.</p>

</dd>
<dt id="verify_name-name"><b>-verify_name name</b></dt>
<dd>

<p>Use default verification policies like trust model and required certificate policies identified by <b>name</b>. The trust model determines which auxiliary trust or reject OIDs are applicable to verifying the given certificate chain. See the <b>-addtrust</b> and <b>-addreject</b> options of the <a href="../man1/x509.html">x509(1)</a> command-line utility. Supported policy names include: <b>default</b>, <b>pkcs7</b>, <b>smime_sign</b>, <b>ssl_client</b>, <b>ssl_server</b>. These mimics the combinations of purpose and trust settings used in SSL, CMS and S/MIME. As of OpenSSL 1.1.0, the trust model is inferred from the purpose when not specified, so the <b>-verify_name</b> options are functionally equivalent to the corresponding <b>-purpose</b> settings.</p>

</dd>
<dt id="x509_strict"><b>-x509_strict</b></dt>
<dd>

<p>For strict X.509 compliance, disable non-compliant workarounds for broken certificates.</p>

</dd>
<dt id="show_chain"><b>-show_chain</b></dt>
<dd>

<p>Display information about the certificate chain that has been built (if successful). Certificates in the chain that came from the untrusted list will be flagged as &quot;untrusted&quot;.</p>

</dd>
<dt id="pod"><b>-</b></dt>
<dd>

<p>Indicates the last option. All arguments following this are assumed to be certificate files. This is useful if the first certificate filename begins with a <b>-</b>.</p>

</dd>
<dt id="certificates"><b>certificates</b></dt>
<dd>

<p>One or more certificates to verify. If no certificates are given, <b>verify</b> will attempt to read a certificate from standard input. Certificates must be in PEM format.</p>

</dd>
</dl>

<h1 id="VERIFY-OPERATION">VERIFY OPERATION</h1>

<p>The <b>verify</b> program uses the same functions as the internal SSL and S/MIME verification, therefore, this description applies to these verify operations too.</p>

<p>There is one crucial difference between the verify operations performed by the <b>verify</b> program: wherever possible an attempt is made to continue after an error whereas normally the verify operation would halt on the first error. This allows all the problems with a certificate chain to be determined.</p>

<p>The verify operation consists of a number of separate steps.</p>

<p>Firstly a certificate chain is built up starting from the supplied certificate and ending in the root CA. It is an error if the whole chain cannot be built up. The chain is built up by looking up the issuers certificate of the current certificate. If a certificate is found which is its own issuer it is assumed to be the root CA.</p>

<p>The process of &#39;looking up the issuers certificate&#39; itself involves a number of steps. After all certificates whose subject name matches the issuer name of the current certificate are subject to further tests. The relevant authority key identifier components of the current certificate (if present) must match the subject key identifier (if present) and issuer and serial number of the candidate issuer, in addition the keyUsage extension of the candidate issuer (if present) must permit certificate signing.</p>

<p>The lookup first looks in the list of untrusted certificates and if no match is found the remaining lookups are from the trusted certificates. The root CA is always looked up in the trusted certificate list: if the certificate to verify is a root certificate then an exact match must be found in the trusted list.</p>

<p>The second operation is to check every untrusted certificate&#39;s extensions for consistency with the supplied purpose. If the <b>-purpose</b> option is not included then no checks are done. The supplied or &quot;leaf&quot; certificate must have extensions compatible with the supplied purpose and all other certificates must also be valid CA certificates. The precise extensions required are described in more detail in the <b>CERTIFICATE EXTENSIONS</b> section of the <b>x509</b> utility.</p>

<p>The third operation is to check the trust settings on the root CA. The root CA should be trusted for the supplied purpose. For compatibility with previous versions of OpenSSL, a certificate with no trust settings is considered to be valid for all purposes.</p>

<p>The final operation is to check the validity of the certificate chain. For each element in the chain, including the root CA certificate, the validity period as specified by the <code>notBefore</code> and <code>notAfter</code> fields is checked against the current system time. The <b>-attime</b> flag may be used to use a reference time other than &quot;now.&quot; The certificate signature is checked as well (except for the signature of the typically self-signed root CA certificate, which is verified only if the <b>-check_ss_sig</b> option is given).</p>

<p>If all operations complete successfully then certificate is considered valid. If any operation fails then the certificate is not valid.</p>

<h1 id="DIAGNOSTICS">DIAGNOSTICS</h1>

<p>When a verify operation fails the output messages can be somewhat cryptic. The general form of the error message is:</p>

<pre><code>server.pem: /C=AU/ST=Queensland/O=CryptSoft Pty Ltd/CN=Test CA (1024 bit)
error 24 at 1 depth lookup:invalid CA certificate</code></pre>

<p>The first line contains the name of the certificate being verified followed by the subject name of the certificate. The second line contains the error number and the depth. The depth is number of the certificate being verified when a problem was detected starting with zero for the certificate being verified itself then 1 for the CA that signed the certificate and so on. Finally a text version of the error number is presented.</p>

<p>A partial list of the error codes and messages is shown below, this also includes the name of the error code as defined in the header file x509_vfy.h Some of the error codes are defined but never returned: these are described as &quot;unused&quot;.</p>

<dl>

<dt id="X509_V_OK"><b>X509_V_OK</b></dt>
<dd>

<p>The operation was successful.</p>

</dd>
<dt id="X509_V_ERR_UNSPECIFIED"><b>X509_V_ERR_UNSPECIFIED</b></dt>
<dd>

<p>Unspecified error; should not happen.</p>

</dd>
<dt id="X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT"><b>X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT</b></dt>
<dd>

<p>The issuer certificate of a looked up certificate could not be found. This normally means the list of trusted certificates is not complete.</p>

</dd>
<dt id="X509_V_ERR_UNABLE_TO_GET_CRL"><b>X509_V_ERR_UNABLE_TO_GET_CRL</b></dt>
<dd>

<p>The CRL of a certificate could not be found.</p>

</dd>
<dt id="X509_V_ERR_UNABLE_TO_DECRYPT_CERT_SIGNATURE"><b>X509_V_ERR_UNABLE_TO_DECRYPT_CERT_SIGNATURE</b></dt>
<dd>

<p>The certificate signature could not be decrypted. This means that the actual signature value could not be determined rather than it not matching the expected value, this is only meaningful for RSA keys.</p>

</dd>
<dt id="X509_V_ERR_UNABLE_TO_DECRYPT_CRL_SIGNATURE"><b>X509_V_ERR_UNABLE_TO_DECRYPT_CRL_SIGNATURE</b></dt>
<dd>

<p>The CRL signature could not be decrypted: this means that the actual signature value could not be determined rather than it not matching the expected value. Unused.</p>

</dd>
<dt id="X509_V_ERR_UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY"><b>X509_V_ERR_UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY</b></dt>
<dd>

<p>The public key in the certificate SubjectPublicKeyInfo could not be read.</p>

</dd>
<dt id="X509_V_ERR_CERT_SIGNATURE_FAILURE"><b>X509_V_ERR_CERT_SIGNATURE_FAILURE</b></dt>
<dd>

<p>The signature of the certificate is invalid.</p>

</dd>
<dt id="X509_V_ERR_CRL_SIGNATURE_FAILURE"><b>X509_V_ERR_CRL_SIGNATURE_FAILURE</b></dt>
<dd>

<p>The signature of the certificate is invalid.</p>

</dd>
<dt id="X509_V_ERR_CERT_NOT_YET_VALID"><b>X509_V_ERR_CERT_NOT_YET_VALID</b></dt>
<dd>

<p>The certificate is not yet valid: the notBefore date is after the current time.</p>

</dd>
<dt id="X509_V_ERR_CERT_HAS_EXPIRED"><b>X509_V_ERR_CERT_HAS_EXPIRED</b></dt>
<dd>

<p>The certificate has expired: that is the notAfter date is before the current time.</p>

</dd>
<dt id="X509_V_ERR_CRL_NOT_YET_VALID"><b>X509_V_ERR_CRL_NOT_YET_VALID</b></dt>
<dd>

<p>The CRL is not yet valid.</p>

</dd>
<dt id="X509_V_ERR_CRL_HAS_EXPIRED"><b>X509_V_ERR_CRL_HAS_EXPIRED</b></dt>
<dd>

<p>The CRL has expired.</p>

</dd>
<dt id="X509_V_ERR_ERROR_IN_CERT_NOT_BEFORE_FIELD"><b>X509_V_ERR_ERROR_IN_CERT_NOT_BEFORE_FIELD</b></dt>
<dd>

<p>The certificate notBefore field contains an invalid time.</p>

</dd>
<dt id="X509_V_ERR_ERROR_IN_CERT_NOT_AFTER_FIELD"><b>X509_V_ERR_ERROR_IN_CERT_NOT_AFTER_FIELD</b></dt>
<dd>

<p>The certificate notAfter field contains an invalid time.</p>

</dd>
<dt id="X509_V_ERR_ERROR_IN_CRL_LAST_UPDATE_FIELD"><b>X509_V_ERR_ERROR_IN_CRL_LAST_UPDATE_FIELD</b></dt>
<dd>

<p>The CRL lastUpdate field contains an invalid time.</p>

</dd>
<dt id="X509_V_ERR_ERROR_IN_CRL_NEXT_UPDATE_FIELD"><b>X509_V_ERR_ERROR_IN_CRL_NEXT_UPDATE_FIELD</b></dt>
<dd>

<p>The CRL nextUpdate field contains an invalid time.</p>

</dd>
<dt id="X509_V_ERR_OUT_OF_MEM"><b>X509_V_ERR_OUT_OF_MEM</b></dt>
<dd>

<p>An error occurred trying to allocate memory. This should never happen.</p>

</dd>
<dt id="X509_V_ERR_DEPTH_ZERO_SELF_SIGNED_CERT"><b>X509_V_ERR_DEPTH_ZERO_SELF_SIGNED_CERT</b></dt>
<dd>

<p>The passed certificate is self-signed and the same certificate cannot be found in the list of trusted certificates.</p>

</dd>
<dt id="X509_V_ERR_SELF_SIGNED_CERT_IN_CHAIN"><b>X509_V_ERR_SELF_SIGNED_CERT_IN_CHAIN</b></dt>
<dd>

<p>The certificate chain could be built up using the untrusted certificates but the root could not be found locally.</p>

</dd>
<dt id="X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT_LOCALLY"><b>X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT_LOCALLY</b></dt>
<dd>

<p>The issuer certificate could not be found: this occurs if the issuer certificate of an untrusted certificate cannot be found.</p>

</dd>
<dt id="X509_V_ERR_UNABLE_TO_VERIFY_LEAF_SIGNATURE"><b>X509_V_ERR_UNABLE_TO_VERIFY_LEAF_SIGNATURE</b></dt>
<dd>

<p>No signatures could be verified because the chain contains only one certificate and it is not self signed.</p>

</dd>
<dt id="X509_V_ERR_CERT_CHAIN_TOO_LONG"><b>X509_V_ERR_CERT_CHAIN_TOO_LONG</b></dt>
<dd>

<p>The certificate chain length is greater than the supplied maximum depth. Unused.</p>

</dd>
<dt id="X509_V_ERR_CERT_REVOKED"><b>X509_V_ERR_CERT_REVOKED</b></dt>
<dd>

<p>The certificate has been revoked.</p>

</dd>
<dt id="X509_V_ERR_INVALID_CA"><b>X509_V_ERR_INVALID_CA</b></dt>
<dd>

<p>A CA certificate is invalid. Either it is not a CA or its extensions are not consistent with the supplied purpose.</p>

</dd>
<dt id="X509_V_ERR_PATH_LENGTH_EXCEEDED"><b>X509_V_ERR_PATH_LENGTH_EXCEEDED</b></dt>
<dd>

<p>The basicConstraints pathlength parameter has been exceeded.</p>

</dd>
<dt id="X509_V_ERR_INVALID_PURPOSE"><b>X509_V_ERR_INVALID_PURPOSE</b></dt>
<dd>

<p>The supplied certificate cannot be used for the specified purpose.</p>

</dd>
<dt id="X509_V_ERR_CERT_UNTRUSTED"><b>X509_V_ERR_CERT_UNTRUSTED</b></dt>
<dd>

<p>The root CA is not marked as trusted for the specified purpose.</p>

</dd>
<dt id="X509_V_ERR_CERT_REJECTED"><b>X509_V_ERR_CERT_REJECTED</b></dt>
<dd>

<p>The root CA is marked to reject the specified purpose.</p>

</dd>
<dt id="X509_V_ERR_SUBJECT_ISSUER_MISMATCH"><b>X509_V_ERR_SUBJECT_ISSUER_MISMATCH</b></dt>
<dd>

<p>Not used as of OpenSSL 1.1.0 as a result of the deprecation of the <b>-issuer_checks</b> option.</p>

</dd>
<dt id="X509_V_ERR_AKID_SKID_MISMATCH"><b>X509_V_ERR_AKID_SKID_MISMATCH</b></dt>
<dd>

<p>Not used as of OpenSSL 1.1.0 as a result of the deprecation of the <b>-issuer_checks</b> option.</p>

</dd>
<dt id="X509_V_ERR_AKID_ISSUER_SERIAL_MISMATCH"><b>X509_V_ERR_AKID_ISSUER_SERIAL_MISMATCH</b></dt>
<dd>

<p>Not used as of OpenSSL 1.1.0 as a result of the deprecation of the <b>-issuer_checks</b> option.</p>

</dd>
<dt id="X509_V_ERR_KEYUSAGE_NO_CERTSIGN"><b>X509_V_ERR_KEYUSAGE_NO_CERTSIGN</b></dt>
<dd>

<p>Not used as of OpenSSL 1.1.0 as a result of the deprecation of the <b>-issuer_checks</b> option.</p>

</dd>
<dt id="X509_V_ERR_UNABLE_TO_GET_CRL_ISSUER"><b>X509_V_ERR_UNABLE_TO_GET_CRL_ISSUER</b></dt>
<dd>

<p>Unable to get CRL issuer certificate.</p>

</dd>
<dt id="X509_V_ERR_UNHANDLED_CRITICAL_EXTENSION"><b>X509_V_ERR_UNHANDLED_CRITICAL_EXTENSION</b></dt>
<dd>

<p>Unhandled critical extension.</p>

</dd>
<dt id="X509_V_ERR_KEYUSAGE_NO_CRL_SIGN"><b>X509_V_ERR_KEYUSAGE_NO_CRL_SIGN</b></dt>
<dd>

<p>Key usage does not include CRL signing.</p>

</dd>
<dt id="X509_V_ERR_UNHANDLED_CRITICAL_CRL_EXTENSION"><b>X509_V_ERR_UNHANDLED_CRITICAL_CRL_EXTENSION</b></dt>
<dd>

<p>Unhandled critical CRL extension.</p>

</dd>
<dt id="X509_V_ERR_INVALID_NON_CA"><b>X509_V_ERR_INVALID_NON_CA</b></dt>
<dd>

<p>Invalid non-CA certificate has CA markings.</p>

</dd>
<dt id="X509_V_ERR_PROXY_PATH_LENGTH_EXCEEDED"><b>X509_V_ERR_PROXY_PATH_LENGTH_EXCEEDED</b></dt>
<dd>

<p>Proxy path length constraint exceeded.</p>

</dd>
<dt id="X509_V_ERR_PROXY_SUBJECT_INVALID"><b>X509_V_ERR_PROXY_SUBJECT_INVALID</b></dt>
<dd>

<p>Proxy certificate subject is invalid. It MUST be the same as the issuer with a single CN component added.</p>

</dd>
<dt id="X509_V_ERR_KEYUSAGE_NO_DIGITAL_SIGNATURE"><b>X509_V_ERR_KEYUSAGE_NO_DIGITAL_SIGNATURE</b></dt>
<dd>

<p>Key usage does not include digital signature.</p>

</dd>
<dt id="X509_V_ERR_PROXY_CERTIFICATES_NOT_ALLOWED"><b>X509_V_ERR_PROXY_CERTIFICATES_NOT_ALLOWED</b></dt>
<dd>

<p>Proxy certificates not allowed, please use <b>-allow_proxy_certs</b>.</p>

</dd>
<dt id="X509_V_ERR_INVALID_EXTENSION"><b>X509_V_ERR_INVALID_EXTENSION</b></dt>
<dd>

<p>Invalid or inconsistent certificate extension.</p>

</dd>
<dt id="X509_V_ERR_INVALID_POLICY_EXTENSION"><b>X509_V_ERR_INVALID_POLICY_EXTENSION</b></dt>
<dd>

<p>Invalid or inconsistent certificate policy extension.</p>

</dd>
<dt id="X509_V_ERR_NO_EXPLICIT_POLICY"><b>X509_V_ERR_NO_EXPLICIT_POLICY</b></dt>
<dd>

<p>No explicit policy.</p>

</dd>
<dt id="X509_V_ERR_DIFFERENT_CRL_SCOPE"><b>X509_V_ERR_DIFFERENT_CRL_SCOPE</b></dt>
<dd>

<p>Different CRL scope.</p>

</dd>
<dt id="X509_V_ERR_UNSUPPORTED_EXTENSION_FEATURE"><b>X509_V_ERR_UNSUPPORTED_EXTENSION_FEATURE</b></dt>
<dd>

<p>Unsupported extension feature.</p>

</dd>
<dt id="X509_V_ERR_UNNESTED_RESOURCE"><b>X509_V_ERR_UNNESTED_RESOURCE</b></dt>
<dd>

<p>RFC 3779 resource not subset of parent&#39;s resources.</p>

</dd>
<dt id="X509_V_ERR_PERMITTED_VIOLATION"><b>X509_V_ERR_PERMITTED_VIOLATION</b></dt>
<dd>

<p>Permitted subtree violation.</p>

</dd>
<dt id="X509_V_ERR_EXCLUDED_VIOLATION"><b>X509_V_ERR_EXCLUDED_VIOLATION</b></dt>
<dd>

<p>Excluded subtree violation.</p>

</dd>
<dt id="X509_V_ERR_SUBTREE_MINMAX"><b>X509_V_ERR_SUBTREE_MINMAX</b></dt>
<dd>

<p>Name constraints minimum and maximum not supported.</p>

</dd>
<dt id="X509_V_ERR_APPLICATION_VERIFICATION"><b>X509_V_ERR_APPLICATION_VERIFICATION</b></dt>
<dd>

<p>Application verification failure. Unused.</p>

</dd>
<dt id="X509_V_ERR_UNSUPPORTED_CONSTRAINT_TYPE"><b>X509_V_ERR_UNSUPPORTED_CONSTRAINT_TYPE</b></dt>
<dd>

<p>Unsupported name constraint type.</p>

</dd>
<dt id="X509_V_ERR_UNSUPPORTED_CONSTRAINT_SYNTAX"><b>X509_V_ERR_UNSUPPORTED_CONSTRAINT_SYNTAX</b></dt>
<dd>

<p>Unsupported or invalid name constraint syntax.</p>

</dd>
<dt id="X509_V_ERR_UNSUPPORTED_NAME_SYNTAX"><b>X509_V_ERR_UNSUPPORTED_NAME_SYNTAX</b></dt>
<dd>

<p>Unsupported or invalid name syntax.</p>

</dd>
<dt id="X509_V_ERR_CRL_PATH_VALIDATION_ERROR"><b>X509_V_ERR_CRL_PATH_VALIDATION_ERROR</b></dt>
<dd>

<p>CRL path validation error.</p>

</dd>
<dt id="X509_V_ERR_PATH_LOOP"><b>X509_V_ERR_PATH_LOOP</b></dt>
<dd>

<p>Path loop.</p>

</dd>
<dt id="X509_V_ERR_SUITE_B_INVALID_VERSION"><b>X509_V_ERR_SUITE_B_INVALID_VERSION</b></dt>
<dd>

<p>Suite B: certificate version invalid.</p>

</dd>
<dt id="X509_V_ERR_SUITE_B_INVALID_ALGORITHM"><b>X509_V_ERR_SUITE_B_INVALID_ALGORITHM</b></dt>
<dd>

<p>Suite B: invalid public key algorithm.</p>

</dd>
<dt id="X509_V_ERR_SUITE_B_INVALID_CURVE"><b>X509_V_ERR_SUITE_B_INVALID_CURVE</b></dt>
<dd>

<p>Suite B: invalid ECC curve.</p>

</dd>
<dt id="X509_V_ERR_SUITE_B_INVALID_SIGNATURE_ALGORITHM"><b>X509_V_ERR_SUITE_B_INVALID_SIGNATURE_ALGORITHM</b></dt>
<dd>

<p>Suite B: invalid signature algorithm.</p>

</dd>
<dt id="X509_V_ERR_SUITE_B_LOS_NOT_ALLOWED"><b>X509_V_ERR_SUITE_B_LOS_NOT_ALLOWED</b></dt>
<dd>

<p>Suite B: curve not allowed for this LOS.</p>

</dd>
<dt id="X509_V_ERR_SUITE_B_CANNOT_SIGN_P_384_WITH_P_256"><b>X509_V_ERR_SUITE_B_CANNOT_SIGN_P_384_WITH_P_256</b></dt>
<dd>

<p>Suite B: cannot sign P-384 with P-256.</p>

</dd>
<dt id="X509_V_ERR_HOSTNAME_MISMATCH"><b>X509_V_ERR_HOSTNAME_MISMATCH</b></dt>
<dd>

<p>Hostname mismatch.</p>

</dd>
<dt id="X509_V_ERR_EMAIL_MISMATCH"><b>X509_V_ERR_EMAIL_MISMATCH</b></dt>
<dd>

<p>Email address mismatch.</p>

</dd>
<dt id="X509_V_ERR_IP_ADDRESS_MISMATCH"><b>X509_V_ERR_IP_ADDRESS_MISMATCH</b></dt>
<dd>

<p>IP address mismatch.</p>

</dd>
<dt id="X509_V_ERR_DANE_NO_MATCH"><b>X509_V_ERR_DANE_NO_MATCH</b></dt>
<dd>

<p>DANE TLSA authentication is enabled, but no TLSA records matched the certificate chain. This error is only possible in <a href="../man1/s_client.html">s_client(1)</a>.</p>

</dd>
<dt id="X509_V_ERR_EE_KEY_TOO_SMALL"><b>X509_V_ERR_EE_KEY_TOO_SMALL</b></dt>
<dd>

<p>EE certificate key too weak.</p>

</dd>
<dt id="X509_ERR_CA_KEY_TOO_SMALL"><b>X509_ERR_CA_KEY_TOO_SMALL</b></dt>
<dd>

<p>CA certificate key too weak.</p>

</dd>
<dt id="X509_ERR_CA_MD_TOO_WEAK"><b>X509_ERR_CA_MD_TOO_WEAK</b></dt>
<dd>

<p>CA signature digest algorithm too weak.</p>

</dd>
<dt id="X509_V_ERR_INVALID_CALL"><b>X509_V_ERR_INVALID_CALL</b></dt>
<dd>

<p>nvalid certificate verification context.</p>

</dd>
<dt id="X509_V_ERR_STORE_LOOKUP"><b>X509_V_ERR_STORE_LOOKUP</b></dt>
<dd>

<p>Issuer certificate lookup error.</p>

</dd>
<dt id="X509_V_ERR_NO_VALID_SCTS"><b>X509_V_ERR_NO_VALID_SCTS</b></dt>
<dd>

<p>Certificate Transparency required, but no valid SCTs found.</p>

</dd>
<dt id="X509_V_ERR_PROXY_SUBJECT_NAME_VIOLATION"><b>X509_V_ERR_PROXY_SUBJECT_NAME_VIOLATION</b></dt>
<dd>

<p>Proxy subject name violation.</p>

</dd>
<dt id="X509_V_ERR_OCSP_VERIFY_NEEDED"><b>X509_V_ERR_OCSP_VERIFY_NEEDED</b></dt>
<dd>

<p>Returned by the verify callback to indicate an OCSP verification is needed.</p>

</dd>
<dt id="X509_V_ERR_OCSP_VERIFY_FAILED"><b>X509_V_ERR_OCSP_VERIFY_FAILED</b></dt>
<dd>

<p>Returned by the verify callback to indicate OCSP verification failed.</p>

</dd>
<dt id="X509_V_ERR_OCSP_CERT_UNKNOWN"><b>X509_V_ERR_OCSP_CERT_UNKNOWN</b></dt>
<dd>

<p>Returned by the verify callback to indicate that the certificate is not recognized by the OCSP responder.</p>

</dd>
</dl>

<h1 id="BUGS">BUGS</h1>

<p>Although the issuer checks are a considerable improvement over the old technique they still suffer from limitations in the underlying X509_LOOKUP API. One consequence of this is that trusted certificates with matching subject name must either appear in a file (as specified by the <b>-CAfile</b> option) or a directory (as specified by <b>-CApath</b>). If they occur in both then only the certificates in the file will be recognised.</p>

<p>Previous versions of OpenSSL assume certificates with matching subject name are identical and mishandled them.</p>

<p>Previous versions of this documentation swapped the meaning of the <b>X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT</b> and <b>X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT_LOCALLY</b> error codes.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/x509.html">x509(1)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The <b>-show_chain</b> option was added in OpenSSL 1.1.0.</p>

<p>The <b>-issuer_checks</b> option is deprecated as of OpenSSL 1.1.0 and is silently ignored.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


