<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_STORE_get0_param</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_STORE_get0_param, X509_STORE_set1_param, X509_STORE_get0_objects - X509_STORE setter and getter functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509_vfy.h&gt;

X509_VERIFY_PARAM *X509_STORE_get0_param(X509_STORE *ctx);
int X509_STORE_set1_param(X509_STORE *ctx, X509_VERIFY_PARAM *pm);
STACK_OF(X509_OBJECT) *X509_STORE_get0_objects(X509_STORE *ctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>X509_STORE_set1_param() sets the verification parameters to <b>pm</b> for <b>ctx</b>.</p>

<p>X509_STORE_get0_param() retrieves an internal pointer to the verification parameters for <b>ctx</b>. The returned pointer must not be freed by the calling application</p>

<p>X509_STORE_get0_objects() retrieve an internal pointer to the store&#39;s X509 object cache. The cache contains <b>X509</b> and <b>X509_CRL</b> objects. The returned pointer must not be freed by the calling application.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>X509_STORE_get0_param() returns a pointer to an <b>X509_VERIFY_PARAM</b> structure.</p>

<p>X509_STORE_set1_param() returns 1 for success and 0 for failure.</p>

<p>X509_STORE_get0_objects() returns a pointer to a stack of <b>X509_OBJECT</b>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/X509_STORE_new.html">X509_STORE_new(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p><b>X509_STORE_get0_param</b> and <b>X509_STORE_get0_objects</b> were added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


