<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY_new, EVP_PKEY_up_ref, EVP_PKEY_free, EVP_PKEY_new_raw_private_key, EVP_PKEY_new_raw_public_key, EVP_PKEY_new_CMAC_key, EVP_PKEY_new_mac_key, EVP_PKEY_get_raw_private_key, EVP_PKEY_get_raw_public_key - public/private key allocation and raw key handling functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

EVP_PKEY *EVP_PKEY_new(void);
int EVP_PKEY_up_ref(EVP_PKEY *key);
void EVP_PKEY_free(EVP_PKEY *key);

EVP_PKEY *EVP_PKEY_new_raw_private_key(int type, ENGINE *e,
                                       const unsigned char *key, size_t keylen);
EVP_PKEY *EVP_PKEY_new_raw_public_key(int type, ENGINE *e,
                                      const unsigned char *key, size_t keylen);
EVP_PKEY *EVP_PKEY_new_CMAC_key(ENGINE *e, const unsigned char *priv,
                                size_t len, const EVP_CIPHER *cipher);
EVP_PKEY *EVP_PKEY_new_mac_key(int type, ENGINE *e, const unsigned char *key,
                               int keylen);

int EVP_PKEY_get_raw_private_key(const EVP_PKEY *pkey, unsigned char *priv,
                                 size_t *len);
int EVP_PKEY_get_raw_public_key(const EVP_PKEY *pkey, unsigned char *pub,
                                size_t *len);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The EVP_PKEY_new() function allocates an empty <b>EVP_PKEY</b> structure which is used by OpenSSL to store public and private keys. The reference count is set to <b>1</b>.</p>

<p>EVP_PKEY_up_ref() increments the reference count of <b>key</b>.</p>

<p>EVP_PKEY_free() decrements the reference count of <b>key</b> and, if the reference count is zero, frees it up. If <b>key</b> is NULL, nothing is done.</p>

<p>EVP_PKEY_new_raw_private_key() allocates a new <b>EVP_PKEY</b>. If <b>e</b> is non-NULL then the new <b>EVP_PKEY</b> structure is associated with the engine <b>e</b>. The <b>type</b> argument indicates what kind of key this is. The value should be a NID for a public key algorithm that supports raw private keys, i.e. one of <b>EVP_PKEY_HMAC</b>, <b>EVP_PKEY_POLY1305</b>, <b>EVP_PKEY_SIPHASH</b>, <b>EVP_PKEY_X25519</b>, <b>EVP_PKEY_ED25519</b>, <b>EVP_PKEY_X448</b> or <b>EVP_PKEY_ED448</b>. <b>key</b> points to the raw private key data for this <b>EVP_PKEY</b> which should be of length <b>keylen</b>. The length should be appropriate for the type of the key. The public key data will be automatically derived from the given private key data (if appropriate for the algorithm type).</p>

<p>EVP_PKEY_new_raw_public_key() works in the same way as EVP_PKEY_new_raw_private_key() except that <b>key</b> points to the raw public key data. The <b>EVP_PKEY</b> structure will be initialised without any private key information. Algorithm types that support raw public keys are <b>EVP_PKEY_X25519</b>, <b>EVP_PKEY_ED25519</b>, <b>EVP_PKEY_X448</b> or <b>EVP_PKEY_ED448</b>.</p>

<p>EVP_PKEY_new_CMAC_key() works in the same way as EVP_PKEY_new_raw_private_key() except it is only for the <b>EVP_PKEY_CMAC</b> algorithm type. In addition to the raw private key data, it also takes a cipher algorithm to be used during creation of a CMAC in the <b>cipher</b> argument. The cipher should be a standard encryption only cipher. For example AEAD and XTS ciphers should not be used.</p>

<p>EVP_PKEY_new_mac_key() works in the same way as EVP_PKEY_new_raw_private_key(). New applications should use EVP_PKEY_new_raw_private_key() instead.</p>

<p>EVP_PKEY_get_raw_private_key() fills the buffer provided by <b>priv</b> with raw private key data. The size of the <b>priv</b> buffer should be in <b>*len</b> on entry to the function, and on exit <b>*len</b> is updated with the number of bytes actually written. If the buffer <b>priv</b> is NULL then <b>*len</b> is populated with the number of bytes required to hold the key. The calling application is responsible for ensuring that the buffer is large enough to receive the private key data. This function only works for algorithms that support raw private keys. Currently this is: <b>EVP_PKEY_HMAC</b>, <b>EVP_PKEY_POLY1305</b>, <b>EVP_PKEY_SIPHASH</b>, <b>EVP_PKEY_X25519</b>, <b>EVP_PKEY_ED25519</b>, <b>EVP_PKEY_X448</b> or <b>EVP_PKEY_ED448</b>.</p>

<p>EVP_PKEY_get_raw_public_key() fills the buffer provided by <b>pub</b> with raw public key data. The size of the <b>pub</b> buffer should be in <b>*len</b> on entry to the function, and on exit <b>*len</b> is updated with the number of bytes actually written. If the buffer <b>pub</b> is NULL then <b>*len</b> is populated with the number of bytes required to hold the key. The calling application is responsible for ensuring that the buffer is large enough to receive the public key data. This function only works for algorithms that support raw public keys. Currently this is: <b>EVP_PKEY_X25519</b>, <b>EVP_PKEY_ED25519</b>, <b>EVP_PKEY_X448</b> or <b>EVP_PKEY_ED448</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>The <b>EVP_PKEY</b> structure is used by various OpenSSL functions which require a general private key without reference to any particular algorithm.</p>

<p>The structure returned by EVP_PKEY_new() is empty. To add a private or public key to this empty structure use the appropriate functions described in <a href="../man3/EVP_PKEY_set1_RSA.html">EVP_PKEY_set1_RSA(3)</a>, <a>EVP_PKEY_set1_DSA</a>, <a>EVP_PKEY_set1_DH</a> or <a>EVP_PKEY_set1_EC_KEY</a>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_PKEY_new(), EVP_PKEY_new_raw_private_key(), EVP_PKEY_new_raw_public_key(), EVP_PKEY_new_CMAC_key() and EVP_PKEY_new_mac_key() return either the newly allocated <b>EVP_PKEY</b> structure or <b>NULL</b> if an error occurred.</p>

<p>EVP_PKEY_up_ref(), EVP_PKEY_get_raw_private_key() and EVP_PKEY_get_raw_public_key() return 1 for success and 0 for failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_PKEY_set1_RSA.html">EVP_PKEY_set1_RSA(3)</a>, <a>EVP_PKEY_set1_DSA</a>, <a>EVP_PKEY_set1_DH</a> or <a>EVP_PKEY_set1_EC_KEY</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The EVP_PKEY_new() and EVP_PKEY_free() functions exist in all versions of OpenSSL.</p>

<p>The EVP_PKEY_up_ref() function was added in OpenSSL 1.1.0.</p>

<p>The EVP_PKEY_new_raw_private_key(), EVP_PKEY_new_raw_public_key(), EVP_PKEY_new_CMAC_key(), EVP_PKEY_new_raw_private_key() and EVP_PKEY_get_raw_public_key() functions were added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2002-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


