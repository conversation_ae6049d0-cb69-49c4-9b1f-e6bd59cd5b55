.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_QUIET_SHUTDOWN 3"
.TH SSL_CTX_SET_QUIET_SHUTDOWN 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_quiet_shutdown, SSL_CTX_get_quiet_shutdown, SSL_set_quiet_shutdown, SSL_get_quiet_shutdown \- manipulate shutdown behaviour
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_quiet_shutdown(SSL_CTX *ctx, int mode);
\& int SSL_CTX_get_quiet_shutdown(const SSL_CTX *ctx);
\&
\& void SSL_set_quiet_shutdown(SSL *ssl, int mode);
\& int SSL_get_quiet_shutdown(const SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_quiet_shutdown()\fR sets the "quiet shutdown" flag for \fBctx\fR to be
\&\fBmode\fR. SSL objects created from \fBctx\fR inherit the \fBmode\fR valid at the time
\&\fBSSL_new\fR\|(3) is called. \fBmode\fR may be 0 or 1.
.PP
\&\fBSSL_CTX_get_quiet_shutdown()\fR returns the "quiet shutdown" setting of \fBctx\fR.
.PP
\&\fBSSL_set_quiet_shutdown()\fR sets the "quiet shutdown" flag for \fBssl\fR to be
\&\fBmode\fR. The setting stays valid until \fBssl\fR is removed with
\&\fBSSL_free\fR\|(3) or \fBSSL_set_quiet_shutdown()\fR is called again.
It is not changed when \fBSSL_clear\fR\|(3) is called.
\&\fBmode\fR may be 0 or 1.
.PP
\&\fBSSL_get_quiet_shutdown()\fR returns the "quiet shutdown" setting of \fBssl\fR.
.SH NOTES
.IX Header "NOTES"
Normally when a SSL connection is finished, the parties must send out
close_notify alert messages using \fBSSL_shutdown\fR\|(3)
for a clean shutdown.
.PP
When setting the "quiet shutdown" flag to 1, \fBSSL_shutdown\fR\|(3)
will set the internal flags to SSL_SENT_SHUTDOWN|SSL_RECEIVED_SHUTDOWN.
(\fBSSL_shutdown\fR\|(3) then behaves like
\&\fBSSL_set_shutdown\fR\|(3) called with
SSL_SENT_SHUTDOWN|SSL_RECEIVED_SHUTDOWN.)
The session is thus considered to be shutdown, but no close_notify alert
is sent to the peer. This behaviour violates the TLS standard.
.PP
The default is normal shutdown behaviour as described by the TLS standard.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_quiet_shutdown()\fR and \fBSSL_set_quiet_shutdown()\fR do not return
diagnostic information.
.PP
\&\fBSSL_CTX_get_quiet_shutdown()\fR and SSL_get_quiet_shutdown return the current
setting.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_shutdown\fR\|(3),
\&\fBSSL_set_shutdown\fR\|(3), \fBSSL_new\fR\|(3),
\&\fBSSL_clear\fR\|(3), \fBSSL_free\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
