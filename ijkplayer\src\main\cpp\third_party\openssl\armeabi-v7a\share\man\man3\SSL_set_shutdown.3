.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SET_SHUTDOWN 3"
.TH SSL_SET_SHUTDOWN 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_set_shutdown, SSL_get_shutdown \- manipulate shutdown state of an SSL connection
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_set_shutdown(SSL *ssl, int mode);
\&
\& int SSL_get_shutdown(const SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_set_shutdown()\fR sets the shutdown state of \fBssl\fR to \fBmode\fR.
.PP
\&\fBSSL_get_shutdown()\fR returns the shutdown mode of \fBssl\fR.
.SH NOTES
.IX Header "NOTES"
The shutdown state of an ssl connection is a bit mask of:
.IP 0 4
No shutdown setting, yet.
.IP SSL_SENT_SHUTDOWN 4
.IX Item "SSL_SENT_SHUTDOWN"
A close_notify shutdown alert was sent to the peer, the connection is being
considered closed and the session is closed and correct.
.IP SSL_RECEIVED_SHUTDOWN 4
.IX Item "SSL_RECEIVED_SHUTDOWN"
A shutdown alert was received form the peer, either a normal close_notify
or a fatal error.
.PP
SSL_SENT_SHUTDOWN and SSL_RECEIVED_SHUTDOWN can be set at the same time.
.PP
The shutdown state of the connection is used to determine the state of
the ssl session. If the session is still open, when
\&\fBSSL_clear\fR\|(3) or \fBSSL_free\fR\|(3) is called,
it is considered bad and removed according to RFC2246.
The actual condition for a correctly closed session is SSL_SENT_SHUTDOWN
(according to the TLS RFC, it is acceptable to only send the close_notify
alert but to not wait for the peer's answer, when the underlying connection
is closed).
\&\fBSSL_set_shutdown()\fR can be used to set this state without sending a
close alert to the peer (see \fBSSL_shutdown\fR\|(3)).
.PP
If a close_notify was received, SSL_RECEIVED_SHUTDOWN will be set,
for setting SSL_SENT_SHUTDOWN the application must however still call
\&\fBSSL_shutdown\fR\|(3) or \fBSSL_set_shutdown()\fR itself.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_set_shutdown()\fR does not return diagnostic information.
.PP
\&\fBSSL_get_shutdown()\fR returns the current setting.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_shutdown\fR\|(3),
\&\fBSSL_CTX_set_quiet_shutdown\fR\|(3),
\&\fBSSL_clear\fR\|(3), \fBSSL_free\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
