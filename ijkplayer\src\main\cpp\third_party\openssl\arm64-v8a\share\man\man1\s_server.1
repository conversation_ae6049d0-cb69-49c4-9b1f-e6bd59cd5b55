.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "S_SERVER 1"
.TH S_SERVER 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-s_server,
s_server \- SSL/TLS server program
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBs_server\fR
[\fB\-help\fR]
[\fB\-port +int\fR]
[\fB\-accept val\fR]
[\fB\-unix val\fR]
[\fB\-4\fR]
[\fB\-6\fR]
[\fB\-unlink\fR]
[\fB\-context val\fR]
[\fB\-verify int\fR]
[\fB\-Verify int\fR]
[\fB\-cert infile\fR]
[\fB\-nameopt val\fR]
[\fB\-naccept +int\fR]
[\fB\-serverinfo val\fR]
[\fB\-certform PEM|DER\fR]
[\fB\-key infile\fR]
[\fB\-keyform format\fR]
[\fB\-pass val\fR]
[\fB\-dcert infile\fR]
[\fB\-dcertform PEM|DER\fR]
[\fB\-dkey infile\fR]
[\fB\-dkeyform PEM|DER\fR]
[\fB\-dpass val\fR]
[\fB\-nbio_test\fR]
[\fB\-crlf\fR]
[\fB\-debug\fR]
[\fB\-msg\fR]
[\fB\-msgfile outfile\fR]
[\fB\-state\fR]
[\fB\-CAfile infile\fR]
[\fB\-CApath dir\fR]
[\fB\-no\-CAfile\fR]
[\fB\-no\-CApath\fR]
[\fB\-nocert\fR]
[\fB\-quiet\fR]
[\fB\-no_resume_ephemeral\fR]
[\fB\-www\fR]
[\fB\-WWW\fR]
[\fB\-servername\fR]
[\fB\-servername_fatal\fR]
[\fB\-cert2 infile\fR]
[\fB\-key2 infile\fR]
[\fB\-tlsextdebug\fR]
[\fB\-HTTP\fR]
[\fB\-id_prefix val\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-keymatexport val\fR]
[\fB\-keymatexportlen +int\fR]
[\fB\-CRL infile\fR]
[\fB\-crl_download\fR]
[\fB\-cert_chain infile\fR]
[\fB\-dcert_chain infile\fR]
[\fB\-chainCApath dir\fR]
[\fB\-verifyCApath dir\fR]
[\fB\-no_cache\fR]
[\fB\-ext_cache\fR]
[\fB\-CRLform PEM|DER\fR]
[\fB\-verify_return_error\fR]
[\fB\-verify_quiet\fR]
[\fB\-build_chain\fR]
[\fB\-chainCAfile infile\fR]
[\fB\-verifyCAfile infile\fR]
[\fB\-ign_eof\fR]
[\fB\-no_ign_eof\fR]
[\fB\-status\fR]
[\fB\-status_verbose\fR]
[\fB\-status_timeout int\fR]
[\fB\-status_url val\fR]
[\fB\-status_file infile\fR]
[\fB\-trace\fR]
[\fB\-security_debug\fR]
[\fB\-security_debug_verbose\fR]
[\fB\-brief\fR]
[\fB\-rev\fR]
[\fB\-async\fR]
[\fB\-ssl_config val\fR]
[\fB\-max_send_frag +int\fR]
[\fB\-split_send_frag +int\fR]
[\fB\-max_pipelines +int\fR]
[\fB\-read_buf +int\fR]
[\fB\-no_ssl3\fR]
[\fB\-no_tls1\fR]
[\fB\-no_tls1_1\fR]
[\fB\-no_tls1_2\fR]
[\fB\-no_tls1_3\fR]
[\fB\-bugs\fR]
[\fB\-no_comp\fR]
[\fB\-comp\fR]
[\fB\-no_ticket\fR]
[\fB\-num_tickets\fR]
[\fB\-serverpref\fR]
[\fB\-legacy_renegotiation\fR]
[\fB\-no_renegotiation\fR]
[\fB\-legacy_server_connect\fR]
[\fB\-no_resumption_on_reneg\fR]
[\fB\-no_legacy_server_connect\fR]
[\fB\-allow_no_dhe_kex\fR]
[\fB\-prioritize_chacha\fR]
[\fB\-strict\fR]
[\fB\-sigalgs val\fR]
[\fB\-client_sigalgs val\fR]
[\fB\-groups val\fR]
[\fB\-curves val\fR]
[\fB\-named_curve val\fR]
[\fB\-cipher val\fR]
[\fB\-ciphersuites val\fR]
[\fB\-dhparam infile\fR]
[\fB\-record_padding val\fR]
[\fB\-debug_broken_protocol\fR]
[\fB\-policy val\fR]
[\fB\-purpose val\fR]
[\fB\-verify_name val\fR]
[\fB\-verify_depth int\fR]
[\fB\-auth_level int\fR]
[\fB\-attime intmax\fR]
[\fB\-verify_hostname val\fR]
[\fB\-verify_email val\fR]
[\fB\-verify_ip\fR]
[\fB\-ignore_critical\fR]
[\fB\-issuer_checks\fR]
[\fB\-crl_check\fR]
[\fB\-crl_check_all\fR]
[\fB\-policy_check\fR]
[\fB\-explicit_policy\fR]
[\fB\-inhibit_any\fR]
[\fB\-inhibit_map\fR]
[\fB\-x509_strict\fR]
[\fB\-extended_crl\fR]
[\fB\-use_deltas\fR]
[\fB\-policy_print\fR]
[\fB\-check_ss_sig\fR]
[\fB\-trusted_first\fR]
[\fB\-suiteB_128_only\fR]
[\fB\-suiteB_128\fR]
[\fB\-suiteB_192\fR]
[\fB\-partial_chain\fR]
[\fB\-no_alt_chains\fR]
[\fB\-no_check_time\fR]
[\fB\-allow_proxy_certs\fR]
[\fB\-xkey\fR]
[\fB\-xcert\fR]
[\fB\-xchain\fR]
[\fB\-xchain_build\fR]
[\fB\-xcertform PEM|DER\fR]
[\fB\-xkeyform PEM|DER\fR]
[\fB\-nbio\fR]
[\fB\-psk_identity val\fR]
[\fB\-psk_hint val\fR]
[\fB\-psk val\fR]
[\fB\-psk_session file\fR]
[\fB\-srpvfile infile\fR]
[\fB\-srpuserseed val\fR]
[\fB\-ssl3\fR]
[\fB\-tls1\fR]
[\fB\-tls1_1\fR]
[\fB\-tls1_2\fR]
[\fB\-tls1_3\fR]
[\fB\-dtls\fR]
[\fB\-timeout\fR]
[\fB\-mtu +int\fR]
[\fB\-listen\fR]
[\fB\-dtls1\fR]
[\fB\-dtls1_2\fR]
[\fB\-sctp\fR]
[\fB\-sctp_label_bug\fR]
[\fB\-no_dhe\fR]
[\fB\-nextprotoneg val\fR]
[\fB\-use_srtp val\fR]
[\fB\-alpn val\fR]
[\fB\-engine val\fR]
[\fB\-keylogfile outfile\fR]
[\fB\-max_early_data int\fR]
[\fB\-early_data\fR]
[\fB\-anti_replay\fR]
[\fB\-no_anti_replay\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBs_server\fR command implements a generic SSL/TLS server which listens
for connections on a given port using SSL/TLS.
.SH OPTIONS
.IX Header "OPTIONS"
In addition to the options below the \fBs_server\fR utility also supports the
common and server only options documented
in the "Supported Command Line Commands" section of the \fBSSL_CONF_cmd\fR\|(3)
manual page.
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-port +int\fR" 4
.IX Item "-port +int"
The TCP port to listen on for connections. If not specified 4433 is used.
.IP "\fB\-accept val\fR" 4
.IX Item "-accept val"
The optional TCP host and port to listen on for connections. If not specified, *:4433 is used.
.IP "\fB\-unix val\fR" 4
.IX Item "-unix val"
Unix domain socket to accept on.
.IP \fB\-4\fR 4
.IX Item "-4"
Use IPv4 only.
.IP \fB\-6\fR 4
.IX Item "-6"
Use IPv6 only.
.IP \fB\-unlink\fR 4
.IX Item "-unlink"
For \-unix, unlink any existing socket first.
.IP "\fB\-context val\fR" 4
.IX Item "-context val"
Sets the SSL context id. It can be given any string value. If this option
is not present a default value will be used.
.IP "\fB\-verify int\fR, \fB\-Verify int\fR" 4
.IX Item "-verify int, -Verify int"
The verify depth to use. This specifies the maximum length of the
client certificate chain and makes the server request a certificate from
the client. With the \fB\-verify\fR option a certificate is requested but the
client does not have to send one, with the \fB\-Verify\fR option the client
must supply a certificate or an error occurs.
.Sp
If the cipher suite cannot request a client certificate (for example an
anonymous cipher suite or PSK) this option has no effect.
.IP "\fB\-cert infile\fR" 4
.IX Item "-cert infile"
The certificate to use, most servers cipher suites require the use of a
certificate and some require a certificate with a certain public key type:
for example the DSS cipher suites require a certificate containing a DSS
(DSA) key. If not specified then the filename "server.pem" will be used.
.IP \fB\-cert_chain\fR 4
.IX Item "-cert_chain"
A file containing trusted certificates to use when attempting to build the
client/server certificate chain related to the certificate specified via the
\&\fB\-cert\fR option.
.IP \fB\-build_chain\fR 4
.IX Item "-build_chain"
Specify whether the application should build the certificate chain to be
provided to the client.
.IP "\fB\-nameopt val\fR" 4
.IX Item "-nameopt val"
Option which determines how the subject or issuer names are displayed. The
\&\fBval\fR argument can be a single option or multiple options separated by
commas.  Alternatively the \fB\-nameopt\fR switch may be used more than once to
set multiple options. See the \fBx509\fR\|(1) manual page for details.
.IP "\fB\-naccept +int\fR" 4
.IX Item "-naccept +int"
The server will exit after receiving the specified number of connections,
default unlimited.
.IP "\fB\-serverinfo val\fR" 4
.IX Item "-serverinfo val"
A file containing one or more blocks of PEM data.  Each PEM block
must encode a TLS ServerHello extension (2 bytes type, 2 bytes length,
followed by "length" bytes of extension data).  If the client sends
an empty TLS ClientHello extension matching the type, the corresponding
ServerHello extension will be returned.
.IP "\fB\-certform PEM|DER\fR" 4
.IX Item "-certform PEM|DER"
The certificate format to use: DER or PEM. PEM is the default.
.IP "\fB\-key infile\fR" 4
.IX Item "-key infile"
The private key to use. If not specified then the certificate file will
be used.
.IP "\fB\-keyform format\fR" 4
.IX Item "-keyform format"
The private format to use: DER or PEM. PEM is the default.
.IP "\fB\-pass val\fR" 4
.IX Item "-pass val"
The private key password source. For more information about the format of \fBval\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-dcert infile\fR, \fB\-dkey infile\fR" 4
.IX Item "-dcert infile, -dkey infile"
Specify an additional certificate and private key, these behave in the
same manner as the \fB\-cert\fR and \fB\-key\fR options except there is no default
if they are not specified (no additional certificate and key is used). As
noted above some cipher suites require a certificate containing a key of
a certain type. Some cipher suites need a certificate carrying an RSA key
and some a DSS (DSA) key. By using RSA and DSS certificates and keys
a server can support clients which only support RSA or DSS cipher suites
by using an appropriate certificate.
.IP \fB\-dcert_chain\fR 4
.IX Item "-dcert_chain"
A file containing trusted certificates to use when attempting to build the
server certificate chain when a certificate specified via the \fB\-dcert\fR option
is in use.
.IP "\fB\-dcertform PEM|DER\fR, \fB\-dkeyform PEM|DER\fR, \fB\-dpass val\fR" 4
.IX Item "-dcertform PEM|DER, -dkeyform PEM|DER, -dpass val"
Additional certificate and private key format and passphrase respectively.
.IP "\fB\-xkey infile\fR, \fB\-xcert infile\fR, \fB\-xchain\fR" 4
.IX Item "-xkey infile, -xcert infile, -xchain"
Specify an extra certificate, private key and certificate chain. These behave
in the same manner as the \fB\-cert\fR, \fB\-key\fR and \fB\-cert_chain\fR options.  When
specified, the callback returning the first valid chain will be in use by
the server.
.IP \fB\-xchain_build\fR 4
.IX Item "-xchain_build"
Specify whether the application should build the certificate chain to be
provided to the client for the extra certificates provided via \fB\-xkey infile\fR,
\&\fB\-xcert infile\fR, \fB\-xchain\fR options.
.IP "\fB\-xcertform PEM|DER\fR, \fB\-xkeyform PEM|DER\fR" 4
.IX Item "-xcertform PEM|DER, -xkeyform PEM|DER"
Extra certificate and private key format respectively.
.IP \fB\-nbio_test\fR 4
.IX Item "-nbio_test"
Tests non blocking I/O.
.IP \fB\-crlf\fR 4
.IX Item "-crlf"
This option translated a line feed from the terminal into CR+LF.
.IP \fB\-debug\fR 4
.IX Item "-debug"
Print extensive debugging information including a hex dump of all traffic.
.IP \fB\-msg\fR 4
.IX Item "-msg"
Show all protocol messages with hex dump.
.IP "\fB\-msgfile outfile\fR" 4
.IX Item "-msgfile outfile"
File to send output of \fB\-msg\fR or \fB\-trace\fR to, default standard output.
.IP \fB\-state\fR 4
.IX Item "-state"
Prints the SSL session states.
.IP "\fB\-CAfile infile\fR" 4
.IX Item "-CAfile infile"
A file containing trusted certificates to use during client authentication
and to use when attempting to build the server certificate chain. The list
is also used in the list of acceptable client CAs passed to the client when
a certificate is requested.
.IP "\fB\-CApath dir\fR" 4
.IX Item "-CApath dir"
The directory to use for client certificate verification. This directory
must be in "hash format", see \fBverify\fR\|(1) for more information. These are
also used when building the server certificate chain.
.IP "\fB\-chainCApath dir\fR" 4
.IX Item "-chainCApath dir"
The directory to use for building the chain provided to the client. This
directory must be in "hash format", see \fBverify\fR\|(1) for more information.
.IP "\fB\-chainCAfile file\fR" 4
.IX Item "-chainCAfile file"
A file containing trusted certificates to use when attempting to build the
server certificate chain.
.IP \fB\-no\-CAfile\fR 4
.IX Item "-no-CAfile"
Do not load the trusted CA certificates from the default file location.
.IP \fB\-no\-CApath\fR 4
.IX Item "-no-CApath"
Do not load the trusted CA certificates from the default directory location.
.IP \fB\-nocert\fR 4
.IX Item "-nocert"
If this option is set then no certificate is used. This restricts the
cipher suites available to the anonymous ones (currently just anonymous
DH).
.IP \fB\-quiet\fR 4
.IX Item "-quiet"
Inhibit printing of session and certificate information.
.IP \fB\-www\fR 4
.IX Item "-www"
Sends a status message back to the client when it connects. This includes
information about the ciphers used and various session parameters.
The output is in HTML format so this option will normally be used with a
web browser. Cannot be used in conjunction with \fB\-early_data\fR.
.IP \fB\-WWW\fR 4
.IX Item "-WWW"
Emulates a simple web server. Pages will be resolved relative to the
current directory, for example if the URL https://myhost/page.html is
requested the file ./page.html will be loaded. Cannot be used in conjunction
with \fB\-early_data\fR.
.IP \fB\-tlsextdebug\fR 4
.IX Item "-tlsextdebug"
Print a hex dump of any TLS extensions received from the server.
.IP \fB\-HTTP\fR 4
.IX Item "-HTTP"
Emulates a simple web server. Pages will be resolved relative to the
current directory, for example if the URL https://myhost/page.html is
requested the file ./page.html will be loaded. The files loaded are
assumed to contain a complete and correct HTTP response (lines that
are part of the HTTP response line and headers must end with CRLF). Cannot be
used in conjunction with \fB\-early_data\fR.
.IP "\fB\-id_prefix val\fR" 4
.IX Item "-id_prefix val"
Generate SSL/TLS session IDs prefixed by \fBval\fR. This is mostly useful
for testing any SSL/TLS code (e.g. proxies) that wish to deal with multiple
servers, when each of which might be generating a unique range of session
IDs (e.g. with a certain prefix).
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP \fB\-verify_return_error\fR 4
.IX Item "-verify_return_error"
Verification errors normally just print a message but allow the
connection to continue, for debugging purposes.
If this option is used, then verification errors close the connection.
.IP \fB\-status\fR 4
.IX Item "-status"
Enables certificate status request support (aka OCSP stapling).
.IP \fB\-status_verbose\fR 4
.IX Item "-status_verbose"
Enables certificate status request support (aka OCSP stapling) and gives
a verbose printout of the OCSP response.
.IP "\fB\-status_timeout int\fR" 4
.IX Item "-status_timeout int"
Sets the timeout for OCSP response to \fBint\fR seconds.
.IP "\fB\-status_url val\fR" 4
.IX Item "-status_url val"
Sets a fallback responder URL to use if no responder URL is present in the
server certificate. Without this option an error is returned if the server
certificate does not contain a responder address.
.IP "\fB\-status_file infile\fR" 4
.IX Item "-status_file infile"
Overrides any OCSP responder URLs from the certificate and always provides the
OCSP Response stored in the file. The file must be in DER format.
.IP \fB\-trace\fR 4
.IX Item "-trace"
Show verbose trace output of protocol messages. OpenSSL needs to be compiled
with \fBenable-ssl-trace\fR for this option to work.
.IP \fB\-brief\fR 4
.IX Item "-brief"
Provide a brief summary of connection parameters instead of the normal verbose
output.
.IP \fB\-rev\fR 4
.IX Item "-rev"
Simple test server which just reverses the text received from the client
and sends it back to the server. Also sets \fB\-brief\fR. Cannot be used in
conjunction with \fB\-early_data\fR.
.IP \fB\-async\fR 4
.IX Item "-async"
Switch on asynchronous mode. Cryptographic operations will be performed
asynchronously. This will only have an effect if an asynchronous capable engine
is also used via the \fB\-engine\fR option. For test purposes the dummy async engine
(dasync) can be used (if available).
.IP "\fB\-max_send_frag +int\fR" 4
.IX Item "-max_send_frag +int"
The maximum size of data fragment to send.
See \fBSSL_CTX_set_max_send_fragment\fR\|(3) for further information.
.IP "\fB\-split_send_frag +int\fR" 4
.IX Item "-split_send_frag +int"
The size used to split data for encrypt pipelines. If more data is written in
one go than this value then it will be split into multiple pipelines, up to the
maximum number of pipelines defined by max_pipelines. This only has an effect if
a suitable cipher suite has been negotiated, an engine that supports pipelining
has been loaded, and max_pipelines is greater than 1. See
\&\fBSSL_CTX_set_split_send_fragment\fR\|(3) for further information.
.IP "\fB\-max_pipelines +int\fR" 4
.IX Item "-max_pipelines +int"
The maximum number of encrypt/decrypt pipelines to be used. This will only have
an effect if an engine has been loaded that supports pipelining (e.g. the dasync
engine) and a suitable cipher suite has been negotiated. The default value is 1.
See \fBSSL_CTX_set_max_pipelines\fR\|(3) for further information.
.IP "\fB\-read_buf +int\fR" 4
.IX Item "-read_buf +int"
The default read buffer size to be used for connections. This will only have an
effect if the buffer size is larger than the size that would otherwise be used
and pipelining is in use (see \fBSSL_CTX_set_default_read_buffer_len\fR\|(3) for
further information).
.IP "\fB\-ssl2\fR, \fB\-ssl3\fR, \fB\-tls1\fR, \fB\-tls1_1\fR, \fB\-tls1_2\fR, \fB\-tls1_3\fR, \fB\-no_ssl2\fR, \fB\-no_ssl3\fR, \fB\-no_tls1\fR, \fB\-no_tls1_1\fR, \fB\-no_tls1_2\fR, \fB\-no_tls1_3\fR" 4
.IX Item "-ssl2, -ssl3, -tls1, -tls1_1, -tls1_2, -tls1_3, -no_ssl2, -no_ssl3, -no_tls1, -no_tls1_1, -no_tls1_2, -no_tls1_3"
These options require or disable the use of the specified SSL or TLS protocols.
By default \fBs_server\fR will negotiate the highest mutually supported protocol
version.
When a specific TLS version is required, only that version will be accepted
from the client.
Note that not all protocols and flags may be available, depending on how
OpenSSL was built.
.IP \fB\-bugs\fR 4
.IX Item "-bugs"
There are several known bugs in SSL and TLS implementations. Adding this
option enables various workarounds.
.IP \fB\-no_comp\fR 4
.IX Item "-no_comp"
Disable negotiation of TLS compression.
TLS compression is not recommended and is off by default as of
OpenSSL 1.1.0.
.IP \fB\-comp\fR 4
.IX Item "-comp"
Enable negotiation of TLS compression.
This option was introduced in OpenSSL 1.1.0.
TLS compression is not recommended and is off by default as of
OpenSSL 1.1.0.
.IP \fB\-no_ticket\fR 4
.IX Item "-no_ticket"
Disable RFC4507bis session ticket support. This option has no effect if TLSv1.3
is negotiated. See \fB\-num_tickets\fR.
.IP \fB\-num_tickets\fR 4
.IX Item "-num_tickets"
Control the number of tickets that will be sent to the client after a full
handshake in TLSv1.3. The default number of tickets is 2. This option does not
affect the number of tickets sent after a resumption handshake.
.IP \fB\-serverpref\fR 4
.IX Item "-serverpref"
Use the server's cipher preferences, rather than the client's preferences.
.IP \fB\-prioritize_chacha\fR 4
.IX Item "-prioritize_chacha"
Prioritize ChaCha ciphers when preferred by clients. Requires \fB\-serverpref\fR.
.IP \fB\-no_resumption_on_reneg\fR 4
.IX Item "-no_resumption_on_reneg"
Set the \fBSSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION\fR option.
.IP "\fB\-client_sigalgs val\fR" 4
.IX Item "-client_sigalgs val"
Signature algorithms to support for client certificate authentication
(colon-separated list).
.IP "\fB\-named_curve val\fR" 4
.IX Item "-named_curve val"
Specifies the elliptic curve to use. NOTE: this is single curve, not a list.
For a list of all possible curves, use:
.Sp
.Vb 1
\&    $ openssl ecparam \-list_curves
.Ve
.IP "\fB\-cipher val\fR" 4
.IX Item "-cipher val"
This allows the list of TLSv1.2 and below ciphersuites used by the server to be
modified. This list is combined with any TLSv1.3 ciphersuites that have been
configured. When the client sends a list of supported ciphers the first client
cipher also included in the server list is used. Because the client specifies
the preference order, the order of the server cipherlist is irrelevant. See
the \fBciphers\fR command for more information.
.IP "\fB\-ciphersuites val\fR" 4
.IX Item "-ciphersuites val"
This allows the list of TLSv1.3 ciphersuites used by the server to be modified.
This list is combined with any TLSv1.2 and below ciphersuites that have been
configured. When the client sends a list of supported ciphers the first client
cipher also included in the server list is used. Because the client specifies
the preference order, the order of the server cipherlist is irrelevant. See
the \fBciphers\fR command for more information. The format for this list is a
simple colon (":") separated list of TLSv1.3 ciphersuite names.
.IP "\fB\-dhparam infile\fR" 4
.IX Item "-dhparam infile"
The DH parameter file to use. The ephemeral DH cipher suites generate keys
using a set of DH parameters. If not specified then an attempt is made to
load the parameters from the server certificate file.
If this fails then a static set of parameters hard coded into the \fBs_server\fR
program will be used.
.IP "\fB\-attime\fR, \fB\-check_ss_sig\fR, \fB\-crl_check\fR, \fB\-crl_check_all\fR, \fB\-explicit_policy\fR, \fB\-extended_crl\fR, \fB\-ignore_critical\fR, \fB\-inhibit_any\fR, \fB\-inhibit_map\fR, \fB\-no_alt_chains\fR, \fB\-no_check_time\fR, \fB\-partial_chain\fR, \fB\-policy\fR, \fB\-policy_check\fR, \fB\-policy_print\fR, \fB\-purpose\fR, \fB\-suiteB_128\fR, \fB\-suiteB_128_only\fR, \fB\-suiteB_192\fR, \fB\-trusted_first\fR, \fB\-use_deltas\fR, \fB\-auth_level\fR, \fB\-verify_depth\fR, \fB\-verify_email\fR, \fB\-verify_hostname\fR, \fB\-verify_ip\fR, \fB\-verify_name\fR, \fB\-x509_strict\fR" 4
.IX Item "-attime, -check_ss_sig, -crl_check, -crl_check_all, -explicit_policy, -extended_crl, -ignore_critical, -inhibit_any, -inhibit_map, -no_alt_chains, -no_check_time, -partial_chain, -policy, -policy_check, -policy_print, -purpose, -suiteB_128, -suiteB_128_only, -suiteB_192, -trusted_first, -use_deltas, -auth_level, -verify_depth, -verify_email, -verify_hostname, -verify_ip, -verify_name, -x509_strict"
Set different peer certificate verification options.
See the \fBverify\fR\|(1) manual page for details.
.IP "\fB\-crl_check\fR, \fB\-crl_check_all\fR" 4
.IX Item "-crl_check, -crl_check_all"
Check the peer certificate has not been revoked by its CA.
The CRL(s) are appended to the certificate file. With the \fB\-crl_check_all\fR
option all CRLs of all CAs in the chain are checked.
.IP \fB\-nbio\fR 4
.IX Item "-nbio"
Turns on non blocking I/O.
.IP "\fB\-psk_identity val\fR" 4
.IX Item "-psk_identity val"
Expect the client to send PSK identity \fBval\fR when using a PSK
cipher suite, and warn if they do not.  By default, the expected PSK
identity is the string "Client_identity".
.IP "\fB\-psk_hint val\fR" 4
.IX Item "-psk_hint val"
Use the PSK identity hint \fBval\fR when using a PSK cipher suite.
.IP "\fB\-psk val\fR" 4
.IX Item "-psk val"
Use the PSK key \fBval\fR when using a PSK cipher suite. The key is
given as a hexadecimal number without leading 0x, for example \-psk
1a2b3c4d.
This option must be provided in order to use a PSK cipher.
.IP "\fB\-psk_session file\fR" 4
.IX Item "-psk_session file"
Use the pem encoded SSL_SESSION data stored in \fBfile\fR as the basis of a PSK.
Note that this will only work if TLSv1.3 is negotiated.
.IP \fB\-listen\fR 4
.IX Item "-listen"
This option can only be used in conjunction with one of the DTLS options above.
With this option \fBs_server\fR will listen on a UDP port for incoming connections.
Any ClientHellos that arrive will be checked to see if they have a cookie in
them or not.
Any without a cookie will be responded to with a HelloVerifyRequest.
If a ClientHello with a cookie is received then \fBs_server\fR will connect to
that peer and complete the handshake.
.IP "\fB\-dtls\fR, \fB\-dtls1\fR, \fB\-dtls1_2\fR" 4
.IX Item "-dtls, -dtls1, -dtls1_2"
These options make \fBs_server\fR use DTLS protocols instead of TLS.
With \fB\-dtls\fR, \fBs_server\fR will negotiate any supported DTLS protocol version,
whilst \fB\-dtls1\fR and \fB\-dtls1_2\fR will only support DTLSv1.0 and DTLSv1.2
respectively.
.IP \fB\-sctp\fR 4
.IX Item "-sctp"
Use SCTP for the transport protocol instead of UDP in DTLS. Must be used in
conjunction with \fB\-dtls\fR, \fB\-dtls1\fR or \fB\-dtls1_2\fR. This option is only
available where OpenSSL has support for SCTP enabled.
.IP \fB\-sctp_label_bug\fR 4
.IX Item "-sctp_label_bug"
Use the incorrect behaviour of older OpenSSL implementations when computing
endpoint-pair shared secrets for DTLS/SCTP. This allows communication with
older broken implementations but breaks interoperability with correct
implementations. Must be used in conjunction with \fB\-sctp\fR. This option is only
available where OpenSSL has support for SCTP enabled.
.IP \fB\-no_dhe\fR 4
.IX Item "-no_dhe"
If this option is set then no DH parameters will be loaded effectively
disabling the ephemeral DH cipher suites.
.IP "\fB\-alpn val\fR, \fB\-nextprotoneg val\fR" 4
.IX Item "-alpn val, -nextprotoneg val"
These flags enable the Application-Layer Protocol Negotiation
or Next Protocol Negotiation (NPN) extension, respectively. ALPN is the
IETF standard and replaces NPN.
The \fBval\fR list is a comma-separated list of supported protocol
names.  The list should contain the most desirable protocols first.
Protocol names are printable ASCII strings, for example "http/1.1" or
"spdy/3".
The flag \fB\-nextprotoneg\fR cannot be specified if \fB\-tls1_3\fR is used.
.IP "\fB\-engine val\fR" 4
.IX Item "-engine val"
Specifying an engine (by its unique id string in \fBval\fR) will cause \fBs_server\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.IP "\fB\-keylogfile outfile\fR" 4
.IX Item "-keylogfile outfile"
Appends TLS secrets to the specified keylog file such that external programs
(like Wireshark) can decrypt TLS connections.
.IP "\fB\-max_early_data int\fR" 4
.IX Item "-max_early_data int"
Change the default maximum early data bytes that are specified for new sessions
and any incoming early data (when used in conjunction with the \fB\-early_data\fR
flag). The default value is approximately 16k. The argument must be an integer
greater than or equal to 0.
.IP \fB\-early_data\fR 4
.IX Item "-early_data"
Accept early data where possible. Cannot be used in conjunction with \fB\-www\fR,
\&\fB\-WWW\fR, \fB\-HTTP\fR or \fB\-rev\fR.
.IP "\fB\-anti_replay\fR, \fB\-no_anti_replay\fR" 4
.IX Item "-anti_replay, -no_anti_replay"
Switches replay protection on or off, respectively. Replay protection is on by
default unless overridden by a configuration file. When it is on, OpenSSL will
automatically detect if a session ticket has been used more than once, TLSv1.3
has been negotiated, and early data is enabled on the server. A full handshake
is forced if a session ticket is used a second or subsequent time. Any early
data that was sent will be rejected.
.SH "CONNECTED COMMANDS"
.IX Header "CONNECTED COMMANDS"
If a connection request is established with an SSL client and neither the
\&\fB\-www\fR nor the \fB\-WWW\fR option has been used then normally any data received
from the client is displayed and any key presses will be sent to the client.
.PP
Certain commands are also recognized which perform special operations. These
commands are a letter which must appear at the start of a line. They are listed
below.
.IP \fBq\fR 4
.IX Item "q"
End the current SSL connection but still accept new connections.
.IP \fBQ\fR 4
.IX Item "Q"
End the current SSL connection and exit.
.IP \fBr\fR 4
.IX Item "r"
Renegotiate the SSL session (TLSv1.2 and below only).
.IP \fBR\fR 4
.IX Item "R"
Renegotiate the SSL session and request a client certificate (TLSv1.2 and below
only).
.IP \fBP\fR 4
.IX Item "P"
Send some plain text down the underlying TCP connection: this should
cause the client to disconnect due to a protocol violation.
.IP \fBS\fR 4
.IX Item "S"
Print out some session cache status information.
.IP \fBB\fR 4
.IX Item "B"
Send a heartbeat message to the client (DTLS only)
.IP \fBk\fR 4
.IX Item "k"
Send a key update message to the client (TLSv1.3 only)
.IP \fBK\fR 4
.IX Item "K"
Send a key update message to the client and request one back (TLSv1.3 only)
.IP \fBc\fR 4
.IX Item "c"
Send a certificate request to the client (TLSv1.3 only)
.SH NOTES
.IX Header "NOTES"
\&\fBs_server\fR can be used to debug SSL clients. To accept connections from
a web browser the command:
.PP
.Vb 1
\& openssl s_server \-accept 443 \-www
.Ve
.PP
can be used for example.
.PP
Although specifying an empty list of CAs when requesting a client certificate
is strictly speaking a protocol violation, some SSL clients interpret this to
mean any CA is acceptable. This is useful for debugging purposes.
.PP
The session parameters can printed out using the \fBsess_id\fR program.
.SH BUGS
.IX Header "BUGS"
Because this program has a lot of options and also because some of the
techniques used are rather old, the C source of \fBs_server\fR is rather hard to
read and not a model of how things should be done.
A typical SSL server program would be much simpler.
.PP
The output of common ciphers is wrong: it just gives the list of ciphers that
OpenSSL recognizes and the client supports.
.PP
There should be a way for the \fBs_server\fR program to print out details of any
unknown cipher suites a client says it supports.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CONF_cmd\fR\|(3), \fBsess_id\fR\|(1), \fBs_client\fR\|(1), \fBciphers\fR\|(1)
\&\fBSSL_CTX_set_max_send_fragment\fR\|(3),
\&\fBSSL_CTX_set_split_send_fragment\fR\|(3),
\&\fBSSL_CTX_set_max_pipelines\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \-no_alt_chains option was added in OpenSSL 1.1.0.
.PP
The
\&\-allow\-no\-dhe\-kex and \-prioritize_chacha options were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
