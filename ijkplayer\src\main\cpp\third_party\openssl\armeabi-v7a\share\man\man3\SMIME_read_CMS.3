.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SMIME_READ_CMS 3"
.TH SMIME_READ_CMS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SMIME_read_CMS \- parse S/MIME message
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& CMS_ContentInfo *SMIME_read_CMS(BIO *in, BIO **bcont);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSMIME_read_CMS()\fR parses a message in S/MIME format.
.PP
\&\fBin\fR is a BIO to read the message from.
.PP
If cleartext signing is used then the content is saved in a memory bio which is
written to \fB*bcont\fR, otherwise \fB*bcont\fR is set to NULL.
.PP
The parsed CMS_ContentInfo structure is returned or NULL if an
error occurred.
.SH NOTES
.IX Header "NOTES"
If \fB*bcont\fR is not NULL then the message is clear text signed. \fB*bcont\fR can
then be passed to \fBCMS_verify()\fR with the \fBCMS_DETACHED\fR flag set.
.PP
Otherwise the type of the returned structure can be determined
using \fBCMS_get0_type()\fR.
.PP
To support future functionality if \fBbcont\fR is not NULL \fB*bcont\fR should be
initialized to NULL. For example:
.PP
.Vb 2
\& BIO *cont = NULL;
\& CMS_ContentInfo *cms;
\&
\& cms = SMIME_read_CMS(in, &cont);
.Ve
.SH BUGS
.IX Header "BUGS"
The MIME parser used by \fBSMIME_read_CMS()\fR is somewhat primitive.  While it will
handle most S/MIME messages more complex compound formats may not work.
.PP
The parser assumes that the CMS_ContentInfo structure is always base64 encoded
and will not handle the case where it is in binary format or uses quoted
printable format.
.PP
The use of a memory BIO to hold the signed content limits the size of message
which can be processed due to memory restraints: a streaming single pass option
should be available.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSMIME_read_CMS()\fR returns a valid \fBCMS_ContentInfo\fR structure or \fBNULL\fR
if an error occurred. The error can be obtained from \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_type\fR\|(3),
\&\fBSMIME_read_CMS\fR\|(3), \fBCMS_sign\fR\|(3),
\&\fBCMS_verify\fR\|(3), \fBCMS_encrypt\fR\|(3),
\&\fBCMS_decrypt\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
