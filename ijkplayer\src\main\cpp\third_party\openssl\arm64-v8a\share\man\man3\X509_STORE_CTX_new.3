.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_STORE_CTX_NEW 3"
.TH X509_STORE_CTX_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_STORE_CTX_new, X509_STORE_CTX_cleanup, X509_STORE_CTX_free,
X509_STORE_CTX_init, X509_STORE_CTX_set0_trusted_stack, X509_STORE_CTX_set_cert,
X509_STORE_CTX_set0_crls,
X509_STORE_CTX_get0_chain, X509_STORE_CTX_set0_verified_chain,
X509_STORE_CTX_get0_param, X509_STORE_CTX_set0_param,
X509_STORE_CTX_get0_untrusted, X509_STORE_CTX_set0_untrusted,
X509_STORE_CTX_get_num_untrusted,
X509_STORE_CTX_set_default,
X509_STORE_CTX_set_verify,
X509_STORE_CTX_verify_fn,
X509_STORE_CTX_set_purpose,
X509_STORE_CTX_set_trust,
X509_STORE_CTX_purpose_inherit
\&\- X509_STORE_CTX initialisation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509_vfy.h>
\&
\& X509_STORE_CTX *X509_STORE_CTX_new(void);
\& void X509_STORE_CTX_cleanup(X509_STORE_CTX *ctx);
\& void X509_STORE_CTX_free(X509_STORE_CTX *ctx);
\&
\& int X509_STORE_CTX_init(X509_STORE_CTX *ctx, X509_STORE *store,
\&                         X509 *x509, STACK_OF(X509) *chain);
\&
\& void X509_STORE_CTX_set0_trusted_stack(X509_STORE_CTX *ctx, STACK_OF(X509) *sk);
\&
\& void X509_STORE_CTX_set_cert(X509_STORE_CTX *ctx, X509 *x);
\& STACK_OF(X509) *X509_STORE_CTX_get0_chain(X509_STORE_CTX *ctx);
\& void X509_STORE_CTX_set0_verified_chain(X509_STORE_CTX *ctx, STACK_OF(X509) *chain);
\& void X509_STORE_CTX_set0_crls(X509_STORE_CTX *ctx, STACK_OF(X509_CRL) *sk);
\&
\& X509_VERIFY_PARAM *X509_STORE_CTX_get0_param(X509_STORE_CTX *ctx);
\& void X509_STORE_CTX_set0_param(X509_STORE_CTX *ctx, X509_VERIFY_PARAM *param);
\& int X509_STORE_CTX_set_default(X509_STORE_CTX *ctx, const char *name);
\&
\& STACK_OF(X509)* X509_STORE_CTX_get0_untrusted(X509_STORE_CTX *ctx);
\& void X509_STORE_CTX_set0_untrusted(X509_STORE_CTX *ctx, STACK_OF(X509) *sk);
\&
\& int X509_STORE_CTX_get_num_untrusted(X509_STORE_CTX *ctx);
\&
\& typedef int (*X509_STORE_CTX_verify_fn)(X509_STORE_CTX *);
\& void X509_STORE_CTX_set_verify(X509_STORE_CTX *ctx, X509_STORE_CTX_verify_fn verify);
\&
\& int X509_STORE_CTX_set_purpose(X509_STORE_CTX *ctx, int purpose);
\& int X509_STORE_CTX_set_trust(X509_STORE_CTX *ctx, int trust);
\& int X509_STORE_CTX_purpose_inherit(X509_STORE_CTX *ctx, int def_purpose,
\&                                    int purpose, int trust);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions initialise an \fBX509_STORE_CTX\fR structure for subsequent use
by \fBX509_verify_cert()\fR.
.PP
\&\fBX509_STORE_CTX_new()\fR returns a newly initialised \fBX509_STORE_CTX\fR structure.
.PP
\&\fBX509_STORE_CTX_cleanup()\fR internally cleans up an \fBX509_STORE_CTX\fR structure.
The context can then be reused with a new call to \fBX509_STORE_CTX_init()\fR.
.PP
\&\fBX509_STORE_CTX_free()\fR completely frees up \fBctx\fR. After this call \fBctx\fR
is no longer valid.
If \fBctx\fR is NULL nothing is done.
.PP
\&\fBX509_STORE_CTX_init()\fR sets up \fBctx\fR for a subsequent verification operation.
It must be called before each call to \fBX509_verify_cert()\fR, i.e. a \fBctx\fR is only
good for one call to \fBX509_verify_cert()\fR; if you want to verify a second
certificate with the same \fBctx\fR then you must call \fBX509_STORE_CTX_cleanup()\fR
and then \fBX509_STORE_CTX_init()\fR again before the second call to
\&\fBX509_verify_cert()\fR. The trusted certificate store is set to \fBstore\fR, the end
entity certificate to be verified is set to \fBx509\fR and a set of additional
certificates (which will be untrusted but may be used to build the chain) in
\&\fBchain\fR. Any or all of the \fBstore\fR, \fBx509\fR and \fBchain\fR parameters can be
\&\fBNULL\fR.
.PP
\&\fBX509_STORE_CTX_set0_trusted_stack()\fR sets the set of trusted certificates of
\&\fBctx\fR to \fBsk\fR. This is an alternative way of specifying trusted certificates
instead of using an \fBX509_STORE\fR.
.PP
\&\fBX509_STORE_CTX_set_cert()\fR sets the certificate to be verified in \fBctx\fR to
\&\fBx\fR.
.PP
\&\fBX509_STORE_CTX_set0_verified_chain()\fR sets the validated chain used
by \fBctx\fR to be \fBchain\fR.
Ownership of the chain is transferred to \fBctx\fR and should not be
free'd by the caller.
\&\fBX509_STORE_CTX_get0_chain()\fR returns the internal pointer used by the
\&\fBctx\fR that contains the validated chain.
.PP
\&\fBX509_STORE_CTX_set0_crls()\fR sets a set of CRLs to use to aid certificate
verification to \fBsk\fR. These CRLs will only be used if CRL verification is
enabled in the associated \fBX509_VERIFY_PARAM\fR structure. This might be
used where additional "useful" CRLs are supplied as part of a protocol,
for example in a PKCS#7 structure.
.PP
\&\fBX509_STORE_CTX_get0_param()\fR retrieves an internal pointer
to the verification parameters associated with \fBctx\fR.
.PP
\&\fBX509_STORE_CTX_get0_untrusted()\fR retrieves an internal pointer to the
stack of untrusted certificates associated with \fBctx\fR.
.PP
\&\fBX509_STORE_CTX_set0_untrusted()\fR sets the internal point to the stack
of untrusted certificates associated with \fBctx\fR to \fBsk\fR.
.PP
\&\fBX509_STORE_CTX_set0_param()\fR sets the internal verification parameter pointer
to \fBparam\fR. After this call \fBparam\fR should not be used.
.PP
\&\fBX509_STORE_CTX_set_default()\fR looks up and sets the default verification
method to \fBname\fR. This uses the function \fBX509_VERIFY_PARAM_lookup()\fR to
find an appropriate set of parameters from \fBname\fR.
.PP
\&\fBX509_STORE_CTX_get_num_untrusted()\fR returns the number of untrusted certificates
that were used in building the chain following a call to \fBX509_verify_cert()\fR.
.PP
\&\fBX509_STORE_CTX_set_verify()\fR provides the capability for overriding the default
verify function. This function is responsible for verifying chain signatures and
expiration times.
.PP
A verify function is defined as an X509_STORE_CTX_verify type which has the
following signature:
.PP
.Vb 1
\& int (*verify)(X509_STORE_CTX *);
.Ve
.PP
This function should receive the current X509_STORE_CTX as a parameter and
return 1 on success or 0 on failure.
.PP
X509 certificates may contain information about what purposes keys contained
within them can be used for. For example "TLS WWW Server Authentication" or
"Email Protection". This "key usage" information is held internally to the
certificate itself. In addition the trust store containing trusted certificates
can declare what purposes we trust different certificates for. This "trust"
information is not held within the certificate itself but is "meta" information
held alongside it. This "meta" information is associated with the certificate
after it is issued and could be determined by a system administrator. For
example a certificate might declare that it is suitable for use for both
"TLS WWW Server Authentication" and "TLS Client Authentication", but a system
administrator might only trust it for the former. An X.509 certificate extension
exists that can record extended key usage information to supplement the purpose
information described above. This extended mechanism is arbitrarily extensible
and not well suited for a generic library API; applications that need to
validate extended key usage information in certifiates will need to define a
custom "purpose" (see below) or supply a nondefault verification callback
(\fBX509_STORE_set_verify_cb_func\fR\|(3)).
.PP
\&\fBX509_STORE_CTX_set_purpose()\fR sets the purpose for the target certificate being
verified in the \fIctx\fR. Built-in available values for the \fIpurpose\fR argument
are \fBX509_PURPOSE_SSL_CLIENT\fR, \fBX509_PURPOSE_SSL_SERVER\fR,
\&\fBX509_PURPOSE_NS_SSL_SERVER\fR, \fBX509_PURPOSE_SMIME_SIGN\fR,
\&\fBX509_PURPOSE_SMIME_ENCRYPT\fR, \fBX509_PURPOSE_CRL_SIGN\fR, \fBX509_PURPOSE_ANY\fR,
\&\fBX509_PURPOSE_OCSP_HELPER\fR and \fBX509_PURPOSE_TIMESTAMP_SIGN\fR. It is also
possible to create a custom purpose value. Setting a purpose will ensure that
the key usage declared within certificates in the chain being verified is
consistent with that purpose as well as, potentially, other checks. Every
purpose also has an associated default trust value which will also be set at the
same time. During verification this trust setting will be verified to check it
is consistent with the trust set by the system administrator for certificates in
the chain.
.PP
\&\fBX509_STORE_CTX_set_trust()\fR sets the trust value for the target certificate
being verified in the \fIctx\fR. Built-in available values for the \fItrust\fR
argument are \fBX509_TRUST_COMPAT\fR, \fBX509_TRUST_SSL_CLIENT\fR,
\&\fBX509_TRUST_SSL_SERVER\fR, \fBX509_TRUST_EMAIL\fR, \fBX509_TRUST_OBJECT_SIGN\fR,
\&\fBX509_TRUST_OCSP_SIGN\fR, \fBX509_TRUST_OCSP_REQUEST\fR and \fBX509_TRUST_TSA\fR. It is
also possible to create a custom trust value. Since \fBX509_STORE_CTX_set_purpose()\fR
also sets the trust value it is normally sufficient to only call that function.
If both are called then \fBX509_STORE_CTX_set_trust()\fR should be called after
\&\fBX509_STORE_CTX_set_purpose()\fR since the trust setting of the last call will be
used.
.PP
It should not normally be necessary for end user applications to call
\&\fBX509_STORE_CTX_purpose_inherit()\fR directly. Typically applications should call
\&\fBX509_STORE_CTX_set_purpose()\fR or \fBX509_STORE_CTX_set_trust()\fR instead. Using this
function it is possible to set the purpose and trust values for the \fIctx\fR at
the same time.
Both \fIctx\fR and its internal verification parameter pointer must not be NULL.
The \fIdef_purpose\fR and \fIpurpose\fR arguments can have the same
purpose values as described for \fBX509_STORE_CTX_set_purpose()\fR above. The \fItrust\fR
argument can have the same trust values as described in
\&\fBX509_STORE_CTX_set_trust()\fR above. Any of the \fIdef_purpose\fR, \fIpurpose\fR or
\&\fItrust\fR values may also have the value 0 to indicate that the supplied
parameter should be ignored. After calling this function the purpose to be used
for verification is set from the \fIpurpose\fR argument unless the purpose was
already set in \fIctx\fR before, and the trust is set from the \fItrust\fR argument
unless the trust was already set in \fIctx\fR before.
If \fItrust\fR is 0 then the trust value will be set from
the default trust value for \fIpurpose\fR. If the default trust value for the
purpose is \fIX509_TRUST_DEFAULT\fR and \fItrust\fR is 0 then the default trust value
associated with the \fIdef_purpose\fR value is used for the trust setting instead.
.SH NOTES
.IX Header "NOTES"
The certificates and CRLs in a store are used internally and should \fBnot\fR
be freed up until after the associated \fBX509_STORE_CTX\fR is freed.
.SH BUGS
.IX Header "BUGS"
The certificates and CRLs in a context are used internally and should \fBnot\fR
be freed up until after the associated \fBX509_STORE_CTX\fR is freed. Copies
should be made or reference counts increased instead.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_STORE_CTX_new()\fR returns a newly allocated context or \fBNULL\fR if an
error occurred.
.PP
\&\fBX509_STORE_CTX_init()\fR returns 1 for success or 0 if an error occurred.
.PP
\&\fBX509_STORE_CTX_get0_param()\fR returns a pointer to an \fBX509_VERIFY_PARAM\fR
structure or \fBNULL\fR if an error occurred.
.PP
\&\fBX509_STORE_CTX_cleanup()\fR, \fBX509_STORE_CTX_free()\fR,
\&\fBX509_STORE_CTX_set0_trusted_stack()\fR,
\&\fBX509_STORE_CTX_set_cert()\fR,
\&\fBX509_STORE_CTX_set0_crls()\fR and \fBX509_STORE_CTX_set0_param()\fR do not return
values.
.PP
\&\fBX509_STORE_CTX_set_default()\fR returns 1 for success or 0 if an error occurred.
.PP
\&\fBX509_STORE_CTX_get_num_untrusted()\fR returns the number of untrusted certificates
used.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_verify_cert\fR\|(3)
\&\fBX509_VERIFY_PARAM_set_flags\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBX509_STORE_CTX_set0_crls()\fR function was added in OpenSSL 1.0.0.
The \fBX509_STORE_CTX_get_num_untrusted()\fR function was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2009\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
