import worker from '@ohos.worker';
import Logger from '../model/Logger';
import image from '@ohos.multimedia.image';
const TAG: string = '[ImageProcessingWorker]';
const workerPort = worker.workerPort;
// 添加处理控制变量
let FpsCount=0
// 接收主线程消息
workerPort.onmessage =async (event) => {
  const message = event.data;
  // let usecount = message.count
  // usecount++
  FpsCount++
  console.log('FpsCount',FpsCount)
  // AppStorage.setOrCreate('FpsCount', FpsCount);
  let A=message.type
  console.log('AAA',A)
  console.log('CUR_IMAGE_BUFFER',message)
  if (message.type === 'processImage') {
    try {
      // 处理图像数据
     // let nv21Buffer= convertYUV420PToNV21(message.data);
      let nv21Buffer=message.data
      if(nv21Buffer){
        console.log('Worker', 'nv21Buffer创建成功，准备传回主线程');
        // readFn(message.data)
        // 处理完成后通知主线程
        workerPort.postMessage({
          type: 'processingComplete',
          success: true,
          nv21Buffer:nv21Buffer,
          FpsCount
        });
      }else{
        console.log('Worker', 'nv21Buffer创建失败');
        workerPort.postMessage({
          type: 'processingComplete',
          success: false,
          error: 'Failed to create nv21Buffer',
          FpsCount
        });
      }

    } catch (error) {
      Logger.error(TAG, `Error processing image: ${JSON.stringify(error)}`);
      workerPort.postMessage({
        type: 'processingComplete',
        success: false,
        error: error
      });
    }
  }
};

// 图像处理函数
function processImageData(buffer: ArrayBuffer): ArrayBuffer|undefined {
  // 在这里实现您的图像处理逻辑
  // 例如，分析亮度、检测特征等
  console.log('触发多线程worker')
  // 创建一个 Uint8Array 视图来访问 buffer 的内容
  // const uint8Array = new Uint8Array(buffer);
  Logger.info(TAG, '获取到预览流buffer');
  Logger.info(TAG, 'kkkkkk');
  Logger.info(TAG, `component.byteBuffer:${buffer}`);
  // 检查 buffer 是否为 ArrayBuffer 类型
  // let buffer = component.byteBuffer;
  Logger.info(TAG, `Buffer type: ${Object.prototype.toString.call(buffer)}`);

  // 打印 buffer 的长度
  Logger.info(TAG, `Buffer length: ${buffer.byteLength}`);

  // 创建一个 Uint8Array 视图来访问 buffer 的内容
  const uint8Array = new Uint8Array(buffer);

  // 打印前10个字节的内容（如果有的话）
  const previewBytes = uint8Array.slice(0, Math.min(10, uint8Array.length));
  Logger.info(TAG, `Buffer preview: [${Array.from(previewBytes).join(', ')}]`);

  Logger.info(TAG, '获取到预览流buffer');
  // 可以在这里添加更复杂的图像处理逻辑
  let processBuffer:ArrayBuffer= convertYUV420PToNV21(buffer)
  if(processBuffer){
    return processBuffer
  }else{
    return undefined
  }
}
//测试函数
function readFn(buffer: string): void {
  // 在这里实现您的图像处理逻辑
  // 例如，分析亮度、检测特征等
  console.log('触发多线程worker',buffer)


  // 可以在这里添加更复杂的图像处理逻辑
}

// 将buffer转换为PixelMap的方法
/**
 * 将 ArrayBuffer 转换为 PixelMap
 */
 function convertYUV420PToNV21(yuv420pBuffer: ArrayBuffer, width: number=1280, height: number=960): ArrayBuffer {
  console.log('触发convertYUV420PToNV21')
  // 确保输入数据大小正确
  const ySize = width * height;
  const uvSize = ySize / 4;
  const totalSize = ySize + uvSize * 2;

  if (yuv420pBuffer.byteLength !== totalSize) {
    console.error(`Invalid YUV420P buffer size: ${yuv420pBuffer.byteLength}, expected: ${totalSize}`);
    return new ArrayBuffer(0);
  }else{
    console.log('是yuv420p')
  }
  console.log('继续执行')
  // 创建输出缓冲区
  const nv21Buffer = new ArrayBuffer(totalSize);
  const nv21View = new Uint8Array(nv21Buffer);
  const yuv420pView = new Uint8Array(yuv420pBuffer);

  // YUV420P 格式中的平面分布
  const yPlane = yuv420pView.subarray(0, ySize);                // Y 平面
  const uPlane = yuv420pView.subarray(ySize, ySize + uvSize);   // U 平面
  const vPlane = yuv420pView.subarray(ySize + uvSize);          // V 平面

  // 复制 Y 平面 (两种格式的 Y 平面相同)
  nv21View.set(yPlane, 0);

  // 交错复制 V 和 U 平面到 NV21 格式 (NV21 中是 VU 交错)
  const vuPlane = nv21View.subarray(ySize);

  for (let i = 0; i < uvSize; i++) {
    vuPlane[i * 2] = vPlane[i];     // V 在偶数位置
    vuPlane[i * 2 + 1] = uPlane[i]; // U 在奇数位置
  }
   console.log('nv21Buffer',nv21Buffer)
   console.log('nv21Buffer',nv21Buffer.byteLength)
  if(nv21Buffer){
    console.log('转换完成至nv21Buffer')
    console.log('nv21Buffer',nv21Buffer.byteLength)

    return nv21Buffer
  }else{
    return undefined;
  }

}