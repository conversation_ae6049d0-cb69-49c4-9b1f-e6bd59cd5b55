<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ec</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-ec, ec - EC key processing</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>ec</b> [<b>-help</b>] [<b>-inform PEM|DER</b>] [<b>-outform PEM|DER</b>] [<b>-in filename</b>] [<b>-passin arg</b>] [<b>-out filename</b>] [<b>-passout arg</b>] [<b>-des</b>] [<b>-des3</b>] [<b>-idea</b>] [<b>-text</b>] [<b>-noout</b>] [<b>-param_out</b>] [<b>-pubin</b>] [<b>-pubout</b>] [<b>-conv_form arg</b>] [<b>-param_enc arg</b>] [<b>-no_public</b>] [<b>-check</b>] [<b>-engine id</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>ec</b> command processes EC keys. They can be converted between various forms and their components printed out. <b>Note</b> OpenSSL uses the private key format specified in &#39;SEC 1: Elliptic Curve Cryptography&#39; (http://www.secg.org/). To convert an OpenSSL EC private key into the PKCS#8 private key format use the <b>pkcs8</b> command.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="inform-DER-PEM"><b>-inform DER|PEM</b></dt>
<dd>

<p>This specifies the input format. The <b>DER</b> option with a private key uses an ASN.1 DER encoded SEC1 private key. When used with a public key it uses the SubjectPublicKeyInfo structure as specified in RFC 3280. The <b>PEM</b> form is the default format: it consists of the <b>DER</b> format base64 encoded with additional header and footer lines. In the case of a private key PKCS#8 format is also accepted.</p>

</dd>
<dt id="outform-DER-PEM"><b>-outform DER|PEM</b></dt>
<dd>

<p>This specifies the output format, the options have the same meaning and default as the <b>-inform</b> option.</p>

</dd>
<dt id="in-filename"><b>-in filename</b></dt>
<dd>

<p>This specifies the input filename to read a key from or standard input if this option is not specified. If the key is encrypted a pass phrase will be prompted for.</p>

</dd>
<dt id="passin-arg"><b>-passin arg</b></dt>
<dd>

<p>The input file password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="out-filename"><b>-out filename</b></dt>
<dd>

<p>This specifies the output filename to write a key to or standard output by is not specified. If any encryption options are set then a pass phrase will be prompted for. The output filename should <b>not</b> be the same as the input filename.</p>

</dd>
<dt id="passout-arg"><b>-passout arg</b></dt>
<dd>

<p>The output file password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="des--des3--idea"><b>-des|-des3|-idea</b></dt>
<dd>

<p>These options encrypt the private key with the DES, triple DES, IDEA or any other cipher supported by OpenSSL before outputting it. A pass phrase is prompted for. If none of these options is specified the key is written in plain text. This means that using the <b>ec</b> utility to read in an encrypted key with no encryption option can be used to remove the pass phrase from a key, or by setting the encryption options it can be use to add or change the pass phrase. These options can only be used with PEM format output files.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>Prints out the public, private key components and parameters.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>This option prevents output of the encoded version of the key.</p>

</dd>
<dt id="pubin"><b>-pubin</b></dt>
<dd>

<p>By default, a private key is read from the input file. With this option a public key is read instead.</p>

</dd>
<dt id="pubout"><b>-pubout</b></dt>
<dd>

<p>By default a private key is output. With this option a public key will be output instead. This option is automatically set if the input is a public key.</p>

</dd>
<dt id="conv_form"><b>-conv_form</b></dt>
<dd>

<p>This specifies how the points on the elliptic curve are converted into octet strings. Possible values are: <b>compressed</b> (the default value), <b>uncompressed</b> and <b>hybrid</b>. For more information regarding the point conversion forms please read the X9.62 standard. <b>Note</b> Due to patent issues the <b>compressed</b> option is disabled by default for binary curves and can be enabled by defining the preprocessor macro <b>OPENSSL_EC_BIN_PT_COMP</b> at compile time.</p>

</dd>
<dt id="param_enc-arg"><b>-param_enc arg</b></dt>
<dd>

<p>This specifies how the elliptic curve parameters are encoded. Possible value are: <b>named_curve</b>, i.e. the ec parameters are specified by an OID, or <b>explicit</b> where the ec parameters are explicitly given (see RFC 3279 for the definition of the EC parameters structures). The default value is <b>named_curve</b>. <b>Note</b> the <b>implicitlyCA</b> alternative, as specified in RFC 3279, is currently not implemented in OpenSSL.</p>

</dd>
<dt id="no_public"><b>-no_public</b></dt>
<dd>

<p>This option omits the public key components from the private key output.</p>

</dd>
<dt id="check"><b>-check</b></dt>
<dd>

<p>This option checks the consistency of an EC private or public key.</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Specifying an engine (by its unique <b>id</b> string) will cause <b>ec</b> to attempt to obtain a functional reference to the specified engine, thus initialising it if needed. The engine will then be set as the default for all available algorithms.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>The PEM private key format uses the header and footer lines:</p>

<pre><code>-----BEGIN EC PRIVATE KEY-----
-----END EC PRIVATE KEY-----</code></pre>

<p>The PEM public key format uses the header and footer lines:</p>

<pre><code>-----BEGIN PUBLIC KEY-----
-----END PUBLIC KEY-----</code></pre>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>To encrypt a private key using triple DES:</p>

<pre><code>openssl ec -in key.pem -des3 -out keyout.pem</code></pre>

<p>To convert a private key from PEM to DER format:</p>

<pre><code>openssl ec -in key.pem -outform DER -out keyout.der</code></pre>

<p>To print out the components of a private key to standard output:</p>

<pre><code>openssl ec -in key.pem -text -noout</code></pre>

<p>To just output the public part of a private key:</p>

<pre><code>openssl ec -in key.pem -pubout -out pubkey.pem</code></pre>

<p>To change the parameters encoding to <b>explicit</b>:</p>

<pre><code>openssl ec -in key.pem -param_enc explicit -out keyout.pem</code></pre>

<p>To change the point conversion form to <b>compressed</b>:</p>

<pre><code>openssl ec -in key.pem -conv_form compressed -out keyout.pem</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/ecparam.html">ecparam(1)</a>, <a href="../man1/dsa.html">dsa(1)</a>, <a href="../man1/rsa.html">rsa(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2003-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


