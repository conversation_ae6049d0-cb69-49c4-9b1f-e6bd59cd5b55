.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_ADDRINFO 3"
.TH BIO_ADDRINFO 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_lookup_type,
BIO_ADDRINFO, BIO_ADDRINFO_next, BIO_ADDRINFO_free,
BIO_ADDRINFO_family, BIO_ADDRINFO_socktype, BIO_ADDRINFO_protocol,
BIO_ADDRINFO_address,
BIO_lookup_ex,
BIO_lookup
\&\- BIO_ADDRINFO type and routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <sys/types.h>
\& #include <openssl/bio.h>
\&
\& typedef union bio_addrinfo_st BIO_ADDRINFO;
\&
\& enum BIO_lookup_type {
\&     BIO_LOOKUP_CLIENT, BIO_LOOKUP_SERVER
\& };
\&
\& int BIO_lookup_ex(const char *host, const char *service, int lookup_type,
\&                   int family, int socktype, int protocol, BIO_ADDRINFO **res);
\& int BIO_lookup(const char *node, const char *service,
\&                enum BIO_lookup_type lookup_type,
\&                int family, int socktype, BIO_ADDRINFO **res);
\&
\& const BIO_ADDRINFO *BIO_ADDRINFO_next(const BIO_ADDRINFO *bai);
\& int BIO_ADDRINFO_family(const BIO_ADDRINFO *bai);
\& int BIO_ADDRINFO_socktype(const BIO_ADDRINFO *bai);
\& int BIO_ADDRINFO_protocol(const BIO_ADDRINFO *bai);
\& const BIO_ADDR *BIO_ADDRINFO_address(const BIO_ADDRINFO *bai);
\& void BIO_ADDRINFO_free(BIO_ADDRINFO *bai);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBBIO_ADDRINFO\fR type is a wrapper for address information
types provided on your platform.
.PP
\&\fBBIO_ADDRINFO\fR normally forms a chain of several that can be
picked at one by one.
.PP
\&\fBBIO_lookup_ex()\fR looks up a specified \fBhost\fR and \fBservice\fR, and
uses \fBlookup_type\fR to determine what the default address should
be if \fBhost\fR is \fBNULL\fR. \fBfamily\fR, \fBsocktype\fR and \fBprotocol\fR are used to
determine what protocol family, socket type and protocol should be used for
the lookup.  \fBfamily\fR can be any of AF_INET, AF_INET6, AF_UNIX and
AF_UNSPEC. \fBsocktype\fR can be SOCK_STREAM, SOCK_DGRAM or 0. Specifying 0
indicates that any type can be used. \fBprotocol\fR specifies a protocol such as
IPPROTO_TCP, IPPROTO_UDP or IPPORTO_SCTP. If set to 0 than any protocol can be
used. \fBres\fR points at a pointer to hold the start of a \fBBIO_ADDRINFO\fR
chain.
.PP
For the family \fBAF_UNIX\fR, \fBBIO_lookup_ex()\fR will ignore the \fBservice\fR
parameter and expects the \fBnode\fR parameter to hold the path to the
socket file.
.PP
\&\fBBIO_lookup()\fR does the same as \fBBIO_lookup_ex()\fR but does not provide the ability
to select based on the protocol (any protocol may be returned).
.PP
\&\fBBIO_ADDRINFO_family()\fR returns the family of the given
\&\fBBIO_ADDRINFO\fR.  The result will be one of the constants
AF_INET, AF_INET6 and AF_UNIX.
.PP
\&\fBBIO_ADDRINFO_socktype()\fR returns the socket type of the given
\&\fBBIO_ADDRINFO\fR.  The result will be one of the constants
SOCK_STREAM and SOCK_DGRAM.
.PP
\&\fBBIO_ADDRINFO_protocol()\fR returns the protocol id of the given
\&\fBBIO_ADDRINFO\fR.  The result will be one of the constants
IPPROTO_TCP and IPPROTO_UDP.
.PP
\&\fBBIO_ADDRINFO_address()\fR returns the underlying \fBBIO_ADDR\fR
of the given \fBBIO_ADDRINFO\fR.
.PP
\&\fBBIO_ADDRINFO_next()\fR returns the next \fBBIO_ADDRINFO\fR in the chain
from the given one.
.PP
\&\fBBIO_ADDRINFO_free()\fR frees the chain of \fBBIO_ADDRINFO\fR starting
with the given one.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_lookup_ex()\fR and \fBBIO_lookup()\fR return 1 on success and 0 when an error
occurred, and will leave an error indication on the OpenSSL error stack in that
case.
.PP
All other functions described here return 0 or \fBNULL\fR when the
information they should return isn't available.
.SH NOTES
.IX Header "NOTES"
The \fBBIO_lookup_ex()\fR implementation uses the platform provided \fBgetaddrinfo()\fR
function. On Linux it is known that specifying 0 for the protocol will not
return any SCTP based addresses when calling \fBgetaddrinfo()\fR. Therefore, if an SCTP
address is required then the \fBprotocol\fR parameter to \fBBIO_lookup_ex()\fR should be
explicitly set to IPPROTO_SCTP. The same may be true on other platforms.
.SH HISTORY
.IX Header "HISTORY"
The \fBBIO_lookup_ex()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
