.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "UI_STRING 3"
.TH UI_STRING 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
UI_STRING, UI_string_types, UI_get_string_type,
UI_get_input_flags, UI_get0_output_string,
UI_get0_action_string, UI_get0_result_string, UI_get_result_string_length,
UI_get0_test_string, UI_get_result_minsize,
UI_get_result_maxsize, UI_set_result, UI_set_result_ex
\&\- User interface string parsing
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ui.h>
\&
\& typedef struct ui_string_st UI_STRING;
\&
\& enum UI_string_types {
\&     UIT_NONE = 0,
\&     UIT_PROMPT,                 /* Prompt for a string */
\&     UIT_VERIFY,                 /* Prompt for a string and verify */
\&     UIT_BOOLEAN,                /* Prompt for a yes/no response */
\&     UIT_INFO,                   /* Send info to the user */
\&     UIT_ERROR                   /* Send an error message to the user */
\& };
\&
\& enum UI_string_types UI_get_string_type(UI_STRING *uis);
\& int UI_get_input_flags(UI_STRING *uis);
\& const char *UI_get0_output_string(UI_STRING *uis);
\& const char *UI_get0_action_string(UI_STRING *uis);
\& const char *UI_get0_result_string(UI_STRING *uis);
\& int UI_get_result_string_length(UI_STRING *uis);
\& const char *UI_get0_test_string(UI_STRING *uis);
\& int UI_get_result_minsize(UI_STRING *uis);
\& int UI_get_result_maxsize(UI_STRING *uis);
\& int UI_set_result(UI *ui, UI_STRING *uis, const char *result);
\& int UI_set_result_ex(UI *ui, UI_STRING *uis, const char *result, int len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBUI_STRING\fR gets created internally and added to a \fBUI\fR whenever
one of the functions \fBUI_add_input_string()\fR, \fBUI_dup_input_string()\fR,
\&\fBUI_add_verify_string()\fR, \fBUI_dup_verify_string()\fR,
\&\fBUI_add_input_boolean()\fR, \fBUI_dup_input_boolean()\fR, \fBUI_add_info_string()\fR,
\&\fBUI_dup_info_string()\fR, \fBUI_add_error_string()\fR or \fBUI_dup_error_string()\fR
is called.
For a \fBUI_METHOD\fR user, there's no need to know more.
For a \fBUI_METHOD\fR creator, it is of interest to fetch text from these
\&\fBUI_STRING\fR objects as well as adding results to some of them.
.PP
\&\fBUI_get_string_type()\fR is used to retrieve the type of the given
\&\fBUI_STRING\fR.
.PP
\&\fBUI_get_input_flags()\fR is used to retrieve the flags associated with the
given \fBUI_STRING\fR.
.PP
\&\fBUI_get0_output_string()\fR is used to retrieve the actual string to
output (prompt, info, error, ...).
.PP
\&\fBUI_get0_action_string()\fR is used to retrieve the action description
associated with a \fBUIT_BOOLEAN\fR type \fBUI_STRING\fR.
For all other \fBUI_STRING\fR types, NULL is returned.
See \fBUI_add_input_boolean\fR\|(3).
.PP
\&\fBUI_get0_result_string()\fR and \fBUI_get_result_string_length()\fR are used to
retrieve the result of a prompt and its length.
This is only useful for \fBUIT_PROMPT\fR and \fBUIT_VERIFY\fR type strings.
For all other \fBUI_STRING\fR types, \fBUI_get0_result_string()\fR returns NULL
and \fBUI_get_result_string_length()\fR returns \-1.
.PP
\&\fBUI_get0_test_string()\fR is used to retrieve the string to compare the
prompt result with.
This is only useful for \fBUIT_VERIFY\fR type strings.
For all other \fBUI_STRING\fR types, NULL is returned.
.PP
\&\fBUI_get_result_minsize()\fR and \fBUI_get_result_maxsize()\fR are used to
retrieve the minimum and maximum required size of the result.
This is only useful for \fBUIT_PROMPT\fR and \fBUIT_VERIFY\fR type strings.
For all other \fBUI_STRING\fR types, \-1 is returned.
.PP
\&\fBUI_set_result_ex()\fR is used to set the result value of a prompt and its length.
For \fBUIT_PROMPT\fR and \fBUIT_VERIFY\fR type UI strings, this sets the
result retrievable with \fBUI_get0_result_string()\fR by copying the
contents of \fBresult\fR if its length fits the minimum and maximum size
requirements.
For \fBUIT_BOOLEAN\fR type UI strings, this sets the first character of
the result retrievable with \fBUI_get0_result_string()\fR to the first
\&\fBok_char\fR given with \fBUI_add_input_boolean()\fR or \fBUI_dup_input_boolean()\fR
if the \fBresult\fR matched any of them, or the first of the
\&\fBcancel_chars\fR if the \fBresult\fR matched any of them, otherwise it's
set to the NUL char \f(CW\*(C`\e0\*(C'\fR.
See \fBUI_add_input_boolean\fR\|(3) for more information on \fBok_chars\fR and
\&\fBcancel_chars\fR.
.PP
\&\fBUI_set_result()\fR does the same thing as \fBUI_set_result_ex()\fR, but calculates
its length internally.
It expects the string to be terminated with a NUL byte, and is therefore
only useful with normal C strings.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBUI_get_string_type()\fR returns the UI string type.
.PP
\&\fBUI_get_input_flags()\fR returns the UI string flags.
.PP
\&\fBUI_get0_output_string()\fR returns the UI string output string.
.PP
\&\fBUI_get0_action_string()\fR returns the UI string action description
string for \fBUIT_BOOLEAN\fR type UI strings, NULL for any other type.
.PP
\&\fBUI_get0_result_string()\fR returns the UI string result buffer for
\&\fBUIT_PROMPT\fR and \fBUIT_VERIFY\fR type UI strings, NULL for any other
type.
.PP
\&\fBUI_get_result_string_length()\fR returns the UI string result buffer's
content length for \fBUIT_PROMPT\fR and \fBUIT_VERIFY\fR type UI strings,
\&\-1 for any other type.
.PP
\&\fBUI_get0_test_string()\fR returns the UI string action description
string for \fBUIT_VERIFY\fR type UI strings, NULL for any other type.
.PP
\&\fBUI_get_result_minsize()\fR returns the minimum allowed result size for
the UI string for \fBUIT_PROMPT\fR and \fBUIT_VERIFY\fR type strings,
\&\-1 for any other type.
.PP
\&\fBUI_get_result_maxsize()\fR returns the minimum allowed result size for
the UI string for \fBUIT_PROMPT\fR and \fBUIT_VERIFY\fR type strings,
\&\-1 for any other type.
.PP
\&\fBUI_set_result()\fR returns 0 on success or when the UI string is of any
type other than \fBUIT_PROMPT\fR, \fBUIT_VERIFY\fR or \fBUIT_BOOLEAN\fR, \-1 on
error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBUI\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
