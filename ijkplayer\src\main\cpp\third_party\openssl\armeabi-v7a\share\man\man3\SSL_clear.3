.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CLEAR 3"
.TH SSL_CLEAR 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_clear \- reset SSL object to allow another connection
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_clear(SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Reset \fBssl\fR to allow another connection. All settings (method, ciphers,
BIOs) are kept.
.SH NOTES
.IX Header "NOTES"
SSL_clear is used to prepare an SSL object for a new connection. While all
settings are kept, a side effect is the handling of the current SSL session.
If a session is still \fBopen\fR, it is considered bad and will be removed
from the session cache, as required by RFC2246. A session is considered open,
if \fBSSL_shutdown\fR\|(3) was not called for the connection
or at least \fBSSL_set_shutdown\fR\|(3) was used to
set the SSL_SENT_SHUTDOWN state.
.PP
If a session was closed cleanly, the session object will be kept and all
settings corresponding. This explicitly means, that e.g. the special method
used during the session will be kept for the next handshake. So if the
session was a TLSv1 session, a SSL client object will use a TLSv1 client
method for the next handshake and a SSL server object will use a TLSv1
server method, even if TLS_*_methods were chosen on startup. This
will might lead to connection failures (see \fBSSL_new\fR\|(3))
for a description of the method's properties.
.SH WARNINGS
.IX Header "WARNINGS"
\&\fBSSL_clear()\fR resets the SSL object to allow for another connection. The
reset operation however keeps several settings of the last sessions
(some of these settings were made automatically during the last
handshake). It only makes sense for a new connection with the exact
same peer that shares these settings, and may fail if that peer
changes its settings between connections. Use the sequence
\&\fBSSL_get_session\fR\|(3);
\&\fBSSL_new\fR\|(3);
\&\fBSSL_set_session\fR\|(3);
\&\fBSSL_free\fR\|(3)
instead to avoid such failures
(or simply \fBSSL_free\fR\|(3); \fBSSL_new\fR\|(3)
if session reuse is not desired).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur:
.IP 0 4
The \fBSSL_clear()\fR operation could not be performed. Check the error stack to
find out the reason.
.IP 1 4
.IX Item "1"
The \fBSSL_clear()\fR operation was successful.
.PP
\&\fBSSL_new\fR\|(3), \fBSSL_free\fR\|(3),
\&\fBSSL_shutdown\fR\|(3), \fBSSL_set_shutdown\fR\|(3),
\&\fBSSL_CTX_set_options\fR\|(3), \fBssl\fR\|(7),
\&\fBSSL_CTX_set_client_cert_cb\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
