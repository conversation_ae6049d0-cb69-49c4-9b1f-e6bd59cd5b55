.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_ALLOC_BUFFERS 3"
.TH SSL_ALLOC_BUFFERS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_free_buffers, SSL_alloc_buffers \- manage SSL structure buffers
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_free_buffers(SSL *ssl);
\& int SSL_alloc_buffers(SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_free_buffers()\fR frees the read and write buffers of the given \fBssl\fR.
\&\fBSSL_alloc_buffers()\fR allocates the read and write buffers of the given \fBssl\fR.
.PP
The \fBSSL_MODE_RELEASE_BUFFERS\fR mode releases read or write buffers whenever
the buffers have been drained. These functions allow applications to manually
control when buffers are freed and allocated.
.PP
After freeing the buffers, the buffers are automatically reallocated upon a
new read or write. The \fBSSL_alloc_buffers()\fR does not need to be called, but
can be used to make sure the buffers are preallocated. This can be used to
avoid allocation during data processing or with \fBCRYPTO_set_mem_functions()\fR
to control where and how buffers are allocated.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur:
.IP "0 (Failure)" 4
.IX Item "0 (Failure)"
The \fBSSL_free_buffers()\fR function returns 0 when there is pending data to be
read or written. The \fBSSL_alloc_buffers()\fR function returns 0 when there is
an allocation failure.
.IP "1 (Success)" 4
.IX Item "1 (Success)"
The \fBSSL_free_buffers()\fR function returns 1 if the buffers have been freed. This
value is also returned if the buffers had been freed before calling
\&\fBSSL_free_buffers()\fR.
The \fBSSL_alloc_buffers()\fR function returns 1 if the buffers have been allocated.
This value is also returned if the buffers had been allocated before calling
\&\fBSSL_alloc_buffers()\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_free\fR\|(3), \fBSSL_clear\fR\|(3),
\&\fBSSL_new\fR\|(3), \fBSSL_CTX_set_mode\fR\|(3),
CRYPTO_set_mem_functions
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
