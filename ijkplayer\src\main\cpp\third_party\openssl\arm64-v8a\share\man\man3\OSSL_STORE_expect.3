.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_STORE_EXPECT 3"
.TH OSSL_STORE_EXPECT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_STORE_expect,
OSSL_STORE_supports_search,
OSSL_STORE_find
\&\- Specify what object type is expected
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/store.h>
\&
\& int OSSL_STORE_expect(OSSL_STORE_CTX *ctx, int expected_type);
\&
\& int OSSL_STORE_supports_search(OSSL_STORE_CTX *ctx, int criterion_type);
\&
\& int OSSL_STORE_find(OSSL_STORE_CTX *ctx, OSSL_STORE_SEARCH *search);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_STORE_expect()\fR helps applications filter what \fBOSSL_STORE_load()\fR returns
by specifying a \fBOSSL_STORE_INFO\fR type.
For example, if \f(CW\*(C`file:/foo/bar/store.pem\*(C'\fR contains several different objects
and only the certificates are interesting, the application can simply say
that it expects the type \fBOSSL_STORE_INFO_CERT\fR.
All known object types (see "SUPPORTED OBJECTS" in \fBOSSL_STORE_INFO\fR\|(3))
except for \fBOSSL_STORE_INFO_NAME\fR are supported.
.PP
\&\fBOSSL_STORE_find()\fR helps applications specify a criterion for a more fine
grained search of objects.
.PP
\&\fBOSSL_STORE_supports_search()\fR checks if the loader of the given OSSL_STORE
context supports the given search type.
See "SUPPORTED CRITERION TYPES" in OSSL_STORE_SEARCH for information on the
supported search criterion types.
.PP
\&\fBOSSL_STORE_expect()\fR and OSSL_STORE_find \fImust\fR be called before the first
\&\fBOSSL_STORE_load()\fR of a given session, or they will fail.
.SH NOTES
.IX Header "NOTES"
If a more elaborate filter is required by the application, a better choice
would be to use a post-processing function.
See \fBOSSL_STORE_open\fR\|(3) for more information.
.PP
However, some loaders may take advantage of the knowledge of an expected type
to make object retrieval more efficient, so if a single type is expected, this
method is usually preferable.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_STORE_expect()\fR returns 1 on success, or 0 on failure.
.PP
\&\fBOSSL_STORE_supports_search()\fR returns 1 if the criterion is supported, or 0
otherwise.
.PP
\&\fBOSSL_STORE_find()\fR returns 1 on success, or 0 on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBossl_store\fR\|(7), \fBOSSL_STORE_INFO\fR\|(3), \fBOSSL_STORE_SEARCH\fR\|(3),
\&\fBOSSL_STORE_load\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBOSSL_STORE_expect()\fR, \fBOSSL_STORE_supports_search()\fR and \fBOSSL_STORE_find()\fR
were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
