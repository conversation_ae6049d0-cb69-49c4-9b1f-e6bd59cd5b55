.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_VERIFY 3"
.TH SSL_CTX_SET_VERIFY 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_ex_data_X509_STORE_CTX_idx,
SSL_CTX_set_verify, SSL_set_verify,
SSL_CTX_set_verify_depth, SSL_set_verify_depth,
SSL_verify_cb,
SSL_verify_client_post_handshake,
SSL_set_post_handshake_auth,
SSL_CTX_set_post_handshake_auth
\&\- set peer certificate verification parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& typedef int (*SSL_verify_cb)(int preverify_ok, X509_STORE_CTX *x509_ctx);
\&
\& void SSL_CTX_set_verify(SSL_CTX *ctx, int mode, SSL_verify_cb verify_callback);
\& void SSL_set_verify(SSL *ssl, int mode, SSL_verify_cb verify_callback);
\& SSL_get_ex_data_X509_STORE_CTX_idx(void);
\&
\& void SSL_CTX_set_verify_depth(SSL_CTX *ctx, int depth);
\& void SSL_set_verify_depth(SSL *ssl, int depth);
\&
\& int SSL_verify_client_post_handshake(SSL *ssl);
\& void SSL_CTX_set_post_handshake_auth(SSL_CTX *ctx, int val);
\& void SSL_set_post_handshake_auth(SSL *ssl, int val);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_verify()\fR sets the verification flags for \fBctx\fR to be \fBmode\fR and
specifies the \fBverify_callback\fR function to be used. If no callback function
shall be specified, the NULL pointer can be used for \fBverify_callback\fR.
.PP
\&\fBSSL_set_verify()\fR sets the verification flags for \fBssl\fR to be \fBmode\fR and
specifies the \fBverify_callback\fR function to be used. If no callback function
shall be specified, the NULL pointer can be used for \fBverify_callback\fR. In
this case last \fBverify_callback\fR set specifically for this \fBssl\fR remains. If
no special \fBcallback\fR was set before, the default callback for the underlying
\&\fBctx\fR is used, that was valid at the time \fBssl\fR was created with
\&\fBSSL_new\fR\|(3). Within the callback function,
\&\fBSSL_get_ex_data_X509_STORE_CTX_idx\fR can be called to get the data index
of the current SSL object that is doing the verification.
.PP
\&\fBSSL_CTX_set_verify_depth()\fR sets the maximum \fBdepth\fR for the certificate chain
verification that shall be allowed for \fBctx\fR.
.PP
\&\fBSSL_set_verify_depth()\fR sets the maximum \fBdepth\fR for the certificate chain
verification that shall be allowed for \fBssl\fR.
.PP
\&\fBSSL_CTX_set_post_handshake_auth()\fR and \fBSSL_set_post_handshake_auth()\fR enable the
Post-Handshake Authentication extension to be added to the ClientHello such that
post-handshake authentication can be requested by the server. If \fBval\fR is 0
then the extension is not sent, otherwise it is. By default the extension is not
sent. A certificate callback will need to be set via
\&\fBSSL_CTX_set_client_cert_cb()\fR if no certificate is provided at initialization.
.PP
\&\fBSSL_verify_client_post_handshake()\fR causes a CertificateRequest message to be
sent by a server on the given \fBssl\fR connection. The SSL_VERIFY_PEER flag must
be set; the SSL_VERIFY_POST_HANDSHAKE flag is optional.
.SH NOTES
.IX Header "NOTES"
The verification of certificates can be controlled by a set of logically
or'ed \fBmode\fR flags:
.IP SSL_VERIFY_NONE 4
.IX Item "SSL_VERIFY_NONE"
\&\fBServer mode:\fR the server will not send a client certificate request to the
client, so the client will not send a certificate.
.Sp
\&\fBClient mode:\fR if not using an anonymous cipher (by default disabled), the
server will send a certificate which will be checked. The result of the
certificate verification process can be checked after the TLS/SSL handshake
using the \fBSSL_get_verify_result\fR\|(3) function.
The handshake will be continued regardless of the verification result.
.IP SSL_VERIFY_PEER 4
.IX Item "SSL_VERIFY_PEER"
\&\fBServer mode:\fR the server sends a client certificate request to the client.
The certificate returned (if any) is checked. If the verification process
fails, the TLS/SSL handshake is
immediately terminated with an alert message containing the reason for
the verification failure.
The behaviour can be controlled by the additional
SSL_VERIFY_FAIL_IF_NO_PEER_CERT, SSL_VERIFY_CLIENT_ONCE and
SSL_VERIFY_POST_HANDSHAKE flags.
.Sp
\&\fBClient mode:\fR the server certificate is verified. If the verification process
fails, the TLS/SSL handshake is
immediately terminated with an alert message containing the reason for
the verification failure. If no server certificate is sent, because an
anonymous cipher is used, SSL_VERIFY_PEER is ignored.
.IP SSL_VERIFY_FAIL_IF_NO_PEER_CERT 4
.IX Item "SSL_VERIFY_FAIL_IF_NO_PEER_CERT"
\&\fBServer mode:\fR if the client did not return a certificate, the TLS/SSL
handshake is immediately terminated with a "handshake failure" alert.
This flag must be used together with SSL_VERIFY_PEER.
.Sp
\&\fBClient mode:\fR ignored (see BUGS)
.IP SSL_VERIFY_CLIENT_ONCE 4
.IX Item "SSL_VERIFY_CLIENT_ONCE"
\&\fBServer mode:\fR only request a client certificate once during the
connection. Do not ask for a client certificate again during
renegotiation or post-authentication if a certificate was requested
during the initial handshake. This flag must be used together with
SSL_VERIFY_PEER.
.Sp
\&\fBClient mode:\fR ignored (see BUGS)
.IP SSL_VERIFY_POST_HANDSHAKE 4
.IX Item "SSL_VERIFY_POST_HANDSHAKE"
\&\fBServer mode:\fR the server will not send a client certificate request
during the initial handshake, but will send the request via
\&\fBSSL_verify_client_post_handshake()\fR. This allows the SSL_CTX or SSL
to be configured for post-handshake peer verification before the
handshake occurs. This flag must be used together with
SSL_VERIFY_PEER. TLSv1.3 only; no effect on pre\-TLSv1.3 connections.
.Sp
\&\fBClient mode:\fR ignored (see BUGS)
.PP
If the \fBmode\fR is SSL_VERIFY_NONE none of the other flags may be set.
.PP
The actual verification procedure is performed either using the built-in
verification procedure or using another application provided verification
function set with
\&\fBSSL_CTX_set_cert_verify_callback\fR\|(3).
The following descriptions apply in the case of the built-in procedure. An
application provided procedure also has access to the verify depth information
and the \fBverify_callback()\fR function, but the way this information is used
may be different.
.PP
\&\fBSSL_CTX_set_verify_depth()\fR and \fBSSL_set_verify_depth()\fR set a limit on the
number of certificates between the end-entity and trust-anchor certificates.
Neither the
end-entity nor the trust-anchor certificates count against \fBdepth\fR. If the
certificate chain needed to reach a trusted issuer is longer than \fBdepth+2\fR,
X509_V_ERR_CERT_CHAIN_TOO_LONG will be issued.
The depth count is "level 0:peer certificate", "level 1: CA certificate",
"level 2: higher level CA certificate", and so on. Setting the maximum
depth to 2 allows the levels 0, 1, 2 and 3 (0 being the end-entity and 3 the
trust-anchor).
The default depth limit is 100,
allowing for the peer certificate, at most 100 intermediate CA certificates and
a final trust anchor certificate.
.PP
The \fBverify_callback\fR function is used to control the behaviour when the
SSL_VERIFY_PEER flag is set. It must be supplied by the application and
receives two arguments: \fBpreverify_ok\fR indicates, whether the verification of
the certificate in question was passed (preverify_ok=1) or not
(preverify_ok=0). \fBx509_ctx\fR is a pointer to the complete context used
for the certificate chain verification.
.PP
The certificate chain is checked starting with the deepest nesting level
(the root CA certificate) and worked upward to the peer's certificate.
At each level signatures and issuer attributes are checked. Whenever
a verification error is found, the error number is stored in \fBx509_ctx\fR
and \fBverify_callback\fR is called with \fBpreverify_ok\fR=0. By applying
X509_CTX_store_* functions \fBverify_callback\fR can locate the certificate
in question and perform additional steps (see EXAMPLES). If no error is
found for a certificate, \fBverify_callback\fR is called with \fBpreverify_ok\fR=1
before advancing to the next level.
.PP
The return value of \fBverify_callback\fR controls the strategy of the further
verification process. If \fBverify_callback\fR returns 0, the verification
process is immediately stopped with "verification failed" state. If
SSL_VERIFY_PEER is set, a verification failure alert is sent to the peer and
the TLS/SSL handshake is terminated. If \fBverify_callback\fR returns 1,
the verification process is continued. If \fBverify_callback\fR always returns
1, the TLS/SSL handshake will not be terminated with respect to verification
failures and the connection will be established. The calling process can
however retrieve the error code of the last verification error using
\&\fBSSL_get_verify_result\fR\|(3) or by maintaining its
own error storage managed by \fBverify_callback\fR.
.PP
If no \fBverify_callback\fR is specified, the default callback will be used.
Its return value is identical to \fBpreverify_ok\fR, so that any verification
failure will lead to a termination of the TLS/SSL handshake with an
alert message, if SSL_VERIFY_PEER is set.
.PP
After calling \fBSSL_set_post_handshake_auth()\fR, the client will need to add a
certificate or certificate callback to its configuration before it can
successfully authenticate. This must be called before \fBSSL_connect()\fR.
.PP
\&\fBSSL_verify_client_post_handshake()\fR requires that verify flags have been
previously set, and that a client sent the post-handshake authentication
extension. When the client returns a certificate the verify callback will be
invoked. A write operation must take place for the Certificate Request to be
sent to the client, this can be done with \fBSSL_do_handshake()\fR or \fBSSL_write_ex()\fR.
Only one certificate request may be outstanding at any time.
.PP
When post-handshake authentication occurs, a refreshed NewSessionTicket
message is sent to the client.
.SH BUGS
.IX Header "BUGS"
In client mode, it is not checked whether the SSL_VERIFY_PEER flag
is set, but whether any flags other than SSL_VERIFY_NONE are set. This can
lead to unexpected behaviour if SSL_VERIFY_PEER and other flags are not used as
required.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The SSL*_set_verify*() functions do not provide diagnostic information.
.PP
The \fBSSL_verify_client_post_handshake()\fR function returns 1 if the request
succeeded, and 0 if the request failed. The error stack can be examined
to determine the failure reason.
.SH EXAMPLES
.IX Header "EXAMPLES"
The following code sequence realizes an example \fBverify_callback\fR function
that will always continue the TLS/SSL handshake regardless of verification
failure, if wished. The callback realizes a verification depth limit with
more informational output.
.PP
All verification errors are printed; information about the certificate chain
is printed on request.
The example is realized for a server that does allow but not require client
certificates.
.PP
The example makes use of the ex_data technique to store application data
into/retrieve application data from the SSL structure
(see \fBCRYPTO_get_ex_new_index\fR\|(3),
\&\fBSSL_get_ex_data_X509_STORE_CTX_idx\fR\|(3)).
.PP
.Vb 7
\& ...
\& typedef struct {
\&   int verbose_mode;
\&   int verify_depth;
\&   int always_continue;
\& } mydata_t;
\& int mydata_index;
\&
\& ...
\& static int verify_callback(int preverify_ok, X509_STORE_CTX *ctx)
\& {
\&     char    buf[256];
\&     X509   *err_cert;
\&     int     err, depth;
\&     SSL    *ssl;
\&     mydata_t *mydata;
\&
\&     err_cert = X509_STORE_CTX_get_current_cert(ctx);
\&     err = X509_STORE_CTX_get_error(ctx);
\&     depth = X509_STORE_CTX_get_error_depth(ctx);
\&
\&     /*
\&      * Retrieve the pointer to the SSL of the connection currently treated
\&      * and the application specific data stored into the SSL object.
\&      */
\&     ssl = X509_STORE_CTX_get_ex_data(ctx, SSL_get_ex_data_X509_STORE_CTX_idx());
\&     mydata = SSL_get_ex_data(ssl, mydata_index);
\&
\&     X509_NAME_oneline(X509_get_subject_name(err_cert), buf, 256);
\&
\&     /*
\&      * Catch a too long certificate chain. The depth limit set using
\&      * SSL_CTX_set_verify_depth() is by purpose set to "limit+1" so
\&      * that whenever the "depth>verify_depth" condition is met, we
\&      * have violated the limit and want to log this error condition.
\&      * We must do it here, because the CHAIN_TOO_LONG error would not
\&      * be found explicitly; only errors introduced by cutting off the
\&      * additional certificates would be logged.
\&      */
\&     if (depth > mydata\->verify_depth) {
\&         preverify_ok = 0;
\&         err = X509_V_ERR_CERT_CHAIN_TOO_LONG;
\&         X509_STORE_CTX_set_error(ctx, err);
\&     }
\&     if (!preverify_ok) {
\&         printf("verify error:num=%d:%s:depth=%d:%s\en", err,
\&                X509_verify_cert_error_string(err), depth, buf);
\&     } else if (mydata\->verbose_mode) {
\&         printf("depth=%d:%s\en", depth, buf);
\&     }
\&
\&     /*
\&      * At this point, err contains the last verification error. We can use
\&      * it for something special
\&      */
\&     if (!preverify_ok && (err == X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT)) {
\&         X509_NAME_oneline(X509_get_issuer_name(err_cert), buf, 256);
\&         printf("issuer= %s\en", buf);
\&     }
\&
\&     if (mydata\->always_continue)
\&         return 1;
\&     else
\&         return preverify_ok;
\& }
\& ...
\&
\& mydata_t mydata;
\&
\& ...
\& mydata_index = SSL_get_ex_new_index(0, "mydata index", NULL, NULL, NULL);
\&
\& ...
\& SSL_CTX_set_verify(ctx, SSL_VERIFY_PEER | SSL_VERIFY_CLIENT_ONCE,
\&                    verify_callback);
\&
\& /*
\&  * Let the verify_callback catch the verify_depth error so that we get
\&  * an appropriate error in the logfile.
\&  */
\& SSL_CTX_set_verify_depth(verify_depth + 1);
\&
\& /*
\&  * Set up the SSL specific data into "mydata" and store it into th SSL
\&  * structure.
\&  */
\& mydata.verify_depth = verify_depth; ...
\& SSL_set_ex_data(ssl, mydata_index, &mydata);
\&
\& ...
\& SSL_accept(ssl);       /* check of success left out for clarity */
\& if (peer = SSL_get_peer_certificate(ssl)) {
\&     if (SSL_get_verify_result(ssl) == X509_V_OK) {
\&         /* The client sent a certificate which verified OK */
\&     }
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_new\fR\|(3),
\&\fBSSL_CTX_get_verify_mode\fR\|(3),
\&\fBSSL_get_verify_result\fR\|(3),
\&\fBSSL_CTX_load_verify_locations\fR\|(3),
\&\fBSSL_get_peer_certificate\fR\|(3),
\&\fBSSL_CTX_set_cert_verify_callback\fR\|(3),
\&\fBSSL_get_ex_data_X509_STORE_CTX_idx\fR\|(3),
\&\fBSSL_CTX_set_client_cert_cb\fR\|(3),
\&\fBCRYPTO_get_ex_new_index\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The SSL_VERIFY_POST_HANDSHAKE option, and the \fBSSL_verify_client_post_handshake()\fR
and \fBSSL_set_post_handshake_auth()\fR functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
