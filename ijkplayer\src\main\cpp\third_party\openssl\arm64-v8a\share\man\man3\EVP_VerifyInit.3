.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_VERIFYINIT 3"
.TH EVP_VERIFYINIT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_VerifyInit_ex,
EVP_VerifyInit, EVP_VerifyUpdate, EVP_VerifyFinal
\&\- EVP signature verification functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_VerifyInit_ex(EVP_MD_CTX *ctx, const EVP_MD *type, ENGINE *impl);
\& int EVP_VerifyUpdate(EVP_MD_CTX *ctx, const void *d, unsigned int cnt);
\& int EVP_VerifyFinal(EVP_MD_CTX *ctx, unsigned char *sigbuf, unsigned int siglen,
\&                     EVP_PKEY *pkey);
\&
\& int EVP_VerifyInit(EVP_MD_CTX *ctx, const EVP_MD *type);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP signature verification routines are a high-level interface to digital
signatures.
.PP
\&\fBEVP_VerifyInit_ex()\fR sets up verification context \fBctx\fR to use digest
\&\fBtype\fR from ENGINE \fBimpl\fR. \fBctx\fR must be created by calling
\&\fBEVP_MD_CTX_new()\fR before calling this function.
.PP
\&\fBEVP_VerifyUpdate()\fR hashes \fBcnt\fR bytes of data at \fBd\fR into the
verification context \fBctx\fR. This function can be called several times on the
same \fBctx\fR to include additional data.
.PP
\&\fBEVP_VerifyFinal()\fR verifies the data in \fBctx\fR using the public key \fBpkey\fR
and against the \fBsiglen\fR bytes at \fBsigbuf\fR.
.PP
\&\fBEVP_VerifyInit()\fR initializes verification context \fBctx\fR to use the default
implementation of digest \fBtype\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_VerifyInit_ex()\fR and \fBEVP_VerifyUpdate()\fR return 1 for success and 0 for
failure.
.PP
\&\fBEVP_VerifyFinal()\fR returns 1 for a correct signature, 0 for failure and \-1 if some
other error occurred.
.PP
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH NOTES
.IX Header "NOTES"
The \fBEVP\fR interface to digital signatures should almost always be used in
preference to the low-level interfaces. This is because the code then becomes
transparent to the algorithm used and much more flexible.
.PP
The call to \fBEVP_VerifyFinal()\fR internally finalizes a copy of the digest context.
This means that calls to \fBEVP_VerifyUpdate()\fR and \fBEVP_VerifyFinal()\fR can be called
later to digest and verify additional data.
.PP
Since only a copy of the digest context is ever finalized the context must
be cleaned up after use by calling \fBEVP_MD_CTX_free()\fR or a memory leak
will occur.
.SH BUGS
.IX Header "BUGS"
Older versions of this documentation wrongly stated that calls to
\&\fBEVP_VerifyUpdate()\fR could not be made after calling \fBEVP_VerifyFinal()\fR.
.PP
Since the public key is passed in the call to \fBEVP_SignFinal()\fR any error
relating to the private key (for example an unsuitable key and digest
combination) will not be indicated until after potentially large amounts of
data have been passed through \fBEVP_SignUpdate()\fR.
.PP
It is not possible to change the signing parameters using these function.
.PP
The previous two bugs are fixed in the newer EVP_DigestVerify*() function.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7),
\&\fBEVP_SignInit\fR\|(3),
\&\fBEVP_DigestInit\fR\|(3),
\&\fBevp\fR\|(7), \fBHMAC\fR\|(3), \fBMD2\fR\|(3),
\&\fBMD5\fR\|(3), \fBMDC2\fR\|(3), \fBRIPEMD160\fR\|(3),
\&\fBSHA1\fR\|(3), \fBdgst\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
