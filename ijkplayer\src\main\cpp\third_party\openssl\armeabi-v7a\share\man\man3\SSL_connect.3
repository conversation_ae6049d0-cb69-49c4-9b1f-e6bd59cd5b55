.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CONNECT 3"
.TH SSL_CONNECT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_connect \- initiate the TLS/SSL handshake with an TLS/SSL server
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_connect(SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_connect()\fR initiates the TLS/SSL handshake with a server. The communication
channel must already have been set and assigned to the \fBssl\fR by setting an
underlying \fBBIO\fR.
.SH NOTES
.IX Header "NOTES"
The behaviour of \fBSSL_connect()\fR depends on the underlying BIO.
.PP
If the underlying BIO is \fBblocking\fR, \fBSSL_connect()\fR will only return once the
handshake has been finished or an error occurred.
.PP
If the underlying BIO is \fBnonblocking\fR, \fBSSL_connect()\fR will also return
when the underlying BIO could not satisfy the needs of \fBSSL_connect()\fR
to continue the handshake, indicating the problem by the return value \-1.
In this case a call to \fBSSL_get_error()\fR with the
return value of \fBSSL_connect()\fR will yield \fBSSL_ERROR_WANT_READ\fR or
\&\fBSSL_ERROR_WANT_WRITE\fR. The calling process then must repeat the call after
taking appropriate action to satisfy the needs of \fBSSL_connect()\fR.
The action depends on the underlying BIO. When using a nonblocking socket,
nothing is to be done, but \fBselect()\fR can be used to check for the required
condition. When using a buffering BIO, like a BIO pair, data must be written
into or retrieved out of the BIO before being able to continue.
.PP
Many systems implement Nagle's algorithm by default which means that it will
buffer outgoing TCP data if a TCP packet has already been sent for which no
corresponding ACK has been received yet from the peer. This can have performance
impacts after a successful TLSv1.3 handshake or a successful TLSv1.2 (or below)
resumption handshake, because the last peer to communicate in the handshake is
the client. If the client is also the first to send application data (as is
typical for many protocols) then this data could be buffered until an ACK has
been received for the final handshake message.
.PP
The \fBTCP_NODELAY\fR socket option is often available to disable Nagle's
algorithm. If an application opts to disable Nagle's algorithm consideration
should be given to turning it back on again later if appropriate. The helper
function \fBBIO_set_tcp_ndelay()\fR can be used to turn on or off the \fBTCP_NODELAY\fR
option.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur:
.IP 0 4
The TLS/SSL handshake was not successful but was shut down controlled and
by the specifications of the TLS/SSL protocol. Call \fBSSL_get_error()\fR with the
return value \fBret\fR to find out the reason.
.IP 1 4
.IX Item "1"
The TLS/SSL handshake was successfully completed, a TLS/SSL connection has been
established.
.IP <0 4
.IX Item "<0"
The TLS/SSL handshake was not successful, because a fatal error occurred either
at the protocol level or a connection failure occurred. The shutdown was
not clean. It can also occur if action is needed to continue the operation
for nonblocking BIOs. Call \fBSSL_get_error()\fR with the return value \fBret\fR
to find out the reason.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_get_error\fR\|(3), \fBSSL_accept\fR\|(3),
\&\fBSSL_shutdown\fR\|(3), \fBssl\fR\|(7), \fBbio\fR\|(7),
\&\fBSSL_set_connect_state\fR\|(3),
\&\fBSSL_do_handshake\fR\|(3),
\&\fBSSL_CTX_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
