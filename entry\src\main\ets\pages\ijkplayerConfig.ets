import systemParameterEnhance from '@ohos.systemParameterEnhance';
import { BusinessError } from '@ohos.base';

export class IjkPlayerConfig {

  // 设置自定义stride
  static async setCustomStride(stride: number): Promise<boolean> {
    try {
      await systemParameterEnhance.set("debug.ijkplayer.stride", stride.toString());
      console.info(`Custom stride set to: ${stride}`);
      return true;
    } catch (error) {
      let err = error as BusinessError;
      console.error(`Failed to set custom stride. Code: ${err.code}, message: ${err.message}`);
      return false;
    }
  }

  // 获取当前stride设置
  static async getCustomStride(): Promise<number> {
    try {
      const value = await systemParameterEnhance.get("debug.ijkplayer.stride", "0");
      return parseInt(value) || 0;
    } catch (error) {
      console.error("Failed to get custom stride:", error);
      return 0;
    }
  }

  // 清除stride设置
  static async clearCustomStride(): Promise<boolean> {
    try {
      await systemParameterEnhance.set("debug.ijkplayer.stride", "");
      console.info("Custom stride cleared");
      return true;
    } catch (error) {
      console.error("Failed to clear custom stride:", error);
      return false;
    }
  }
}