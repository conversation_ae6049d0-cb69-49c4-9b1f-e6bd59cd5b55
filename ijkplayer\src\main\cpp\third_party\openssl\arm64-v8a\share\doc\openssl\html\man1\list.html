<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>list</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-list, list - list algorithms and features</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl list</b> [<b>-help</b>] [<b>-1</b>] [<b>-commands</b>] [<b>-digest-commands</b>] [<b>-digest-algorithms</b>] [<b>-cipher-commands</b>] [<b>-cipher-algorithms</b>] [<b>-public-key-algorithms</b>] [<b>-public-key-methods</b>] [<b>-disabled</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command is used to generate list of algorithms or disabled features.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Display a usage message.</p>

</dd>
<dt id="pod-1"><b>-1</b></dt>
<dd>

<p>List the commands, digest-commands, or cipher-commands in a single column. If used, this option must be given first.</p>

</dd>
<dt id="commands"><b>-commands</b></dt>
<dd>

<p>Display a list of standard commands.</p>

</dd>
<dt id="digest-commands"><b>-digest-commands</b></dt>
<dd>

<p>Display a list of message digest commands, which are typically used as input to the <a href="../man1/dgst.html">dgst(1)</a> or <a href="../man1/speed.html">speed(1)</a> commands.</p>

</dd>
<dt id="digest-algorithms"><b>-digest-algorithms</b></dt>
<dd>

<p>Display a list of message digest algorithms. If a line is of the form foo =&gt; bar then <b>foo</b> is an alias for the official algorithm name, <b>bar</b>.</p>

</dd>
<dt id="cipher-commands"><b>-cipher-commands</b></dt>
<dd>

<p>Display a list of cipher commands, which are typically used as input to the <a href="../man1/dgst.html">dgst(1)</a> or <a href="../man1/speed.html">speed(1)</a> commands.</p>

</dd>
<dt id="cipher-algorithms"><b>-cipher-algorithms</b></dt>
<dd>

<p>Display a list of cipher algorithms. If a line is of the form foo =&gt; bar then <b>foo</b> is an alias for the official algorithm name, <b>bar</b>.</p>

</dd>
<dt id="public-key-algorithms"><b>-public-key-algorithms</b></dt>
<dd>

<p>Display a list of public key algorithms, with each algorithm as a block of multiple lines, all but the first are indented.</p>

</dd>
<dt id="public-key-methods"><b>-public-key-methods</b></dt>
<dd>

<p>Display a list of public key method OIDs: this also includes public key methods without an associated ASN.1 method, for example, KDF algorithms.</p>

</dd>
<dt id="disabled"><b>-disabled</b></dt>
<dd>

<p>Display a list of disabled features, those that were compiled out of the installation.</p>

</dd>
</dl>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016-2017 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


