/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the 'License');
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an 'AS IS' BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import Logger from './Logger';

const TAG: string = 'FpsBufferManager';

/**
 * FPS数据接口定义
 */
export interface FpsData {
  fpsCount: number;
  fpsBuffer: ArrayBuffer;
  timestamp: number;
}

/**
 * 观察者回调函数类型
 */
export type FpsDataObserver = (data: FpsData) => void;

/**
 * FPS状态信息接口（用于@State状态管理）
 */
export interface FpsStateInfo {
  fpsCount: number;
  bufferLength: number;
  timestamp: number;
  hasData: boolean;
}

/**
 * FpsBuffer全局状态管理器
 * 使用单例模式实现全局状态管理，支持ArrayBuffer类型数据
 * 提供类似AppStorage的API接口和状态监听功能
 */
export class FpsBufferManager {
  private static instance: FpsBufferManager;
  private fpsData: FpsData | null = null;
  private observers: Map<string, FpsDataObserver> = new Map();
  private observerIdCounter: number = 0;

  /**
   * 私有构造函数，确保单例模式
   */
  private constructor() {
    Logger.info(TAG, 'FpsBufferManager initialized');
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): FpsBufferManager {
    if (!FpsBufferManager.instance) {
      FpsBufferManager.instance = new FpsBufferManager();
    }
    return FpsBufferManager.instance;
  }

  /**
   * 设置FPS数据
   * @param fpsCount FPS计数
   * @param fpsBuffer FPS缓冲区数据
   */
  public setFpsData(fpsCount: number, fpsBuffer: ArrayBuffer): void {
    try {
      const newData: FpsData = {
        fpsCount,
        fpsBuffer,
        timestamp: Date.now()
      };

      this.fpsData = newData;
      Logger.info(TAG, `FPS data updated: count=${fpsCount}, bufferSize=${fpsBuffer.byteLength}`);

      // 通知所有观察者
      this.notifyObservers(newData);
    } catch (error) {
      Logger.error(TAG, `Failed to set FPS data: ${JSON.stringify(error)}`);
    }
  }

  /**
   * 获取当前FPS数据（返回引用，性能更好但需要注意不要修改）
   */
  public getFpsData(): FpsData | null {
    return this.fpsData;
  }

  /**
   * 获取FPS数据的深拷贝（安全但性能稍差）
   */
  public getFpsDataCopy(): FpsData | null {
    if (!this.fpsData) {
      return null;
    }

    try {
      // 深拷贝ArrayBuffer
      const bufferCopy = new ArrayBuffer(this.fpsData.fpsBuffer.byteLength);
      new Uint8Array(bufferCopy).set(new Uint8Array(this.fpsData.fpsBuffer));

      return {
        fpsCount: this.fpsData.fpsCount,
        fpsBuffer: bufferCopy,
        timestamp: this.fpsData.timestamp
      };
    } catch (error) {
      Logger.error(TAG, `Failed to copy FPS data: ${JSON.stringify(error)}`);
      return null;
    }
  }

  /**
   * 获取FPS计数
   */
  public getFpsCount(): number {
    return this.fpsData?.fpsCount ?? 0;
  }

  /**
   * 获取FPS缓冲区
   */
  public getFpsBuffer(): ArrayBuffer | null {
    return this.fpsData?.fpsBuffer ?? null;
  }

  /**
   * 获取FPS缓冲区的拷贝
   */
  public getFpsBufferCopy(): ArrayBuffer | null {
    if (!this.fpsData?.fpsBuffer) {
      return null;
    }

    try {
      const bufferCopy = new ArrayBuffer(this.fpsData.fpsBuffer.byteLength);
      new Uint8Array(bufferCopy).set(new Uint8Array(this.fpsData.fpsBuffer));
      return bufferCopy;
    } catch (error) {
      Logger.error(TAG, `Failed to copy FPS buffer: ${JSON.stringify(error)}`);
      return null;
    }
  }

  /**
   * 添加观察者
   * @param observer 观察者回调函数
   * @returns 观察者ID，用于后续移除观察者
   */
  public addObserver(observer: FpsDataObserver): string {
    const observerId = `observer_${++this.observerIdCounter}`;
    this.observers.set(observerId, observer);
    Logger.info(TAG, `Observer added: ${observerId}`);

    // 如果已有数据，立即通知新观察者
    if (this.fpsData) {
      try {
        observer(this.fpsData);
      } catch (error) {
        Logger.error(TAG, `Observer callback error: ${JSON.stringify(error)}`);
      }
    }

    return observerId;
  }

  /**
   * 移除观察者
   * @param observerId 观察者ID
   */
  public removeObserver(observerId: string): void {
    if (this.observers.delete(observerId)) {
      Logger.info(TAG, `Observer removed: ${observerId}`);
    } else {
      Logger.warn(TAG, `Observer not found: ${observerId}`);
    }
  }

  /**
   * 清除所有观察者
   */
  public clearObservers(): void {
    this.observers.clear();
    Logger.info(TAG, 'All observers cleared');
  }

  /**
   * 通知所有观察者
   */
  private notifyObservers(data: FpsData): void {
    this.observers.forEach((observer, observerId) => {
      try {
        observer(data);
      } catch (error) {
        Logger.error(TAG, `Observer ${observerId} callback error: ${JSON.stringify(error)}`);
      }
    });
  }

  /**
   * 清除FPS数据
   */
  public clearFpsData(): void {
    this.fpsData = null;
    Logger.info(TAG, 'FPS data cleared');
  }

  /**
   * 获取观察者数量
   */
  public getObserverCount(): number {
    return this.observers.size;
  }

  /**
   * 检查是否有FPS数据
   */
  public hasFpsData(): boolean {
    return this.fpsData !== null;
  }

  /**
   * 获取数据时间戳
   */
  public getTimestamp(): number {
    return this.fpsData?.timestamp ?? 0;
  }

  /**
   * 获取FPS状态信息（用于@State状态管理）
   */
  public getFpsStateInfo(): FpsStateInfo {
    return {
      fpsCount: this.fpsData?.fpsCount ?? 0,
      bufferLength: this.fpsData?.fpsBuffer.byteLength ?? 0,
      timestamp: this.fpsData?.timestamp ?? 0,
      hasData: this.fpsData !== null
    };
  }
}

/**
 * 全局FpsBufferManager实例
 * 提供简洁的全局访问方式
 */
export const fpsBufferManager = FpsBufferManager.getInstance();

/**
 * 便捷的工具函数
 */
export class FpsBufferUtils {
  /**
   * 快速设置FPS数据
   */
  static setFpsData(fpsCount: number, fpsBuffer: ArrayBuffer): void {
    fpsBufferManager.setFpsData(fpsCount, fpsBuffer);
  }

  /**
   * 快速获取FPS计数
   */
  static getFpsCount(): number {
    return fpsBufferManager.getFpsCount();
  }

  /**
   * 快速获取FPS缓冲区
   */
  static getFpsBuffer(): ArrayBuffer | null|undefined {
    return fpsBufferManager.getFpsBuffer();
  }

  /**
   * 快速获取FPS缓冲区拷贝
   */
  static getFpsBufferCopy(): ArrayBuffer | null|undefined {
    return fpsBufferManager.getFpsBufferCopy();
  }

  /**
   * 快速添加观察者
   */
  static watch(observer: FpsDataObserver): string {
    return fpsBufferManager.addObserver(observer);
  }

  /**
   * 快速移除观察者
   */
  static unwatch(observerId: string): void {
    fpsBufferManager.removeObserver(observerId);
  }

  /**
   * 获取FPS状态信息
   */
  static getFpsStateInfo(): FpsStateInfo {
    return fpsBufferManager.getFpsStateInfo();
  }
}
