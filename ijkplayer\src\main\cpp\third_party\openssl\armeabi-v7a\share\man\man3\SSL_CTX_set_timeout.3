.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_TIMEOUT 3"
.TH SSL_CTX_SET_TIMEOUT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_timeout, SSL_CTX_get_timeout \- manipulate timeout values for session caching
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_set_timeout(SSL_CTX *ctx, long t);
\& long SSL_CTX_get_timeout(SSL_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_timeout()\fR sets the timeout for newly created sessions for
\&\fBctx\fR to \fBt\fR. The timeout value \fBt\fR must be given in seconds.
.PP
\&\fBSSL_CTX_get_timeout()\fR returns the currently set timeout value for \fBctx\fR.
.SH NOTES
.IX Header "NOTES"
Whenever a new session is created, it is assigned a maximum lifetime. This
lifetime is specified by storing the creation time of the session and the
timeout value valid at this time. If the actual time is later than creation
time plus timeout, the session is not reused.
.PP
Due to this realization, all sessions behave according to the timeout value
valid at the time of the session negotiation. Changes of the timeout value
do not affect already established sessions.
.PP
The expiration time of a single session can be modified using the
\&\fBSSL_SESSION_get_time\fR\|(3) family of functions.
.PP
Expired sessions are removed from the internal session cache, whenever
\&\fBSSL_CTX_flush_sessions\fR\|(3) is called, either
directly by the application or automatically (see
\&\fBSSL_CTX_set_session_cache_mode\fR\|(3))
.PP
The default value for session timeout is decided on a per protocol
basis, see \fBSSL_get_default_timeout\fR\|(3).
All currently supported protocols have the same default timeout value
of 300 seconds.
.PP
This timeout value is used as the ticket lifetime hint for stateless session
tickets. It is also used as the timeout value within the ticket itself.
.PP
For TLSv1.3, RFC8446 limits transmission of this value to 1 week (604800
seconds).
.PP
For TLSv1.2, tickets generated during an initial handshake use the value
as specified. Tickets generated during a resumed handshake have a value
of 0 for the ticket lifetime hint.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_timeout()\fR returns the previously set timeout value.
.PP
\&\fBSSL_CTX_get_timeout()\fR returns the currently set timeout value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_set_session_cache_mode\fR\|(3),
\&\fBSSL_SESSION_get_time\fR\|(3),
\&\fBSSL_CTX_flush_sessions\fR\|(3),
\&\fBSSL_get_default_timeout\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
