.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_NUM_TICKETS 3"
.TH SSL_CTX_SET_NUM_TICKETS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_set_num_tickets,
SSL_get_num_tickets,
SSL_CTX_set_num_tickets,
SSL_CTX_get_num_tickets
\&\- control the number of TLSv1.3 session tickets that are issued
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_set_num_tickets(SSL *s, size_t num_tickets);
\& size_t SSL_get_num_tickets(SSL *s);
\& int SSL_CTX_set_num_tickets(SSL_CTX *ctx, size_t num_tickets);
\& size_t SSL_CTX_get_num_tickets(SSL_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_num_tickets()\fR and \fBSSL_set_num_tickets()\fR can be called for a server
application and set the number of TLSv1.3 session tickets that will be sent to
the client after a full handshake. Set the desired value (which could be 0) in
the \fBnum_tickets\fR argument. Typically these functions should be called before
the start of the handshake.
.PP
The default number of tickets is 2. Following a resumption the number of tickets
issued will never be more than 1 regardless of the value set via
\&\fBSSL_set_num_tickets()\fR or \fBSSL_CTX_set_num_tickets()\fR. If \fBnum_tickets\fR is set to
0 then no tickets will be issued for either a normal connection or a resumption.
.PP
Tickets are also issued on receipt of a post-handshake certificate from the
client following a request by the server using
\&\fBSSL_verify_client_post_handshake\fR\|(3). These new tickets will be associated
with the updated client identity (i.e. including their certificate and
verification status). The number of tickets issued will normally be the same as
was used for the initial handshake. If the initial handshake was a full
handshake then \fBSSL_set_num_tickets()\fR can be called again prior to calling
\&\fBSSL_verify_client_post_handshake()\fR to update the number of tickets that will be
sent.
.PP
\&\fBSSL_CTX_get_num_tickets()\fR and \fBSSL_get_num_tickets()\fR return the number of
tickets set by a previous call to \fBSSL_CTX_set_num_tickets()\fR or
\&\fBSSL_set_num_tickets()\fR, or 2 if no such call has been made.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_num_tickets()\fR and \fBSSL_set_num_tickets()\fR return 1 on success or 0 on
failure.
.PP
\&\fBSSL_CTX_get_num_tickets()\fR and \fBSSL_get_num_tickets()\fR return the number of tickets
that have been previously set.
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
