<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>cms</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXIT-CODES">EXIT CODES</a></li>
  <li><a href="#COMPATIBILITY-WITH-PKCS-7-format">COMPATIBILITY WITH PKCS#7 format.</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-cms, cms - CMS utility</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>cms</b> [<b>-help</b>] [<b>-encrypt</b>] [<b>-decrypt</b>] [<b>-sign</b>] [<b>-verify</b>] [<b>-cmsout</b>] [<b>-resign</b>] [<b>-data_create</b>] [<b>-data_out</b>] [<b>-digest_create</b>] [<b>-digest_verify</b>] [<b>-compress</b>] [<b>-uncompress</b>] [<b>-EncryptedData_encrypt</b>] [<b>-sign_receipt</b>] [<b>-verify_receipt receipt</b>] [<b>-in filename</b>] [<b>-inform SMIME|PEM|DER</b>] [<b>-rctform SMIME|PEM|DER</b>] [<b>-out filename</b>] [<b>-outform SMIME|PEM|DER</b>] [<b>-stream -indef -noindef</b>] [<b>-noindef</b>] [<b>-content filename</b>] [<b>-text</b>] [<b>-noout</b>] [<b>-print</b>] [<b>-CAfile file</b>] [<b>-CApath dir</b>] [<b>-no-CAfile</b>] [<b>-no-CApath</b>] [<b>-attime timestamp</b>] [<b>-check_ss_sig</b>] [<b>-crl_check</b>] [<b>-crl_check_all</b>] [<b>-explicit_policy</b>] [<b>-extended_crl</b>] [<b>-ignore_critical</b>] [<b>-inhibit_any</b>] [<b>-inhibit_map</b>] [<b>-no_check_time</b>] [<b>-partial_chain</b>] [<b>-policy arg</b>] [<b>-policy_check</b>] [<b>-policy_print</b>] [<b>-purpose purpose</b>] [<b>-suiteB_128</b>] [<b>-suiteB_128_only</b>] [<b>-suiteB_192</b>] [<b>-trusted_first</b>] [<b>-no_alt_chains</b>] [<b>-use_deltas</b>] [<b>-auth_level num</b>] [<b>-verify_depth num</b>] [<b>-verify_email email</b>] [<b>-verify_hostname hostname</b>] [<b>-verify_ip ip</b>] [<b>-verify_name name</b>] [<b>-x509_strict</b>] [<b>-md digest</b>] [<b>-<i>cipher</i></b>] [<b>-nointern</b>] [<b>-noverify</b>] [<b>-nocerts</b>] [<b>-noattr</b>] [<b>-nosmimecap</b>] [<b>-binary</b>] [<b>-crlfeol</b>] [<b>-asciicrlf</b>] [<b>-nodetach</b>] [<b>-certfile file</b>] [<b>-certsout file</b>] [<b>-signer file</b>] [<b>-recip file</b>] [<b>-keyid</b>] [<b>-receipt_request_all</b>] [<b>-receipt_request_first</b>] [<b>-receipt_request_from emailaddress</b>] [<b>-receipt_request_to emailaddress</b>] [<b>-receipt_request_print</b>] [<b>-secretkey key</b>] [<b>-secretkeyid id</b>] [<b>-econtent_type type</b>] [<b>-inkey file</b>] [<b>-keyopt name:parameter</b>] [<b>-passin arg</b>] [<b>-rand file...</b>] [<b>-writerand file</b>] [<b>cert.pem...</b>] [<b>-to addr</b>] [<b>-from addr</b>] [<b>-subject subj</b>] [cert.pem]...</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>cms</b> command handles S/MIME v3.1 mail. It can encrypt, decrypt, sign and verify, compress and uncompress S/MIME messages.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<p>There are fourteen operation options that set the type of operation to be performed. The meaning of the other options varies according to the operation type.</p>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="encrypt"><b>-encrypt</b></dt>
<dd>

<p>Encrypt mail for the given recipient certificates. Input file is the message to be encrypted. The output file is the encrypted mail in MIME format. The actual CMS type is &lt;B&gt;EnvelopedData&lt;B&gt;.</p>

<p>Note that no revocation check is done for the recipient cert, so if that key has been compromised, others may be able to decrypt the text.</p>

</dd>
<dt id="decrypt"><b>-decrypt</b></dt>
<dd>

<p>Decrypt mail using the supplied certificate and private key. Expects an encrypted mail message in MIME format for the input file. The decrypted mail is written to the output file.</p>

</dd>
<dt id="debug_decrypt"><b>-debug_decrypt</b></dt>
<dd>

<p>This option sets the <b>CMS_DEBUG_DECRYPT</b> flag. This option should be used with caution: see the notes section below.</p>

</dd>
<dt id="sign"><b>-sign</b></dt>
<dd>

<p>Sign mail using the supplied certificate and private key. Input file is the message to be signed. The signed message in MIME format is written to the output file.</p>

</dd>
<dt id="verify"><b>-verify</b></dt>
<dd>

<p>Verify signed mail. Expects a signed mail message on input and outputs the signed data. Both clear text and opaque signing is supported.</p>

</dd>
<dt id="cmsout"><b>-cmsout</b></dt>
<dd>

<p>Takes an input message and writes out a PEM encoded CMS structure.</p>

</dd>
<dt id="resign"><b>-resign</b></dt>
<dd>

<p>Resign a message: take an existing message and one or more new signers.</p>

</dd>
<dt id="data_create"><b>-data_create</b></dt>
<dd>

<p>Create a CMS <b>Data</b> type.</p>

</dd>
<dt id="data_out"><b>-data_out</b></dt>
<dd>

<p><b>Data</b> type and output the content.</p>

</dd>
<dt id="digest_create"><b>-digest_create</b></dt>
<dd>

<p>Create a CMS <b>DigestedData</b> type.</p>

</dd>
<dt id="digest_verify"><b>-digest_verify</b></dt>
<dd>

<p>Verify a CMS <b>DigestedData</b> type and output the content.</p>

</dd>
<dt id="compress"><b>-compress</b></dt>
<dd>

<p>Create a CMS <b>CompressedData</b> type. OpenSSL must be compiled with <b>zlib</b> support for this option to work, otherwise it will output an error.</p>

</dd>
<dt id="uncompress"><b>-uncompress</b></dt>
<dd>

<p>Uncompress a CMS <b>CompressedData</b> type and output the content. OpenSSL must be compiled with <b>zlib</b> support for this option to work, otherwise it will output an error.</p>

</dd>
<dt id="EncryptedData_encrypt"><b>-EncryptedData_encrypt</b></dt>
<dd>

<p>Encrypt content using supplied symmetric key and algorithm using a CMS <b>EncryptedData</b> type and output the content.</p>

</dd>
<dt id="sign_receipt"><b>-sign_receipt</b></dt>
<dd>

<p>Generate and output a signed receipt for the supplied message. The input message <b>must</b> contain a signed receipt request. Functionality is otherwise similar to the <b>-sign</b> operation.</p>

</dd>
<dt id="verify_receipt-receipt"><b>-verify_receipt receipt</b></dt>
<dd>

<p>Verify a signed receipt in filename <b>receipt</b>. The input message <b>must</b> contain the original receipt request. Functionality is otherwise similar to the <b>-verify</b> operation.</p>

</dd>
<dt id="in-filename"><b>-in filename</b></dt>
<dd>

<p>The input message to be encrypted or signed or the message to be decrypted or verified.</p>

</dd>
<dt id="inform-SMIME-PEM-DER"><b>-inform SMIME|PEM|DER</b></dt>
<dd>

<p>This specifies the input format for the CMS structure. The default is <b>SMIME</b> which reads an S/MIME format message. <b>PEM</b> and <b>DER</b> format change this to expect PEM and DER format CMS structures instead. This currently only affects the input format of the CMS structure, if no CMS structure is being input (for example with <b>-encrypt</b> or <b>-sign</b>) this option has no effect.</p>

</dd>
<dt id="rctform-SMIME-PEM-DER"><b>-rctform SMIME|PEM|DER</b></dt>
<dd>

<p>Specify the format for a signed receipt for use with the <b>-receipt_verify</b> operation.</p>

</dd>
<dt id="out-filename"><b>-out filename</b></dt>
<dd>

<p>The message text that has been decrypted or verified or the output MIME format message that has been signed or verified.</p>

</dd>
<dt id="outform-SMIME-PEM-DER"><b>-outform SMIME|PEM|DER</b></dt>
<dd>

<p>This specifies the output format for the CMS structure. The default is <b>SMIME</b> which writes an S/MIME format message. <b>PEM</b> and <b>DER</b> format change this to write PEM and DER format CMS structures instead. This currently only affects the output format of the CMS structure, if no CMS structure is being output (for example with <b>-verify</b> or <b>-decrypt</b>) this option has no effect.</p>

</dd>
<dt id="stream--indef--noindef"><b>-stream -indef -noindef</b></dt>
<dd>

<p>The <b>-stream</b> and <b>-indef</b> options are equivalent and enable streaming I/O for encoding operations. This permits single pass processing of data without the need to hold the entire contents in memory, potentially supporting very large files. Streaming is automatically set for S/MIME signing with detached data if the output format is <b>SMIME</b> it is currently off by default for all other operations.</p>

</dd>
<dt id="noindef"><b>-noindef</b></dt>
<dd>

<p>Disable streaming I/O where it would produce and indefinite length constructed encoding. This option currently has no effect. In future streaming will be enabled by default on all relevant operations and this option will disable it.</p>

</dd>
<dt id="content-filename"><b>-content filename</b></dt>
<dd>

<p>This specifies a file containing the detached content, this is only useful with the <b>-verify</b> command. This is only usable if the CMS structure is using the detached signature form where the content is not included. This option will override any content if the input format is S/MIME and it uses the multipart/signed MIME content type.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>This option adds plain text (text/plain) MIME headers to the supplied message if encrypting or signing. If decrypting or verifying it strips off text headers: if the decrypted or verified message is not of MIME type text/plain then an error occurs.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>For the <b>-cmsout</b> operation do not output the parsed CMS structure. This is useful when combined with the <b>-print</b> option or if the syntax of the CMS structure is being checked.</p>

</dd>
<dt id="print"><b>-print</b></dt>
<dd>

<p>For the <b>-cmsout</b> operation print out all fields of the CMS structure. This is mainly useful for testing purposes.</p>

</dd>
<dt id="CAfile-file"><b>-CAfile file</b></dt>
<dd>

<p>A file containing trusted CA certificates, only used with <b>-verify</b>.</p>

</dd>
<dt id="CApath-dir"><b>-CApath dir</b></dt>
<dd>

<p>A directory containing trusted CA certificates, only used with <b>-verify</b>. This directory must be a standard certificate directory: that is a hash of each subject name (using <b>x509 -hash</b>) should be linked to each certificate.</p>

</dd>
<dt id="no-CAfile"><b>-no-CAfile</b></dt>
<dd>

<p>Do not load the trusted CA certificates from the default file location</p>

</dd>
<dt id="no-CApath"><b>-no-CApath</b></dt>
<dd>

<p>Do not load the trusted CA certificates from the default directory location</p>

</dd>
<dt id="md-digest"><b>-md digest</b></dt>
<dd>

<p>Digest algorithm to use when signing or resigning. If not present then the default digest algorithm for the signing key will be used (usually SHA1).</p>

</dd>
<dt id="cipher"><b>-<i>cipher</i></b></dt>
<dd>

<p>The encryption algorithm to use. For example triple DES (168 bits) - <b>-des3</b> or 256 bit AES - <b>-aes256</b>. Any standard algorithm name (as used by the EVP_get_cipherbyname() function) can also be used preceded by a dash, for example <b>-aes-128-cbc</b>. See <a href="../man1/enc.html">enc(1)</a> for a list of ciphers supported by your version of OpenSSL.</p>

<p>If not specified triple DES is used. Only used with <b>-encrypt</b> and <b>-EncryptedData_create</b> commands.</p>

</dd>
<dt id="nointern"><b>-nointern</b></dt>
<dd>

<p>When verifying a message normally certificates (if any) included in the message are searched for the signing certificate. With this option only the certificates specified in the <b>-certfile</b> option are used. The supplied certificates can still be used as untrusted CAs however.</p>

</dd>
<dt id="noverify"><b>-noverify</b></dt>
<dd>

<p>Do not verify the signers certificate of a signed message.</p>

</dd>
<dt id="nocerts"><b>-nocerts</b></dt>
<dd>

<p>When signing a message the signer&#39;s certificate is normally included with this option it is excluded. This will reduce the size of the signed message but the verifier must have a copy of the signers certificate available locally (passed using the <b>-certfile</b> option for example).</p>

</dd>
<dt id="noattr"><b>-noattr</b></dt>
<dd>

<p>Normally when a message is signed a set of attributes are included which include the signing time and supported symmetric algorithms. With this option they are not included.</p>

</dd>
<dt id="nosmimecap"><b>-nosmimecap</b></dt>
<dd>

<p>Exclude the list of supported algorithms from signed attributes, other options such as signing time and content type are still included.</p>

</dd>
<dt id="binary"><b>-binary</b></dt>
<dd>

<p>Normally the input message is converted to &quot;canonical&quot; format which is effectively using CR and LF as end of line: as required by the S/MIME specification. When this option is present no translation occurs. This is useful when handling binary data which may not be in MIME format.</p>

</dd>
<dt id="crlfeol"><b>-crlfeol</b></dt>
<dd>

<p>Normally the output file uses a single <b>LF</b> as end of line. When this option is present <b>CRLF</b> is used instead.</p>

</dd>
<dt id="asciicrlf"><b>-asciicrlf</b></dt>
<dd>

<p>When signing use ASCII CRLF format canonicalisation. This strips trailing whitespace from all lines, deletes trailing blank lines at EOF and sets the encapsulated content type. This option is normally used with detached content and an output signature format of DER. This option is not normally needed when verifying as it is enabled automatically if the encapsulated content format is detected.</p>

</dd>
<dt id="nodetach"><b>-nodetach</b></dt>
<dd>

<p>When signing a message use opaque signing: this form is more resistant to translation by mail relays but it cannot be read by mail agents that do not support S/MIME. Without this option cleartext signing with the MIME type multipart/signed is used.</p>

</dd>
<dt id="certfile-file"><b>-certfile file</b></dt>
<dd>

<p>Allows additional certificates to be specified. When signing these will be included with the message. When verifying these will be searched for the signers certificates. The certificates should be in PEM format.</p>

</dd>
<dt id="certsout-file"><b>-certsout file</b></dt>
<dd>

<p>Any certificates contained in the message are written to <b>file</b>.</p>

</dd>
<dt id="signer-file"><b>-signer file</b></dt>
<dd>

<p>A signing certificate when signing or resigning a message, this option can be used multiple times if more than one signer is required. If a message is being verified then the signers certificates will be written to this file if the verification was successful.</p>

</dd>
<dt id="recip-file"><b>-recip file</b></dt>
<dd>

<p>When decrypting a message this specifies the recipients certificate. The certificate must match one of the recipients of the message or an error occurs.</p>

<p>When encrypting a message this option may be used multiple times to specify each recipient. This form <b>must</b> be used if customised parameters are required (for example to specify RSA-OAEP).</p>

<p>Only certificates carrying RSA, Diffie-Hellman or EC keys are supported by this option.</p>

</dd>
<dt id="keyid"><b>-keyid</b></dt>
<dd>

<p>Use subject key identifier to identify certificates instead of issuer name and serial number. The supplied certificate <b>must</b> include a subject key identifier extension. Supported by <b>-sign</b> and <b>-encrypt</b> options.</p>

</dd>
<dt id="receipt_request_all--receipt_request_first"><b>-receipt_request_all</b>, <b>-receipt_request_first</b></dt>
<dd>

<p>For <b>-sign</b> option include a signed receipt request. Indicate requests should be provided by all recipient or first tier recipients (those mailed directly and not from a mailing list). Ignored it <b>-receipt_request_from</b> is included.</p>

</dd>
<dt id="receipt_request_from-emailaddress"><b>-receipt_request_from emailaddress</b></dt>
<dd>

<p>For <b>-sign</b> option include a signed receipt request. Add an explicit email address where receipts should be supplied.</p>

</dd>
<dt id="receipt_request_to-emailaddress"><b>-receipt_request_to emailaddress</b></dt>
<dd>

<p>Add an explicit email address where signed receipts should be sent to. This option <b>must</b> but supplied if a signed receipt it requested.</p>

</dd>
<dt id="receipt_request_print"><b>-receipt_request_print</b></dt>
<dd>

<p>For the <b>-verify</b> operation print out the contents of any signed receipt requests.</p>

</dd>
<dt id="secretkey-key"><b>-secretkey key</b></dt>
<dd>

<p>Specify symmetric key to use. The key must be supplied in hex format and be consistent with the algorithm used. Supported by the <b>-EncryptedData_encrypt</b> <b>-EncryptedData_decrypt</b>, <b>-encrypt</b> and <b>-decrypt</b> options. When used with <b>-encrypt</b> or <b>-decrypt</b> the supplied key is used to wrap or unwrap the content encryption key using an AES key in the <b>KEKRecipientInfo</b> type.</p>

</dd>
<dt id="secretkeyid-id"><b>-secretkeyid id</b></dt>
<dd>

<p>The key identifier for the supplied symmetric key for <b>KEKRecipientInfo</b> type. This option <b>must</b> be present if the <b>-secretkey</b> option is used with <b>-encrypt</b>. With <b>-decrypt</b> operations the <b>id</b> is used to locate the relevant key if it is not supplied then an attempt is used to decrypt any <b>KEKRecipientInfo</b> structures.</p>

</dd>
<dt id="econtent_type-type"><b>-econtent_type type</b></dt>
<dd>

<p>Set the encapsulated content type to <b>type</b> if not supplied the <b>Data</b> type is used. The <b>type</b> argument can be any valid OID name in either text or numerical format.</p>

</dd>
<dt id="inkey-file"><b>-inkey file</b></dt>
<dd>

<p>The private key to use when signing or decrypting. This must match the corresponding certificate. If this option is not specified then the private key must be included in the certificate file specified with the <b>-recip</b> or <b>-signer</b> file. When signing this option can be used multiple times to specify successive keys.</p>

</dd>
<dt id="keyopt-name:opt"><b>-keyopt name:opt</b></dt>
<dd>

<p>For signing and encryption this option can be used multiple times to set customised parameters for the preceding key or certificate. It can currently be used to set RSA-PSS for signing, RSA-OAEP for encryption or to modify default parameters for ECDH.</p>

</dd>
<dt id="passin-arg"><b>-passin arg</b></dt>
<dd>

<p>The private key password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="rand-file"><b>-rand file...</b></dt>
<dd>

<p>A file or files containing random data used to seed the random number generator. Multiple files can be specified separated by an OS-dependent character. The separator is <b>;</b> for MS-Windows, <b>,</b> for OpenVMS, and <b>:</b> for all others.</p>

</dd>
<dt id="writerand-file">[<b>-writerand file</b>]</dt>
<dd>

<p>Writes random data to the specified <i>file</i> upon exit. This can be used with a subsequent <b>-rand</b> flag.</p>

</dd>
<dt id="cert.pem"><b>cert.pem...</b></dt>
<dd>

<p>One or more certificates of message recipients: used when encrypting a message.</p>

</dd>
<dt id="to--from--subject"><b>-to, -from, -subject</b></dt>
<dd>

<p>The relevant mail headers. These are included outside the signed portion of a message so they may be included manually. If signing then many S/MIME mail clients check the signers certificate&#39;s email address matches that specified in the From: address.</p>

</dd>
<dt id="attime--check_ss_sig--crl_check--crl_check_all--explicit_policy--extended_crl--ignore_critical--inhibit_any--inhibit_map--no_alt_chains--no_check_time--partial_chain--policy--policy_check--policy_print--purpose--suiteB_128--suiteB_128_only--suiteB_192--trusted_first--use_deltas--auth_level--verify_depth--verify_email--verify_hostname--verify_ip--verify_name--x509_strict"><b>-attime</b>, <b>-check_ss_sig</b>, <b>-crl_check</b>, <b>-crl_check_all</b>, <b>-explicit_policy</b>, <b>-extended_crl</b>, <b>-ignore_critical</b>, <b>-inhibit_any</b>, <b>-inhibit_map</b>, <b>-no_alt_chains</b>, <b>-no_check_time</b>, <b>-partial_chain</b>, <b>-policy</b>, <b>-policy_check</b>, <b>-policy_print</b>, <b>-purpose</b>, <b>-suiteB_128</b>, <b>-suiteB_128_only</b>, <b>-suiteB_192</b>, <b>-trusted_first</b>, <b>-use_deltas</b>, <b>-auth_level</b>, <b>-verify_depth</b>, <b>-verify_email</b>, <b>-verify_hostname</b>, <b>-verify_ip</b>, <b>-verify_name</b>, <b>-x509_strict</b></dt>
<dd>

<p>Set various certificate chain validation options. See the <a href="../man1/verify.html">verify(1)</a> manual page for details.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>The MIME message must be sent without any blank lines between the headers and the output. Some mail programs will automatically add a blank line. Piping the mail directly to sendmail is one way to achieve the correct format.</p>

<p>The supplied message to be signed or encrypted must include the necessary MIME headers or many S/MIME clients won&#39;t display it properly (if at all). You can use the <b>-text</b> option to automatically add plain text headers.</p>

<p>A &quot;signed and encrypted&quot; message is one where a signed message is then encrypted. This can be produced by encrypting an already signed message: see the examples section.</p>

<p>This version of the program only allows one signer per message but it will verify multiple signers on received messages. Some S/MIME clients choke if a message contains multiple signers. It is possible to sign messages &quot;in parallel&quot; by signing an already signed message.</p>

<p>The options <b>-encrypt</b> and <b>-decrypt</b> reflect common usage in S/MIME clients. Strictly speaking these process CMS enveloped data: CMS encrypted data is used for other purposes.</p>

<p>The <b>-resign</b> option uses an existing message digest when adding a new signer. This means that attributes must be present in at least one existing signer using the same message digest or this operation will fail.</p>

<p>The <b>-stream</b> and <b>-indef</b> options enable streaming I/O support. As a result the encoding is BER using indefinite length constructed encoding and no longer DER. Streaming is supported for the <b>-encrypt</b> operation and the <b>-sign</b> operation if the content is not detached.</p>

<p>Streaming is always used for the <b>-sign</b> operation with detached data but since the content is no longer part of the CMS structure the encoding remains DER.</p>

<p>If the <b>-decrypt</b> option is used without a recipient certificate then an attempt is made to locate the recipient by trying each potential recipient in turn using the supplied private key. To thwart the MMA attack (Bleichenbacher&#39;s attack on PKCS #1 v1.5 RSA padding) all recipients are tried whether they succeed or not and if no recipients match the message is &quot;decrypted&quot; using a random key which will typically output garbage. The <b>-debug_decrypt</b> option can be used to disable the MMA attack protection and return an error if no recipient can be found: this option should be used with caution. For a fuller description see <a href="../man3/CMS_decrypt.html">CMS_decrypt(3)</a>).</p>

<h1 id="EXIT-CODES">EXIT CODES</h1>

<dl>

<dt id="pod0">0</dt>
<dd>

<p>The operation was completely successfully.</p>

</dd>
<dt id="pod1">1</dt>
<dd>

<p>An error occurred parsing the command options.</p>

</dd>
<dt id="pod2">2</dt>
<dd>

<p>One of the input files could not be read.</p>

</dd>
<dt id="pod3">3</dt>
<dd>

<p>An error occurred creating the CMS file or when reading the MIME message.</p>

</dd>
<dt id="pod4">4</dt>
<dd>

<p>An error occurred decrypting or verifying the message.</p>

</dd>
<dt id="pod5">5</dt>
<dd>

<p>The message was verified correctly but an error occurred writing out the signers certificates.</p>

</dd>
</dl>

<h1 id="COMPATIBILITY-WITH-PKCS-7-format">COMPATIBILITY WITH PKCS#7 format.</h1>

<p>The <b>smime</b> utility can only process the older <b>PKCS#7</b> format. The <b>cms</b> utility supports Cryptographic Message Syntax format. Use of some features will result in messages which cannot be processed by applications which only support the older format. These are detailed below.</p>

<p>The use of the <b>-keyid</b> option with <b>-sign</b> or <b>-encrypt</b>.</p>

<p>The <b>-outform PEM</b> option uses different headers.</p>

<p>The <b>-compress</b> option.</p>

<p>The <b>-secretkey</b> option when used with <b>-encrypt</b>.</p>

<p>The use of PSS with <b>-sign</b>.</p>

<p>The use of OAEP or non-RSA keys with <b>-encrypt</b>.</p>

<p>Additionally the <b>-EncryptedData_create</b> and <b>-data_create</b> type cannot be processed by the older <b>smime</b> command.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Create a cleartext signed message:</p>

<pre><code>openssl cms -sign -in message.txt -text -out mail.msg \
       -signer mycert.pem</code></pre>

<p>Create an opaque signed message</p>

<pre><code>openssl cms -sign -in message.txt -text -out mail.msg -nodetach \
       -signer mycert.pem</code></pre>

<p>Create a signed message, include some additional certificates and read the private key from another file:</p>

<pre><code>openssl cms -sign -in in.txt -text -out mail.msg \
       -signer mycert.pem -inkey mykey.pem -certfile mycerts.pem</code></pre>

<p>Create a signed message with two signers, use key identifier:</p>

<pre><code>openssl cms -sign -in message.txt -text -out mail.msg \
       -signer mycert.pem -signer othercert.pem -keyid</code></pre>

<p>Send a signed message under Unix directly to sendmail, including headers:</p>

<pre><code>openssl cms -sign -in in.txt -text -signer mycert.pem \
       -from <EMAIL> -to someone@somewhere \
       -subject &quot;Signed message&quot; | sendmail someone@somewhere</code></pre>

<p>Verify a message and extract the signer&#39;s certificate if successful:</p>

<pre><code>openssl cms -verify -in mail.msg -signer user.pem -out signedtext.txt</code></pre>

<p>Send encrypted mail using triple DES:</p>

<pre><code>openssl cms -encrypt -in in.txt -from <EMAIL> \
       -to someone@somewhere -subject &quot;Encrypted message&quot; \
       -des3 user.pem -out mail.msg</code></pre>

<p>Sign and encrypt mail:</p>

<pre><code>openssl cms -sign -in ml.txt -signer my.pem -text \
       | openssl cms -encrypt -out mail.msg \
       -from <EMAIL> -to someone@somewhere \
       -subject &quot;Signed and Encrypted message&quot; -des3 user.pem</code></pre>

<p>Note: the encryption command does not include the <b>-text</b> option because the message being encrypted already has MIME headers.</p>

<p>Decrypt mail:</p>

<pre><code>openssl cms -decrypt -in mail.msg -recip mycert.pem -inkey key.pem</code></pre>

<p>The output from Netscape form signing is a PKCS#7 structure with the detached signature format. You can use this program to verify the signature by line wrapping the base64 encoded structure and surrounding it with:</p>

<pre><code>-----BEGIN PKCS7-----
-----END PKCS7-----</code></pre>

<p>and using the command,</p>

<pre><code>openssl cms -verify -inform PEM -in signature.pem -content content.txt</code></pre>

<p>alternatively you can base64 decode the signature and use</p>

<pre><code>openssl cms -verify -inform DER -in signature.der -content content.txt</code></pre>

<p>Create an encrypted message using 128 bit Camellia:</p>

<pre><code>openssl cms -encrypt -in plain.txt -camellia128 -out mail.msg cert.pem</code></pre>

<p>Add a signer to an existing message:</p>

<pre><code>openssl cms -resign -in mail.msg -signer newsign.pem -out mail2.msg</code></pre>

<p>Sign mail using RSA-PSS:</p>

<pre><code>openssl cms -sign -in message.txt -text -out mail.msg \
       -signer mycert.pem -keyopt rsa_padding_mode:pss</code></pre>

<p>Create encrypted mail using RSA-OAEP:</p>

<pre><code>openssl cms -encrypt -in plain.txt -out mail.msg \
       -recip cert.pem -keyopt rsa_padding_mode:oaep</code></pre>

<p>Use SHA256 KDF with an ECDH certificate:</p>

<pre><code>openssl cms -encrypt -in plain.txt -out mail.msg \
       -recip ecdhcert.pem -keyopt ecdh_kdf_md:sha256</code></pre>

<h1 id="BUGS">BUGS</h1>

<p>The MIME parser isn&#39;t very clever: it seems to handle most messages that I&#39;ve thrown at it but it may choke on others.</p>

<p>The code currently will only write out the signer&#39;s certificate to a file: if the signer has a separate encryption certificate this must be manually extracted. There should be some heuristic that determines the correct encryption certificate.</p>

<p>Ideally a database should be maintained of a certificates for each email address.</p>

<p>The code doesn&#39;t currently take note of the permitted symmetric encryption algorithms as supplied in the SMIMECapabilities signed attribute. this means the user has to manually include the correct encryption algorithm. It should store the list of permitted ciphers in a database and only use those.</p>

<p>No revocation checking is done on the signer&#39;s certificate.</p>

<p>The <b>-binary</b> option does not work correctly when processing text input which (contrary to the S/MIME specification) uses LF rather than CRLF line endings.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The use of multiple <b>-signer</b> options and the <b>-resign</b> command were first added in OpenSSL 1.0.0.</p>

<p>The <b>keyopt</b> option was added in OpenSSL 1.0.2.</p>

<p>Support for RSA-OAEP and RSA-PSS was added in OpenSSL 1.0.2.</p>

<p>The use of non-RSA keys with <b>-encrypt</b> and <b>-decrypt</b> was added in OpenSSL 1.0.2.</p>

<p>The -no_alt_chains option was added in OpenSSL 1.0.2b.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2008-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


