.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "REQ 1"
.TH REQ 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-req,
req \- PKCS#10 certificate request and certificate generating utility
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBreq\fR
[\fB\-help\fR]
[\fB\-inform PEM|DER\fR]
[\fB\-outform PEM|DER\fR]
[\fB\-in filename\fR]
[\fB\-passin arg\fR]
[\fB\-out filename\fR]
[\fB\-passout arg\fR]
[\fB\-text\fR]
[\fB\-pubkey\fR]
[\fB\-noout\fR]
[\fB\-verify\fR]
[\fB\-modulus\fR]
[\fB\-new\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-newkey rsa:bits\fR]
[\fB\-newkey alg:file\fR]
[\fB\-nodes\fR]
[\fB\-key filename\fR]
[\fB\-keyform PEM|DER\fR]
[\fB\-keyout filename\fR]
[\fB\-keygen_engine id\fR]
[\fB\-\fR\f(BIdigest\fR]
[\fB\-config filename\fR]
[\fB\-multivalue\-rdn\fR]
[\fB\-x509\fR]
[\fB\-days n\fR]
[\fB\-set_serial n\fR]
[\fB\-newhdr\fR]
[\fB\-addext ext\fR]
[\fB\-extensions section\fR]
[\fB\-reqexts section\fR]
[\fB\-precert\fR]
[\fB\-utf8\fR]
[\fB\-nameopt\fR]
[\fB\-reqopt\fR]
[\fB\-subject\fR]
[\fB\-subj arg\fR]
[\fB\-sigopt nm:v\fR]
[\fB\-batch\fR]
[\fB\-verbose\fR]
[\fB\-engine id\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBreq\fR command primarily creates and processes certificate requests
in PKCS#10 format. It can additionally create self signed certificates
for use as root CAs for example.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format. The \fBDER\fR option uses an ASN1 DER encoded
form compatible with the PKCS#10. The \fBPEM\fR form is the default format: it
consists of the \fBDER\fR format base64 encoded with additional header and
footer lines.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format, the options have the same meaning and default
as the \fB\-inform\fR option.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read a request from or standard input
if this option is not specified. A request is only read if the creation
options (\fB\-new\fR and \fB\-newkey\fR) are not specified.
.IP "\fB\-sigopt nm:v\fR" 4
.IX Item "-sigopt nm:v"
Pass options to the signature algorithm during sign or verify operations.
Names and values of these options are algorithm-specific.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
The input file password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write to or standard output by
default.
.IP "\fB\-passout arg\fR" 4
.IX Item "-passout arg"
The output file password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the certificate request in text form.
.IP \fB\-subject\fR 4
.IX Item "-subject"
Prints out the request subject (or certificate subject if \fB\-x509\fR is
specified)
.IP \fB\-pubkey\fR 4
.IX Item "-pubkey"
Outputs the public key.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option prevents output of the encoded version of the request.
.IP \fB\-modulus\fR 4
.IX Item "-modulus"
This option prints out the value of the modulus of the public key
contained in the request.
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verifies the signature on the request.
.IP \fB\-new\fR 4
.IX Item "-new"
This option generates a new certificate request. It will prompt
the user for the relevant field values. The actual fields
prompted for and their maximum and minimum sizes are specified
in the configuration file and any requested extensions.
.Sp
If the \fB\-key\fR option is not used it will generate a new RSA private
key using information specified in the configuration file.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP "\fB\-newkey arg\fR" 4
.IX Item "-newkey arg"
This option creates a new certificate request and a new private
key. The argument takes one of several forms. \fBrsa:nbits\fR, where
\&\fBnbits\fR is the number of bits, generates an RSA key \fBnbits\fR
in size. If \fBnbits\fR is omitted, i.e. \fB\-newkey rsa\fR specified,
the default key size, specified in the configuration file is used.
.Sp
All other algorithms support the \fB\-newkey alg:file\fR form, where file may be
an algorithm parameter file, created by the \fBgenpkey \-genparam\fR command
or and X.509 certificate for a key with appropriate algorithm.
.Sp
\&\fBparam:file\fR generates a key using the parameter file or certificate \fBfile\fR,
the algorithm is determined by the parameters. \fBalgname:file\fR use algorithm
\&\fBalgname\fR and parameter file \fBfile\fR: the two algorithms must match or an
error occurs. \fBalgname\fR just uses algorithm \fBalgname\fR, and parameters,
if necessary should be specified via \fB\-pkeyopt\fR parameter.
.Sp
\&\fBdsa:filename\fR generates a DSA key using the parameters
in the file \fBfilename\fR. \fBec:filename\fR generates EC key (usable both with
ECDSA or ECDH algorithms), \fBgost2001:filename\fR generates GOST R
34.10\-2001 key (requires \fBccgost\fR engine configured in the configuration
file). If just \fBgost2001\fR is specified a parameter set should be
specified by \fB\-pkeyopt paramset:X\fR
.IP "\fB\-pkeyopt opt:value\fR" 4
.IX Item "-pkeyopt opt:value"
Set the public key algorithm option \fBopt\fR to \fBvalue\fR. The precise set of
options supported depends on the public key algorithm used and its
implementation. See \fBKEY GENERATION OPTIONS\fR in the \fBgenpkey\fR manual page
for more details.
.IP "\fB\-key filename\fR" 4
.IX Item "-key filename"
This specifies the file to read the private key from. It also
accepts PKCS#8 format private keys for PEM format files.
.IP "\fB\-keyform PEM|DER\fR" 4
.IX Item "-keyform PEM|DER"
The format of the private key file specified in the \fB\-key\fR
argument. PEM is the default.
.IP "\fB\-keyout filename\fR" 4
.IX Item "-keyout filename"
This gives the filename to write the newly created private key to.
If this option is not specified then the filename present in the
configuration file is used.
.IP \fB\-nodes\fR 4
.IX Item "-nodes"
If this option is specified then if a private key is created it
will not be encrypted.
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
This specifies the message digest to sign the request.
Any digest supported by the OpenSSL \fBdgst\fR command can be used.
This overrides the digest algorithm specified in
the configuration file.
.Sp
Some public key algorithms may override this choice. For instance, DSA
signatures always use SHA1, GOST R 34.10 signatures always use
GOST R 34.11\-94 (\fB\-md_gost94\fR), Ed25519 and Ed448 never use any digest.
.IP "\fB\-config filename\fR" 4
.IX Item "-config filename"
This allows an alternative configuration file to be specified.
Optional; for a description of the default value,
see "COMMAND SUMMARY" in \fBopenssl\fR\|(1).
.IP "\fB\-subj arg\fR" 4
.IX Item "-subj arg"
Sets subject name for new request or supersedes the subject name
when processing a request.
The arg must be formatted as \fI/type0=value0/type1=value1/type2=...\fR.
Keyword characters may be escaped by \e (backslash), and whitespace is retained.
Empty values are permitted, but the corresponding type will not be included
in the request.
.IP \fB\-multivalue\-rdn\fR 4
.IX Item "-multivalue-rdn"
This option causes the \-subj argument to be interpreted with full
support for multivalued RDNs. Example:
.Sp
\&\fI/DC=org/DC=OpenSSL/DC=users/UID=123456+CN=John Doe\fR
.Sp
If \-multi\-rdn is not used then the UID value is \fI123456+CN=John Doe\fR.
.IP \fB\-x509\fR 4
.IX Item "-x509"
This option outputs a self signed certificate instead of a certificate
request. This is typically used to generate a test certificate or
a self signed root CA. The extensions added to the certificate
(if any) are specified in the configuration file. Unless specified
using the \fBset_serial\fR option, a large random number will be used for
the serial number.
.Sp
If existing request is specified with the \fB\-in\fR option, it is converted
to the self signed certificate otherwise new request is created.
.IP "\fB\-days n\fR" 4
.IX Item "-days n"
When the \fB\-x509\fR option is being used this specifies the number of
days to certify the certificate for, otherwise it is ignored. \fBn\fR should
be a positive integer. The default is 30 days.
.IP "\fB\-set_serial n\fR" 4
.IX Item "-set_serial n"
Serial number to use when outputting a self signed certificate. This
may be specified as a decimal value or a hex value if preceded by \fB0x\fR.
.IP "\fB\-addext ext\fR" 4
.IX Item "-addext ext"
Add a specific extension to the certificate (if the \fB\-x509\fR option is
present) or certificate request.  The argument must have the form of
a key=value pair as it would appear in a config file.
.Sp
This option can be given multiple times.
.IP "\fB\-extensions section\fR" 4
.IX Item "-extensions section"
.PD 0
.IP "\fB\-reqexts section\fR" 4
.IX Item "-reqexts section"
.PD
These options specify alternative sections to include certificate
extensions (if the \fB\-x509\fR option is present) or certificate
request extensions. This allows several different sections to
be used in the same configuration file to specify requests for
a variety of purposes.
.IP \fB\-precert\fR 4
.IX Item "-precert"
A poison extension will be added to the certificate, making it a
"pre-certificate" (see RFC6962). This can be submitted to Certificate
Transparency logs in order to obtain signed certificate timestamps (SCTs).
These SCTs can then be embedded into the pre-certificate as an extension, before
removing the poison and signing the certificate.
.Sp
This implies the \fB\-new\fR flag.
.IP \fB\-utf8\fR 4
.IX Item "-utf8"
This option causes field values to be interpreted as UTF8 strings, by
default they are interpreted as ASCII. This means that the field
values, whether prompted from a terminal or obtained from a
configuration file, must be valid UTF8 strings.
.IP "\fB\-nameopt option\fR" 4
.IX Item "-nameopt option"
Option which determines how the subject or issuer names are displayed. The
\&\fBoption\fR argument can be a single option or multiple options separated by
commas.  Alternatively the \fB\-nameopt\fR switch may be used more than once to
set multiple options. See the \fBx509\fR\|(1) manual page for details.
.IP \fB\-reqopt\fR 4
.IX Item "-reqopt"
Customise the output format used with \fB\-text\fR. The \fBoption\fR argument can be
a single option or multiple options separated by commas.
.Sp
See discussion of the  \fB\-certopt\fR parameter in the \fBx509\fR\|(1)
command.
.IP \fB\-newhdr\fR 4
.IX Item "-newhdr"
Adds the word \fBNEW\fR to the PEM file header and footer lines on the outputted
request. Some software (Netscape certificate server) and some CAs need this.
.IP \fB\-batch\fR 4
.IX Item "-batch"
Non-interactive mode.
.IP \fB\-verbose\fR 4
.IX Item "-verbose"
Print extra details about the operations being performed.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBreq\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.IP "\fB\-keygen_engine id\fR" 4
.IX Item "-keygen_engine id"
Specifies an engine (by its unique \fBid\fR string) which would be used
for key generation operations.
.SH "CONFIGURATION FILE FORMAT"
.IX Header "CONFIGURATION FILE FORMAT"
The configuration options are specified in the \fBreq\fR section of
the configuration file. As with all configuration files if no
value is specified in the specific section (i.e. \fBreq\fR) then
the initial unnamed or \fBdefault\fR section is searched too.
.PP
The options available are described in detail below.
.IP "\fBinput_password output_password\fR" 4
.IX Item "input_password output_password"
The passwords for the input private key file (if present) and
the output private key file (if one will be created). The
command line options \fBpassin\fR and \fBpassout\fR override the
configuration file values.
.IP \fBdefault_bits\fR 4
.IX Item "default_bits"
Specifies the default key size in bits.
.Sp
This option is used in conjunction with the \fB\-new\fR option to generate
a new key. It can be overridden by specifying an explicit key size in
the \fB\-newkey\fR option. The smallest accepted key size is 512 bits. If
no key size is specified then 2048 bits is used.
.IP \fBdefault_keyfile\fR 4
.IX Item "default_keyfile"
This is the default filename to write a private key to. If not
specified the key is written to standard output. This can be
overridden by the \fB\-keyout\fR option.
.IP \fBoid_file\fR 4
.IX Item "oid_file"
This specifies a file containing additional \fBOBJECT IDENTIFIERS\fR.
Each line of the file should consist of the numerical form of the
object identifier followed by white space then the short name followed
by white space and finally the long name.
.IP \fBoid_section\fR 4
.IX Item "oid_section"
This specifies a section in the configuration file containing extra
object identifiers. Each line should consist of the short name of the
object identifier followed by \fB=\fR and the numerical form. The short
and long names are the same when this option is used.
.IP \fBRANDFILE\fR 4
.IX Item "RANDFILE"
At startup the specified file is loaded into the random number generator,
and at exit 256 bytes will be written to it.
It is used for private key generation.
.IP \fBencrypt_key\fR 4
.IX Item "encrypt_key"
If this is set to \fBno\fR then if a private key is generated it is
\&\fBnot\fR encrypted. This is equivalent to the \fB\-nodes\fR command line
option. For compatibility \fBencrypt_rsa_key\fR is an equivalent option.
.IP \fBdefault_md\fR 4
.IX Item "default_md"
This option specifies the digest algorithm to use. Any digest supported by the
OpenSSL \fBdgst\fR command can be used. This option can be overridden on the
command line. Certain signing algorithms (i.e. Ed25519 and Ed448) will ignore
any digest that has been set.
.IP \fBstring_mask\fR 4
.IX Item "string_mask"
This option masks out the use of certain string types in certain
fields. Most users will not need to change this option.
.Sp
It can be set to several values \fBdefault\fR which is also the default
option uses PrintableStrings, T61Strings and BMPStrings if the
\&\fBpkix\fR value is used then only PrintableStrings and BMPStrings will
be used. This follows the PKIX recommendation in RFC2459. If the
\&\fButf8only\fR option is used then only UTF8Strings will be used: this
is the PKIX recommendation in RFC2459 after 2003. Finally the \fBnombstr\fR
option just uses PrintableStrings and T61Strings: certain software has
problems with BMPStrings and UTF8Strings: in particular Netscape.
.IP \fBreq_extensions\fR 4
.IX Item "req_extensions"
This specifies the configuration file section containing a list of
extensions to add to the certificate request. It can be overridden
by the \fB\-reqexts\fR command line switch. See the
\&\fBx509v3_config\fR\|(5) manual page for details of the
extension section format.
.IP \fBx509_extensions\fR 4
.IX Item "x509_extensions"
This specifies the configuration file section containing a list of
extensions to add to certificate generated when the \fB\-x509\fR switch
is used. It can be overridden by the \fB\-extensions\fR command line switch.
.IP \fBprompt\fR 4
.IX Item "prompt"
If set to the value \fBno\fR this disables prompting of certificate fields
and just takes values from the config file directly. It also changes the
expected format of the \fBdistinguished_name\fR and \fBattributes\fR sections.
.IP \fButf8\fR 4
.IX Item "utf8"
If set to the value \fByes\fR then field values to be interpreted as UTF8
strings, by default they are interpreted as ASCII. This means that
the field values, whether prompted from a terminal or obtained from a
configuration file, must be valid UTF8 strings.
.IP \fBattributes\fR 4
.IX Item "attributes"
This specifies the section containing any request attributes: its format
is the same as \fBdistinguished_name\fR. Typically these may contain the
challengePassword or unstructuredName types. They are currently ignored
by OpenSSL's request signing utilities but some CAs might want them.
.IP \fBdistinguished_name\fR 4
.IX Item "distinguished_name"
This specifies the section containing the distinguished name fields to
prompt for when generating a certificate or certificate request. The format
is described in the next section.
.SH "DISTINGUISHED NAME AND ATTRIBUTE SECTION FORMAT"
.IX Header "DISTINGUISHED NAME AND ATTRIBUTE SECTION FORMAT"
There are two separate formats for the distinguished name and attribute
sections. If the \fBprompt\fR option is set to \fBno\fR then these sections
just consist of field names and values: for example,
.PP
.Vb 3
\& CN=My Name
\& OU=My Organization
\& emailAddress=<EMAIL>
.Ve
.PP
This allows external programs (e.g. GUI based) to generate a template file
with all the field names and values and just pass it to \fBreq\fR. An example
of this kind of configuration file is contained in the \fBEXAMPLES\fR section.
.PP
Alternatively if the \fBprompt\fR option is absent or not set to \fBno\fR then the
file contains field prompting information. It consists of lines of the form:
.PP
.Vb 4
\& fieldName="prompt"
\& fieldName_default="default field value"
\& fieldName_min= 2
\& fieldName_max= 4
.Ve
.PP
"fieldName" is the field name being used, for example commonName (or CN).
The "prompt" string is used to ask the user to enter the relevant
details. If the user enters nothing then the default value is used if no
default value is present then the field is omitted. A field can
still be omitted if a default value is present if the user just
enters the '.' character.
.PP
The number of characters entered must be between the fieldName_min and
fieldName_max limits: there may be additional restrictions based
on the field being used (for example countryName can only ever be
two characters long and must fit in a PrintableString).
.PP
Some fields (such as organizationName) can be used more than once
in a DN. This presents a problem because configuration files will
not recognize the same name occurring twice. To avoid this problem
if the fieldName contains some characters followed by a full stop
they will be ignored. So for example a second organizationName can
be input by calling it "1.organizationName".
.PP
The actual permitted field names are any object identifier short or
long names. These are compiled into OpenSSL and include the usual
values such as commonName, countryName, localityName, organizationName,
organizationalUnitName, stateOrProvinceName. Additionally emailAddress
is included as well as name, surname, givenName, initials, and dnQualifier.
.PP
Additional object identifiers can be defined with the \fBoid_file\fR or
\&\fBoid_section\fR options in the configuration file. Any additional fields
will be treated as though they were a DirectoryString.
.SH EXAMPLES
.IX Header "EXAMPLES"
Examine and verify certificate request:
.PP
.Vb 1
\& openssl req \-in req.pem \-text \-verify \-noout
.Ve
.PP
Create a private key and then generate a certificate request from it:
.PP
.Vb 2
\& openssl genrsa \-out key.pem 2048
\& openssl req \-new \-key key.pem \-out req.pem
.Ve
.PP
The same but just using req:
.PP
.Vb 1
\& openssl req \-newkey rsa:2048 \-keyout key.pem \-out req.pem
.Ve
.PP
Generate a self signed root certificate:
.PP
.Vb 1
\& openssl req \-x509 \-newkey rsa:2048 \-keyout key.pem \-out req.pem
.Ve
.PP
Example of a file pointed to by the \fBoid_file\fR option:
.PP
.Vb 2
\& *******        shortName       A longer Name
\& *******        otherName       Other longer Name
.Ve
.PP
Example of a section pointed to by \fBoid_section\fR making use of variable
expansion:
.PP
.Vb 2
\& testoid1=1.2.3.5
\& testoid2=${testoid1}.6
.Ve
.PP
Sample configuration file prompting for field values:
.PP
.Vb 6
\& [ req ]
\& default_bits           = 2048
\& default_keyfile        = privkey.pem
\& distinguished_name     = req_distinguished_name
\& attributes             = req_attributes
\& req_extensions         = v3_ca
\&
\& dirstring_type = nobmp
\&
\& [ req_distinguished_name ]
\& countryName                    = Country Name (2 letter code)
\& countryName_default            = AU
\& countryName_min                = 2
\& countryName_max                = 2
\&
\& localityName                   = Locality Name (eg, city)
\&
\& organizationalUnitName         = Organizational Unit Name (eg, section)
\&
\& commonName                     = Common Name (eg, YOUR name)
\& commonName_max                 = 64
\&
\& emailAddress                   = Email Address
\& emailAddress_max               = 40
\&
\& [ req_attributes ]
\& challengePassword              = A challenge password
\& challengePassword_min          = 4
\& challengePassword_max          = 20
\&
\& [ v3_ca ]
\&
\& subjectKeyIdentifier=hash
\& authorityKeyIdentifier=keyid:always,issuer:always
\& basicConstraints = critical, CA:true
.Ve
.PP
Sample configuration containing all field values:
.PP
.Vb 1
\& RANDFILE               = $ENV::HOME/.rnd
\&
\& [ req ]
\& default_bits           = 2048
\& default_keyfile        = keyfile.pem
\& distinguished_name     = req_distinguished_name
\& attributes             = req_attributes
\& prompt                 = no
\& output_password        = mypass
\&
\& [ req_distinguished_name ]
\& C                      = GB
\& ST                     = Test State or Province
\& L                      = Test Locality
\& O                      = Organization Name
\& OU                     = Organizational Unit Name
\& CN                     = Common Name
\& emailAddress           = <EMAIL>
\&
\& [ req_attributes ]
\& challengePassword              = A challenge password
.Ve
.PP
Example of giving the most common attributes (subject and extensions)
on the command line:
.PP
.Vb 4
\& openssl req \-new \-subj "/C=GB/CN=foo" \e
\&                  \-addext "subjectAltName = DNS:foo.co.uk" \e
\&                  \-addext "certificatePolicies = *******" \e
\&                  \-newkey rsa:2048 \-keyout key.pem \-out req.pem
.Ve
.SH NOTES
.IX Header "NOTES"
The header and footer lines in the \fBPEM\fR format are normally:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN CERTIFICATE REQUEST\-\-\-\-\-
\& \-\-\-\-\-END CERTIFICATE REQUEST\-\-\-\-\-
.Ve
.PP
some software (some versions of Netscape certificate server) instead needs:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN NEW CERTIFICATE REQUEST\-\-\-\-\-
\& \-\-\-\-\-END NEW CERTIFICATE REQUEST\-\-\-\-\-
.Ve
.PP
which is produced with the \fB\-newhdr\fR option but is otherwise compatible.
Either form is accepted transparently on input.
.PP
The certificate requests generated by \fBXenroll\fR with MSIE have extensions
added. It includes the \fBkeyUsage\fR extension which determines the type of
key (signature only or general purpose) and any additional OIDs entered
by the script in an extendedKeyUsage extension.
.SH DIAGNOSTICS
.IX Header "DIAGNOSTICS"
The following messages are frequently asked about:
.PP
.Vb 2
\&        Using configuration from /some/path/openssl.cnf
\&        Unable to load config info
.Ve
.PP
This is followed some time later by...
.PP
.Vb 2
\&        unable to find \*(Aqdistinguished_name\*(Aq in config
\&        problems making Certificate Request
.Ve
.PP
The first error message is the clue: it can't find the configuration
file! Certain operations (like examining a certificate request) don't
need a configuration file so its use isn't enforced. Generation of
certificates or requests however does need a configuration file. This
could be regarded as a bug.
.PP
Another puzzling message is this:
.PP
.Vb 2
\&        Attributes:
\&            a0:00
.Ve
.PP
this is displayed when no attributes are present and the request includes
the correct empty \fBSET OF\fR structure (the DER encoding of which is 0xa0
0x00). If you just see:
.PP
.Vb 1
\&        Attributes:
.Ve
.PP
then the \fBSET OF\fR is missing and the encoding is technically invalid (but
it is tolerated). See the description of the command line option \fB\-asn1\-kludge\fR
for more information.
.SH BUGS
.IX Header "BUGS"
OpenSSL's handling of T61Strings (aka TeletexStrings) is broken: it effectively
treats them as ISO\-8859\-1 (Latin 1), Netscape and MSIE have similar behaviour.
This can cause problems if you need characters that aren't available in
PrintableStrings and you don't want to or can't use BMPStrings.
.PP
As a consequence of the T61String handling the only correct way to represent
accented characters in OpenSSL is to use a BMPString: unfortunately Netscape
currently chokes on these. If you have to use accented characters with Netscape
and MSIE then you currently need to use the invalid T61String form.
.PP
The current prompting is not very friendly. It doesn't allow you to confirm what
you've just entered. Other things like extensions in certificate requests are
statically defined in the configuration file. Some of these: like an email
address in subjectAltName should be input by the user.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBx509\fR\|(1), \fBca\fR\|(1), \fBgenrsa\fR\|(1),
\&\fBgendsa\fR\|(1), \fBconfig\fR\|(5),
\&\fBx509v3_config\fR\|(5)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
