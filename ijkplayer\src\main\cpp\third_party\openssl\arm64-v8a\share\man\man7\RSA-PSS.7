.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA-PSS 7"
.TH RSA-PSS 7 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA\-PSS \- EVP_PKEY RSA\-PSS algorithm support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBRSA-PSS\fR EVP_PKEY implementation is a restricted version of the RSA
algorithm which only supports signing, verification and key generation
using PSS padding modes with optional parameter restrictions.
.PP
It has associated private key and public key formats.
.PP
This algorithm shares several control operations with the \fBRSA\fR algorithm
but with some restrictions described below.
.SS "Signing and Verification"
.IX Subsection "Signing and Verification"
Signing and verification is similar to the \fBRSA\fR algorithm except the
padding mode is always PSS. If the key in use has parameter restrictions then
the corresponding signature parameters are set to the restrictions:
for example, if the key can only be used with digest SHA256, MGF1 SHA256
and minimum salt length 32 then the digest, MGF1 digest and salt length
will be set to SHA256, SHA256 and 32 respectively.
.SS "Key Generation"
.IX Subsection "Key Generation"
By default no parameter restrictions are placed on the generated key.
.SH NOTES
.IX Header "NOTES"
The public key format is documented in RFC4055.
.PP
The PKCS#8 private key format used for RSA-PSS keys is similar to the RSA
format except it uses the \fBid-RSASSA-PSS\fR OID and the parameters field, if
present, restricts the key parameters in the same way as the public key.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
RFC 4055
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_set_rsa_pss_keygen_md\fR\|(3),
\&\fBEVP_PKEY_CTX_set_rsa_pss_keygen_mgf1_md\fR\|(3),
\&\fBEVP_PKEY_CTX_set_rsa_pss_keygen_saltlen\fR\|(3),
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_CTX_ctrl_str\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
