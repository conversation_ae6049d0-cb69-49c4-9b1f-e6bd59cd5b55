.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DEFINE_STACK_OF 3"
.TH DEFINE_STACK_OF 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DEFINE_STACK_OF, DEFINE_STACK_OF_CONST, DEFINE_SPECIAL_STACK_OF,
DEFINE_SPECIAL_STACK_OF_CONST,
sk_TYPE_num, sk_TYPE_value, sk_TYPE_new, sk_TYPE_new_null,
sk_TYPE_reserve, sk_TYPE_free, sk_TYPE_zero, sk_TYPE_delete,
sk_TYPE_delete_ptr, sk_TYPE_push, sk_TYPE_unshift, sk_TYPE_pop,
sk_TYPE_shift, sk_TYPE_pop_free, sk_TYPE_insert, sk_TYPE_set,
sk_TYPE_find, sk_TYPE_find_ex, sk_TYPE_sort, sk_TYPE_is_sorted,
sk_TYPE_dup, sk_TYPE_deep_copy, sk_TYPE_set_cmp_func, sk_TYPE_new_reserve
\&\- stack container
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/safestack.h>
\&
\& STACK_OF(TYPE)
\& DEFINE_STACK_OF(TYPE)
\& DEFINE_STACK_OF_CONST(TYPE)
\& DEFINE_SPECIAL_STACK_OF(FUNCTYPE, TYPE)
\& DEFINE_SPECIAL_STACK_OF_CONST(FUNCTYPE, TYPE)
\&
\& typedef int (*sk_TYPE_compfunc)(const TYPE *const *a, const TYPE *const *b);
\& typedef TYPE * (*sk_TYPE_copyfunc)(const TYPE *a);
\& typedef void (*sk_TYPE_freefunc)(TYPE *a);
\&
\& int sk_TYPE_num(const STACK_OF(TYPE) *sk);
\& TYPE *sk_TYPE_value(const STACK_OF(TYPE) *sk, int idx);
\& STACK_OF(TYPE) *sk_TYPE_new(sk_TYPE_compfunc compare);
\& STACK_OF(TYPE) *sk_TYPE_new_null(void);
\& int sk_TYPE_reserve(STACK_OF(TYPE) *sk, int n);
\& void sk_TYPE_free(const STACK_OF(TYPE) *sk);
\& void sk_TYPE_zero(const STACK_OF(TYPE) *sk);
\& TYPE *sk_TYPE_delete(STACK_OF(TYPE) *sk, int i);
\& TYPE *sk_TYPE_delete_ptr(STACK_OF(TYPE) *sk, TYPE *ptr);
\& int sk_TYPE_push(STACK_OF(TYPE) *sk, const TYPE *ptr);
\& int sk_TYPE_unshift(STACK_OF(TYPE) *sk, const TYPE *ptr);
\& TYPE *sk_TYPE_pop(STACK_OF(TYPE) *sk);
\& TYPE *sk_TYPE_shift(STACK_OF(TYPE) *sk);
\& void sk_TYPE_pop_free(STACK_OF(TYPE) *sk, sk_TYPE_freefunc freefunc);
\& int sk_TYPE_insert(STACK_OF(TYPE) *sk, TYPE *ptr, int idx);
\& TYPE *sk_TYPE_set(STACK_OF(TYPE) *sk, int idx, const TYPE *ptr);
\& int sk_TYPE_find(STACK_OF(TYPE) *sk, TYPE *ptr);
\& int sk_TYPE_find_ex(STACK_OF(TYPE) *sk, TYPE *ptr);
\& void sk_TYPE_sort(const STACK_OF(TYPE) *sk);
\& int sk_TYPE_is_sorted(const STACK_OF(TYPE) *sk);
\& STACK_OF(TYPE) *sk_TYPE_dup(const STACK_OF(TYPE) *sk);
\& STACK_OF(TYPE) *sk_TYPE_deep_copy(const STACK_OF(TYPE) *sk,
\&                                   sk_TYPE_copyfunc copyfunc,
\&                                   sk_TYPE_freefunc freefunc);
\& sk_TYPE_compfunc (*sk_TYPE_set_cmp_func(STACK_OF(TYPE) *sk,
\&                                         sk_TYPE_compfunc compare));
\& STACK_OF(TYPE) *sk_TYPE_new_reserve(sk_TYPE_compfunc compare, int n);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Applications can create and use their own stacks by placing any of the macros
described below in a header file. These macros define typesafe inline
functions that wrap around the utility \fBOPENSSL_sk_\fR API.
In the description here, \fITYPE\fR is used
as a placeholder for any of the OpenSSL datatypes, such as \fIX509\fR.
.PP
\&\fBSTACK_OF()\fR returns the name for a stack of the specified \fBTYPE\fR.
\&\fBDEFINE_STACK_OF()\fR creates set of functions for a stack of \fBTYPE\fR. This
will mean that type \fBTYPE\fR is stored in each stack, the type is referenced by
STACK_OF(TYPE) and each function name begins with \fIsk_TYPE_\fR. For example:
.PP
.Vb 1
\& TYPE *sk_TYPE_value(STACK_OF(TYPE) *sk, int idx);
.Ve
.PP
\&\fBDEFINE_STACK_OF_CONST()\fR is identical to \fBDEFINE_STACK_OF()\fR except
each element is constant. For example:
.PP
.Vb 1
\& const TYPE *sk_TYPE_value(STACK_OF(TYPE) *sk, int idx);
.Ve
.PP
\&\fBDEFINE_SPECIAL_STACK_OF()\fR defines a stack of \fBTYPE\fR but
each function uses \fBFUNCNAME\fR in the function name. For example:
.PP
.Vb 1
\& TYPE *sk_FUNCNAME_value(STACK_OF(TYPE) *sk, int idx);
.Ve
.PP
\&\fBDEFINE_SPECIAL_STACK_OF_CONST()\fR is similar except that each element is
constant:
.PP
.Vb 1
\& const TYPE *sk_FUNCNAME_value(STACK_OF(TYPE) *sk, int idx);
.Ve
.PP
\&\fBsk_TYPE_num()\fR returns the number of elements in \fBsk\fR or \-1 if \fBsk\fR is
\&\fBNULL\fR.
.PP
\&\fBsk_TYPE_value()\fR returns element \fBidx\fR in \fBsk\fR, where \fBidx\fR starts at
zero. If \fBidx\fR is out of range then \fBNULL\fR is returned.
.PP
\&\fBsk_TYPE_new()\fR allocates a new empty stack using comparison function \fBcompare\fR.
If \fBcompare\fR is \fBNULL\fR then no comparison function is used. This function is
equivalent to sk_TYPE_new_reserve(compare, 0).
.PP
\&\fBsk_TYPE_new_null()\fR allocates a new empty stack with no comparison function. This
function is equivalent to sk_TYPE_new_reserve(NULL, 0).
.PP
\&\fBsk_TYPE_reserve()\fR allocates additional memory in the \fBsk\fR structure
such that the next \fBn\fR calls to \fBsk_TYPE_insert()\fR, \fBsk_TYPE_push()\fR
or \fBsk_TYPE_unshift()\fR will not fail or cause memory to be allocated
or reallocated. If \fBn\fR is zero, any excess space allocated in the
\&\fBsk\fR structure is freed. On error \fBsk\fR is unchanged.
.PP
\&\fBsk_TYPE_new_reserve()\fR allocates a new stack. The new stack will have additional
memory allocated to hold \fBn\fR elements if \fBn\fR is positive. The next \fBn\fR calls
to \fBsk_TYPE_insert()\fR, \fBsk_TYPE_push()\fR or \fBsk_TYPE_unshift()\fR will not fail or cause
memory to be allocated or reallocated. If \fBn\fR is zero or less than zero, no
memory is allocated. \fBsk_TYPE_new_reserve()\fR also sets the comparison function
\&\fBcompare\fR to the newly created stack. If \fBcompare\fR is \fBNULL\fR then no
comparison function is used.
.PP
\&\fBsk_TYPE_set_cmp_func()\fR sets the comparison function of \fBsk\fR to \fBcompare\fR.
The previous comparison function is returned or \fBNULL\fR if there was
no previous comparison function.
.PP
\&\fBsk_TYPE_free()\fR frees up the \fBsk\fR structure. It does \fBnot\fR free up any
elements of \fBsk\fR. After this call \fBsk\fR is no longer valid.
.PP
\&\fBsk_TYPE_zero()\fR sets the number of elements in \fBsk\fR to zero. It does not free
\&\fBsk\fR so after this call \fBsk\fR is still valid.
.PP
\&\fBsk_TYPE_pop_free()\fR frees up all elements of \fBsk\fR and \fBsk\fR itself. The
free function \fBfreefunc()\fR is called on each element to free it.
.PP
\&\fBsk_TYPE_delete()\fR deletes element \fBi\fR from \fBsk\fR. It returns the deleted
element or \fBNULL\fR if \fBi\fR is out of range.
.PP
\&\fBsk_TYPE_delete_ptr()\fR deletes element matching \fBptr\fR from \fBsk\fR. It returns
the deleted element or \fBNULL\fR if no element matching \fBptr\fR was found.
.PP
\&\fBsk_TYPE_insert()\fR inserts \fBptr\fR into \fBsk\fR at position \fBidx\fR. Any existing
elements at or after \fBidx\fR are moved downwards. If \fBidx\fR is out of range
the new element is appended to \fBsk\fR. \fBsk_TYPE_insert()\fR either returns the
number of elements in \fBsk\fR after the new element is inserted or zero if
an error (such as memory allocation failure) occurred.
.PP
\&\fBsk_TYPE_push()\fR appends \fBptr\fR to \fBsk\fR it is equivalent to:
.PP
.Vb 1
\& sk_TYPE_insert(sk, ptr, \-1);
.Ve
.PP
\&\fBsk_TYPE_unshift()\fR inserts \fBptr\fR at the start of \fBsk\fR it is equivalent to:
.PP
.Vb 1
\& sk_TYPE_insert(sk, ptr, 0);
.Ve
.PP
\&\fBsk_TYPE_pop()\fR returns and removes the last element from \fBsk\fR.
.PP
\&\fBsk_TYPE_shift()\fR returns and removes the first element from \fBsk\fR.
.PP
\&\fBsk_TYPE_set()\fR sets element \fBidx\fR of \fBsk\fR to \fBptr\fR replacing the current
element. The new element value is returned or \fBNULL\fR if an error occurred:
this will only happen if \fBsk\fR is \fBNULL\fR or \fBidx\fR is out of range.
.PP
\&\fBsk_TYPE_find()\fR searches \fBsk\fR for the element \fBptr\fR.  In the case
where no comparison function has been specified, the function performs
a linear search for a pointer equal to \fBptr\fR. The index of the first
matching element is returned or \fB\-1\fR if there is no match. In the case
where a comparison function has been specified, \fBsk\fR is sorted then
\&\fBsk_TYPE_find()\fR returns the index of a matching element or \fB\-1\fR if there
is no match. Note that, in this case, the matching element returned is
not guaranteed to be the first; the comparison function will usually
compare the values pointed to rather than the pointers themselves and
the order of elements in \fBsk\fR could change.
.PP
\&\fBsk_TYPE_find_ex()\fR operates like \fBsk_TYPE_find()\fR except when a comparison
function has been specified and no matching element is found. Instead
of returning \fB\-1\fR, \fBsk_TYPE_find_ex()\fR returns the index of the element
either before or after the location where \fBptr\fR would be if it were
present in \fBsk\fR.
.PP
\&\fBsk_TYPE_sort()\fR sorts \fBsk\fR using the supplied comparison function.
.PP
\&\fBsk_TYPE_is_sorted()\fR returns \fB1\fR if \fBsk\fR is sorted and \fB0\fR otherwise.
.PP
\&\fBsk_TYPE_dup()\fR returns a copy of \fBsk\fR. Note the pointers in the copy
are identical to the original.
.PP
\&\fBsk_TYPE_deep_copy()\fR returns a new stack where each element has been copied.
Copying is performed by the supplied \fBcopyfunc()\fR and freeing by \fBfreefunc()\fR. The
function \fBfreefunc()\fR is only called if an error occurs.
.SH NOTES
.IX Header "NOTES"
Care should be taken when accessing stacks in multi-threaded environments.
Any operation which increases the size of a stack such as \fBsk_TYPE_insert()\fR or
\&\fBsk_push()\fR can "grow" the size of an internal array and cause race conditions
if the same stack is accessed in a different thread. Operations such as
\&\fBsk_find()\fR and \fBsk_sort()\fR can also reorder the stack.
.PP
Any comparison function supplied should use a metric suitable
for use in a binary search operation. That is it should return zero, a
positive or negative value if \fBa\fR is equal to, greater than
or less than \fBb\fR respectively.
.PP
Care should be taken when checking the return values of the functions
\&\fBsk_TYPE_find()\fR and \fBsk_TYPE_find_ex()\fR. They return an index to the
matching element. In particular \fB0\fR indicates a matching first element.
A failed search is indicated by a \fB\-1\fR return value.
.PP
\&\fBSTACK_OF()\fR, \fBDEFINE_STACK_OF()\fR, \fBDEFINE_STACK_OF_CONST()\fR, and
\&\fBDEFINE_SPECIAL_STACK_OF()\fR are implemented as macros.
.PP
The underlying utility \fBOPENSSL_sk_\fR API should not be used directly.
It defines these functions: \fBOPENSSL_sk_deep_copy()\fR,
\&\fBOPENSSL_sk_delete()\fR, \fBOPENSSL_sk_delete_ptr()\fR, \fBOPENSSL_sk_dup()\fR,
\&\fBOPENSSL_sk_find()\fR, \fBOPENSSL_sk_find_ex()\fR, \fBOPENSSL_sk_free()\fR,
\&\fBOPENSSL_sk_insert()\fR, \fBOPENSSL_sk_is_sorted()\fR, \fBOPENSSL_sk_new()\fR,
\&\fBOPENSSL_sk_new_null()\fR, \fBOPENSSL_sk_num()\fR, \fBOPENSSL_sk_pop()\fR,
\&\fBOPENSSL_sk_pop_free()\fR, \fBOPENSSL_sk_push()\fR, \fBOPENSSL_sk_reserve()\fR,
\&\fBOPENSSL_sk_set()\fR, \fBOPENSSL_sk_set_cmp_func()\fR, \fBOPENSSL_sk_shift()\fR,
\&\fBOPENSSL_sk_sort()\fR, \fBOPENSSL_sk_unshift()\fR, \fBOPENSSL_sk_value()\fR,
\&\fBOPENSSL_sk_zero()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBsk_TYPE_num()\fR returns the number of elements in the stack or \fB\-1\fR if the
passed stack is \fBNULL\fR.
.PP
\&\fBsk_TYPE_value()\fR returns a pointer to a stack element or \fBNULL\fR if the
index is out of range.
.PP
\&\fBsk_TYPE_new()\fR, \fBsk_TYPE_new_null()\fR and \fBsk_TYPE_new_reserve()\fR return an empty
stack or \fBNULL\fR if an error occurs.
.PP
\&\fBsk_TYPE_reserve()\fR returns \fB1\fR on successful allocation of the required memory
or \fB0\fR on error.
.PP
\&\fBsk_TYPE_set_cmp_func()\fR returns the old comparison function or \fBNULL\fR if
there was no old comparison function.
.PP
\&\fBsk_TYPE_free()\fR, \fBsk_TYPE_zero()\fR, \fBsk_TYPE_pop_free()\fR and \fBsk_TYPE_sort()\fR do
not return values.
.PP
\&\fBsk_TYPE_pop()\fR, \fBsk_TYPE_shift()\fR, \fBsk_TYPE_delete()\fR and \fBsk_TYPE_delete_ptr()\fR
return a pointer to the deleted element or \fBNULL\fR on error.
.PP
\&\fBsk_TYPE_insert()\fR, \fBsk_TYPE_push()\fR and \fBsk_TYPE_unshift()\fR return the total
number of elements in the stack and 0 if an error occurred. \fBsk_TYPE_push()\fR
further returns \-1 if \fBsk\fR is \fBNULL\fR.
.PP
\&\fBsk_TYPE_set()\fR returns a pointer to the replacement element or \fBNULL\fR on
error.
.PP
\&\fBsk_TYPE_find()\fR and \fBsk_TYPE_find_ex()\fR return an index to the found element
or \fB\-1\fR on error.
.PP
\&\fBsk_TYPE_is_sorted()\fR returns \fB1\fR if the stack is sorted and \fB0\fR if it is
not.
.PP
\&\fBsk_TYPE_dup()\fR and \fBsk_TYPE_deep_copy()\fR return a pointer to the copy of the
stack.
.SH HISTORY
.IX Header "HISTORY"
Before OpenSSL 1.1.0, this was implemented via macros and not inline functions
and was not a public API.
.PP
\&\fBsk_TYPE_reserve()\fR and \fBsk_TYPE_new_reserve()\fR were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
