.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CONF_CTX_SET_SSL_CTX 3"
.TH SSL_CONF_CTX_SET_SSL_CTX 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CONF_CTX_set_ssl_ctx, SSL_CONF_CTX_set_ssl \- set context to configure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CONF_CTX_set_ssl_ctx(SSL_CONF_CTX *cctx, SSL_CTX *ctx);
\& void SSL_CONF_CTX_set_ssl(SSL_CONF_CTX *cctx, SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CONF_CTX_set_ssl_ctx()\fR sets the context associated with \fBcctx\fR to the
\&\fBSSL_CTX\fR structure \fBctx\fR. Any previous \fBSSL\fR or \fBSSL_CTX\fR associated with
\&\fBcctx\fR is cleared. Subsequent calls to \fBSSL_CONF_cmd()\fR will be sent to
\&\fBctx\fR.
.PP
\&\fBSSL_CONF_CTX_set_ssl()\fR sets the context associated with \fBcctx\fR to the
\&\fBSSL\fR structure \fBssl\fR. Any previous \fBSSL\fR or \fBSSL_CTX\fR associated with
\&\fBcctx\fR is cleared. Subsequent calls to \fBSSL_CONF_cmd()\fR will be sent to
\&\fBssl\fR.
.SH NOTES
.IX Header "NOTES"
The context need not be set or it can be set to \fBNULL\fR in which case only
syntax checking of commands is performed, where possible.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CONF_CTX_set_ssl_ctx()\fR and \fBSSL_CTX_set_ssl()\fR do not return a value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CONF_CTX_new\fR\|(3),
\&\fBSSL_CONF_CTX_set_flags\fR\|(3),
\&\fBSSL_CONF_CTX_set1_prefix\fR\|(3),
\&\fBSSL_CONF_cmd\fR\|(3),
\&\fBSSL_CONF_cmd_argv\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.2.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2012\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
