.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EC 1"
.TH EC 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-ec,
ec \- EC key processing
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBec\fR
[\fB\-help\fR]
[\fB\-inform PEM|DER\fR]
[\fB\-outform PEM|DER\fR]
[\fB\-in filename\fR]
[\fB\-passin arg\fR]
[\fB\-out filename\fR]
[\fB\-passout arg\fR]
[\fB\-des\fR]
[\fB\-des3\fR]
[\fB\-idea\fR]
[\fB\-text\fR]
[\fB\-noout\fR]
[\fB\-param_out\fR]
[\fB\-pubin\fR]
[\fB\-pubout\fR]
[\fB\-conv_form arg\fR]
[\fB\-param_enc arg\fR]
[\fB\-no_public\fR]
[\fB\-check\fR]
[\fB\-engine id\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBec\fR command processes EC keys. They can be converted between various
forms and their components printed out. \fBNote\fR OpenSSL uses the
private key format specified in 'SEC 1: Elliptic Curve Cryptography'
(http://www.secg.org/). To convert an OpenSSL EC private key into the
PKCS#8 private key format use the \fBpkcs8\fR command.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format. The \fBDER\fR option with a private key uses
an ASN.1 DER encoded SEC1 private key. When used with a public key it
uses the SubjectPublicKeyInfo structure as specified in RFC 3280.
The \fBPEM\fR form is the default format: it consists of the \fBDER\fR format base64
encoded with additional header and footer lines. In the case of a private key
PKCS#8 format is also accepted.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format, the options have the same meaning and default
as the \fB\-inform\fR option.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read a key from or standard input if this
option is not specified. If the key is encrypted a pass phrase will be
prompted for.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
The input file password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write a key to or standard output by
is not specified. If any encryption options are set then a pass phrase will be
prompted for. The output filename should \fBnot\fR be the same as the input
filename.
.IP "\fB\-passout arg\fR" 4
.IX Item "-passout arg"
The output file password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP \fB\-des|\-des3|\-idea\fR 4
.IX Item "-des|-des3|-idea"
These options encrypt the private key with the DES, triple DES, IDEA or
any other cipher supported by OpenSSL before outputting it. A pass phrase is
prompted for.
If none of these options is specified the key is written in plain text. This
means that using the \fBec\fR utility to read in an encrypted key with no
encryption option can be used to remove the pass phrase from a key, or by
setting the encryption options it can be use to add or change the pass phrase.
These options can only be used with PEM format output files.
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the public, private key components and parameters.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option prevents output of the encoded version of the key.
.IP \fB\-pubin\fR 4
.IX Item "-pubin"
By default, a private key is read from the input file. With this option a
public key is read instead.
.IP \fB\-pubout\fR 4
.IX Item "-pubout"
By default a private key is output. With this option a public
key will be output instead. This option is automatically set if the input is
a public key.
.IP \fB\-conv_form\fR 4
.IX Item "-conv_form"
This specifies how the points on the elliptic curve are converted
into octet strings. Possible values are: \fBcompressed\fR (the default
value), \fBuncompressed\fR and \fBhybrid\fR. For more information regarding
the point conversion forms please read the X9.62 standard.
\&\fBNote\fR Due to patent issues the \fBcompressed\fR option is disabled
by default for binary curves and can be enabled by defining
the preprocessor macro \fBOPENSSL_EC_BIN_PT_COMP\fR at compile time.
.IP "\fB\-param_enc arg\fR" 4
.IX Item "-param_enc arg"
This specifies how the elliptic curve parameters are encoded.
Possible value are: \fBnamed_curve\fR, i.e. the ec parameters are
specified by an OID, or \fBexplicit\fR where the ec parameters are
explicitly given (see RFC 3279 for the definition of the
EC parameters structures). The default value is \fBnamed_curve\fR.
\&\fBNote\fR the \fBimplicitlyCA\fR alternative, as specified in RFC 3279,
is currently not implemented in OpenSSL.
.IP \fB\-no_public\fR 4
.IX Item "-no_public"
This option omits the public key components from the private key output.
.IP \fB\-check\fR 4
.IX Item "-check"
This option checks the consistency of an EC private or public key.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBec\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.SH NOTES
.IX Header "NOTES"
The PEM private key format uses the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN EC PRIVATE KEY\-\-\-\-\-
\& \-\-\-\-\-END EC PRIVATE KEY\-\-\-\-\-
.Ve
.PP
The PEM public key format uses the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN PUBLIC KEY\-\-\-\-\-
\& \-\-\-\-\-END PUBLIC KEY\-\-\-\-\-
.Ve
.SH EXAMPLES
.IX Header "EXAMPLES"
To encrypt a private key using triple DES:
.PP
.Vb 1
\& openssl ec \-in key.pem \-des3 \-out keyout.pem
.Ve
.PP
To convert a private key from PEM to DER format:
.PP
.Vb 1
\& openssl ec \-in key.pem \-outform DER \-out keyout.der
.Ve
.PP
To print out the components of a private key to standard output:
.PP
.Vb 1
\& openssl ec \-in key.pem \-text \-noout
.Ve
.PP
To just output the public part of a private key:
.PP
.Vb 1
\& openssl ec \-in key.pem \-pubout \-out pubkey.pem
.Ve
.PP
To change the parameters encoding to \fBexplicit\fR:
.PP
.Vb 1
\& openssl ec \-in key.pem \-param_enc explicit \-out keyout.pem
.Ve
.PP
To change the point conversion form to \fBcompressed\fR:
.PP
.Vb 1
\& openssl ec \-in key.pem \-conv_form compressed \-out keyout.pem
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBecparam\fR\|(1), \fBdsa\fR\|(1), \fBrsa\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2003\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
