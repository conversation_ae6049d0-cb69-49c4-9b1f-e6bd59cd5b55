.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_CERT_VERIFY_CALLBACK 3"
.TH SSL_CTX_SET_CERT_VERIFY_CALLBACK 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_cert_verify_callback \- set peer certificate verification procedure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_cert_verify_callback(SSL_CTX *ctx,
\&                                       int (*callback)(X509_STORE_CTX *, void *),
\&                                       void *arg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_cert_verify_callback()\fR sets the verification callback function for
\&\fIctx\fR. SSL objects that are created from \fIctx\fR inherit the setting valid at
the time when \fBSSL_new\fR\|(3) is called.
.SH NOTES
.IX Header "NOTES"
Whenever a certificate is verified during a SSL/TLS handshake, a verification
function is called. If the application does not explicitly specify a
verification callback function, the built-in verification function is used.
If a verification callback \fIcallback\fR is specified via
\&\fBSSL_CTX_set_cert_verify_callback()\fR, the supplied callback function is called
instead. By setting \fIcallback\fR to NULL, the default behaviour is restored.
.PP
When the verification must be performed, \fIcallback\fR will be called with
the arguments callback(X509_STORE_CTX *x509_store_ctx, void *arg). The
argument \fIarg\fR is specified by the application when setting \fIcallback\fR.
.PP
\&\fIcallback\fR should return 1 to indicate verification success and 0 to
indicate verification failure. If SSL_VERIFY_PEER is set and \fIcallback\fR
returns 0, the handshake will fail. As the verification procedure may
allow the connection to continue in the case of failure (by always
returning 1) the verification result must be set in any case using the
\&\fBerror\fR member of \fIx509_store_ctx\fR so that the calling application
will be informed about the detailed result of the verification procedure!
.PP
Within \fIx509_store_ctx\fR, \fIcallback\fR has access to the \fIverify_callback\fR
function set using \fBSSL_CTX_set_verify\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_cert_verify_callback()\fR does not return a value.
.SH WARNINGS
.IX Header "WARNINGS"
Do not mix the verification callback described in this function with the
\&\fBverify_callback\fR function called during the verification process. The
latter is set using the \fBSSL_CTX_set_verify\fR\|(3)
family of functions.
.PP
Providing a complete verification procedure including certificate purpose
settings etc is a complex task. The built-in procedure is quite powerful
and in most cases it should be sufficient to modify its behaviour using
the \fBverify_callback\fR function.
.SH BUGS
.IX Header "BUGS"
\&\fBSSL_CTX_set_cert_verify_callback()\fR does not provide diagnostic information.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_set_verify\fR\|(3),
\&\fBSSL_get_verify_result\fR\|(3),
\&\fBSSL_CTX_load_verify_locations\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
