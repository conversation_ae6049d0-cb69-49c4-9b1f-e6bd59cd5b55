.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EC_GROUP_NEW 3"
.TH EC_GROUP_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EC_GROUP_get_ecparameters,
EC_GROUP_get_ecpkparameters,
EC_GROUP_new,
EC_GROUP_new_from_ecparameters,
EC_GROUP_new_from_ecpkparameters,
EC_GROUP_free,
EC_GROUP_clear_free,
EC_GROUP_new_curve_GFp,
EC_GROUP_new_curve_GF2m,
EC_GROUP_new_by_curve_name,
EC_GROUP_set_curve,
EC_GROUP_get_curve,
EC_GROUP_set_curve_GFp,
EC_GROUP_get_curve_GFp,
EC_GROUP_set_curve_GF2m,
EC_GROUP_get_curve_GF2m,
EC_get_builtin_curves \- Functions for creating and destroying EC_GROUP
objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ec.h>
\&
\& EC_GROUP *EC_GROUP_new(const EC_METHOD *meth);
\& EC_GROUP *EC_GROUP_new_from_ecparameters(const ECPARAMETERS *params)
\& EC_GROUP *EC_GROUP_new_from_ecpkparameters(const ECPKPARAMETERS *params)
\& void EC_GROUP_free(EC_GROUP *group);
\& void EC_GROUP_clear_free(EC_GROUP *group);
\&
\& EC_GROUP *EC_GROUP_new_curve_GFp(const BIGNUM *p, const BIGNUM *a,
\&                                  const BIGNUM *b, BN_CTX *ctx);
\& EC_GROUP *EC_GROUP_new_curve_GF2m(const BIGNUM *p, const BIGNUM *a,
\&                                   const BIGNUM *b, BN_CTX *ctx);
\& EC_GROUP *EC_GROUP_new_by_curve_name(int nid);
\&
\& int EC_GROUP_set_curve(EC_GROUP *group, const BIGNUM *p, const BIGNUM *a,
\&                        const BIGNUM *b, BN_CTX *ctx);
\& int EC_GROUP_get_curve(const EC_GROUP *group, BIGNUM *p, BIGNUM *a, BIGNUM *b,
\&                        BN_CTX *ctx);
\& int EC_GROUP_set_curve_GFp(EC_GROUP *group, const BIGNUM *p,
\&                            const BIGNUM *a, const BIGNUM *b, BN_CTX *ctx);
\& int EC_GROUP_get_curve_GFp(const EC_GROUP *group, BIGNUM *p,
\&                            BIGNUM *a, BIGNUM *b, BN_CTX *ctx);
\& int EC_GROUP_set_curve_GF2m(EC_GROUP *group, const BIGNUM *p,
\&                             const BIGNUM *a, const BIGNUM *b, BN_CTX *ctx);
\& int EC_GROUP_get_curve_GF2m(const EC_GROUP *group, BIGNUM *p,
\&                             BIGNUM *a, BIGNUM *b, BN_CTX *ctx);
\&
\& ECPARAMETERS *EC_GROUP_get_ecparameters(const EC_GROUP *group, ECPARAMETERS *params)
\& ECPKPARAMETERS *EC_GROUP_get_ecpkparameters(const EC_GROUP *group, ECPKPARAMETERS *params)
\&
\& size_t EC_get_builtin_curves(EC_builtin_curve *r, size_t nitems);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Within the library there are two forms of elliptic curve that are of interest.
The first form is those defined over the prime field Fp. The elements of Fp are
the integers 0 to p\-1, where p is a prime number. This gives us a revised
elliptic curve equation as follows:
.PP
y^2 mod p = x^3 +ax + b mod p
.PP
The second form is those defined over a binary field F2^m where the elements of
the field are integers of length at most m bits. For this form the elliptic
curve equation is modified to:
.PP
y^2 + xy = x^3 + ax^2 + b (where b != 0)
.PP
Operations in a binary field are performed relative to an \fBirreducible
polynomial\fR. All such curves with OpenSSL use a trinomial or a pentanomial for
this parameter.
.PP
A new curve can be constructed by calling \fBEC_GROUP_new()\fR, using the
implementation provided by \fBmeth\fR (see \fBEC_GFp_simple_method\fR\|(3)). It is then
necessary to call \fBEC_GROUP_set_curve()\fR to set the curve parameters.
\&\fBEC_GROUP_new_from_ecparameters()\fR will create a group from the specified
\&\fBparams\fR and \fBEC_GROUP_new_from_ecpkparameters()\fR will create a group from the
specific PK \fBparams\fR.
.PP
\&\fBEC_GROUP_set_curve()\fR sets the curve parameters \fBp\fR, \fBa\fR and \fBb\fR. For a curve
over Fp \fBp\fR is the prime for the field. For a curve over F2^m \fBp\fR represents
the irreducible polynomial \- each bit represents a term in the polynomial.
Therefore, there will either be three or five bits set dependent on whether the
polynomial is a trinomial or a pentanomial.
In either case, \fBa\fR and \fBb\fR represents the coefficients a and b from the
relevant equation introduced above.
.PP
\&\fBEC_group_get_curve()\fR obtains the previously set curve parameters.
.PP
\&\fBEC_GROUP_set_curve_GFp()\fR and \fBEC_GROUP_set_curve_GF2m()\fR are synonyms for
\&\fBEC_GROUP_set_curve()\fR. They are defined for backwards compatibility only and
should not be used.
.PP
\&\fBEC_GROUP_get_curve_GFp()\fR and \fBEC_GROUP_get_curve_GF2m()\fR are synonyms for
\&\fBEC_GROUP_get_curve()\fR. They are defined for backwards compatibility only and
should not be used.
.PP
The functions \fBEC_GROUP_new_curve_GFp()\fR and \fBEC_GROUP_new_curve_GF2m()\fR are
shortcuts for calling \fBEC_GROUP_new()\fR and then the \fBEC_GROUP_set_curve()\fR function.
An appropriate default implementation method will be used.
.PP
Whilst the library can be used to create any curve using the functions described
above, there are also a number of predefined curves that are available. In order
to obtain a list of all of the predefined curves, call the function
\&\fBEC_get_builtin_curves()\fR. The parameter \fBr\fR should be an array of
EC_builtin_curve structures of size \fBnitems\fR. The function will populate the
\&\fBr\fR array with information about the builtin curves. If \fBnitems\fR is less than
the total number of curves available, then the first \fBnitems\fR curves will be
returned. Otherwise the total number of curves will be provided. The return
value is the total number of curves available (whether that number has been
populated in \fBr\fR or not). Passing a NULL \fBr\fR, or setting \fBnitems\fR to 0 will
do nothing other than return the total number of curves available.
The EC_builtin_curve structure is defined as follows:
.PP
.Vb 4
\& typedef struct {
\&        int nid;
\&        const char *comment;
\&        } EC_builtin_curve;
.Ve
.PP
Each EC_builtin_curve item has a unique integer id (\fBnid\fR), and a human
readable comment string describing the curve.
.PP
In order to construct a builtin curve use the function
\&\fBEC_GROUP_new_by_curve_name()\fR and provide the \fBnid\fR of the curve to
be constructed.
.PP
\&\fBEC_GROUP_free()\fR frees the memory associated with the EC_GROUP.
If \fBgroup\fR is NULL nothing is done.
.PP
\&\fBEC_GROUP_clear_free()\fR destroys any sensitive data held within the EC_GROUP and
then frees its memory. If \fBgroup\fR is NULL nothing is done.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All EC_GROUP_new* functions return a pointer to the newly constructed group, or
NULL on error.
.PP
\&\fBEC_get_builtin_curves()\fR returns the number of builtin curves that are available.
.PP
\&\fBEC_GROUP_set_curve_GFp()\fR, \fBEC_GROUP_get_curve_GFp()\fR, \fBEC_GROUP_set_curve_GF2m()\fR,
\&\fBEC_GROUP_get_curve_GF2m()\fR return 1 on success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBEC_GROUP_copy\fR\|(3),
\&\fBEC_POINT_new\fR\|(3), \fBEC_POINT_add\fR\|(3), \fBEC_KEY_new\fR\|(3),
\&\fBEC_GFp_simple_method\fR\|(3), \fBd2i_ECPKParameters\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2013\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
