.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_READ 3"
.TH BIO_READ 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_read_ex, BIO_write_ex, BIO_read, BIO_write, BIO_gets, BIO_puts
\&\- BIO I/O functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& int BIO_read_ex(BIO *b, void *data, size_t dlen, size_t *readbytes);
\& int BIO_write_ex(BIO *b, const void *data, size_t dlen, size_t *written);
\&
\& int BIO_read(BIO *b, void *data, int dlen);
\& int BIO_gets(BIO *b, char *buf, int size);
\& int BIO_write(BIO *b, const void *data, int dlen);
\& int BIO_puts(BIO *b, const char *buf);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_read_ex()\fR attempts to read \fBdlen\fR bytes from BIO \fBb\fR and places the data
in \fBdata\fR. If any bytes were successfully read then the number of bytes read is
stored in \fB*readbytes\fR.
.PP
\&\fBBIO_write_ex()\fR attempts to write \fBdlen\fR bytes from \fBdata\fR to BIO \fBb\fR. If
successful then the number of bytes written is stored in \fB*written\fR.
.PP
\&\fBBIO_read()\fR attempts to read \fBlen\fR bytes from BIO \fBb\fR and places
the data in \fBbuf\fR.
.PP
\&\fBBIO_gets()\fR performs the BIOs "gets" operation and places the data
in \fBbuf\fR. Usually this operation will attempt to read a line of data
from the BIO of maximum length \fBsize\-1\fR. There are exceptions to this,
however; for example, \fBBIO_gets()\fR on a digest BIO will calculate and
return the digest and other BIOs may not support \fBBIO_gets()\fR at all.
The returned string is always NUL-terminated and the '\en' is preserved
if present in the input data.
.PP
\&\fBBIO_write()\fR attempts to write \fBlen\fR bytes from \fBbuf\fR to BIO \fBb\fR.
.PP
\&\fBBIO_puts()\fR attempts to write a NUL-terminated string \fBbuf\fR to BIO \fBb\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_read_ex()\fR and \fBBIO_write_ex()\fR return 1 if data was successfully read or
written, and 0 otherwise.
.PP
All other functions return either the amount of data successfully read or
written (if the return value is positive) or that no data was successfully
read or written if the result is 0 or \-1. If the return value is \-2 then
the operation is not implemented in the specific BIO type.  The trailing
NUL is not included in the length returned by \fBBIO_gets()\fR.
.SH NOTES
.IX Header "NOTES"
A 0 or \-1 return is not necessarily an indication of an error. In
particular when the source/sink is nonblocking or of a certain type
it may merely be an indication that no data is currently available and that
the application should retry the operation later.
.PP
One technique sometimes used with blocking sockets is to use a system call
(such as \fBselect()\fR, \fBpoll()\fR or equivalent) to determine when data is available
and then call \fBread()\fR to read the data. The equivalent with BIOs (that is call
\&\fBselect()\fR on the underlying I/O structure and then call \fBBIO_read()\fR to
read the data) should \fBnot\fR be used because a single call to \fBBIO_read()\fR
can cause several reads (and writes in the case of SSL BIOs) on the underlying
I/O structure and may block as a result. Instead \fBselect()\fR (or equivalent)
should be combined with non blocking I/O so successive reads will request
a retry instead of blocking.
.PP
See \fBBIO_should_retry\fR\|(3) for details of how to
determine the cause of a retry and other I/O issues.
.PP
If the \fBBIO_gets()\fR function is not supported by a BIO then it possible to
work around this by adding a buffering BIO \fBBIO_f_buffer\fR\|(3)
to the chain.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBIO_should_retry\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBBIO_gets()\fR on 1.1.0 and older when called on \fBBIO_fd()\fR based BIO does not
keep the '\en' at the end of the line in the buffer.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
