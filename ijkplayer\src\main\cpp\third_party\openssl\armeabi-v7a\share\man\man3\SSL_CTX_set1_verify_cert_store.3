.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET1_VERIFY_CERT_STORE 3"
.TH SSL_CTX_SET1_VERIFY_CERT_STORE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set0_verify_cert_store, SSL_CTX_set1_verify_cert_store,
SSL_CTX_set0_chain_cert_store, SSL_CTX_set1_chain_cert_store,
SSL_set0_verify_cert_store, SSL_set1_verify_cert_store,
SSL_set0_chain_cert_store, SSL_set1_chain_cert_store,
SSL_CTX_get0_verify_cert_store, SSL_CTX_get0_chain_cert_store,
SSL_get0_verify_cert_store, SSL_get0_chain_cert_store \- set certificate
verification or chain store
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_set0_verify_cert_store(SSL_CTX *ctx, X509_STORE *st);
\& int SSL_CTX_set1_verify_cert_store(SSL_CTX *ctx, X509_STORE *st);
\& int SSL_CTX_set0_chain_cert_store(SSL_CTX *ctx, X509_STORE *st);
\& int SSL_CTX_set1_chain_cert_store(SSL_CTX *ctx, X509_STORE *st);
\& int SSL_CTX_get0_verify_cert_store(SSL_CTX *ctx, X509_STORE **st);
\& int SSL_CTX_get0_chain_cert_store(SSL_CTX *ctx, X509_STORE **st);
\&
\& int SSL_set0_verify_cert_store(SSL *ctx, X509_STORE *st);
\& int SSL_set1_verify_cert_store(SSL *ctx, X509_STORE *st);
\& int SSL_set0_chain_cert_store(SSL *ctx, X509_STORE *st);
\& int SSL_set1_chain_cert_store(SSL *ctx, X509_STORE *st);
\& int SSL_get0_verify_cert_store(SSL *ctx, X509_STORE **st);
\& int SSL_get0_chain_cert_store(SSL *ctx, X509_STORE **st);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set0_verify_cert_store()\fR and \fBSSL_CTX_set1_verify_cert_store()\fR
set the certificate store used for certificate verification to \fBst\fR.
.PP
\&\fBSSL_CTX_set0_chain_cert_store()\fR and \fBSSL_CTX_set1_chain_cert_store()\fR
set the certificate store used for certificate chain building to \fBst\fR.
.PP
\&\fBSSL_set0_verify_cert_store()\fR, \fBSSL_set1_verify_cert_store()\fR,
\&\fBSSL_set0_chain_cert_store()\fR and \fBSSL_set1_chain_cert_store()\fR are similar
except they apply to SSL structure \fBssl\fR.
.PP
\&\fBSSL_CTX_get0_verify_chain_store()\fR, \fBSSL_get0_verify_chain_store()\fR,
\&\fBSSL_CTX_get0_chain_cert_store()\fR and \fBSSL_get0_chain_cert_store()\fR retrieve the
objects previously set via the above calls. A pointer to the object (or NULL if
no such object has been set) is written to \fB*st\fR.
.PP
All these functions are implemented as macros. Those containing a \fB1\fR
increment the reference count of the supplied store so it must
be freed at some point after the operation. Those containing a \fB0\fR do
not increment reference counts and the supplied store \fBMUST NOT\fR be freed
after the operation.
.SH NOTES
.IX Header "NOTES"
The stores pointers associated with an SSL_CTX structure are copied to any SSL
structures when \fBSSL_new()\fR is called. As a result SSL structures will not be
affected if the parent SSL_CTX store pointer is set to a new value.
.PP
The verification store is used to verify the certificate chain sent by the
peer: that is an SSL/TLS client will use the verification store to verify
the server's certificate chain and a SSL/TLS server will use it to verify
any client certificate chain.
.PP
The chain store is used to build the certificate chain.
.PP
If the mode \fBSSL_MODE_NO_AUTO_CHAIN\fR is set or a certificate chain is
configured already (for example using the functions such as
\&\fBSSL_CTX_add1_chain_cert\fR\|(3) or
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3)) then
automatic chain building is disabled.
.PP
If the mode \fBSSL_MODE_NO_AUTO_CHAIN\fR is set then automatic chain building
is disabled.
.PP
If the chain or the verification store is not set then the store associated
with the parent SSL_CTX is used instead to retain compatibility with previous
versions of OpenSSL.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All these functions return 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3)
\&\fBSSL_CTX_set0_chain\fR\|(3)
\&\fBSSL_CTX_set1_chain\fR\|(3)
\&\fBSSL_CTX_add0_chain_cert\fR\|(3)
\&\fBSSL_CTX_add1_chain_cert\fR\|(3)
\&\fBSSL_set0_chain\fR\|(3)
\&\fBSSL_set1_chain\fR\|(3)
\&\fBSSL_add0_chain_cert\fR\|(3)
\&\fBSSL_add1_chain_cert\fR\|(3)
\&\fBSSL_CTX_build_cert_chain\fR\|(3)
\&\fBSSL_build_cert_chain\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.2.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2013\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
