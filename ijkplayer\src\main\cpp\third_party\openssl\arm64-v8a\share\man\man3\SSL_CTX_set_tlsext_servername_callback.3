.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_TLSEXT_SERVERNAME_CALLBACK 3"
.TH SSL_CTX_SET_TLSEXT_SERVERNAME_CALLBACK 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_tlsext_servername_callback, SSL_CTX_set_tlsext_servername_arg,
SSL_get_servername_type, SSL_get_servername,
SSL_set_tlsext_host_name \- handle server name indication (SNI)
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_set_tlsext_servername_callback(SSL_CTX *ctx,
\&                                   int (*cb)(SSL *s, int *al, void *arg));
\& long SSL_CTX_set_tlsext_servername_arg(SSL_CTX *ctx, void *arg);
\&
\& const char *SSL_get_servername(const SSL *s, const int type);
\& int SSL_get_servername_type(const SSL *s);
\&
\& int SSL_set_tlsext_host_name(const SSL *s, const char *name);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The functionality provided by the servername callback is mostly superseded by
the ClientHello callback, which can be set using \fBSSL_CTX_set_client_hello_cb()\fR.
However, even where the ClientHello callback is used, the servername callback is
still necessary in order to acknowledge the servername requested by the client.
.PP
\&\fBSSL_CTX_set_tlsext_servername_callback()\fR sets the application callback \fBcb\fR
used by a server to perform any actions or configuration required based on
the servername extension received in the incoming connection. When \fBcb\fR
is NULL, SNI is not used.
.PP
The servername callback should return one of the following values:
.IP SSL_TLSEXT_ERR_OK 4
.IX Item "SSL_TLSEXT_ERR_OK"
This is used to indicate that the servername requested by the client has been
accepted. Typically a server will call \fBSSL_set_SSL_CTX()\fR in the callback to set
up a different configuration for the selected servername in this case.
.IP SSL_TLSEXT_ERR_ALERT_FATAL 4
.IX Item "SSL_TLSEXT_ERR_ALERT_FATAL"
In this case the servername requested by the client is not accepted and the
handshake will be aborted. The value of the alert to be used should be stored in
the location pointed to by the \fBal\fR parameter to the callback. By default this
value is initialised to SSL_AD_UNRECOGNIZED_NAME.
.IP SSL_TLSEXT_ERR_ALERT_WARNING 4
.IX Item "SSL_TLSEXT_ERR_ALERT_WARNING"
If this value is returned then the servername is not accepted by the server.
However, the handshake will continue and send a warning alert instead. The value
of the alert should be stored in the location pointed to by the \fBal\fR parameter
as for SSL_TLSEXT_ERR_ALERT_FATAL above. Note that TLSv1.3 does not support
warning alerts, so if TLSv1.3 has been negotiated then this return value is
treated the same way as SSL_TLSEXT_ERR_NOACK.
.IP SSL_TLSEXT_ERR_NOACK 4
.IX Item "SSL_TLSEXT_ERR_NOACK"
This return value indicates that the servername is not accepted by the server.
No alerts are sent and the server will not acknowledge the requested servername.
.PP
\&\fBSSL_CTX_set_tlsext_servername_arg()\fR sets a context-specific argument to be
passed into the callback (via the \fBarg\fR parameter) for this \fBSSL_CTX\fR.
.PP
The behaviour of \fBSSL_get_servername()\fR depends on a number of different factors.
In particular note that in TLSv1.3 the servername is negotiated in every
handshake. In TLSv1.2 the servername is only negotiated on initial handshakes
and not on resumption handshakes.
.IP "On the client, before the handshake" 4
.IX Item "On the client, before the handshake"
If a servername has been set via a call to \fBSSL_set_tlsext_host_name()\fR then it
will return that servername.
.Sp
If one has not been set, but a TLSv1.2 resumption is being attempted and the
session from the original handshake had a servername accepted by the server then
it will return that servername.
.Sp
Otherwise it returns NULL.
.IP "On the client, during or after the handshake and a TLSv1.2 (or below) resumption occurred" 4
.IX Item "On the client, during or after the handshake and a TLSv1.2 (or below) resumption occurred"
If the session from the original handshake had a servername accepted by the
server then it will return that servername.
.Sp
Otherwise it returns the servername set via \fBSSL_set_tlsext_host_name()\fR or NULL
if it was not called.
.IP "On the client, during or after the handshake and a TLSv1.2 (or below) resumption did not occur" 4
.IX Item "On the client, during or after the handshake and a TLSv1.2 (or below) resumption did not occur"
It will return the servername set via \fBSSL_set_tlsext_host_name()\fR or NULL if it
was not called.
.IP "On the server, before the handshake" 4
.IX Item "On the server, before the handshake"
The function will always return NULL before the handshake
.IP "On the server, after the servername extension has been processed and a TLSv1.2 (or below) resumption occurred" 4
.IX Item "On the server, after the servername extension has been processed and a TLSv1.2 (or below) resumption occurred"
If a servername was accepted by the server in the original handshake then it
will return that servername, or NULL otherwise.
.IP "On the server, after the servername extension has been processed and a TLSv1.2 (or below) resumption did not occur" 4
.IX Item "On the server, after the servername extension has been processed and a TLSv1.2 (or below) resumption did not occur"
The function will return the servername requested by the client in this
handshake or NULL if none was requested.
.PP
Note that the ClientHello callback occurs before a servername extension from the
client is processed. The servername, certificate and ALPN callbacks occur after
a servername extension from the client is processed.
.PP
\&\fBSSL_get_servername_type()\fR returns the servername type or \-1 if no servername
is present. Currently the only supported type (defined in RFC3546) is
\&\fBTLSEXT_NAMETYPE_host_name\fR.
.PP
\&\fBSSL_set_tlsext_host_name()\fR sets the server name indication ClientHello extension
to contain the value \fBname\fR. The type of server name indication extension is set
to \fBTLSEXT_NAMETYPE_host_name\fR (defined in RFC3546).
.SH NOTES
.IX Header "NOTES"
Several callbacks are executed during ClientHello processing, including
the ClientHello, ALPN, and servername callbacks.  The ClientHello callback is
executed first, then the servername callback, followed by the ALPN callback.
.PP
The \fBSSL_set_tlsext_host_name()\fR function should only be called on SSL objects
that will act as clients; otherwise the configured \fBname\fR will be ignored.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_tlsext_servername_callback()\fR and
\&\fBSSL_CTX_set_tlsext_servername_arg()\fR both always return 1 indicating success.
\&\fBSSL_set_tlsext_host_name()\fR returns 1 on success, 0 in case of error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_set_alpn_select_cb\fR\|(3),
\&\fBSSL_get0_alpn_selected\fR\|(3), \fBSSL_CTX_set_client_hello_cb\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBSSL_get_servername()\fR historically provided some unexpected results in certain
corner cases. This has been fixed from OpenSSL 1.1.1e.
.PP
Prior to 1.1.1e, when the client requested a servername in an initial TLSv1.2
handshake, the server accepted it, and then the client successfully resumed but
set a different explicit servername in the second handshake then when called by
the client it returned the servername from the second handshake. This has now
been changed to return the servername requested in the original handshake.
.PP
Also prior to 1.1.1e, if the client sent a servername in the first handshake but
the server did not accept it, and then a second handshake occurred where TLSv1.2
resumption was successful then when called by the server it returned the
servername requested in the original handshake. This has now been changed to
NULL.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
