.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_CONNECT 3"
.TH BIO_CONNECT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_socket, BIO_bind, BIO_connect, BIO_listen, BIO_accept_ex, BIO_closesocket \- BIO
socket communication setup routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& int BIO_socket(int domain, int socktype, int protocol, int options);
\& int BIO_bind(int sock, const BIO_ADDR *addr, int options);
\& int BIO_connect(int sock, const BIO_ADDR *addr, int options);
\& int BIO_listen(int sock, const BIO_ADDR *addr, int options);
\& int BIO_accept_ex(int accept_sock, BIO_ADDR *peer, int options);
\& int BIO_closesocket(int sock);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_socket()\fR creates a socket in the domain \fBdomain\fR, of type
\&\fBsocktype\fR and \fBprotocol\fR.  Socket \fBoptions\fR are currently unused,
but is present for future use.
.PP
\&\fBBIO_bind()\fR binds the source address and service to a socket and
may be useful before calling \fBBIO_connect()\fR.  The options may include
\&\fBBIO_SOCK_REUSEADDR\fR, which is described in "FLAGS" below.
.PP
\&\fBBIO_connect()\fR connects \fBsock\fR to the address and service given by
\&\fBaddr\fR.  Connection \fBoptions\fR may be zero or any combination of
\&\fBBIO_SOCK_KEEPALIVE\fR, \fBBIO_SOCK_NONBLOCK\fR and \fBBIO_SOCK_NODELAY\fR.
The flags are described in "FLAGS" below.
.PP
\&\fBBIO_listen()\fR has \fBsock\fR start listening on the address and service
given by \fBaddr\fR.  Connection \fBoptions\fR may be zero or any
combination of \fBBIO_SOCK_KEEPALIVE\fR, \fBBIO_SOCK_NONBLOCK\fR,
\&\fBBIO_SOCK_NODELAY\fR, \fBBIO_SOCK_REUSEADDR\fR and \fBBIO_SOCK_V6_ONLY\fR.
The flags are described in "FLAGS" below.
.PP
\&\fBBIO_accept_ex()\fR waits for an incoming connections on the given
socket \fBaccept_sock\fR.  When it gets a connection, the address and
port of the peer gets stored in \fBpeer\fR if that one is non-NULL.
Accept \fBoptions\fR may be zero or \fBBIO_SOCK_NONBLOCK\fR, and is applied
on the accepted socket.  The flags are described in "FLAGS" below.
.PP
\&\fBBIO_closesocket()\fR closes \fBsock\fR.
.SH FLAGS
.IX Header "FLAGS"
.IP BIO_SOCK_KEEPALIVE 4
.IX Item "BIO_SOCK_KEEPALIVE"
Enables regular sending of keep-alive messages.
.IP BIO_SOCK_NONBLOCK 4
.IX Item "BIO_SOCK_NONBLOCK"
Sets the socket to nonblocking mode.
.IP BIO_SOCK_NODELAY 4
.IX Item "BIO_SOCK_NODELAY"
Corresponds to \fBTCP_NODELAY\fR, and disables the Nagle algorithm.  With
this set, any data will be sent as soon as possible instead of being
buffered until there's enough for the socket to send out in one go.
.IP BIO_SOCK_REUSEADDR 4
.IX Item "BIO_SOCK_REUSEADDR"
Try to reuse the address and port combination for a recently closed
port.
.IP BIO_SOCK_V6_ONLY 4
.IX Item "BIO_SOCK_V6_ONLY"
When creating an IPv6 socket, make it only listen for IPv6 addresses
and not IPv4 addresses mapped to IPv6.
.PP
These flags are bit flags, so they are to be combined with the
\&\f(CW\*(C`|\*(C'\fR operator, for example:
.PP
.Vb 1
\& BIO_connect(sock, addr, BIO_SOCK_KEEPALIVE | BIO_SOCK_NONBLOCK);
.Ve
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_socket()\fR returns the socket number on success or \fBINVALID_SOCKET\fR
(\-1) on error.  When an error has occurred, the OpenSSL error stack
will hold the error data and errno has the system error.
.PP
\&\fBBIO_bind()\fR, \fBBIO_connect()\fR and \fBBIO_listen()\fR return 1 on success or 0 on error.
When an error has occurred, the OpenSSL error stack will hold the error
data and errno has the system error.
.PP
\&\fBBIO_accept_ex()\fR returns the accepted socket on success or
\&\fBINVALID_SOCKET\fR (\-1) on error.  When an error has occurred, the
OpenSSL error stack will hold the error data and errno has the system
error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBIO_ADDR\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBBIO_gethostname()\fR, \fBBIO_get_port()\fR, \fBBIO_get_host_ip()\fR,
\&\fBBIO_get_accept_socket()\fR and \fBBIO_accept()\fR were deprecated in OpenSSL 1.1.0.
Use the functions described above instead.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
