<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>pkcs12</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#PARSING-OPTIONS">PARSING OPTIONS</a></li>
  <li><a href="#FILE-CREATION-OPTIONS">FILE CREATION OPTIONS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-pkcs12, pkcs12 - PKCS#12 file utility</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>pkcs12</b> [<b>-help</b>] [<b>-export</b>] [<b>-chain</b>] [<b>-inkey file_or_id</b>] [<b>-certfile filename</b>] [<b>-name name</b>] [<b>-caname name</b>] [<b>-in filename</b>] [<b>-out filename</b>] [<b>-noout</b>] [<b>-nomacver</b>] [<b>-nocerts</b>] [<b>-clcerts</b>] [<b>-cacerts</b>] [<b>-nokeys</b>] [<b>-info</b>] [<b>-des | -des3 | -idea | -aes128 | -aes192 | -aes256 | -aria128 | -aria192 | -aria256 | -camellia128 | -camellia192 | -camellia256 | -nodes</b>] [<b>-noiter</b>] [<b>-maciter | -nomaciter | -nomac</b>] [<b>-twopass</b>] [<b>-descert</b>] [<b>-certpbe cipher</b>] [<b>-keypbe cipher</b>] [<b>-macalg digest</b>] [<b>-keyex</b>] [<b>-keysig</b>] [<b>-password arg</b>] [<b>-passin arg</b>] [<b>-passout arg</b>] [<b>-rand file...</b>] [<b>-writerand file</b>] [<b>-CAfile file</b>] [<b>-CApath dir</b>] [<b>-no-CAfile</b>] [<b>-no-CApath</b>] [<b>-CSP name</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>pkcs12</b> command allows PKCS#12 files (sometimes referred to as PFX files) to be created and parsed. PKCS#12 files are used by several programs including Netscape, MSIE and MS Outlook.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<p>There are a lot of options the meaning of some depends of whether a PKCS#12 file is being created or parsed. By default a PKCS#12 file is parsed. A PKCS#12 file can be created by using the <b>-export</b> option (see below).</p>

<h1 id="PARSING-OPTIONS">PARSING OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="in-filename"><b>-in filename</b></dt>
<dd>

<p>This specifies filename of the PKCS#12 file to be parsed. Standard input is used by default.</p>

</dd>
<dt id="out-filename"><b>-out filename</b></dt>
<dd>

<p>The filename to write certificates and private keys to, standard output by default. They are all written in PEM format.</p>

</dd>
<dt id="passin-arg"><b>-passin arg</b></dt>
<dd>

<p>The PKCS#12 file (i.e. input file) password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="passout-arg"><b>-passout arg</b></dt>
<dd>

<p>Pass phrase source to encrypt any outputted private keys with. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="password-arg"><b>-password arg</b></dt>
<dd>

<p>With -export, -password is equivalent to -passout. Otherwise, -password is equivalent to -passin.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>This option inhibits output of the keys and certificates to the output file version of the PKCS#12 file.</p>

</dd>
<dt id="clcerts"><b>-clcerts</b></dt>
<dd>

<p>Only output client certificates (not CA certificates).</p>

</dd>
<dt id="cacerts"><b>-cacerts</b></dt>
<dd>

<p>Only output CA certificates (not client certificates).</p>

</dd>
<dt id="nocerts"><b>-nocerts</b></dt>
<dd>

<p>No certificates at all will be output.</p>

</dd>
<dt id="nokeys"><b>-nokeys</b></dt>
<dd>

<p>No private keys will be output.</p>

</dd>
<dt id="info"><b>-info</b></dt>
<dd>

<p>Output additional information about the PKCS#12 file structure, algorithms used and iteration counts.</p>

</dd>
<dt id="des"><b>-des</b></dt>
<dd>

<p>Use DES to encrypt private keys before outputting.</p>

</dd>
<dt id="des3"><b>-des3</b></dt>
<dd>

<p>Use triple DES to encrypt private keys before outputting, this is the default.</p>

</dd>
<dt id="idea"><b>-idea</b></dt>
<dd>

<p>Use IDEA to encrypt private keys before outputting.</p>

</dd>
<dt id="aes128--aes192--aes256"><b>-aes128</b>, <b>-aes192</b>, <b>-aes256</b></dt>
<dd>

<p>Use AES to encrypt private keys before outputting.</p>

</dd>
<dt id="aria128--aria192--aria256"><b>-aria128</b>, <b>-aria192</b>, <b>-aria256</b></dt>
<dd>

<p>Use ARIA to encrypt private keys before outputting.</p>

</dd>
<dt id="camellia128--camellia192--camellia256"><b>-camellia128</b>, <b>-camellia192</b>, <b>-camellia256</b></dt>
<dd>

<p>Use Camellia to encrypt private keys before outputting.</p>

</dd>
<dt id="nodes"><b>-nodes</b></dt>
<dd>

<p>Don&#39;t encrypt the private keys at all.</p>

</dd>
<dt id="nomacver"><b>-nomacver</b></dt>
<dd>

<p>Don&#39;t attempt to verify the integrity MAC before reading the file.</p>

</dd>
<dt id="twopass"><b>-twopass</b></dt>
<dd>

<p>Prompt for separate integrity and encryption passwords: most software always assumes these are the same so this option will render such PKCS#12 files unreadable. Cannot be used in combination with the options -password, -passin (if importing) or -passout (if exporting).</p>

</dd>
</dl>

<h1 id="FILE-CREATION-OPTIONS">FILE CREATION OPTIONS</h1>

<dl>

<dt id="export"><b>-export</b></dt>
<dd>

<p>This option specifies that a PKCS#12 file will be created rather than parsed.</p>

</dd>
<dt id="out-filename1"><b>-out filename</b></dt>
<dd>

<p>This specifies filename to write the PKCS#12 file to. Standard output is used by default.</p>

</dd>
<dt id="in-filename1"><b>-in filename</b></dt>
<dd>

<p>The filename to read certificates and private keys from, standard input by default. They must all be in PEM format. The order doesn&#39;t matter but one private key and its corresponding certificate should be present. If additional certificates are present they will also be included in the PKCS#12 file.</p>

</dd>
<dt id="inkey-file_or_id"><b>-inkey file_or_id</b></dt>
<dd>

<p>File to read private key from. If not present then a private key must be present in the input file. If no engine is used, the argument is taken as a file; if an engine is specified, the argument is given to the engine as a key identifier.</p>

</dd>
<dt id="name-friendlyname"><b>-name friendlyname</b></dt>
<dd>

<p>This specifies the &quot;friendly name&quot; for the certificate and private key. This name is typically displayed in list boxes by software importing the file.</p>

</dd>
<dt id="certfile-filename"><b>-certfile filename</b></dt>
<dd>

<p>A filename to read additional certificates from.</p>

</dd>
<dt id="caname-friendlyname"><b>-caname friendlyname</b></dt>
<dd>

<p>This specifies the &quot;friendly name&quot; for other certificates. This option may be used multiple times to specify names for all certificates in the order they appear. Netscape ignores friendly names on other certificates whereas MSIE displays them.</p>

</dd>
<dt id="pass-arg--passout-arg"><b>-pass arg</b>, <b>-passout arg</b></dt>
<dd>

<p>The PKCS#12 file (i.e. output file) password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="passin-password"><b>-passin password</b></dt>
<dd>

<p>Pass phrase source to decrypt any input private keys with. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="chain"><b>-chain</b></dt>
<dd>

<p>If this option is present then an attempt is made to include the entire certificate chain of the user certificate. The standard CA store is used for this search. If the search fails it is considered a fatal error.</p>

</dd>
<dt id="descert"><b>-descert</b></dt>
<dd>

<p>Encrypt the certificate using triple DES, this may render the PKCS#12 file unreadable by some &quot;export grade&quot; software. By default the private key is encrypted using triple DES and the certificate using 40 bit RC2 unless RC2 is disabled in which case triple DES is used.</p>

</dd>
<dt id="keypbe-alg--certpbe-alg"><b>-keypbe alg</b>, <b>-certpbe alg</b></dt>
<dd>

<p>These options allow the algorithm used to encrypt the private key and certificates to be selected. Any PKCS#5 v1.5 or PKCS#12 PBE algorithm name can be used (see <b>NOTES</b> section for more information). If a cipher name (as output by the <b>list-cipher-algorithms</b> command is specified then it is used with PKCS#5 v2.0. For interoperability reasons it is advisable to only use PKCS#12 algorithms.</p>

</dd>
<dt id="keyex--keysig"><b>-keyex|-keysig</b></dt>
<dd>

<p>Specifies that the private key is to be used for key exchange or just signing. This option is only interpreted by MSIE and similar MS software. Normally &quot;export grade&quot; software will only allow 512 bit RSA keys to be used for encryption purposes but arbitrary length keys for signing. The <b>-keysig</b> option marks the key for signing only. Signing only keys can be used for S/MIME signing, authenticode (ActiveX control signing) and SSL client authentication, however, due to a bug only MSIE 5.0 and later support the use of signing only keys for SSL client authentication.</p>

</dd>
<dt id="macalg-digest"><b>-macalg digest</b></dt>
<dd>

<p>Specify the MAC digest algorithm. If not included them SHA1 will be used.</p>

</dd>
<dt id="nomaciter--noiter"><b>-nomaciter</b>, <b>-noiter</b></dt>
<dd>

<p>These options affect the iteration counts on the MAC and key algorithms. Unless you wish to produce files compatible with MSIE 4.0 you should leave these options alone.</p>

<p>To discourage attacks by using large dictionaries of common passwords the algorithm that derives keys from passwords can have an iteration count applied to it: this causes a certain part of the algorithm to be repeated and slows it down. The MAC is used to check the file integrity but since it will normally have the same password as the keys and certificates it could also be attacked. By default both MAC and encryption iteration counts are set to 2048, using these options the MAC and encryption iteration counts can be set to 1, since this reduces the file security you should not use these options unless you really have to. Most software supports both MAC and key iteration counts. MSIE 4.0 doesn&#39;t support MAC iteration counts so it needs the <b>-nomaciter</b> option.</p>

</dd>
<dt id="maciter"><b>-maciter</b></dt>
<dd>

<p>This option is included for compatibility with previous versions, it used to be needed to use MAC iterations counts but they are now used by default.</p>

</dd>
<dt id="nomac"><b>-nomac</b></dt>
<dd>

<p>Don&#39;t attempt to provide the MAC integrity.</p>

</dd>
<dt id="rand-file"><b>-rand file...</b></dt>
<dd>

<p>A file or files containing random data used to seed the random number generator. Multiple files can be specified separated by an OS-dependent character. The separator is <b>;</b> for MS-Windows, <b>,</b> for OpenVMS, and <b>:</b> for all others.</p>

</dd>
<dt id="writerand-file">[<b>-writerand file</b>]</dt>
<dd>

<p>Writes random data to the specified <i>file</i> upon exit. This can be used with a subsequent <b>-rand</b> flag.</p>

</dd>
<dt id="CAfile-file"><b>-CAfile file</b></dt>
<dd>

<p>CA storage as a file.</p>

</dd>
<dt id="CApath-dir"><b>-CApath dir</b></dt>
<dd>

<p>CA storage as a directory. This directory must be a standard certificate directory: that is a hash of each subject name (using <b>x509 -hash</b>) should be linked to each certificate.</p>

</dd>
<dt id="no-CAfile"><b>-no-CAfile</b></dt>
<dd>

<p>Do not load the trusted CA certificates from the default file location.</p>

</dd>
<dt id="no-CApath"><b>-no-CApath</b></dt>
<dd>

<p>Do not load the trusted CA certificates from the default directory location.</p>

</dd>
<dt id="CSP-name"><b>-CSP name</b></dt>
<dd>

<p>Write <b>name</b> as a Microsoft CSP name.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>Although there are a large number of options most of them are very rarely used. For PKCS#12 file parsing only <b>-in</b> and <b>-out</b> need to be used for PKCS#12 file creation <b>-export</b> and <b>-name</b> are also used.</p>

<p>If none of the <b>-clcerts</b>, <b>-cacerts</b> or <b>-nocerts</b> options are present then all certificates will be output in the order they appear in the input PKCS#12 files. There is no guarantee that the first certificate present is the one corresponding to the private key. Certain software which requires a private key and certificate and assumes the first certificate in the file is the one corresponding to the private key: this may not always be the case. Using the <b>-clcerts</b> option will solve this problem by only outputting the certificate corresponding to the private key. If the CA certificates are required then they can be output to a separate file using the <b>-nokeys -cacerts</b> options to just output CA certificates.</p>

<p>The <b>-keypbe</b> and <b>-certpbe</b> algorithms allow the precise encryption algorithms for private keys and certificates to be specified. Normally the defaults are fine but occasionally software can&#39;t handle triple DES encrypted private keys, then the option <b>-keypbe PBE-SHA1-RC2-40</b> can be used to reduce the private key encryption to 40 bit RC2. A complete description of all algorithms is contained in the <b>pkcs8</b> manual page.</p>

<p>Prior 1.1 release passwords containing non-ASCII characters were encoded in non-compliant manner, which limited interoperability, in first hand with Windows. But switching to standard-compliant password encoding poses problem accessing old data protected with broken encoding. For this reason even legacy encodings is attempted when reading the data. If you use PKCS#12 files in production application you are advised to convert the data, because implemented heuristic approach is not MT-safe, its sole goal is to facilitate the data upgrade with this utility.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Parse a PKCS#12 file and output it to a file:</p>

<pre><code>openssl pkcs12 -in file.p12 -out file.pem</code></pre>

<p>Output only client certificates to a file:</p>

<pre><code>openssl pkcs12 -in file.p12 -clcerts -out file.pem</code></pre>

<p>Don&#39;t encrypt the private key:</p>

<pre><code>openssl pkcs12 -in file.p12 -out file.pem -nodes</code></pre>

<p>Print some info about a PKCS#12 file:</p>

<pre><code>openssl pkcs12 -in file.p12 -info -noout</code></pre>

<p>Create a PKCS#12 file:</p>

<pre><code>openssl pkcs12 -export -in file.pem -out file.p12 -name &quot;My Certificate&quot;</code></pre>

<p>Include some extra certificates:</p>

<pre><code>openssl pkcs12 -export -in file.pem -out file.p12 -name &quot;My Certificate&quot; \
 -certfile othercerts.pem</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/pkcs8.html">pkcs8(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


