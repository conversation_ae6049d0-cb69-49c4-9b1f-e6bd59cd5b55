.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_DES 3"
.TH EVP_DES 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_des_cbc,
EVP_des_cfb,
EVP_des_cfb1,
EVP_des_cfb8,
EVP_des_cfb64,
EVP_des_ecb,
EVP_des_ofb,
EVP_des_ede,
EVP_des_ede_cbc,
EVP_des_ede_cfb,
EVP_des_ede_cfb64,
EVP_des_ede_ecb,
EVP_des_ede_ofb,
EVP_des_ede3,
EVP_des_ede3_cbc,
EVP_des_ede3_cfb,
EVP_des_ede3_cfb1,
EVP_des_ede3_cfb8,
EVP_des_ede3_cfb64,
EVP_des_ede3_ecb,
EVP_des_ede3_ofb,
EVP_des_ede3_wrap
\&\- EVP DES cipher
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& const EVP_CIPHER *EVP_ciphername(void)
.Ve
.PP
\&\fIEVP_ciphername\fR is used a placeholder for any of the described cipher
functions, such as \fIEVP_des_cbc\fR.
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The DES encryption algorithm for EVP.
.IP "\fBEVP_des_cbc()\fR, \fBEVP_des_ecb()\fR, \fBEVP_des_cfb()\fR, \fBEVP_des_cfb1()\fR, \fBEVP_des_cfb8()\fR, \fBEVP_des_cfb64()\fR, \fBEVP_des_ofb()\fR" 4
.IX Item "EVP_des_cbc(), EVP_des_ecb(), EVP_des_cfb(), EVP_des_cfb1(), EVP_des_cfb8(), EVP_des_cfb64(), EVP_des_ofb()"
DES in CBC, ECB, CFB with 64\-bit shift, CFB with 1\-bit shift, CFB with 8\-bit
shift and OFB modes.
.IP "\fBEVP_des_ede()\fR, \fBEVP_des_ede_cbc()\fR, \fBEVP_des_ede_cfb()\fR, \fBEVP_des_ede_cfb64()\fR, \fBEVP_des_ede_ecb()\fR, \fBEVP_des_ede_ofb()\fR" 4
.IX Item "EVP_des_ede(), EVP_des_ede_cbc(), EVP_des_ede_cfb(), EVP_des_ede_cfb64(), EVP_des_ede_ecb(), EVP_des_ede_ofb()"
Two key triple DES in ECB, CBC, CFB with 64\-bit shift and OFB modes.
.IP "\fBEVP_des_ede3()\fR, \fBEVP_des_ede3_cbc()\fR, \fBEVP_des_ede3_cfb()\fR, \fBEVP_des_ede3_cfb1()\fR, \fBEVP_des_ede3_cfb8()\fR, \fBEVP_des_ede3_cfb64()\fR, \fBEVP_des_ede3_ecb()\fR, \fBEVP_des_ede3_ofb()\fR" 4
.IX Item "EVP_des_ede3(), EVP_des_ede3_cbc(), EVP_des_ede3_cfb(), EVP_des_ede3_cfb1(), EVP_des_ede3_cfb8(), EVP_des_ede3_cfb64(), EVP_des_ede3_ecb(), EVP_des_ede3_ofb()"
Three-key triple DES in ECB, CBC, CFB with 64\-bit shift, CFB with 1\-bit shift,
CFB with 8\-bit shift and OFB modes.
.IP \fBEVP_des_ede3_wrap()\fR 4
.IX Item "EVP_des_ede3_wrap()"
Triple-DES key wrap according to RFC 3217 Section 3.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return an \fBEVP_CIPHER\fR structure that contains the
implementation of the symmetric cipher. See \fBEVP_CIPHER_meth_new\fR\|(3) for
details of the \fBEVP_CIPHER\fR structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7),
\&\fBEVP_EncryptInit\fR\|(3),
\&\fBEVP_CIPHER_meth_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
