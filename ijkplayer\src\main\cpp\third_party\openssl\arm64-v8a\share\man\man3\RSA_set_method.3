.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_SET_METHOD 3"
.TH RSA_SET_METHOD 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_set_default_method, RSA_get_default_method, RSA_set_method,
RSA_get_method, RSA_PKCS1_OpenSSL, RSA_flags,
RSA_new_method \- select RSA method
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
\&
\& void RSA_set_default_method(const RSA_METHOD *meth);
\&
\& RSA_METHOD *RSA_get_default_method(void);
\&
\& int RSA_set_method(RSA *rsa, const RSA_METHOD *meth);
\&
\& RSA_METHOD *RSA_get_method(const RSA *rsa);
\&
\& RSA_METHOD *RSA_PKCS1_OpenSSL(void);
\&
\& int RSA_flags(const RSA *rsa);
\&
\& RSA *RSA_new_method(ENGINE *engine);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
An \fBRSA_METHOD\fR specifies the functions that OpenSSL uses for RSA
operations. By modifying the method, alternative implementations such as
hardware accelerators may be used. IMPORTANT: See the NOTES section for
important information about how these RSA API functions are affected by the
use of \fBENGINE\fR API calls.
.PP
Initially, the default RSA_METHOD is the OpenSSL internal implementation,
as returned by \fBRSA_PKCS1_OpenSSL()\fR.
.PP
\&\fBRSA_set_default_method()\fR makes \fBmeth\fR the default method for all RSA
structures created later.
\&\fBNB\fR: This is true only whilst no ENGINE has
been set as a default for RSA, so this function is no longer recommended.
This function is not thread-safe and should not be called at the same time
as other OpenSSL functions.
.PP
\&\fBRSA_get_default_method()\fR returns a pointer to the current default
RSA_METHOD. However, the meaningfulness of this result is dependent on
whether the ENGINE API is being used, so this function is no longer
recommended.
.PP
\&\fBRSA_set_method()\fR selects \fBmeth\fR to perform all operations using the key
\&\fBrsa\fR. This will replace the RSA_METHOD used by the RSA key and if the
previous method was supplied by an ENGINE, the handle to that ENGINE will
be released during the change. It is possible to have RSA keys that only
work with certain RSA_METHOD implementations (e.g. from an ENGINE module
that supports embedded hardware-protected keys), and in such cases
attempting to change the RSA_METHOD for the key can have unexpected
results.
.PP
\&\fBRSA_get_method()\fR returns a pointer to the RSA_METHOD being used by \fBrsa\fR.
This method may or may not be supplied by an ENGINE implementation, but if
it is, the return value can only be guaranteed to be valid as long as the
RSA key itself is valid and does not have its implementation changed by
\&\fBRSA_set_method()\fR.
.PP
\&\fBRSA_flags()\fR returns the \fBflags\fR that are set for \fBrsa\fR's current
RSA_METHOD. See the BUGS section.
.PP
\&\fBRSA_new_method()\fR allocates and initializes an RSA structure so that
\&\fBengine\fR will be used for the RSA operations. If \fBengine\fR is NULL, the
default ENGINE for RSA operations is used, and if no default ENGINE is set,
the RSA_METHOD controlled by \fBRSA_set_default_method()\fR is used.
.PP
\&\fBRSA_flags()\fR returns the \fBflags\fR that are set for \fBrsa\fR's current method.
.PP
\&\fBRSA_new_method()\fR allocates and initializes an \fBRSA\fR structure so that
\&\fBmethod\fR will be used for the RSA operations. If \fBmethod\fR is \fBNULL\fR,
the default method is used.
.SH "THE RSA_METHOD STRUCTURE"
.IX Header "THE RSA_METHOD STRUCTURE"
.Vb 4
\& typedef struct rsa_meth_st
\& {
\&     /* name of the implementation */
\&     const char *name;
\&
\&     /* encrypt */
\&     int (*rsa_pub_enc)(int flen, unsigned char *from,
\&                        unsigned char *to, RSA *rsa, int padding);
\&
\&     /* verify arbitrary data */
\&     int (*rsa_pub_dec)(int flen, unsigned char *from,
\&                        unsigned char *to, RSA *rsa, int padding);
\&
\&     /* sign arbitrary data */
\&     int (*rsa_priv_enc)(int flen, unsigned char *from,
\&                         unsigned char *to, RSA *rsa, int padding);
\&
\&     /* decrypt */
\&     int (*rsa_priv_dec)(int flen, unsigned char *from,
\&                         unsigned char *to, RSA *rsa, int padding);
\&
\&     /* compute r0 = r0 ^ I mod rsa\->n (May be NULL for some implementations) */
\&     int (*rsa_mod_exp)(BIGNUM *r0, BIGNUM *I, RSA *rsa);
\&
\&     /* compute r = a ^ p mod m (May be NULL for some implementations) */
\&     int (*bn_mod_exp)(BIGNUM *r, BIGNUM *a, const BIGNUM *p,
\&                       const BIGNUM *m, BN_CTX *ctx, BN_MONT_CTX *m_ctx);
\&
\&     /* called at RSA_new */
\&     int (*init)(RSA *rsa);
\&
\&     /* called at RSA_free */
\&     int (*finish)(RSA *rsa);
\&
\&     /*
\&      * RSA_FLAG_EXT_PKEY        \- rsa_mod_exp is called for private key
\&      *                            operations, even if p,q,dmp1,dmq1,iqmp
\&      *                            are NULL
\&      * RSA_METHOD_FLAG_NO_CHECK \- don\*(Aqt check pub/private match
\&      */
\&     int flags;
\&
\&     char *app_data; /* ?? */
\&
\&     int (*rsa_sign)(int type,
\&                     const unsigned char *m, unsigned int m_length,
\&                     unsigned char *sigret, unsigned int *siglen, const RSA *rsa);
\&     int (*rsa_verify)(int dtype,
\&                       const unsigned char *m, unsigned int m_length,
\&                       const unsigned char *sigbuf, unsigned int siglen,
\&                       const RSA *rsa);
\&     /* keygen. If NULL builtin RSA key generation will be used */
\&     int (*rsa_keygen)(RSA *rsa, int bits, BIGNUM *e, BN_GENCB *cb);
\&
\& } RSA_METHOD;
.Ve
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRSA_PKCS1_OpenSSL()\fR, \fBRSA_PKCS1_null_method()\fR, \fBRSA_get_default_method()\fR
and \fBRSA_get_method()\fR return pointers to the respective RSA_METHODs.
.PP
\&\fBRSA_set_default_method()\fR returns no value.
.PP
\&\fBRSA_set_method()\fR returns a pointer to the old RSA_METHOD implementation
that was replaced. However, this return value should probably be ignored
because if it was supplied by an ENGINE, the pointer could be invalidated
at any time if the ENGINE is unloaded (in fact it could be unloaded as a
result of the \fBRSA_set_method()\fR function releasing its handle to the
ENGINE). For this reason, the return type may be replaced with a \fBvoid\fR
declaration in a future release.
.PP
\&\fBRSA_new_method()\fR returns NULL and sets an error code that can be obtained
by \fBERR_get_error\fR\|(3) if the allocation fails. Otherwise
it returns a pointer to the newly allocated structure.
.SH BUGS
.IX Header "BUGS"
The behaviour of \fBRSA_flags()\fR is a mis-feature that is left as-is for now
to avoid creating compatibility problems. RSA functionality, such as the
encryption functions, are controlled by the \fBflags\fR value in the RSA key
itself, not by the \fBflags\fR value in the RSA_METHOD attached to the RSA key
(which is what this function returns). If the flags element of an RSA key
is changed, the changes will be honoured by RSA functionality but will not
be reflected in the return value of the \fBRSA_flags()\fR function \- in effect
\&\fBRSA_flags()\fR behaves more like an \fBRSA_default_flags()\fR function (which does
not currently exist).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRSA_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBRSA_null_method()\fR, which was a partial attempt to avoid patent issues,
was replaced to always return NULL in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
