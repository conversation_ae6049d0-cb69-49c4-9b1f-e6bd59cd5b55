<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>d2i_PrivateKey</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>d2i_PrivateKey, d2i_PublicKey, d2i_AutoPrivateKey, i2d_PrivateKey, i2d_PublicKey, d2i_PrivateKey_bio, d2i_PrivateKey_fp - decode and encode functions for reading and saving EVP_PKEY structures</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

EVP_PKEY *d2i_PrivateKey(int type, EVP_PKEY **a, const unsigned char **pp,
                         long length);
EVP_PKEY *d2i_PublicKey(int type, EVP_PKEY **a, const unsigned char **pp,
                        long length);
EVP_PKEY *d2i_AutoPrivateKey(EVP_PKEY **a, const unsigned char **pp,
                             long length);
int i2d_PrivateKey(EVP_PKEY *a, unsigned char **pp);
int i2d_PublicKey(EVP_PKEY *a, unsigned char **pp);

EVP_PKEY *d2i_PrivateKey_bio(BIO *bp, EVP_PKEY **a);
EVP_PKEY *d2i_PrivateKey_fp(FILE *fp, EVP_PKEY **a)</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>d2i_PrivateKey() decodes a private key using algorithm <b>type</b>. It attempts to use any key specific format or PKCS#8 unencrypted PrivateKeyInfo format. The <b>type</b> parameter should be a public key algorithm constant such as <b>EVP_PKEY_RSA</b>. An error occurs if the decoded key does not match <b>type</b>. d2i_PublicKey() does the same for public keys.</p>

<p>d2i_AutoPrivateKey() is similar to d2i_PrivateKey() except it attempts to automatically detect the private key format.</p>

<p>i2d_PrivateKey() encodes <b>key</b>. It uses a key specific format or, if none is defined for that key type, PKCS#8 unencrypted PrivateKeyInfo format. i2d_PublicKey() does the same for public keys.</p>

<p>These functions are similar to the d2i_X509() functions; see <a href="../man3/d2i_X509.html">d2i_X509(3)</a>.</p>

<h1 id="NOTES">NOTES</h1>

<p>All the functions that operate on data in memory update the data pointer <i>*pp</i> after a successful operation, just like the other d2i and i2d functions; see <a href="../man3/d2i_X509.html">d2i_X509(3)</a>.</p>

<p>All these functions use DER format and unencrypted keys. Applications wishing to encrypt or decrypt private keys should use other functions such as d2i_PKCS8PrivateKey() instead.</p>

<p>If the <b>*a</b> is not NULL when calling d2i_PrivateKey() or d2i_AutoPrivateKey() (i.e. an existing structure is being reused) and the key format is PKCS#8 then <b>*a</b> will be freed and replaced on a successful call.</p>

<p>To decode a key with type <b>EVP_PKEY_EC</b>, d2i_PublicKey() requires <b>*a</b> to be a non-NULL EVP_PKEY structure assigned an EC_KEY structure referencing the proper EC_GROUP.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The d2i_PrivateKey(), d2i_AutoPrivateKey(), d2i_PrivateKey_bio(), d2i_PrivateKey_fp(), and d2i_PublicKey() functions return a valid <b>EVP_KEY</b> structure or <b>NULL</b> if an error occurs. The error code can be obtained by calling <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>.</p>

<p>i2d_PrivateKey() and i2d_PublicKey() return the number of bytes successfully encoded or a negative value if an error occurs. The error code can be obtained by calling <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/crypto.html">crypto(7)</a>, <a href="../man3/d2i_PKCS8PrivateKey_bio.html">d2i_PKCS8PrivateKey_bio(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


