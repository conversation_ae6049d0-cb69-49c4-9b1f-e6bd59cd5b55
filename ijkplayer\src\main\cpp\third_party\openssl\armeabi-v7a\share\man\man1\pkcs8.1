.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS8 1"
.TH PKCS8 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-pkcs8,
pkcs8 \- PKCS#8 format private key conversion tool
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBpkcs8\fR
[\fB\-help\fR]
[\fB\-topk8\fR]
[\fB\-inform PEM|DER\fR]
[\fB\-outform PEM|DER\fR]
[\fB\-in filename\fR]
[\fB\-passin arg\fR]
[\fB\-out filename\fR]
[\fB\-passout arg\fR]
[\fB\-iter count\fR]
[\fB\-noiter\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-nocrypt\fR]
[\fB\-traditional\fR]
[\fB\-v2 alg\fR]
[\fB\-v2prf alg\fR]
[\fB\-v1 alg\fR]
[\fB\-engine id\fR]
[\fB\-scrypt\fR]
[\fB\-scrypt_N N\fR]
[\fB\-scrypt_r r\fR]
[\fB\-scrypt_p p\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBpkcs8\fR command processes private keys in PKCS#8 format. It can handle
both unencrypted PKCS#8 PrivateKeyInfo format and EncryptedPrivateKeyInfo
format with a variety of PKCS#5 (v1.5 and v2.0) and PKCS#12 algorithms.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP \fB\-topk8\fR 4
.IX Item "-topk8"
Normally a PKCS#8 private key is expected on input and a private key will be
written to the output file. With the \fB\-topk8\fR option the situation is
reversed: it reads a private key and writes a PKCS#8 format key.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format: see "KEY FORMATS" for more details. The default
format is PEM.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format: see "KEY FORMATS" for more details. The default
format is PEM.
.IP \fB\-traditional\fR 4
.IX Item "-traditional"
When this option is present and \fB\-topk8\fR is not a traditional format private
key is written.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read a key from or standard input if this
option is not specified. If the key is encrypted a pass phrase will be
prompted for.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
The input file password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write a key to or standard output by
default. If any encryption options are set then a pass phrase will be
prompted for. The output filename should \fBnot\fR be the same as the input
filename.
.IP "\fB\-passout arg\fR" 4
.IX Item "-passout arg"
The output file password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-iter count\fR" 4
.IX Item "-iter count"
When creating new PKCS#8 containers, use a given number of iterations on
the password in deriving the encryption key for the PKCS#8 output.
High values increase the time required to brute-force a PKCS#8 container.
.IP \fB\-nocrypt\fR 4
.IX Item "-nocrypt"
PKCS#8 keys generated or input are normally PKCS#8 EncryptedPrivateKeyInfo
structures using an appropriate password based encryption algorithm. With
this option an unencrypted PrivateKeyInfo structure is expected or output.
This option does not encrypt private keys at all and should only be used
when absolutely necessary. Certain software such as some versions of Java
code signing software used unencrypted private keys.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP "\fB\-v2 alg\fR" 4
.IX Item "-v2 alg"
This option sets the PKCS#5 v2.0 algorithm.
.Sp
The \fBalg\fR argument is the encryption algorithm to use, valid values include
\&\fBaes128\fR, \fBaes256\fR and \fBdes3\fR. If this option isn't specified then \fBaes256\fR
is used.
.IP "\fB\-v2prf alg\fR" 4
.IX Item "-v2prf alg"
This option sets the PRF algorithm to use with PKCS#5 v2.0. A typical value
value would be \fBhmacWithSHA256\fR. If this option isn't set then the default
for the cipher is used or \fBhmacWithSHA256\fR if there is no default.
.Sp
Some implementations may not support custom PRF algorithms and may require
the \fBhmacWithSHA1\fR option to work.
.IP "\fB\-v1 alg\fR" 4
.IX Item "-v1 alg"
This option indicates a PKCS#5 v1.5 or PKCS#12 algorithm should be used.  Some
older implementations may not support PKCS#5 v2.0 and may require this option.
If not specified PKCS#5 v2.0 form is used.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBpkcs8\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.IP \fB\-scrypt\fR 4
.IX Item "-scrypt"
Uses the \fBscrypt\fR algorithm for private key encryption using default
parameters: currently N=16384, r=8 and p=1 and AES in CBC mode with a 256 bit
key. These parameters can be modified using the \fB\-scrypt_N\fR, \fB\-scrypt_r\fR,
\&\fB\-scrypt_p\fR and \fB\-v2\fR options.
.IP "\fB\-scrypt_N N\fR \fB\-scrypt_r r\fR \fB\-scrypt_p p\fR" 4
.IX Item "-scrypt_N N -scrypt_r r -scrypt_p p"
Sets the scrypt \fBN\fR, \fBr\fR or \fBp\fR parameters.
.SH "KEY FORMATS"
.IX Header "KEY FORMATS"
Various different formats are used by the pkcs8 utility. These are detailed
below.
.PP
If a key is being converted from PKCS#8 form (i.e. the \fB\-topk8\fR option is
not used) then the input file must be in PKCS#8 format. An encrypted
key is expected unless \fB\-nocrypt\fR is included.
.PP
If \fB\-topk8\fR is not used and \fBPEM\fR mode is set the output file will be an
unencrypted private key in PKCS#8 format. If the \fB\-traditional\fR option is
used then a traditional format private key is written instead.
.PP
If \fB\-topk8\fR is not used and \fBDER\fR mode is set the output file will be an
unencrypted private key in traditional DER format.
.PP
If \fB\-topk8\fR is used then any supported private key can be used for the input
file in a format specified by \fB\-inform\fR. The output file will be encrypted
PKCS#8 format using the specified encryption parameters unless \fB\-nocrypt\fR
is included.
.SH NOTES
.IX Header "NOTES"
By default, when converting a key to PKCS#8 format, PKCS#5 v2.0 using 256 bit
AES with HMAC and SHA256 is used.
.PP
Some older implementations do not support PKCS#5 v2.0 format and require
the older PKCS#5 v1.5 form instead, possibly also requiring insecure weak
encryption algorithms such as 56 bit DES.
.PP
The encrypted form of a PEM encode PKCS#8 files uses the following
headers and footers:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN ENCRYPTED PRIVATE KEY\-\-\-\-\-
\& \-\-\-\-\-END ENCRYPTED PRIVATE KEY\-\-\-\-\-
.Ve
.PP
The unencrypted form uses:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN PRIVATE KEY\-\-\-\-\-
\& \-\-\-\-\-END PRIVATE KEY\-\-\-\-\-
.Ve
.PP
Private keys encrypted using PKCS#5 v2.0 algorithms and high iteration
counts are more secure that those encrypted using the traditional
SSLeay compatible formats. So if additional security is considered
important the keys should be converted.
.PP
It is possible to write out DER encoded encrypted private keys in
PKCS#8 format because the encryption details are included at an ASN1
level whereas the traditional format includes them at a PEM level.
.SH "PKCS#5 v1.5 and PKCS#12 algorithms."
.IX Header "PKCS#5 v1.5 and PKCS#12 algorithms."
Various algorithms can be used with the \fB\-v1\fR command line option,
including PKCS#5 v1.5 and PKCS#12. These are described in more detail
below.
.IP "\fBPBE\-MD2\-DES PBE\-MD5\-DES\fR" 4
.IX Item "PBE-MD2-DES PBE-MD5-DES"
These algorithms were included in the original PKCS#5 v1.5 specification.
They only offer 56 bits of protection since they both use DES.
.IP "\fBPBE\-SHA1\-RC2\-64\fR, \fBPBE\-MD2\-RC2\-64\fR, \fBPBE\-MD5\-RC2\-64\fR, \fBPBE\-SHA1\-DES\fR" 4
.IX Item "PBE-SHA1-RC2-64, PBE-MD2-RC2-64, PBE-MD5-RC2-64, PBE-SHA1-DES"
These algorithms are not mentioned in the original PKCS#5 v1.5 specification
but they use the same key derivation algorithm and are supported by some
software. They are mentioned in PKCS#5 v2.0. They use either 64 bit RC2 or
56 bit DES.
.IP "\fBPBE\-SHA1\-RC4\-128\fR, \fBPBE\-SHA1\-RC4\-40\fR, \fBPBE\-SHA1\-3DES\fR, \fBPBE\-SHA1\-2DES\fR, \fBPBE\-SHA1\-RC2\-128\fR, \fBPBE\-SHA1\-RC2\-40\fR" 4
.IX Item "PBE-SHA1-RC4-128, PBE-SHA1-RC4-40, PBE-SHA1-3DES, PBE-SHA1-2DES, PBE-SHA1-RC2-128, PBE-SHA1-RC2-40"
These algorithms use the PKCS#12 password based encryption algorithm and
allow strong encryption algorithms like triple DES or 128 bit RC2 to be used.
.SH EXAMPLES
.IX Header "EXAMPLES"
Convert a private key to PKCS#8 format using default parameters (AES with
256 bit key and \fBhmacWithSHA256\fR):
.PP
.Vb 1
\& openssl pkcs8 \-in key.pem \-topk8 \-out enckey.pem
.Ve
.PP
Convert a private key to PKCS#8 unencrypted format:
.PP
.Vb 1
\& openssl pkcs8 \-in key.pem \-topk8 \-nocrypt \-out enckey.pem
.Ve
.PP
Convert a private key to PKCS#5 v2.0 format using triple DES:
.PP
.Vb 1
\& openssl pkcs8 \-in key.pem \-topk8 \-v2 des3 \-out enckey.pem
.Ve
.PP
Convert a private key to PKCS#5 v2.0 format using AES with 256 bits in CBC
mode and \fBhmacWithSHA512\fR PRF:
.PP
.Vb 1
\& openssl pkcs8 \-in key.pem \-topk8 \-v2 aes\-256\-cbc \-v2prf hmacWithSHA512 \-out enckey.pem
.Ve
.PP
Convert a private key to PKCS#8 using a PKCS#5 1.5 compatible algorithm
(DES):
.PP
.Vb 1
\& openssl pkcs8 \-in key.pem \-topk8 \-v1 PBE\-MD5\-DES \-out enckey.pem
.Ve
.PP
Convert a private key to PKCS#8 using a PKCS#12 compatible algorithm
(3DES):
.PP
.Vb 1
\& openssl pkcs8 \-in key.pem \-topk8 \-out enckey.pem \-v1 PBE\-SHA1\-3DES
.Ve
.PP
Read a DER unencrypted PKCS#8 format private key:
.PP
.Vb 1
\& openssl pkcs8 \-inform DER \-nocrypt \-in key.der \-out key.pem
.Ve
.PP
Convert a private key from any PKCS#8 encrypted format to traditional format:
.PP
.Vb 1
\& openssl pkcs8 \-in pk8.pem \-traditional \-out key.pem
.Ve
.PP
Convert a private key to PKCS#8 format, encrypting with AES\-256 and with
one million iterations of the password:
.PP
.Vb 1
\& openssl pkcs8 \-in key.pem \-topk8 \-v2 aes\-256\-cbc \-iter 1000000 \-out pk8.pem
.Ve
.SH STANDARDS
.IX Header "STANDARDS"
Test vectors from this PKCS#5 v2.0 implementation were posted to the
pkcs-tng mailing list using triple DES, DES and RC2 with high iteration
counts, several people confirmed that they could decrypt the private
keys produced and therefore, it can be assumed that the PKCS#5 v2.0
implementation is reasonably accurate at least as far as these
algorithms are concerned.
.PP
The format of PKCS#8 DSA (and other) private keys is not well documented:
it is hidden away in PKCS#11 v2.01, section 11.9. OpenSSL's default DSA
PKCS#8 private key format complies with this standard.
.SH BUGS
.IX Header "BUGS"
There should be an option that prints out the encryption algorithm
in use and other details such as the iteration count.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBdsa\fR\|(1), \fBrsa\fR\|(1), \fBgenrsa\fR\|(1),
\&\fBgendsa\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The \fB\-iter\fR option was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
