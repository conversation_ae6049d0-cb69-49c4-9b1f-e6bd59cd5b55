<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>pkey</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-pkey, pkey - public or private key processing tool</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>pkey</b> [<b>-help</b>] [<b>-inform PEM|DER</b>] [<b>-outform PEM|DER</b>] [<b>-in filename</b>] [<b>-passin arg</b>] [<b>-out filename</b>] [<b>-passout arg</b>] [<b>-traditional</b>] [<b>-<i>cipher</i></b>] [<b>-text</b>] [<b>-text_pub</b>] [<b>-noout</b>] [<b>-pubin</b>] [<b>-pubout</b>] [<b>-engine id</b>] [<b>-check</b>] [<b>-pubcheck</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>pkey</b> command processes public or private keys. They can be converted between various forms and their components printed out.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="inform-DER-PEM"><b>-inform DER|PEM</b></dt>
<dd>

<p>This specifies the input format DER or PEM. The default format is PEM.</p>

</dd>
<dt id="outform-DER-PEM"><b>-outform DER|PEM</b></dt>
<dd>

<p>This specifies the output format, the options have the same meaning and default as the <b>-inform</b> option.</p>

</dd>
<dt id="in-filename"><b>-in filename</b></dt>
<dd>

<p>This specifies the input filename to read a key from or standard input if this option is not specified. If the key is encrypted a pass phrase will be prompted for.</p>

</dd>
<dt id="passin-arg"><b>-passin arg</b></dt>
<dd>

<p>The input file password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="out-filename"><b>-out filename</b></dt>
<dd>

<p>This specifies the output filename to write a key to or standard output if this option is not specified. If any encryption options are set then a pass phrase will be prompted for. The output filename should <b>not</b> be the same as the input filename.</p>

</dd>
<dt id="passout-password"><b>-passout password</b></dt>
<dd>

<p>The output file password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="traditional"><b>-traditional</b></dt>
<dd>

<p>Normally a private key is written using standard format: this is PKCS#8 form with the appropriate encryption algorithm (if any). If the <b>-traditional</b> option is specified then the older &quot;traditional&quot; format is used instead.</p>

</dd>
<dt id="cipher"><b>-<i>cipher</i></b></dt>
<dd>

<p>These options encrypt the private key with the supplied cipher. Any algorithm name accepted by EVP_get_cipherbyname() is acceptable such as <b>des3</b>.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>Prints out the various public or private key components in plain text in addition to the encoded version.</p>

</dd>
<dt id="text_pub"><b>-text_pub</b></dt>
<dd>

<p>Print out only public key components even if a private key is being processed.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>Do not output the encoded version of the key.</p>

</dd>
<dt id="pubin"><b>-pubin</b></dt>
<dd>

<p>By default a private key is read from the input file: with this option a public key is read instead.</p>

</dd>
<dt id="pubout"><b>-pubout</b></dt>
<dd>

<p>By default a private key is output: with this option a public key will be output instead. This option is automatically set if the input is a public key.</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Specifying an engine (by its unique <b>id</b> string) will cause <b>pkey</b> to attempt to obtain a functional reference to the specified engine, thus initialising it if needed. The engine will then be set as the default for all available algorithms.</p>

</dd>
<dt id="check"><b>-check</b></dt>
<dd>

<p>This option checks the consistency of a key pair for both public and private components.</p>

</dd>
<dt id="pubcheck"><b>-pubcheck</b></dt>
<dd>

<p>This option checks the correctness of either a public key or the public component of a key pair.</p>

</dd>
</dl>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>To remove the pass phrase on an RSA private key:</p>

<pre><code>openssl pkey -in key.pem -out keyout.pem</code></pre>

<p>To encrypt a private key using triple DES:</p>

<pre><code>openssl pkey -in key.pem -des3 -out keyout.pem</code></pre>

<p>To convert a private key from PEM to DER format:</p>

<pre><code>openssl pkey -in key.pem -outform DER -out keyout.der</code></pre>

<p>To print out the components of a private key to standard output:</p>

<pre><code>openssl pkey -in key.pem -text -noout</code></pre>

<p>To print out the public components of a private key to standard output:</p>

<pre><code>openssl pkey -in key.pem -text_pub -noout</code></pre>

<p>To just output the public part of a private key:</p>

<pre><code>openssl pkey -in key.pem -pubout -out pubkey.pem</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/genpkey.html">genpkey(1)</a>, <a href="../man1/rsa.html">rsa(1)</a>, <a href="../man1/pkcs8.html">pkcs8(1)</a>, <a href="../man1/dsa.html">dsa(1)</a>, <a href="../man1/genrsa.html">genrsa(1)</a>, <a href="../man1/gendsa.html">gendsa(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2006-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


