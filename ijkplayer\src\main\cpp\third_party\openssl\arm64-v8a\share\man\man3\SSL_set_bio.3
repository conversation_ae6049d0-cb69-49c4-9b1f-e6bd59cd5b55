.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SET_BIO 3"
.TH SSL_SET_BIO 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_set_bio, SSL_set0_rbio, SSL_set0_wbio \- connect the SSL object with a BIO
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_set_bio(SSL *ssl, BIO *rbio, BIO *wbio);
\& void SSL_set0_rbio(SSL *s, BIO *rbio);
\& void SSL_set0_wbio(SSL *s, BIO *wbio);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_set0_rbio()\fR connects the BIO \fBrbio\fR for the read operations of the \fBssl\fR
object. The SSL engine inherits the behaviour of \fBrbio\fR. If the BIO is
nonblocking then the \fBssl\fR object will also have nonblocking behaviour. This
function transfers ownership of \fBrbio\fR to \fBssl\fR. It will be automatically
freed using \fBBIO_free_all\fR\|(3) when the \fBssl\fR is freed. On calling this
function, any existing \fBrbio\fR that was previously set will also be freed via a
call to \fBBIO_free_all\fR\|(3) (this includes the case where the \fBrbio\fR is set to
the same value as previously).
.PP
\&\fBSSL_set0_wbio()\fR works in the same as \fBSSL_set0_rbio()\fR except that it connects
the BIO \fBwbio\fR for the write operations of the \fBssl\fR object. Note that if the
rbio and wbio are the same then \fBSSL_set0_rbio()\fR and \fBSSL_set0_wbio()\fR each take
ownership of one reference. Therefore, it may be necessary to increment the
number of references available using \fBBIO_up_ref\fR\|(3) before calling the set0
functions.
.PP
\&\fBSSL_set_bio()\fR is similar to \fBSSL_set0_rbio()\fR and \fBSSL_set0_wbio()\fR except
that it connects both the \fBrbio\fR and the \fBwbio\fR at the same time, and
transfers the ownership of \fBrbio\fR and \fBwbio\fR to \fBssl\fR according to
the following set of rules:
.IP \(bu 2
If neither the \fBrbio\fR or \fBwbio\fR have changed from their previous values
then nothing is done.
.IP \(bu 2
If the \fBrbio\fR and \fBwbio\fR parameters are different and both are different
to their
previously set values then one reference is consumed for the rbio and one
reference is consumed for the wbio.
.IP \(bu 2
If the \fBrbio\fR and \fBwbio\fR parameters are the same and the \fBrbio\fR is not
the same as the previously set value then one reference is consumed.
.IP \(bu 2
If the \fBrbio\fR and \fBwbio\fR parameters are the same and the \fBrbio\fR is the
same as the previously set value, then no additional references are consumed.
.IP \(bu 2
If the \fBrbio\fR and \fBwbio\fR parameters are different and the \fBrbio\fR is the
same as the
previously set value then one reference is consumed for the \fBwbio\fR and no
references are consumed for the \fBrbio\fR.
.IP \(bu 2
If the \fBrbio\fR and \fBwbio\fR parameters are different and the \fBwbio\fR is the
same as the previously set value and the old \fBrbio\fR and \fBwbio\fR values
were the same as each other then one reference is consumed for the \fBrbio\fR
and no references are consumed for the \fBwbio\fR.
.IP \(bu 2
If the \fBrbio\fR and \fBwbio\fR parameters are different and the \fBwbio\fR
is the same as the
previously set value and the old \fBrbio\fR and \fBwbio\fR values were different
to each
other then one reference is consumed for the \fBrbio\fR and one reference
is consumed
for the \fBwbio\fR.
.PP
Because of this complexity, this function should be avoided;
use \fBSSL_set0_rbio()\fR and \fBSSL_set0_wbio()\fR instead.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_set_bio()\fR, \fBSSL_set0_rbio()\fR and \fBSSL_set0_wbio()\fR cannot fail.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_get_rbio\fR\|(3),
\&\fBSSL_connect\fR\|(3), \fBSSL_accept\fR\|(3),
\&\fBSSL_shutdown\fR\|(3), \fBssl\fR\|(7), \fBbio\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBSSL_set0_rbio()\fR and \fBSSL_set0_wbio()\fR were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
