<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CRYPTO_memcmp</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>CRYPTO_memcmp - Constant time memory comparison</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/crypto.h&gt;

int CRYPTO_memcmp(const void *a, const void *b, size_t len);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The CRYPTO_memcmp function compares the <b>len</b> bytes pointed to by <b>a</b> and <b>b</b> for equality. It takes an amount of time dependent on <b>len</b>, but independent of the contents of the memory regions pointed to by <b>a</b> and <b>b</b>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>CRYPTO_memcmp() returns 0 if the memory regions are equal and nonzero otherwise.</p>

<h1 id="NOTES">NOTES</h1>

<p>Unlike memcmp(2), this function cannot be used to order the two memory regions as the return value when they differ is undefined, other than being nonzero.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


