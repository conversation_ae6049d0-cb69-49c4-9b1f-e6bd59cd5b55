.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SPEED 1"
.TH SPEED 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-speed,
speed \- test library performance
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl speed\fR
[\fB\-help\fR]
[\fB\-engine id\fR]
[\fB\-elapsed\fR]
[\fB\-evp algo\fR]
[\fB\-decrypt\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-primes num\fR]
[\fB\-seconds num\fR]
[\fB\-bytes num\fR]
[\fBalgorithm...\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to test the performance of cryptographic algorithms.
To see the list of supported algorithms, use the \fIlist \-\-digest\-commands\fR
or \fIlist \-\-cipher\-commands\fR command. The global CSPRNG is denoted by
the \fIrand\fR algorithm name.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBspeed\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.IP \fB\-elapsed\fR 4
.IX Item "-elapsed"
When calculating operations\- or bytes-per-second, use wall-clock time
instead of CPU user time as divisor. It can be useful when testing speed
of hardware engines.
.IP "\fB\-evp algo\fR" 4
.IX Item "-evp algo"
Use the specified cipher or message digest algorithm via the EVP interface.
If \fBalgo\fR is an AEAD cipher, then you can pass <\-aead> to benchmark a
TLS-like sequence. And if \fBalgo\fR is a multi-buffer capable cipher, e.g.
aes\-128\-cbc\-hmac\-sha1, then \fB\-mb\fR will time multi-buffer operation.
.IP \fB\-decrypt\fR 4
.IX Item "-decrypt"
Time the decryption instead of encryption. Affects only the EVP testing.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP "\fB\-primes num\fR" 4
.IX Item "-primes num"
Generate a \fBnum\fR\-prime RSA key and use it to run the benchmarks. This option
is only effective if RSA algorithm is specified to test.
.IP "\fB\-seconds num\fR" 4
.IX Item "-seconds num"
Run benchmarks for \fBnum\fR seconds.
.IP "\fB\-bytes num\fR" 4
.IX Item "-bytes num"
Run benchmarks on \fBnum\fR\-byte buffers. Affects ciphers, digests and the CSPRNG.
.IP "\fB[zero or more test algorithms]\fR" 4
.IX Item "[zero or more test algorithms]"
If any options are given, \fBspeed\fR tests those algorithms, otherwise a
pre-compiled grand selection is tested.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
