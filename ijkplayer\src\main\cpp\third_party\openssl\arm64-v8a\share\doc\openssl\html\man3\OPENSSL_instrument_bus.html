<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OPENSSL_instrument_bus</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OPENSSL_instrument_bus, OPENSSL_instrument_bus2 - instrument references to memory bus</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#ifdef OPENSSL_CPUID_OBJ
size_t OPENSSL_instrument_bus(int *vector, size_t num);
size_t OPENSSL_instrument_bus2(int *vector, size_t num, size_t max);
#endif</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>It was empirically found that timings of references to primary memory are subject to irregular, apparently non-deterministic variations. The subroutines in question instrument these references for purposes of gathering randomness for random number generator. In order to make it bus-bound a &#39;flush cache line&#39; instruction is used between probes. In addition probes are added to <b>vector</b> elements in atomic or interlocked manner, which should contribute additional noise on multi-processor systems. This also means that <b>vector[num]</b> should be zeroed upon invocation (if you want to retrieve actual probe values).</p>

<p>OPENSSL_instrument_bus() performs <b>num</b> probes and records the number of oscillator cycles every probe took.</p>

<p>OPENSSL_instrument_bus2() on the other hand <b>accumulates</b> consecutive probes with the same value, i.e. in a way it records duration of periods when probe values appeared deterministic. The subroutine performs at most <b>max</b> probes in attempt to fill the <b>vector[num]</b>, with <b>max</b> value of 0 meaning &quot;as many as it takes.&quot;</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>Return value of 0 indicates that CPU is not capable of performing the benchmark, either because oscillator counter or &#39;flush cache line&#39; is not available on current platform. For reference, on x86 &#39;flush cache line&#39; was introduced with the SSE2 extensions.</p>

<p>Otherwise number of recorded values is returned.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2011-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


