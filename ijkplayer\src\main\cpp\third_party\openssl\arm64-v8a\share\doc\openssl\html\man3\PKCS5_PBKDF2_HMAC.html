<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>PKCS5_PBKDF2_HMAC</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>PKCS5_PBKDF2_HMAC, PKCS5_PBKDF2_HMAC_SHA1 - password based derivation routines with salt and iteration count</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

int PKCS5_PBKDF2_HMAC(const char *pass, int passlen,
                      const unsigned char *salt, int saltlen, int iter,
                      const EVP_MD *digest,
                      int keylen, unsigned char *out);

int PKCS5_PBKDF2_HMAC_SHA1(const char *pass, int passlen,
                           const unsigned char *salt, int saltlen, int iter,
                           int keylen, unsigned char *out);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>PKCS5_PBKDF2_HMAC() derives a key from a password using a salt and iteration count as specified in RFC 2898.</p>

<p><b>pass</b> is the password used in the derivation of length <b>passlen</b>. <b>pass</b> is an optional parameter and can be NULL. If <b>passlen</b> is -1, then the function will calculate the length of <b>pass</b> using strlen().</p>

<p><b>salt</b> is the salt used in the derivation of length <b>saltlen</b>. If the <b>salt</b> is NULL, then <b>saltlen</b> must be 0. The function will not attempt to calculate the length of the <b>salt</b> because it is not assumed to be NULL terminated.</p>

<p><b>iter</b> is the iteration count and its value should be greater than or equal to 1. RFC 2898 suggests an iteration count of at least 1000. Any <b>iter</b> less than 1 is treated as a single iteration.</p>

<p><b>digest</b> is the message digest function used in the derivation. Values include any of the EVP_* message digests. PKCS5_PBKDF2_HMAC_SHA1() calls PKCS5_PBKDF2_HMAC() with EVP_sha1().</p>

<p>The derived key will be written to <b>out</b>. The size of the <b>out</b> buffer is specified via <b>keylen</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>A typical application of this function is to derive keying material for an encryption algorithm from a password in the <b>pass</b>, a salt in <b>salt</b>, and an iteration count.</p>

<p>Increasing the <b>iter</b> parameter slows down the algorithm which makes it harder for an attacker to perform a brute force attack using a large number of candidate passwords.</p>

<p>These functions make no assumption regarding the given password. It will simply be treated as a byte sequence.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>PKCS5_PBKDF2_HMAC() and PBKCS5_PBKDF2_HMAC_SHA1() return 1 on success or 0 on error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/evp.html">evp(7)</a>, <a href="../man3/RAND_bytes.html">RAND_bytes(3)</a>, <a href="../man3/EVP_BytesToKey.html">EVP_BytesToKey(3)</a>, <a href="../man7/passphrase-encoding.html">passphrase-encoding(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2014-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


