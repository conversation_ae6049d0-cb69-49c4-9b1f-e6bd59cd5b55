.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_NEW 3"
.TH BN_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_new, BN_secure_new, BN_clear, BN_free, BN_clear_free \- allocate and free BIGNUMs
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& BIGNUM *BN_new(void);
\&
\& BIGNUM *BN_secure_new(void);
\&
\& void BN_clear(BIGNUM *a);
\&
\& void BN_free(BIGNUM *a);
\&
\& void BN_clear_free(BIGNUM *a);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_new()\fR allocates and initializes a \fBBIGNUM\fR structure.
\&\fBBN_secure_new()\fR does the same except that the secure heap
\&\fBOPENSSL_secure_malloc\fR\|(3) is used to store the value.
.PP
\&\fBBN_clear()\fR is used to destroy sensitive data such as keys when they
are no longer needed. It erases the memory used by \fBa\fR and sets it
to the value 0.
If \fBa\fR is NULL, nothing is done.
.PP
\&\fBBN_free()\fR frees the components of the \fBBIGNUM\fR, and if it was created
by \fBBN_new()\fR, also the structure itself. \fBBN_clear_free()\fR additionally
overwrites the data before the memory is returned to the system.
If \fBa\fR is NULL, nothing is done.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_new()\fR and \fBBN_secure_new()\fR
return a pointer to the \fBBIGNUM\fR initialised to the value 0.
If the allocation fails,
they return \fBNULL\fR and set an error code that can be obtained
by \fBERR_get_error\fR\|(3).
.PP
\&\fBBN_clear()\fR, \fBBN_free()\fR and \fBBN_clear_free()\fR have no return values.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBOPENSSL_secure_malloc\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBBN_init()\fR was removed in OpenSSL 1.1.0; use \fBBN_new()\fR instead.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
