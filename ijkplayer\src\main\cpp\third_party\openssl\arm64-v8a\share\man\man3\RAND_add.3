.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_ADD 3"
.TH RAND_ADD 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_add, RAND_poll, RAND_seed, RAND_status, RAND_event, RAND_screen,
RAND_keep_random_devices_open
\&\- add randomness to the PRNG or get its status
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand.h>
\&
\& int RAND_status(void);
\& int RAND_poll();
\&
\& void RAND_add(const void *buf, int num, double randomness);
\& void RAND_seed(const void *buf, int num);
\&
\& void RAND_keep_random_devices_open(int keep);
.Ve
.PP
Deprecated:
.PP
.Vb 4
\& #if OPENSSL_API_COMPAT < 0x10100000L
\& int RAND_event(UINT iMsg, WPARAM wParam, LPARAM lParam);
\& void RAND_screen(void);
\& #endif
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions can be used to seed the random generator and to check its
seeded state.
In general, manual (re\-)seeding of the default OpenSSL random generator
(\fBRAND_OpenSSL\fR\|(3)) is not necessary (but allowed), since it does (re\-)seed
itself automatically using trusted system entropy sources.
This holds unless the default RAND_METHOD has been replaced or OpenSSL was
built with automatic reseeding disabled, see \fBRAND\fR\|(7) for more details.
.PP
\&\fBRAND_status()\fR indicates whether or not the random generator has been sufficiently
seeded. If not, functions such as \fBRAND_bytes\fR\|(3) will fail.
.PP
\&\fBRAND_poll()\fR uses the system's capabilities to seed the random generator using
random input obtained from polling various trusted entropy sources.
The default choice of the entropy source can be modified at build time,
see \fBRAND\fR\|(7) for more details.
.PP
\&\fBRAND_add()\fR mixes the \fBnum\fR bytes at \fBbuf\fR into the internal state
of the random generator.
This function will not normally be needed, as mentioned above.
The \fBrandomness\fR argument is an estimate of how much randomness is
contained in
\&\fBbuf\fR, in bytes, and should be a number between zero and \fBnum\fR.
Details about sources of randomness and how to estimate their randomness
can be found in the literature; for example [NIST SP 800\-90B].
The content of \fBbuf\fR cannot be recovered from subsequent random generator output.
Applications that intend to save and restore random state in an external file
should consider using \fBRAND_load_file\fR\|(3) instead.
.PP
\&\fBRAND_seed()\fR is equivalent to \fBRAND_add()\fR with \fBrandomness\fR set to \fBnum\fR.
.PP
\&\fBRAND_keep_random_devices_open()\fR is used to control file descriptor
usage by the random seed sources. Some seed sources maintain open file
descriptors by default, which allows such sources to operate in a
\&\fBchroot\fR\|(2) jail without the associated device nodes being available. When
the \fBkeep\fR argument is zero, this call disables the retention of file
descriptors. Conversely, a nonzero argument enables the retention of
file descriptors. This function is usually called during initialization
and it takes effect immediately.
.PP
\&\fBRAND_event()\fR and \fBRAND_screen()\fR are equivalent to \fBRAND_poll()\fR and exist
for compatibility reasons only. See HISTORY section below.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_status()\fR returns 1 if the random generator has been seeded
with enough data, 0 otherwise.
.PP
\&\fBRAND_poll()\fR returns 1 if it generated seed data, 0 otherwise.
.PP
\&\fBRAND_event()\fR returns \fBRAND_status()\fR.
.PP
The other functions do not return values.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND_bytes\fR\|(3),
\&\fBRAND_egd\fR\|(3),
\&\fBRAND_load_file\fR\|(3),
\&\fBRAND\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBRAND_event()\fR and \fBRAND_screen()\fR were deprecated in OpenSSL 1.1.0 and should
not be used.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
