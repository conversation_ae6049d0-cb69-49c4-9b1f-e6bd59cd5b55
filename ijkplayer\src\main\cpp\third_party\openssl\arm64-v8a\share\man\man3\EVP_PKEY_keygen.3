.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_KEYGEN 3"
.TH EVP_PKEY_KEYGEN 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_keygen_init, EVP_PKEY_keygen, EVP_PKEY_paramgen_init,
EVP_PKEY_paramgen, EVP_PKEY_CTX_set_cb, EVP_PKEY_CTX_get_cb,
EVP_PKEY_CTX_get_keygen_info, EVP_PKEY_CTX_set_app_data,
EVP_PKEY_CTX_get_app_data,
EVP_PKEY_gen_cb, EVP_PKEY_check, EVP_PKEY_public_check,
EVP_PKEY_param_check
\&\- key and parameter generation and check functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_keygen_init(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_keygen(EVP_PKEY_CTX *ctx, EVP_PKEY **ppkey);
\& int EVP_PKEY_paramgen_init(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_paramgen(EVP_PKEY_CTX *ctx, EVP_PKEY **ppkey);
\&
\& typedef int EVP_PKEY_gen_cb(EVP_PKEY_CTX *ctx);
\&
\& void EVP_PKEY_CTX_set_cb(EVP_PKEY_CTX *ctx, EVP_PKEY_gen_cb *cb);
\& EVP_PKEY_gen_cb *EVP_PKEY_CTX_get_cb(EVP_PKEY_CTX *ctx);
\&
\& int EVP_PKEY_CTX_get_keygen_info(EVP_PKEY_CTX *ctx, int idx);
\&
\& void EVP_PKEY_CTX_set_app_data(EVP_PKEY_CTX *ctx, void *data);
\& void *EVP_PKEY_CTX_get_app_data(EVP_PKEY_CTX *ctx);
\&
\& int EVP_PKEY_check(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_public_check(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_param_check(EVP_PKEY_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_keygen_init()\fR function initializes a public key algorithm
context using key \fBpkey\fR for a key generation operation.
.PP
The \fBEVP_PKEY_keygen()\fR function performs a key generation operation, the
generated key is written to \fBppkey\fR.
.PP
The functions \fBEVP_PKEY_paramgen_init()\fR and \fBEVP_PKEY_paramgen()\fR are similar
except parameters are generated.
.PP
The function \fBEVP_PKEY_set_cb()\fR sets the key or parameter generation callback
to \fBcb\fR. The function \fBEVP_PKEY_CTX_get_cb()\fR returns the key or parameter
generation callback.
.PP
The function \fBEVP_PKEY_CTX_get_keygen_info()\fR returns parameters associated
with the generation operation. If \fBidx\fR is \-1 the total number of
parameters available is returned. Any non negative value returns the value of
that parameter. \fBEVP_PKEY_CTX_gen_keygen_info()\fR with a nonnegative value for
\&\fBidx\fR should only be called within the generation callback.
.PP
If the callback returns 0 then the key generation operation is aborted and an
error occurs. This might occur during a time consuming operation where
a user clicks on a "cancel" button.
.PP
The functions \fBEVP_PKEY_CTX_set_app_data()\fR and \fBEVP_PKEY_CTX_get_app_data()\fR set
and retrieve an opaque pointer. This can be used to set some application
defined value which can be retrieved in the callback: for example a handle
which is used to update a "progress dialog".
.PP
\&\fBEVP_PKEY_check()\fR validates the key-pair given by \fBctx\fR. This function first tries
to use customized key check method in \fBEVP_PKEY_METHOD\fR if it's present; otherwise
it calls a default one defined in \fBEVP_PKEY_ASN1_METHOD\fR.
.PP
\&\fBEVP_PKEY_public_check()\fR validates the public component of the key-pair given by \fBctx\fR.
This function first tries to use customized key check method in \fBEVP_PKEY_METHOD\fR
if it's present; otherwise it calls a default one defined in \fBEVP_PKEY_ASN1_METHOD\fR.
.PP
\&\fBEVP_PKEY_param_check()\fR validates the algorithm parameters of the key-pair given by \fBctx\fR.
This function first tries to use customized key check method in \fBEVP_PKEY_METHOD\fR
if it's present; otherwise it calls a default one defined in \fBEVP_PKEY_ASN1_METHOD\fR.
.SH NOTES
.IX Header "NOTES"
After the call to \fBEVP_PKEY_keygen_init()\fR or \fBEVP_PKEY_paramgen_init()\fR algorithm
specific control operations can be performed to set any appropriate parameters
for the operation.
.PP
The functions \fBEVP_PKEY_keygen()\fR and \fBEVP_PKEY_paramgen()\fR can be called more than
once on the same context if several operations are performed using the same
parameters.
.PP
The meaning of the parameters passed to the callback will depend on the
algorithm and the specific implementation of the algorithm. Some might not
give any useful information at all during key or parameter generation. Others
might not even call the callback.
.PP
The operation performed by key or parameter generation depends on the algorithm
used. In some cases (e.g. EC with a supplied named curve) the "generation"
option merely sets the appropriate fields in an EVP_PKEY structure.
.PP
In OpenSSL an EVP_PKEY structure containing a private key also contains the
public key components and parameters (if any). An OpenSSL private key is
equivalent to what some libraries call a "key pair". A private key can be used
in functions which require the use of a public key or parameters.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_keygen_init()\fR, \fBEVP_PKEY_paramgen_init()\fR, \fBEVP_PKEY_keygen()\fR and
\&\fBEVP_PKEY_paramgen()\fR return 1 for success and 0 or a negative value for failure.
In particular a return value of \-2 indicates the operation is not supported by
the public key algorithm.
.PP
\&\fBEVP_PKEY_check()\fR, \fBEVP_PKEY_public_check()\fR and \fBEVP_PKEY_param_check()\fR return 1
for success or others for failure. They return \-2 if the operation is not supported
for the specific algorithm.
.SH EXAMPLES
.IX Header "EXAMPLES"
Generate a 2048 bit RSA key:
.PP
.Vb 2
\& #include <openssl/evp.h>
\& #include <openssl/rsa.h>
\&
\& EVP_PKEY_CTX *ctx;
\& EVP_PKEY *pkey = NULL;
\&
\& ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA, NULL);
\& if (!ctx)
\&     /* Error occurred */
\& if (EVP_PKEY_keygen_init(ctx) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set_rsa_keygen_bits(ctx, 2048) <= 0)
\&     /* Error */
\&
\& /* Generate key */
\& if (EVP_PKEY_keygen(ctx, &pkey) <= 0)
\&     /* Error */
.Ve
.PP
Generate a key from a set of parameters:
.PP
.Vb 2
\& #include <openssl/evp.h>
\& #include <openssl/rsa.h>
\&
\& EVP_PKEY_CTX *ctx;
\& ENGINE *eng;
\& EVP_PKEY *pkey = NULL, *param;
\&
\& /* Assumed param, eng are set up already */
\& ctx = EVP_PKEY_CTX_new(param, eng);
\& if (!ctx)
\&     /* Error occurred */
\& if (EVP_PKEY_keygen_init(ctx) <= 0)
\&     /* Error */
\&
\& /* Generate key */
\& if (EVP_PKEY_keygen(ctx, &pkey) <= 0)
\&     /* Error */
.Ve
.PP
Example of generation callback for OpenSSL public key implementations:
.PP
.Vb 1
\& /* Application data is a BIO to output status to */
\&
\& EVP_PKEY_CTX_set_app_data(ctx, status_bio);
\&
\& static int genpkey_cb(EVP_PKEY_CTX *ctx)
\& {
\&     char c = \*(Aq*\*(Aq;
\&     BIO *b = EVP_PKEY_CTX_get_app_data(ctx);
\&     int p = EVP_PKEY_CTX_get_keygen_info(ctx, 0);
\&
\&     if (p == 0)
\&         c = \*(Aq.\*(Aq;
\&     if (p == 1)
\&         c = \*(Aq+\*(Aq;
\&     if (p == 2)
\&         c = \*(Aq*\*(Aq;
\&     if (p == 3)
\&         c = \*(Aq\en\*(Aq;
\&     BIO_write(b, &c, 1);
\&     (void)BIO_flush(b);
\&     return 1;
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_decrypt\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.0.
.PP
\&\fBEVP_PKEY_check()\fR, \fBEVP_PKEY_public_check()\fR and \fBEVP_PKEY_param_check()\fR were added
in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
