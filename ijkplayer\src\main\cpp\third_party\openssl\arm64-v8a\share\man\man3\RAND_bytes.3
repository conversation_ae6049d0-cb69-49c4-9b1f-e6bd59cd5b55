.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_BYTES 3"
.TH RAND_BYTES 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_bytes, RAND_priv_bytes, RAND_pseudo_bytes \- generate random data
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand.h>
\&
\& int RAND_bytes(unsigned char *buf, int num);
\& int RAND_priv_bytes(unsigned char *buf, int num);
.Ve
.PP
Deprecated:
.PP
.Vb 3
\& #if OPENSSL_API_COMPAT < 0x10100000L
\& int RAND_pseudo_bytes(unsigned char *buf, int num);
\& #endif
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBRAND_bytes()\fR generates \fBnum\fR random bytes using a cryptographically
secure pseudo random generator (CSPRNG) and stores them in \fBbuf\fR.
.PP
\&\fBRAND_priv_bytes()\fR has the same semantics as \fBRAND_bytes()\fR.  It is intended to
be used for generating values that should remain private. If using the
default RAND_METHOD, this function uses a separate "private" PRNG
instance so that a compromise of the "public" PRNG instance will not
affect the secrecy of these private values, as described in \fBRAND\fR\|(7)
and \fBRAND_DRBG\fR\|(7).
.SH NOTES
.IX Header "NOTES"
By default, the OpenSSL CSPRNG supports a security level of 256 bits, provided it
was able to seed itself from a trusted entropy source.
On all major platforms supported by OpenSSL (including the Unix-like platforms
and Windows), OpenSSL is configured to automatically seed the CSPRNG on first use
using the operating systems's random generator.
.PP
If the entropy source fails or is not available, the CSPRNG will enter an
error state and refuse to generate random bytes. For that reason, it is important
to always check the error return value of \fBRAND_bytes()\fR and \fBRAND_priv_bytes()\fR and
not take randomness for granted.
.PP
On other platforms, there might not be a trusted entropy source available
or OpenSSL might have been explicitly configured to use different entropy sources.
If you are in doubt about the quality of the entropy source, don't hesitate to ask
your operating system vendor or post a question on GitHub or the openssl-users
mailing list.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_bytes()\fR and \fBRAND_priv_bytes()\fR
return 1 on success, \-1 if not supported by the current
RAND method, or 0 on other failure. The error code can be
obtained by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND_add\fR\|(3),
\&\fBRAND_bytes\fR\|(3),
\&\fBRAND_priv_bytes\fR\|(3),
\&\fBERR_get_error\fR\|(3),
\&\fBRAND\fR\|(7),
\&\fBRAND_DRBG\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
.IP \(bu 2
\&\fBRAND_pseudo_bytes()\fR was deprecated in OpenSSL 1.1.0; use \fBRAND_bytes()\fR instead.
.IP \(bu 2
The \fBRAND_priv_bytes()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
