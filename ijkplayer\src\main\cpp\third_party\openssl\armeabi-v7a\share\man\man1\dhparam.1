.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DHPARAM 1"
.TH DHPARAM 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-dhparam,
dhparam \- DH parameter manipulation and generation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl dhparam\fR
[\fB\-help\fR]
[\fB\-inform DER|PEM\fR]
[\fB\-outform DER|PEM\fR]
[\fB\-in\fR \fIfilename\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-dsaparam\fR]
[\fB\-check\fR]
[\fB\-noout\fR]
[\fB\-text\fR]
[\fB\-C\fR]
[\fB\-2\fR]
[\fB\-5\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-engine id\fR]
[\fInumbits\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to manipulate DH parameter files.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format. The \fBDER\fR option uses an ASN1 DER encoded
form compatible with the PKCS#3 DHparameter structure. The PEM form is the
default format: it consists of the \fBDER\fR format base64 encoded with
additional header and footer lines.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format, the options have the same meaning and default
as the \fB\-inform\fR option.
.IP "\fB\-in\fR \fIfilename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read parameters from or standard input if
this option is not specified.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
This specifies the output filename parameters to. Standard output is used
if this option is not present. The output filename should \fBnot\fR be the same
as the input filename.
.IP \fB\-dsaparam\fR 4
.IX Item "-dsaparam"
If this option is used, DSA rather than DH parameters are read or created;
they are converted to DH format.  Otherwise, "strong" primes (such
that (p\-1)/2 is also prime) will be used for DH parameter generation.
.Sp
DH parameter generation with the \fB\-dsaparam\fR option is much faster,
and the recommended exponent length is shorter, which makes DH key
exchange more efficient.  Beware that with such DSA-style DH
parameters, a fresh DH key should be created for each use to
avoid small-subgroup attacks that may be possible otherwise.
.IP \fB\-check\fR 4
.IX Item "-check"
Performs numerous checks to see if the supplied parameters are valid and
displays a warning if not.
.IP "\fB\-2\fR, \fB\-5\fR" 4
.IX Item "-2, -5"
The generator to use, either 2 or 5. If present then the
input file is ignored and parameters are generated instead. If not
present but \fBnumbits\fR is present, parameters are generated with the
default generator 2.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP \fInumbits\fR 4
.IX Item "numbits"
This option specifies that a parameter set should be generated of size
\&\fInumbits\fR. It must be the last option. If this option is present then
the input file is ignored and parameters are generated instead. If
this option is not present but a generator (\fB\-2\fR or \fB\-5\fR) is
present, parameters are generated with a default length of 2048 bits.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option inhibits the output of the encoded version of the parameters.
.IP \fB\-text\fR 4
.IX Item "-text"
This option prints out the DH parameters in human readable form.
.IP \fB\-C\fR 4
.IX Item "-C"
This option converts the parameters into C code. The parameters can then
be loaded by calling the \fBget_dhNNNN()\fR function.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBdhparam\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.SH WARNINGS
.IX Header "WARNINGS"
The program \fBdhparam\fR combines the functionality of the programs \fBdh\fR and
\&\fBgendh\fR in previous versions of OpenSSL. The \fBdh\fR and \fBgendh\fR
programs are retained for now but may have different purposes in future
versions of OpenSSL.
.SH NOTES
.IX Header "NOTES"
PEM format DH parameters use the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN DH PARAMETERS\-\-\-\-\-
\& \-\-\-\-\-END DH PARAMETERS\-\-\-\-\-
.Ve
.PP
OpenSSL currently only supports the older PKCS#3 DH, not the newer X9.42
DH.
.PP
This program manipulates DH parameters not keys.
.SH BUGS
.IX Header "BUGS"
There should be a way to generate and manipulate DH keys.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBdsaparam\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
