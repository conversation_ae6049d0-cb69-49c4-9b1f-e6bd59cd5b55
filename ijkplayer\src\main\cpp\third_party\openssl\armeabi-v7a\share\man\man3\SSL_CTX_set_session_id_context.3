.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_SESSION_ID_CONTEXT 3"
.TH SSL_CTX_SET_SESSION_ID_CONTEXT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_session_id_context, SSL_set_session_id_context \- set context within which session can be reused (server side only)
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_set_session_id_context(SSL_CTX *ctx, const unsigned char *sid_ctx,
\&                                    unsigned int sid_ctx_len);
\& int SSL_set_session_id_context(SSL *ssl, const unsigned char *sid_ctx,
\&                                unsigned int sid_ctx_len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_session_id_context()\fR sets the context \fBsid_ctx\fR of length
\&\fBsid_ctx_len\fR within which a session can be reused for the \fBctx\fR object.
.PP
\&\fBSSL_set_session_id_context()\fR sets the context \fBsid_ctx\fR of length
\&\fBsid_ctx_len\fR within which a session can be reused for the \fBssl\fR object.
.SH NOTES
.IX Header "NOTES"
Sessions are generated within a certain context. When exporting/importing
sessions with \fBi2d_SSL_SESSION\fR/\fBd2i_SSL_SESSION\fR it would be possible,
to re-import a session generated from another context (e.g. another
application), which might lead to malfunctions. Therefore, each application
must set its own session id context \fBsid_ctx\fR which is used to distinguish
the contexts and is stored in exported sessions. The \fBsid_ctx\fR can be
any kind of binary data with a given length, it is therefore possible
to use e.g. the name of the application and/or the hostname and/or service
name ...
.PP
The session id context becomes part of the session. The session id context
is set by the SSL/TLS server. The \fBSSL_CTX_set_session_id_context()\fR and
\&\fBSSL_set_session_id_context()\fR functions are therefore only useful on the
server side.
.PP
OpenSSL clients will check the session id context returned by the server
when reusing a session.
.PP
The maximum length of the \fBsid_ctx\fR is limited to
\&\fBSSL_MAX_SID_CTX_LENGTH\fR.
.SH WARNINGS
.IX Header "WARNINGS"
If the session id context is not set on an SSL/TLS server and client
certificates are used, stored sessions
will not be reused but a fatal error will be flagged and the handshake
will fail.
.PP
If a server returns a different session id context to an OpenSSL client
when reusing a session, an error will be flagged and the handshake will
fail. OpenSSL servers will always return the correct session id context,
as an OpenSSL server checks the session id context itself before reusing
a session as described above.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_session_id_context()\fR and \fBSSL_set_session_id_context()\fR
return the following values:
.IP 0 4
The length \fBsid_ctx_len\fR of the session id context \fBsid_ctx\fR exceeded
the maximum allowed length of \fBSSL_MAX_SID_CTX_LENGTH\fR. The error
is logged to the error stack.
.IP 1 4
.IX Item "1"
The operation succeeded.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
