.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DGST 1"
.TH DGST 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-dgst,
dgst \- perform digest operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl dgst\fR
[\fB\-\fR\f(BIdigest\fR]
[\fB\-help\fR]
[\fB\-c\fR]
[\fB\-d\fR]
[\fB\-list\fR]
[\fB\-hex\fR]
[\fB\-binary\fR]
[\fB\-r\fR]
[\fB\-out filename\fR]
[\fB\-sign filename\fR]
[\fB\-keyform arg\fR]
[\fB\-passin arg\fR]
[\fB\-verify filename\fR]
[\fB\-prverify filename\fR]
[\fB\-signature filename\fR]
[\fB\-sigopt nm:v\fR]
[\fB\-hmac key\fR]
[\fB\-fips\-fingerprint\fR]
[\fB\-rand file...\fR]
[\fB\-engine id\fR]
[\fB\-engine_impl\fR]
[\fBfile...\fR]
.PP
\&\fBopenssl\fR \fIdigest\fR [\fB...\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The digest functions output the message digest of a supplied file or files
in hexadecimal.  The digest functions also generate and verify digital
signatures using message digests.
.PP
The generic name, \fBdgst\fR, may be used with an option specifying the
algorithm to be used.
The default digest is \fIsha256\fR.
A supported \fIdigest\fR name may also be used as the command name.
To see the list of supported algorithms, use the \fIlist \-\-digest\-commands\fR
command.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
Specifies name of a supported digest to be used. To see the list of
supported digests, use the command \fIlist \-\-digest\-commands\fR.
.IP \fB\-c\fR 4
.IX Item "-c"
Print out the digest in two digit groups separated by colons, only relevant if
\&\fBhex\fR format output is used.
.IP \fB\-d\fR 4
.IX Item "-d"
Print out BIO debugging information.
.IP \fB\-list\fR 4
.IX Item "-list"
Prints out a list of supported message digests.
.IP \fB\-hex\fR 4
.IX Item "-hex"
Digest is to be output as a hex dump. This is the default case for a "normal"
digest as opposed to a digital signature.  See NOTES below for digital
signatures using \fB\-hex\fR.
.IP \fB\-binary\fR 4
.IX Item "-binary"
Output the digest or signature in binary form.
.IP \fB\-r\fR 4
.IX Item "-r"
Output the digest in the "coreutils" format, including newlines.
Used by programs like \fBsha1sum\fR.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
Filename to output to, or standard output by default.
.IP "\fB\-sign filename\fR" 4
.IX Item "-sign filename"
Digitally sign the digest using the private key in "filename". Note this option
does not support Ed25519 or Ed448 private keys.
.IP "\fB\-keyform arg\fR" 4
.IX Item "-keyform arg"
Specifies the key format to sign digest with. The DER, PEM, P12,
and ENGINE formats are supported.
.IP "\fB\-sigopt nm:v\fR" 4
.IX Item "-sigopt nm:v"
Pass options to the signature algorithm during sign or verify operations.
Names and values of these options are algorithm-specific.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
The private key password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-verify filename\fR" 4
.IX Item "-verify filename"
Verify the signature using the public key in "filename".
The output is either "Verification OK" or "Verification Failure".
.IP "\fB\-prverify filename\fR" 4
.IX Item "-prverify filename"
Verify the signature using the private key in "filename".
.IP "\fB\-signature filename\fR" 4
.IX Item "-signature filename"
The actual signature to verify.
.IP "\fB\-hmac key\fR" 4
.IX Item "-hmac key"
Create a hashed MAC using "key".
.IP "\fB\-mac alg\fR" 4
.IX Item "-mac alg"
Create MAC (keyed Message Authentication Code). The most popular MAC
algorithm is HMAC (hash-based MAC), but there are other MAC algorithms
which are not based on hash, for instance \fBgost-mac\fR algorithm,
supported by \fBccgost\fR engine. MAC keys and other options should be set
via \fB\-macopt\fR parameter.
.IP "\fB\-macopt nm:v\fR" 4
.IX Item "-macopt nm:v"
Passes options to MAC algorithm, specified by \fB\-mac\fR key.
Following options are supported by both by \fBHMAC\fR and \fBgost-mac\fR:
.RS 4
.IP \fBkey:string\fR 4
.IX Item "key:string"
Specifies MAC key as alphanumeric string (use if key contain printable
characters only). String length must conform to any restrictions of
the MAC algorithm for example exactly 32 chars for gost-mac.
.IP \fBhexkey:string\fR 4
.IX Item "hexkey:string"
Specifies MAC key in hexadecimal form (two hex digits per byte).
Key length must conform to any restrictions of the MAC algorithm
for example exactly 32 chars for gost-mac.
.RE
.RS 4
.RE
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP \fB\-fips\-fingerprint\fR 4
.IX Item "-fips-fingerprint"
Compute HMAC using a specific key for certain OpenSSL-FIPS operations.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Use engine \fBid\fR for operations (including private key storage).
This engine is not used as source for digest algorithms, unless it is
also specified in the configuration file or \fB\-engine_impl\fR is also
specified.
.IP \fB\-engine_impl\fR 4
.IX Item "-engine_impl"
When used with the \fB\-engine\fR option, it specifies to also use
engine \fBid\fR for digest operations.
.IP \fBfile...\fR 4
.IX Item "file..."
File or files to digest. If no files are specified then standard input is
used.
.SH EXAMPLES
.IX Header "EXAMPLES"
To create a hex-encoded message digest of a file:
 openssl dgst \-md5 \-hex file.txt
.PP
To sign a file using SHA\-256 with binary file output:
 openssl dgst \-sha256 \-sign privatekey.pem \-out signature.sign file.txt
.PP
To verify a signature:
 openssl dgst \-sha256 \-verify publickey.pem \e
 \-signature signature.sign \e
 file.txt
.SH NOTES
.IX Header "NOTES"
The digest mechanisms that are available will depend on the options
used when building OpenSSL.
The \fBlist digest-commands\fR command can be used to list them.
.PP
New or agile applications should use probably use SHA\-256. Other digests,
particularly SHA\-1 and MD5, are still widely used for interoperating
with existing formats and protocols.
.PP
When signing a file, \fBdgst\fR will automatically determine the algorithm
(RSA, ECC, etc) to use for signing based on the private key's ASN.1 info.
When verifying signatures, it only handles the RSA, DSA, or ECDSA signature
itself, not the related data to identify the signer and algorithm used in
formats such as x.509, CMS, and S/MIME.
.PP
A source of random numbers is required for certain signing algorithms, in
particular ECDSA and DSA.
.PP
The signing and verify options should only be used if a single file is
being signed or verified.
.PP
Hex signatures cannot be verified using \fBopenssl\fR.  Instead, use "xxd \-r"
or similar program to transform the hex signature into a binary signature
prior to verification.
.SH HISTORY
.IX Header "HISTORY"
The default digest was changed from MD5 to SHA256 in OpenSSL 1.1.0.
The FIPS-related options were removed in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
