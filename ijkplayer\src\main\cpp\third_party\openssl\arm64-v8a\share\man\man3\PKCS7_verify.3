.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS7_VERIFY 3"
.TH PKCS7_VERIFY 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS7_verify, PKCS7_get0_signers \- verify a PKCS#7 signedData structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs7.h>
\&
\& int PKCS7_verify(PKCS7 *p7, STACK_OF(X509) *certs, X509_STORE *store,
\&                  BIO *indata, BIO *out, int flags);
\&
\& STACK_OF(X509) *PKCS7_get0_signers(PKCS7 *p7, STACK_OF(X509) *certs, int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS7_verify()\fR is very similar to \fBCMS_verify\fR\|(3).
It verifies a PKCS#7 signedData structure given in \fIp7\fR.
The optional \fIcerts\fR parameter refers to a set of certificates
in which to search for signer's certificates.
\&\fIp7\fR may contain extra untrusted CA certificates that may be used for
chain building as well as CRLs that may be used for certificate validation.
\&\fIstore\fR may be NULL or point to
the trusted certificate store to use for chain verification.
\&\fIindata\fR refers to the signed data if the content is detached from \fIp7\fR.
Otherwise \fIindata\fR should be NULL, and then the signed data must be in \fIp7\fR.
The content is written to the BIO \fIout\fR unless it is NULL.
\&\fIflags\fR is an optional set of flags, which can be used to modify the operation.
.PP
\&\fBPKCS7_get0_signers()\fR retrieves the signer's certificates from \fIp7\fR, it does
\&\fBnot\fR check their validity or whether any signatures are valid. The \fIcerts\fR
and \fIflags\fR parameters have the same meanings as in \fBPKCS7_verify()\fR.
.SH "VERIFY PROCESS"
.IX Header "VERIFY PROCESS"
Normally the verify process proceeds as follows.
.PP
Initially some sanity checks are performed on \fIp7\fR. The type of \fIp7\fR must
be SignedData. There must be at least one signature on the data and if
the content is detached \fIindata\fR cannot be NULL.  If the content is
not detached and \fIindata\fR is not NULL then the structure has both
embedded and external content. To treat this as an error, use the flag
\&\fBPKCS7_NO_DUAL_CONTENT\fR.
The default behavior allows this, for compatibility with older
versions of OpenSSL.
.PP
An attempt is made to locate all the signer's certificates, first looking in
the \fIcerts\fR parameter (if it is not NULL). Then they are looked up in any
certificates contained in the \fIp7\fR structure unless \fBPKCS7_NOINTERN\fR is set.
If any signer's certificates cannot be located the operation fails.
.PP
Each signer's certificate is chain verified using the \fBsmimesign\fR purpose and
using the trusted certificate store \fIstore\fR if supplied.
Any internal certificates in the message, which may have been added using
\&\fBPKCS7_add_certificate\fR\|(3), are used as untrusted CAs unless \fBPKCS7_NOCHAIN\fR
is set.
If CRL checking is enabled in \fIstore\fR and \fBPKCS7_NOCRL\fR is not set,
any internal CRLs, which may have been added using \fBPKCS7_add_crl\fR\|(3),
are used in addition to attempting to look them up in \fIstore\fR.
If \fIstore\fR is not NULL and any chain verify fails an error code is returned.
.PP
Finally the signed content is read (and written to \fIout\fR unless it is NULL)
and the signature is checked.
.PP
If all signatures verify correctly then the function is successful.
.PP
Any of the following flags (ored together) can be passed in the \fIflags\fR
parameter to change the default verify behaviour.
Only the flag \fBPKCS7_NOINTERN\fR is meaningful to \fBPKCS7_get0_signers()\fR.
.PP
If \fBPKCS7_NOINTERN\fR is set the certificates in the message itself are not
searched when locating the signer's certificates.
This means that all the signer's certificates must be in the \fIcerts\fR parameter.
.PP
If \fBPKCS7_NOCRL\fR is set and CRL checking is enabled in \fIstore\fR then any
CRLs in the message itself are ignored.
.PP
If the \fBPKCS7_TEXT\fR flag is set MIME headers for type \f(CW\*(C`text/plain\*(C'\fR are deleted
from the content. If the content is not of type \f(CW\*(C`text/plain\*(C'\fR then an error is
returned.
.PP
If \fBPKCS7_NOVERIFY\fR is set the signer's certificates are not chain verified.
.PP
If \fBPKCS7_NOCHAIN\fR is set then the certificates contained in the message are
not used as untrusted CAs. This means that the whole verify chain (apart from
the signer's certificates) must be contained in the trusted store.
.PP
If \fBPKCS7_NOSIGS\fR is set then the signatures on the data are not checked.
.SH NOTES
.IX Header "NOTES"
One application of \fBPKCS7_NOINTERN\fR is to only accept messages signed by
a small number of certificates. The acceptable certificates would be passed
in the \fIcerts\fR parameter. In this case if the signer's certificate is not one
of the certificates supplied in \fIcerts\fR then the verify will fail because the
signer cannot be found.
.PP
Care should be taken when modifying the default verify behaviour, for example
setting \fBPKCS7_NOVERIFY|PKCS7_NOSIGS\fR will totally disable all verification
and any signed message will be considered valid. This combination is however
useful if one merely wishes to write the content to \fIout\fR and its validity
is not considered important.
.PP
Chain verification should arguably be performed using the signing time rather
than the current time. However, since the signing time is supplied by the
signer it cannot be trusted without additional evidence (such as a trusted
timestamp).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS7_verify()\fR returns 1 for a successful verification and 0 if an error occurs.
.PP
\&\fBPKCS7_get0_signers()\fR returns all signers or NULL if an error occurred.
.PP
The error can be obtained from \fBERR_get_error\fR\|(3).
.SH BUGS
.IX Header "BUGS"
The trusted certificate store is not searched for the signer's certificates.
This is primarily due to the inadequacies of the current \fBX509_STORE\fR
functionality.
.PP
The lack of single pass processing means that the signed content must all
be held in memory if it is not detached.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBCMS_verify\fR\|(3), \fBPKCS7_add_certificate\fR\|(3), \fBPKCS7_add_crl\fR\|(3),
\&\fBERR_get_error\fR\|(3), \fBPKCS7_sign\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
