.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SET_SESSION 3"
.TH SSL_SET_SESSION 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_set_session \- set a TLS/SSL session to be used during TLS/SSL connect
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_set_session(SSL *ssl, SSL_SESSION *session);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_set_session()\fR sets \fBsession\fR to be used when the TLS/SSL connection
is to be established. \fBSSL_set_session()\fR is only useful for TLS/SSL clients.
When the session is set, the reference count of \fBsession\fR is incremented
by 1. If the session is not reused, the reference count is decremented
again during \fBSSL_connect()\fR. Whether the session was reused can be queried
with the \fBSSL_session_reused\fR\|(3) call.
.PP
If there is already a session set inside \fBssl\fR (because it was set with
\&\fBSSL_set_session()\fR before or because the same \fBssl\fR was already used for
a connection), \fBSSL_SESSION_free()\fR will be called for that session. If that old
session is still \fBopen\fR, it is considered bad and will be removed from the
session cache (if used). A session is considered open, if \fBSSL_shutdown\fR\|(3) was
not called for the connection (or at least \fBSSL_set_shutdown\fR\|(3) was used to
set the SSL_SENT_SHUTDOWN state).
.SH NOTES
.IX Header "NOTES"
SSL_SESSION objects keep internal link information about the session cache
list, when being inserted into one SSL_CTX object's session cache.
One SSL_SESSION object, regardless of its reference count, must therefore
only be used with one SSL_CTX object (and the SSL objects created
from this SSL_CTX object).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur:
.IP 0 4
The operation failed; check the error stack to find out the reason.
.IP 1 4
.IX Item "1"
The operation succeeded.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_SESSION_free\fR\|(3),
\&\fBSSL_get_session\fR\|(3),
\&\fBSSL_session_reused\fR\|(3),
\&\fBSSL_CTX_set_session_cache_mode\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
