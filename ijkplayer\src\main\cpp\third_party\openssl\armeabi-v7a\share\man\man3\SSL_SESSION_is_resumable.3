.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SESSION_IS_RESUMABLE 3"
.TH SSL_SESSION_IS_RESUMABLE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_SESSION_is_resumable
\&\- determine whether an SSL_SESSION object can be used for resumption
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_SESSION_is_resumable(const SSL_SESSION *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_SESSION_is_resumable()\fR determines whether an SSL_SESSION object can be used
to resume a session or not. Returns 1 if it can or 0 if not. Note that
attempting to resume with a non-resumable session will result in a full
handshake.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_SESSION_is_resumable()\fR returns 1 if the session is resumable or 0 otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_get_session\fR\|(3),
\&\fBSSL_CTX_sess_set_new_cb\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_SESSION_is_resumable()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
