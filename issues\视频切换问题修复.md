# 视频切换问题修复

## 问题描述
playermp4.ets 中视频切换时报错：ijkmp_set_data_source(url="/data/storage/el2/base/haps/entry/cache/temp_video_1_1754567920200.mp4")=-3

## 根本原因
- 错误码 -3 = EIJK_INVALID_STATE，表示播放器状态无效
- ijkmp_set_data_source 只能在 MP_STATE_IDLE 状态下调用
- switchVideo() 中 reset() 后没有等待状态恢复就设置数据源

## 解决方案
改进重置等待机制，添加状态检查和用户提示

## 修复计划
1. 修改 switchVideo() 方法 - 添加状态提示和等待机制
2. 添加 waitForPlayerState() 辅助方法
3. 添加 getPlayerStateString() 状态显示方法
4. 增加重试机制（最多3次）
5. 改进用户反馈和错误处理

## 实现状态
- [/] 正在执行修复
