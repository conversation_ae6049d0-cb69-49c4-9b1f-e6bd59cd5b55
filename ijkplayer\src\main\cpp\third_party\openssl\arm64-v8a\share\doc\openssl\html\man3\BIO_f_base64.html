<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BIO_f_base64</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BIO_f_base64 - base64 BIO filter</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bio.h&gt;
#include &lt;openssl/evp.h&gt;

const BIO_METHOD *BIO_f_base64(void);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>BIO_f_base64() returns the base64 BIO method. This is a filter BIO that base64 encodes any data written through it and decodes any data read through it.</p>

<p>Base64 BIOs do not support BIO_gets() or BIO_puts().</p>

<p>For writing, output is by default divided to lines of length 64 characters and there is always a newline at the end of output.</p>

<p>For reading, first line should be at most 1024 characters long. If it is longer then it is ignored completely. Other input lines can be of any length. There must be a newline at the end of input.</p>

<p>This behavior can be changed with BIO_FLAGS_BASE64_NO_NL flag.</p>

<p>BIO_flush() on a base64 BIO that is being written through is used to signal that no more data is to be encoded: this is used to flush the final block through the BIO.</p>

<p>The flag BIO_FLAGS_BASE64_NO_NL can be set with BIO_set_flags(). For writing, it causes all data to be written on one line without newline at the end. For reading, it expects the data to be all on one line (with or without a trailing newline).</p>

<h1 id="NOTES">NOTES</h1>

<p>Because of the format of base64 encoding the end of the encoded block cannot always be reliably determined.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BIO_f_base64() returns the base64 BIO method.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Base64 encode the string &quot;Hello World\n&quot; and write the result to standard output:</p>

<pre><code>BIO *bio, *b64;
char message[] = &quot;Hello World \n&quot;;

b64 = BIO_new(BIO_f_base64());
bio = BIO_new_fp(stdout, BIO_NOCLOSE);
BIO_push(b64, bio);
BIO_write(b64, message, strlen(message));
BIO_flush(b64);

BIO_free_all(b64);</code></pre>

<p>Read Base64 encoded data from standard input and write the decoded data to standard output:</p>

<pre><code>BIO *bio, *b64, *bio_out;
char inbuf[512];
int inlen;

b64 = BIO_new(BIO_f_base64());
bio = BIO_new_fp(stdin, BIO_NOCLOSE);
bio_out = BIO_new_fp(stdout, BIO_NOCLOSE);
BIO_push(b64, bio);
while ((inlen = BIO_read(b64, inbuf, 512)) &gt; 0)
    BIO_write(bio_out, inbuf, inlen);

BIO_flush(bio_out);
BIO_free_all(b64);</code></pre>

<h1 id="BUGS">BUGS</h1>

<p>The ambiguity of EOF in base64 encoded data can cause additional data following the base64 encoded block to be misinterpreted.</p>

<p>There should be some way of specifying a test that the BIO can perform to reliably determine EOF (for example a MIME boundary).</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


