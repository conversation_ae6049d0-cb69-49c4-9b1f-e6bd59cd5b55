.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_IA32CAP 3"
.TH OPENSSL_IA32CAP 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_ia32cap \- the x86[_64] processor capabilities vector
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& env OPENSSL_ia32cap=... <application>
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
OpenSSL supports a range of x86[_64] instruction set extensions. These
extensions are denoted by individual bits in capability vector returned
by processor in EDX:ECX register pair after executing CPUID instruction
with EAX=1 input value (see Intel Application Note #241618). This vector
is copied to memory upon toolkit initialization and used to choose
between different code paths to provide optimal performance across wide
range of processors. For the moment of this writing following bits are
significant:
.IP "bit #4 denoting presence of Time-Stamp Counter." 4
.IX Item "bit #4 denoting presence of Time-Stamp Counter."
.PD 0
.IP "bit #19 denoting availability of CLFLUSH instruction;" 4
.IX Item "bit #19 denoting availability of CLFLUSH instruction;"
.IP "bit #20, reserved by Intel, is used to choose among RC4 code paths;" 4
.IX Item "bit #20, reserved by Intel, is used to choose among RC4 code paths;"
.IP "bit #23 denoting MMX support;" 4
.IX Item "bit #23 denoting MMX support;"
.IP "bit #24, FXSR bit, denoting availability of XMM registers;" 4
.IX Item "bit #24, FXSR bit, denoting availability of XMM registers;"
.IP "bit #25 denoting SSE support;" 4
.IX Item "bit #25 denoting SSE support;"
.IP "bit #26 denoting SSE2 support;" 4
.IX Item "bit #26 denoting SSE2 support;"
.IP "bit #28 denoting Hyperthreading, which is used to distinguish cores with shared cache;" 4
.IX Item "bit #28 denoting Hyperthreading, which is used to distinguish cores with shared cache;"
.IP "bit #30, reserved by Intel, denotes specifically Intel CPUs;" 4
.IX Item "bit #30, reserved by Intel, denotes specifically Intel CPUs;"
.IP "bit #33 denoting availability of PCLMULQDQ instruction;" 4
.IX Item "bit #33 denoting availability of PCLMULQDQ instruction;"
.IP "bit #41 denoting SSSE3, Supplemental SSE3, support;" 4
.IX Item "bit #41 denoting SSSE3, Supplemental SSE3, support;"
.IP "bit #43 denoting AMD XOP support (forced to zero on non-AMD CPUs);" 4
.IX Item "bit #43 denoting AMD XOP support (forced to zero on non-AMD CPUs);"
.IP "bit #54 denoting availability of MOVBE instruction;" 4
.IX Item "bit #54 denoting availability of MOVBE instruction;"
.IP "bit #57 denoting AES-NI instruction set extension;" 4
.IX Item "bit #57 denoting AES-NI instruction set extension;"
.IP "bit #58, XSAVE bit, lack of which in combination with MOVBE is used to identify Atom Silvermont core;" 4
.IX Item "bit #58, XSAVE bit, lack of which in combination with MOVBE is used to identify Atom Silvermont core;"
.IP "bit #59, OSXSAVE bit, denoting availability of YMM registers;" 4
.IX Item "bit #59, OSXSAVE bit, denoting availability of YMM registers;"
.IP "bit #60 denoting AVX extension;" 4
.IX Item "bit #60 denoting AVX extension;"
.IP "bit #62 denoting availability of RDRAND instruction;" 4
.IX Item "bit #62 denoting availability of RDRAND instruction;"
.PD
.PP
For example, in 32\-bit application context clearing bit #26 at run-time
disables high-performance SSE2 code present in the crypto library, while
clearing bit #24 disables SSE2 code operating on 128\-bit XMM register
bank. You might have to do the latter if target OpenSSL application is
executed on SSE2 capable CPU, but under control of OS that does not
enable XMM registers. Historically address of the capability vector copy
was exposed to application through \fBOPENSSL_ia32cap_loc()\fR, but not
anymore. Now the only way to affect the capability detection is to set
OPENSSL_ia32cap environment variable prior target application start. To
give a specific example, on Intel P4 processor 'env
OPENSSL_ia32cap=0x16980010 apps/openssl', or better yet 'env
OPENSSL_ia32cap=~0x1000000 apps/openssl' would achieve the desired
effect. Alternatively you can reconfigure the toolkit with no\-sse2
option and recompile.
.PP
Less intuitive is clearing bit #28, or ~0x10000000 in the "environment
variable" terms. The truth is that it's not copied from CPUID output
verbatim, but is adjusted to reflect whether or not the data cache is
actually shared between logical cores. This in turn affects the decision
on whether or not expensive countermeasures against cache-timing attacks
are applied, most notably in AES assembler module.
.PP
The capability vector is further extended with EBX value returned by
CPUID with EAX=7 and ECX=0 as input. Following bits are significant:
.IP "bit #64+3 denoting availability of BMI1 instructions, e.g. ANDN;" 4
.IX Item "bit #64+3 denoting availability of BMI1 instructions, e.g. ANDN;"
.PD 0
.IP "bit #64+5 denoting availability of AVX2 instructions;" 4
.IX Item "bit #64+5 denoting availability of AVX2 instructions;"
.IP "bit #64+8 denoting availability of BMI2 instructions, e.g. MULX and RORX;" 4
.IX Item "bit #64+8 denoting availability of BMI2 instructions, e.g. MULX and RORX;"
.IP "bit #64+16 denoting availability of AVX512F extension;" 4
.IX Item "bit #64+16 denoting availability of AVX512F extension;"
.IP "bit #64+18 denoting availability of RDSEED instruction;" 4
.IX Item "bit #64+18 denoting availability of RDSEED instruction;"
.IP "bit #64+19 denoting availability of ADCX and ADOX instructions;" 4
.IX Item "bit #64+19 denoting availability of ADCX and ADOX instructions;"
.IP "bit #64+21 denoting availability of VPMADD52[LH]UQ instructions, aka AVX512IFMA extension;" 4
.IX Item "bit #64+21 denoting availability of VPMADD52[LH]UQ instructions, aka AVX512IFMA extension;"
.IP "bit #64+29 denoting availability of SHA extension;" 4
.IX Item "bit #64+29 denoting availability of SHA extension;"
.IP "bit #64+30 denoting availability of AVX512BW extension;" 4
.IX Item "bit #64+30 denoting availability of AVX512BW extension;"
.IP "bit #64+31 denoting availability of AVX512VL extension;" 4
.IX Item "bit #64+31 denoting availability of AVX512VL extension;"
.IP "bit #64+41 denoting availability of VAES extension;" 4
.IX Item "bit #64+41 denoting availability of VAES extension;"
.IP "bit #64+42 denoting availability of VPCLMULQDQ extension;" 4
.IX Item "bit #64+42 denoting availability of VPCLMULQDQ extension;"
.PD
.PP
To control this extended capability word use ':' as delimiter when
setting up OPENSSL_ia32cap environment variable. For example assigning
\&':~0x20' would disable AVX2 code paths, and ':0' \- all post-AVX
extensions.
.PP
It should be noted that whether or not some of the most "fancy"
extension code paths are actually assembled depends on current assembler
version. Base minimum of AES\-NI/PCLMULQDQ, SSSE3 and SHA extension code
paths are always assembled. Apart from that, minimum assembler version
requirements are summarized in below table:
.PP
.Vb 8
\&   Extension   | GNU as | nasm   | llvm
\&   \-\-\-\-\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-
\&   AVX         | 2.19   | 2.09   | 3.0
\&   AVX2        | 2.22   | 2.10   | 3.1
\&   ADCX/ADOX   | 2.23   | 2.10   | 3.3
\&   AVX512      | 2.25   | 2.11.8 | see NOTES
\&   AVX512IFMA  | 2.26   | 2.11.8 | see NOTES
\&   VAES        | 2.30   | 2.13.3 |
.Ve
.SH NOTES
.IX Header "NOTES"
Even though AVX512 support was implemented in llvm 3.6, compilation of
assembly modules apparently requires explicit \-march flag. But then
compiler generates processor-specific code, which in turn contradicts
the mere idea of run-time switch execution facilitated by the variable
in question. Till the limitation is lifted, it's possible to work around
the problem by making build procedure use following script:
.PP
.Vb 2
\&   #!/bin/sh
\&   exec clang \-no\-integrated\-as "$@"
.Ve
.PP
instead of real clang. In which case it doesn't matter which clang
version is used, as it is GNU assembler version that will be checked.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Not available.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2004\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
