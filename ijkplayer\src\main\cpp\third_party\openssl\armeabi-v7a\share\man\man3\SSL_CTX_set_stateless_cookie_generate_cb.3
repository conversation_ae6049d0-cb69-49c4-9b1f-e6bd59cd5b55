.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_STATELESS_COOKIE_GENERATE_CB 3"
.TH SSL_CTX_SET_STATELESS_COOKIE_GENERATE_CB 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_stateless_cookie_generate_cb,
SSL_CTX_set_stateless_cookie_verify_cb,
SSL_CTX_set_cookie_generate_cb,
SSL_CTX_set_cookie_verify_cb
\&\- Callback functions for stateless TLS1.3 cookies
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_stateless_cookie_generate_cb(
\&     SSL_CTX *ctx,
\&     int (*gen_stateless_cookie_cb) (SSL *ssl,
\&                                     unsigned char *cookie,
\&                                     size_t *cookie_len));
\& void SSL_CTX_set_stateless_cookie_verify_cb(
\&     SSL_CTX *ctx,
\&     int (*verify_stateless_cookie_cb) (SSL *ssl,
\&                                        const unsigned char *cookie,
\&                                        size_t cookie_len));
\&
\& void SSL_CTX_set_cookie_generate_cb(SSL_CTX *ctx,
\&                                     int (*app_gen_cookie_cb) (SSL *ssl,
\&                                                               unsigned char
\&                                                               *cookie,
\&                                                               unsigned int
\&                                                               *cookie_len));
\& void SSL_CTX_set_cookie_verify_cb(SSL_CTX *ctx,
\&                                   int (*app_verify_cookie_cb) (SSL *ssl,
\&                                                                const unsigned
\&                                                                char *cookie,
\&                                                                unsigned int
\&                                                                cookie_len));
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_stateless_cookie_generate_cb()\fR sets the callback used by
\&\fBSSL_stateless\fR\|(3) to generate the application-controlled portion of the cookie
provided to clients in the HelloRetryRequest transmitted as a response to a
ClientHello with a missing or invalid cookie. \fBgen_stateless_cookie_cb()\fR must
write at most SSL_COOKIE_LENGTH bytes into \fBcookie\fR, and must write the number
of bytes written to \fBcookie_len\fR. If a cookie cannot be generated, a zero
return value can be used to abort the handshake.
.PP
\&\fBSSL_CTX_set_stateless_cookie_verify_cb()\fR sets the callback used by
\&\fBSSL_stateless\fR\|(3) to determine whether the application-controlled portion of a
ClientHello cookie is valid. The cookie data is pointed to by \fBcookie\fR and is of
length \fBcookie_len\fR. A nonzero return value from \fBverify_stateless_cookie_cb()\fR
communicates that the cookie is valid. The integrity of the entire cookie,
including the application-controlled portion, is automatically verified by HMAC
before \fBverify_stateless_cookie_cb()\fR is called.
.PP
\&\fBSSL_CTX_set_cookie_generate_cb()\fR sets the callback used by \fBDTLSv1_listen\fR\|(3)
to generate the cookie provided to clients in the HelloVerifyRequest transmitted
as a response to a ClientHello with a missing or invalid cookie.
\&\fBapp_gen_cookie_cb()\fR  must write at most DTLS1_COOKIE_LENGTH bytes into
\&\fBcookie\fR, and must write the number of bytes written to \fBcookie_len\fR. If a
cookie cannot be generated, a zero return value can be used to abort the
handshake.
.PP
\&\fBSSL_CTX_set_cookie_verify_cb()\fR sets the callback used by \fBDTLSv1_listen\fR\|(3) to
determine whether the cookie in a ClientHello is valid. The cookie data is
pointed to by \fBcookie\fR and is of length \fBcookie_len\fR. A nonzero return value
from \fBapp_verify_cookie_cb()\fR communicates that the cookie is valid. The
integrity of the cookie is not verified by OpenSSL. This is an application
responsibility.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Neither function returns a value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_stateless\fR\|(3),
\&\fBDTLSv1_listen\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBSSL_CTX_set_stateless_cookie_generate_cb()\fR and
\&\fBSSL_CTX_set_stateless_cookie_verify_cb()\fR were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
