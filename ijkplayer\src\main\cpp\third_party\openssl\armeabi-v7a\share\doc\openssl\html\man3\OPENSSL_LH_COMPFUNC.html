<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OPENSSL_LH_COMPFUNC</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTE">NOTE</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>LHASH, DECLARE_LHASH_OF, OPENSSL_LH_COMPFUNC, OPENSSL_LH_HASHFUNC, OPENSSL_LH_DOALL_FUNC, LHASH_DOALL_ARG_FN_TYPE, IMPLEMENT_LHASH_HASH_FN, IMPLEMENT_LHASH_COMP_FN, lh_TYPE_new, lh_TYPE_free, lh_TYPE_insert, lh_TYPE_delete, lh_TYPE_retrieve, lh_TYPE_doall, lh_TYPE_doall_arg, lh_TYPE_error - dynamic hash table</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/lhash.h&gt;

DECLARE_LHASH_OF(TYPE);

LHASH *lh_TYPE_new(OPENSSL_LH_HASHFUNC hash, OPENSSL_LH_COMPFUNC compare);
void lh_TYPE_free(LHASH_OF(TYPE) *table);

TYPE *lh_TYPE_insert(LHASH_OF(TYPE) *table, TYPE *data);
TYPE *lh_TYPE_delete(LHASH_OF(TYPE) *table, TYPE *data);
TYPE *lh_TYPE_retrieve(LHASH_OF(TYPE) *table, TYPE *data);

void lh_TYPE_doall(LHASH_OF(TYPE) *table, OPENSSL_LH_DOALL_FUNC func);
void lh_TYPE_doall_arg(LHASH_OF(TYPE) *table, OPENSSL_LH_DOALL_FUNCARG func,
                       TYPE *arg);

int lh_TYPE_error(LHASH_OF(TYPE) *table);

typedef int (*OPENSSL_LH_COMPFUNC)(const void *, const void *);
typedef unsigned long (*OPENSSL_LH_HASHFUNC)(const void *);
typedef void (*OPENSSL_LH_DOALL_FUNC)(const void *);
typedef void (*LHASH_DOALL_ARG_FN_TYPE)(const void *, const void *);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This library implements type-checked dynamic hash tables. The hash table entries can be arbitrary structures. Usually they consist of key and value fields. In the description here, <i>TYPE</i> is used a placeholder for any of the OpenSSL datatypes, such as <i>SSL_SESSION</i>.</p>

<p>lh_TYPE_new() creates a new <b>LHASH_OF(TYPE)</b> structure to store arbitrary data entries, and specifies the &#39;hash&#39; and &#39;compare&#39; callbacks to be used in organising the table&#39;s entries. The <b>hash</b> callback takes a pointer to a table entry as its argument and returns an unsigned long hash value for its key field. The hash value is normally truncated to a power of 2, so make sure that your hash function returns well mixed low order bits. The <b>compare</b> callback takes two arguments (pointers to two hash table entries), and returns 0 if their keys are equal, nonzero otherwise.</p>

<p>If your hash table will contain items of some particular type and the <b>hash</b> and <b>compare</b> callbacks hash/compare these types, then the <b>IMPLEMENT_LHASH_HASH_FN</b> and <b>IMPLEMENT_LHASH_COMP_FN</b> macros can be used to create callback wrappers of the prototypes required by lh_TYPE_new() as shown in this example:</p>

<pre><code>/*
 * Implement the hash and compare functions; &quot;stuff&quot; can be any word.
 */
static unsigned long stuff_hash(const TYPE *a)
{
    ...
}
static int stuff_cmp(const TYPE *a, const TYPE *b)
{
    ...
}

/*
 * Implement the wrapper functions.
 */
static IMPLEMENT_LHASH_HASH_FN(stuff, TYPE)
static IMPLEMENT_LHASH_COMP_FN(stuff, TYPE)</code></pre>

<p>If the type is going to be used in several places, the following macros can be used in a common header file to declare the function wrappers:</p>

<pre><code>DECLARE_LHASH_HASH_FN(stuff, TYPE)
DECLARE_LHASH_COMP_FN(stuff, TYPE)</code></pre>

<p>Then a hash table of TYPE objects can be created using this:</p>

<pre><code>LHASH_OF(TYPE) *htable;

htable = lh_TYPE_new(LHASH_HASH_FN(stuff), LHASH_COMP_FN(stuff));</code></pre>

<p>lh_TYPE_free() frees the <b>LHASH_OF(TYPE)</b> structure <b>table</b>. Allocated hash table entries will not be freed; consider using lh_TYPE_doall() to deallocate any remaining entries in the hash table (see below).</p>

<p>lh_TYPE_insert() inserts the structure pointed to by <b>data</b> into <b>table</b>. If there already is an entry with the same key, the old value is replaced. Note that lh_TYPE_insert() stores pointers, the data are not copied.</p>

<p>lh_TYPE_delete() deletes an entry from <b>table</b>.</p>

<p>lh_TYPE_retrieve() looks up an entry in <b>table</b>. Normally, <b>data</b> is a structure with the key field(s) set; the function will return a pointer to a fully populated structure.</p>

<p>lh_TYPE_doall() will, for every entry in the hash table, call <b>func</b> with the data item as its parameter. For example:</p>

<pre><code>/* Cleans up resources belonging to &#39;a&#39; (this is implemented elsewhere) */
void TYPE_cleanup_doall(TYPE *a);

/* Implement a prototype-compatible wrapper for &quot;TYPE_cleanup&quot; */
IMPLEMENT_LHASH_DOALL_FN(TYPE_cleanup, TYPE)

/* Call &quot;TYPE_cleanup&quot; against all items in a hash table. */
lh_TYPE_doall(hashtable, LHASH_DOALL_FN(TYPE_cleanup));

/* Then the hash table itself can be deallocated */
lh_TYPE_free(hashtable);</code></pre>

<p>When doing this, be careful if you delete entries from the hash table in your callbacks: the table may decrease in size, moving the item that you are currently on down lower in the hash table - this could cause some entries to be skipped during the iteration. The second best solution to this problem is to set hash-&gt;down_load=0 before you start (which will stop the hash table ever decreasing in size). The best solution is probably to avoid deleting items from the hash table inside a &quot;doall&quot; callback!</p>

<p>lh_TYPE_doall_arg() is the same as lh_TYPE_doall() except that <b>func</b> will be called with <b>arg</b> as the second argument and <b>func</b> should be of type <b>LHASH_DOALL_ARG_FN_TYPE</b> (a callback prototype that is passed both the table entry and an extra argument). As with lh_doall(), you can instead choose to declare your callback with a prototype matching the types you are dealing with and use the declare/implement macros to create compatible wrappers that cast variables before calling your type-specific callbacks. An example of this is demonstrated here (printing all hash table entries to a BIO that is provided by the caller):</p>

<pre><code>/* Prints item &#39;a&#39; to &#39;output_bio&#39; (this is implemented elsewhere) */
void TYPE_print_doall_arg(const TYPE *a, BIO *output_bio);

/* Implement a prototype-compatible wrapper for &quot;TYPE_print&quot; */
static IMPLEMENT_LHASH_DOALL_ARG_FN(TYPE, const TYPE, BIO)

/* Print out the entire hashtable to a particular BIO */
lh_TYPE_doall_arg(hashtable, LHASH_DOALL_ARG_FN(TYPE_print), BIO,
                  logging_bio);</code></pre>

<p>lh_TYPE_error() can be used to determine if an error occurred in the last operation.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>lh_TYPE_new() returns <b>NULL</b> on error, otherwise a pointer to the new <b>LHASH</b> structure.</p>

<p>When a hash table entry is replaced, lh_TYPE_insert() returns the value being replaced. <b>NULL</b> is returned on normal operation and on error.</p>

<p>lh_TYPE_delete() returns the entry being deleted. <b>NULL</b> is returned if there is no such value in the hash table.</p>

<p>lh_TYPE_retrieve() returns the hash table entry if it has been found, <b>NULL</b> otherwise.</p>

<p>lh_TYPE_error() returns 1 if an error occurred in the last operation, 0 otherwise. It&#39;s meaningful only after non-retrieve operations.</p>

<p>lh_TYPE_free(), lh_TYPE_doall() and lh_TYPE_doall_arg() return no values.</p>

<h1 id="NOTE">NOTE</h1>

<p>The LHASH code is not thread safe. All updating operations, as well as lh_TYPE_error call must be performed under a write lock. All retrieve operations should be performed under a read lock, <i>unless</i> accurate usage statistics are desired. In which case, a write lock should be used for retrieve operations as well. For output of the usage statistics, using the functions from <a href="../man3/OPENSSL_LH_stats.html">OPENSSL_LH_stats(3)</a>, a read lock suffices.</p>

<p>The LHASH code regards table entries as constant data. As such, it internally represents lh_insert()&#39;d items with a &quot;const void *&quot; pointer type. This is why callbacks such as those used by lh_doall() and lh_doall_arg() declare their prototypes with &quot;const&quot;, even for the parameters that pass back the table items&#39; data pointers - for consistency, user-provided data is &quot;const&quot; at all times as far as the LHASH code is concerned. However, as callers are themselves providing these pointers, they can choose whether they too should be treating all such parameters as constant.</p>

<p>As an example, a hash table may be maintained by code that, for reasons of encapsulation, has only &quot;const&quot; access to the data being indexed in the hash table (i.e. it is returned as &quot;const&quot; from elsewhere in their code) - in this case the LHASH prototypes are appropriate as-is. Conversely, if the caller is responsible for the life-time of the data in question, then they may well wish to make modifications to table item passed back in the lh_doall() or lh_doall_arg() callbacks (see the &quot;TYPE_cleanup&quot; example above). If so, the caller can either cast the &quot;const&quot; away (if they&#39;re providing the raw callbacks themselves) or use the macros to declare/implement the wrapper functions without &quot;const&quot; types.</p>

<p>Callers that only have &quot;const&quot; access to data they&#39;re indexing in a table, yet declare callbacks without constant types (or cast the &quot;const&quot; away themselves), are therefore creating their own risks/bugs without being encouraged to do so by the API. On a related note, those auditing code should pay special attention to any instances of DECLARE/IMPLEMENT_LHASH_DOALL_[ARG_]_FN macros that provide types without any &quot;const&quot; qualifiers.</p>

<h1 id="BUGS">BUGS</h1>

<p>lh_TYPE_insert() returns <b>NULL</b> both for success and error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/OPENSSL_LH_stats.html">OPENSSL_LH_stats(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>In OpenSSL 1.0.0, the lhash interface was revamped for better type checking.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


