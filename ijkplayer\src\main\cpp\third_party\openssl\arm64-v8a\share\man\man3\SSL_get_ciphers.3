.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_CIPHERS 3"
.TH SSL_GET_CIPHERS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get1_supported_ciphers,
SSL_get_client_ciphers,
SSL_get_ciphers,
SSL_CTX_get_ciphers,
SSL_bytes_to_cipher_list,
SSL_get_cipher_list,
SSL_get_shared_ciphers
\&\- get list of available SSL_CIPHERs
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& STACK_OF(SSL_CIPHER) *SSL_get_ciphers(const SSL *ssl);
\& STACK_OF(SSL_CIPHER) *SSL_CTX_get_ciphers(const SSL_CTX *ctx);
\& STACK_OF(SSL_CIPHER) *SSL_get1_supported_ciphers(SSL *s);
\& STACK_OF(SSL_CIPHER) *SSL_get_client_ciphers(const SSL *ssl);
\& int SSL_bytes_to_cipher_list(SSL *s, const unsigned char *bytes, size_t len,
\&                              int isv2format, STACK_OF(SSL_CIPHER) **sk,
\&                              STACK_OF(SSL_CIPHER) **scsvs);
\& const char *SSL_get_cipher_list(const SSL *ssl, int priority);
\& char *SSL_get_shared_ciphers(const SSL *s, char *buf, int size);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_ciphers()\fR returns the stack of available SSL_CIPHERs for \fBssl\fR,
sorted by preference. If \fBssl\fR is NULL or no ciphers are available, NULL
is returned.
.PP
\&\fBSSL_CTX_get_ciphers()\fR returns the stack of available SSL_CIPHERs for \fBctx\fR.
.PP
\&\fBSSL_get1_supported_ciphers()\fR returns the stack of enabled SSL_CIPHERs for
\&\fBssl\fR as would be sent in a ClientHello (that is, sorted by preference).
The list depends on settings like the cipher list, the supported protocol
versions, the security level, and the enabled signature algorithms.
SRP and PSK ciphers are only enabled if the appropriate callbacks or settings
have been applied.
The list of ciphers that would be sent in a ClientHello can differ from
the list of ciphers that would be acceptable when acting as a server.
For example, additional ciphers may be usable by a server if there is
a gap in the list of supported protocols, and some ciphers may not be
usable by a server if there is not a suitable certificate configured.
If \fBssl\fR is NULL or no ciphers are available, NULL is returned.
.PP
\&\fBSSL_get_client_ciphers()\fR returns the stack of available SSL_CIPHERs matching the
list received from the client on \fBssl\fR. If \fBssl\fR is NULL, no ciphers are
available, or \fBssl\fR is not operating in server mode, NULL is returned.
.PP
\&\fBSSL_bytes_to_cipher_list()\fR treats the supplied \fBlen\fR octets in \fBbytes\fR
as a wire-protocol cipher suite specification (in the three-octet-per-cipher
SSLv2 wire format if \fBisv2format\fR is nonzero; otherwise the two-octet
SSLv3/TLS wire format), and parses the cipher suites supported by the library
into the returned stacks of SSL_CIPHER objects sk and Signalling Cipher-Suite
Values scsvs.  Unsupported cipher suites are ignored.  Returns 1 on success
and 0 on failure.
.PP
\&\fBSSL_get_cipher_list()\fR returns a pointer to the name of the SSL_CIPHER
listed for \fBssl\fR with \fBpriority\fR. If \fBssl\fR is NULL, no ciphers are
available, or there are less ciphers than \fBpriority\fR available, NULL
is returned.
.PP
\&\fBSSL_get_shared_ciphers()\fR creates a colon separated and NUL terminated list of
SSL_CIPHER names that are available in both the client and the server. \fBbuf\fR is
the buffer that should be populated with the list of names and \fBsize\fR is the
size of that buffer. A pointer to \fBbuf\fR is returned on success or NULL on
error. If the supplied buffer is not large enough to contain the complete list
of names then a truncated list of names will be returned. Note that just because
a ciphersuite is available (i.e. it is configured in the cipher list) and shared
by both the client and the server it does not mean that it is enabled (see the
description of \fBSSL_get1_supported_ciphers()\fR above). This function will return
available shared ciphersuites whether or not they are enabled. This is a server
side function only and must only be called after the completion of the initial
handshake.
.SH NOTES
.IX Header "NOTES"
The details of the ciphers obtained by \fBSSL_get_ciphers()\fR, \fBSSL_CTX_get_ciphers()\fR
\&\fBSSL_get1_supported_ciphers()\fR and \fBSSL_get_client_ciphers()\fR can be obtained using
the \fBSSL_CIPHER_get_name\fR\|(3) family of functions.
.PP
Call \fBSSL_get_cipher_list()\fR with \fBpriority\fR starting from 0 to obtain the
sorted list of available ciphers, until NULL is returned.
.PP
Note: \fBSSL_get_ciphers()\fR, \fBSSL_CTX_get_ciphers()\fR and \fBSSL_get_client_ciphers()\fR
return a pointer to an internal cipher stack, which will be freed later on when
the SSL or SSL_SESSION object is freed.  Therefore, the calling code \fBMUST NOT\fR
free the return value itself.
.PP
The stack returned by \fBSSL_get1_supported_ciphers()\fR should be freed using
\&\fBsk_SSL_CIPHER_free()\fR.
.PP
The stacks returned by \fBSSL_bytes_to_cipher_list()\fR should be freed using
\&\fBsk_SSL_CIPHER_free()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
See DESCRIPTION
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_set_cipher_list\fR\|(3),
\&\fBSSL_CIPHER_get_name\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
