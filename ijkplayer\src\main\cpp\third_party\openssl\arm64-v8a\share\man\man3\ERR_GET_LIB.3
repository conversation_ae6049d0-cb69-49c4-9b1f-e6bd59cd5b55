.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ERR_GET_LIB 3"
.TH ERR_GET_LIB 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ERR_GET_LIB, ERR_GET_FUNC, ERR_GET_REASON, ERR_FATAL_ERROR
\&\- get information from error codes
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/err.h>
\&
\& int ERR_GET_LIB(unsigned long e);
\&
\& int ERR_GET_FUNC(unsigned long e);
\&
\& int ERR_GET_REASON(unsigned long e);
\&
\& int ERR_FATAL_ERROR(unsigned long e);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The error code returned by \fBERR_get_error()\fR consists of a library
number, function code and reason code. \fBERR_GET_LIB()\fR, \fBERR_GET_FUNC()\fR
and \fBERR_GET_REASON()\fR can be used to extract these.
.PP
\&\fBERR_FATAL_ERROR()\fR indicates whether a given error code is a fatal error.
.PP
The library number and function code describe where the error
occurred, the reason code is the information about what went wrong.
.PP
Each sub-library of OpenSSL has a unique library number; function and
reason codes are unique within each sub-library.  Note that different
libraries may use the same value to signal different functions and
reasons.
.PP
\&\fBERR_R_...\fR reason codes such as \fBERR_R_MALLOC_FAILURE\fR are globally
unique. However, when checking for sub-library specific reason codes,
be sure to also compare the library number.
.PP
\&\fBERR_GET_LIB()\fR, \fBERR_GET_FUNC()\fR, \fBERR_GET_REASON()\fR, and \fBERR_FATAL_ERROR()\fR
 are macros.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The library number, function code, reason code, and whether the error
is fatal, respectively.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBERR_GET_LIB()\fR, \fBERR_GET_FUNC()\fR and \fBERR_GET_REASON()\fR are available in
all versions of OpenSSL.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
