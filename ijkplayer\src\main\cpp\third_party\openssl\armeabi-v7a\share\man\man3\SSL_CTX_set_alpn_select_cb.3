.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_ALPN_SELECT_CB 3"
.TH SSL_CTX_SET_ALPN_SELECT_CB 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_alpn_protos, SSL_set_alpn_protos, SSL_CTX_set_alpn_select_cb,
SSL_CTX_set_next_proto_select_cb, SSL_CTX_set_next_protos_advertised_cb,
SSL_select_next_proto, SSL_get0_alpn_selected, SSL_get0_next_proto_negotiated
\&\- handle application layer protocol negotiation (ALPN)
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_set_alpn_protos(SSL_CTX *ctx, const unsigned char *protos,
\&                             unsigned int protos_len);
\& int SSL_set_alpn_protos(SSL *ssl, const unsigned char *protos,
\&                         unsigned int protos_len);
\& void SSL_CTX_set_alpn_select_cb(SSL_CTX *ctx,
\&                                 int (*cb) (SSL *ssl,
\&                                            const unsigned char **out,
\&                                            unsigned char *outlen,
\&                                            const unsigned char *in,
\&                                            unsigned int inlen,
\&                                            void *arg), void *arg);
\& void SSL_get0_alpn_selected(const SSL *ssl, const unsigned char **data,
\&                             unsigned int *len);
\&
\& void SSL_CTX_set_next_protos_advertised_cb(SSL_CTX *ctx,
\&                                            int (*cb)(SSL *ssl,
\&                                                      const unsigned char **out,
\&                                                      unsigned int *outlen,
\&                                                      void *arg),
\&                                            void *arg);
\& void SSL_CTX_set_next_proto_select_cb(SSL_CTX *ctx,
\&                               int (*cb)(SSL *s,
\&                                         unsigned char **out,
\&                                         unsigned char *outlen,
\&                                         const unsigned char *in,
\&                                         unsigned int inlen,
\&                                         void *arg),
\&                               void *arg);
\& int SSL_select_next_proto(unsigned char **out, unsigned char *outlen,
\&                           const unsigned char *server,
\&                           unsigned int server_len,
\&                           const unsigned char *client,
\&                           unsigned int client_len)
\& void SSL_get0_next_proto_negotiated(const SSL *s, const unsigned char **data,
\&                             unsigned *len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_alpn_protos()\fR and \fBSSL_set_alpn_protos()\fR are used by the client to
set the list of protocols available to be negotiated. The \fBprotos\fR must be in
protocol-list format, described below. The length of \fBprotos\fR is specified in
\&\fBprotos_len\fR.
.PP
\&\fBSSL_CTX_set_alpn_select_cb()\fR sets the application callback \fBcb\fR used by a
server to select which protocol to use for the incoming connection. When \fBcb\fR
is NULL, ALPN is not used. The \fBarg\fR value is a pointer which is passed to
the application callback.
.PP
\&\fBcb\fR is the application defined callback. The \fBin\fR, \fBinlen\fR parameters are a
vector in protocol-list format. The value of the \fBout\fR, \fBoutlen\fR vector
should be set to the value of a single protocol selected from the \fBin\fR,
\&\fBinlen\fR vector. The \fBout\fR buffer may point directly into \fBin\fR, or to a
buffer that outlives the handshake. The \fBarg\fR parameter is the pointer set via
\&\fBSSL_CTX_set_alpn_select_cb()\fR.
.PP
\&\fBSSL_select_next_proto()\fR is a helper function used to select protocols. It
implements the standard protocol selection. It is expected that this function
is called from the application callback \fBcb\fR. The protocol data in \fBserver\fR,
\&\fBserver_len\fR and \fBclient\fR, \fBclient_len\fR must be in the protocol-list format
described below. The first item in the \fBserver\fR, \fBserver_len\fR list that
matches an item in the \fBclient\fR, \fBclient_len\fR list is selected, and returned
in \fBout\fR, \fBoutlen\fR. The \fBout\fR value will point into either \fBserver\fR or
\&\fBclient\fR, so it should be copied immediately. If no match is found, the first
item in \fBclient\fR, \fBclient_len\fR is returned in \fBout\fR, \fBoutlen\fR. This
function can also be used in the NPN callback.
.PP
\&\fBSSL_CTX_set_next_proto_select_cb()\fR sets a callback \fBcb\fR that is called when a
client needs to select a protocol from the server's provided list, and a
user-defined pointer argument \fBarg\fR which will be passed to this callback.
For the callback itself, \fBout\fR
must be set to point to the selected protocol (which may be within \fBin\fR).
The length of the protocol name must be written into \fBoutlen\fR. The
server's advertised protocols are provided in \fBin\fR and \fBinlen\fR. The
callback can assume that \fBin\fR is syntactically valid. The client must
select a protocol. It is fatal to the connection if this callback returns
a value other than \fBSSL_TLSEXT_ERR_OK\fR. The \fBarg\fR parameter is the pointer
set via \fBSSL_CTX_set_next_proto_select_cb()\fR.
.PP
\&\fBSSL_CTX_set_next_protos_advertised_cb()\fR sets a callback \fBcb\fR that is called
when a TLS server needs a list of supported protocols for Next Protocol
Negotiation. The returned list must be in protocol-list format, described
below.  The list is
returned by setting \fBout\fR to point to it and \fBoutlen\fR to its length. This
memory will not be modified, but the \fBSSL\fR does keep a
reference to it. The callback should return \fBSSL_TLSEXT_ERR_OK\fR if it
wishes to advertise. Otherwise, no such extension will be included in the
ServerHello.
.PP
\&\fBSSL_get0_alpn_selected()\fR returns a pointer to the selected protocol in \fBdata\fR
with length \fBlen\fR. It is not NUL-terminated. \fBdata\fR is set to NULL and \fBlen\fR
is set to 0 if no protocol has been selected. \fBdata\fR must not be freed.
.PP
\&\fBSSL_get0_next_proto_negotiated()\fR sets \fBdata\fR and \fBlen\fR to point to the
client's requested protocol for this connection. If the client did not
request any protocol or NPN is not enabled, then \fBdata\fR is set to NULL and
\&\fBlen\fR to 0. Note that
the client can request any protocol it chooses. The value returned from
this function need not be a member of the list of supported protocols
provided by the callback.
.SH NOTES
.IX Header "NOTES"
The protocol-lists must be in wire-format, which is defined as a vector of
nonempty, 8\-bit length-prefixed, byte strings. The length-prefix byte is not
included in the length. Each string is limited to 255 bytes. A byte-string
length of 0 is invalid. A truncated byte-string is invalid. The length of the
vector is not in the vector itself, but in a separate variable.
.PP
Example:
.PP
.Vb 5
\& unsigned char vector[] = {
\&     6, \*(Aqs\*(Aq, \*(Aqp\*(Aq, \*(Aqd\*(Aq, \*(Aqy\*(Aq, \*(Aq/\*(Aq, \*(Aq1\*(Aq,
\&     8, \*(Aqh\*(Aq, \*(Aqt\*(Aq, \*(Aqt\*(Aq, \*(Aqp\*(Aq, \*(Aq/\*(Aq, \*(Aq1\*(Aq, \*(Aq.\*(Aq, \*(Aq1\*(Aq
\& };
\& unsigned int length = sizeof(vector);
.Ve
.PP
The ALPN callback is executed after the servername callback; as that servername
callback may update the SSL_CTX, and subsequently, the ALPN callback.
.PP
If there is no ALPN proposed in the ClientHello, the ALPN callback is not
invoked.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_alpn_protos()\fR and \fBSSL_set_alpn_protos()\fR return 0 on success, and
non\-0 on failure. WARNING: these functions reverse the return value convention.
.PP
\&\fBSSL_select_next_proto()\fR returns one of the following:
.IP OPENSSL_NPN_NEGOTIATED 4
.IX Item "OPENSSL_NPN_NEGOTIATED"
A match was found and is returned in \fBout\fR, \fBoutlen\fR.
.IP OPENSSL_NPN_NO_OVERLAP 4
.IX Item "OPENSSL_NPN_NO_OVERLAP"
No match was found. The first item in \fBclient\fR, \fBclient_len\fR is returned in
\&\fBout\fR, \fBoutlen\fR.
.PP
The ALPN select callback \fBcb\fR, must return one of the following:
.IP SSL_TLSEXT_ERR_OK 4
.IX Item "SSL_TLSEXT_ERR_OK"
ALPN protocol selected.
.IP SSL_TLSEXT_ERR_ALERT_FATAL 4
.IX Item "SSL_TLSEXT_ERR_ALERT_FATAL"
There was no overlap between the client's supplied list and the server
configuration.
.IP SSL_TLSEXT_ERR_NOACK 4
.IX Item "SSL_TLSEXT_ERR_NOACK"
ALPN protocol not selected, e.g., because no ALPN protocols are configured for
this connection.
.PP
The callback set using \fBSSL_CTX_set_next_proto_select_cb()\fR should return
\&\fBSSL_TLSEXT_ERR_OK\fR if successful. Any other value is fatal to the connection.
.PP
The callback set using \fBSSL_CTX_set_next_protos_advertised_cb()\fR should return
\&\fBSSL_TLSEXT_ERR_OK\fR if it wishes to advertise. Otherwise, no such extension
will be included in the ServerHello.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_set_tlsext_servername_callback\fR\|(3),
\&\fBSSL_CTX_set_tlsext_servername_arg\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
