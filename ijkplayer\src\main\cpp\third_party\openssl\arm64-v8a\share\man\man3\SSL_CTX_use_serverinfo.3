.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_USE_SERVERINFO 3"
.TH SSL_CTX_USE_SERVERINFO 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_use_serverinfo_ex,
SSL_CTX_use_serverinfo,
SSL_CTX_use_serverinfo_file
\&\- use serverinfo extension
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_use_serverinfo_ex(SSL_CTX *ctx, unsigned int version,
\&                               const unsigned char *serverinfo,
\&                               size_t serverinfo_length);
\&
\& int SSL_CTX_use_serverinfo(SSL_CTX *ctx, const unsigned char *serverinfo,
\&                            size_t serverinfo_length);
\&
\& int SSL_CTX_use_serverinfo_file(SSL_CTX *ctx, const char *file);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions load "serverinfo" TLS extensions into the SSL_CTX. A
"serverinfo" extension is returned in response to an empty ClientHello
Extension.
.PP
\&\fBSSL_CTX_use_serverinfo_ex()\fR loads one or more serverinfo extensions from
a byte array into \fBctx\fR. The \fBversion\fR parameter specifies the format of the
byte array provided in \fB*serverinfo\fR which is of length \fBserverinfo_length\fR.
.PP
If \fBversion\fR is \fBSSL_SERVERINFOV2\fR then the extensions in the array must
consist of a 4\-byte context, a 2\-byte Extension Type, a 2\-byte length, and then
length bytes of extension_data. The context and type values have the same
meaning as for \fBSSL_CTX_add_custom_ext\fR\|(3). If serverinfo is being loaded for
extensions to be added to a Certificate message, then the extension will only
be added for the first certificate in the message (which is always the
end-entity certificate).
.PP
If \fBversion\fR is \fBSSL_SERVERINFOV1\fR then the extensions in the array must
consist of a 2\-byte Extension Type, a 2\-byte length, and then length bytes of
extension_data. The type value has the same meaning as for
\&\fBSSL_CTX_add_custom_ext\fR\|(3). The following default context value will be used
in this case:
.PP
.Vb 2
\& SSL_EXT_TLS1_2_AND_BELOW_ONLY | SSL_EXT_CLIENT_HELLO
\& | SSL_EXT_TLS1_2_SERVER_HELLO | SSL_EXT_IGNORE_ON_RESUMPTION
.Ve
.PP
\&\fBSSL_CTX_use_serverinfo()\fR does the same thing as \fBSSL_CTX_use_serverinfo_ex()\fR
except that there is no \fBversion\fR parameter so a default version of
SSL_SERVERINFOV1 is used instead.
.PP
\&\fBSSL_CTX_use_serverinfo_file()\fR loads one or more serverinfo extensions from
\&\fBfile\fR into \fBctx\fR.  The extensions must be in PEM format.  Each extension
must be in a format as described above for \fBSSL_CTX_use_serverinfo_ex()\fR.  Each
PEM extension name must begin with the phrase "BEGIN SERVERINFOV2 FOR " for
SSL_SERVERINFOV2 data or "BEGIN SERVERINFO FOR " for SSL_SERVERINFOV1 data.
.PP
If more than one certificate (RSA/DSA) is installed using
\&\fBSSL_CTX_use_certificate()\fR, the serverinfo extension will be loaded into the
last certificate installed.  If e.g. the last item was a RSA certificate, the
loaded serverinfo extension data will be loaded for that certificate.  To
use the serverinfo extension for multiple certificates,
\&\fBSSL_CTX_use_serverinfo()\fR needs to be called multiple times, once \fBafter\fR
each time a certificate is loaded via a call to \fBSSL_CTX_use_certificate()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
On success, the functions return 1.
On failure, the functions return 0.  Check out the error stack to find out
the reason.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2013\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
