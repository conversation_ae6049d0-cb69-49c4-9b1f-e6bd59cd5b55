<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SMIME_read_PKCS7</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SMIME_read_PKCS7 - parse S/MIME message</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/pkcs7.h&gt;

PKCS7 *SMIME_read_PKCS7(BIO *in, BIO **bcont);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SMIME_read_PKCS7() parses a message in S/MIME format.</p>

<p><b>in</b> is a BIO to read the message from.</p>

<p>If cleartext signing is used then the content is saved in a memory bio which is written to <b>*bcont</b>, otherwise <b>*bcont</b> is set to <b>NULL</b>.</p>

<p>The parsed PKCS#7 structure is returned or <b>NULL</b> if an error occurred.</p>

<h1 id="NOTES">NOTES</h1>

<p>If <b>*bcont</b> is not <b>NULL</b> then the message is clear text signed. <b>*bcont</b> can then be passed to PKCS7_verify() with the <b>PKCS7_DETACHED</b> flag set.</p>

<p>Otherwise the type of the returned structure can be determined using PKCS7_type_is_enveloped(), etc.</p>

<p>To support future functionality if <b>bcont</b> is not <b>NULL</b> <b>*bcont</b> should be initialized to <b>NULL</b>. For example:</p>

<pre><code>BIO *cont = NULL;
PKCS7 *p7;

p7 = SMIME_read_PKCS7(in, &amp;cont);</code></pre>

<h1 id="BUGS">BUGS</h1>

<p>The MIME parser used by SMIME_read_PKCS7() is somewhat primitive. While it will handle most S/MIME messages more complex compound formats may not work.</p>

<p>The parser assumes that the PKCS7 structure is always base64 encoded and will not handle the case where it is in binary format or uses quoted printable format.</p>

<p>The use of a memory BIO to hold the signed content limits the size of message which can be processed due to memory restraints: a streaming single pass option should be available.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SMIME_read_PKCS7() returns a valid <b>PKCS7</b> structure or <b>NULL</b> if an error occurred. The error can be obtained from ERR_get_error(3).</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/SMIME_read_PKCS7.html">SMIME_read_PKCS7(3)</a>, <a href="../man3/PKCS7_sign.html">PKCS7_sign(3)</a>, <a href="../man3/PKCS7_verify.html">PKCS7_verify(3)</a>, <a href="../man3/PKCS7_encrypt.html">PKCS7_encrypt(3)</a> <a href="../man3/PKCS7_decrypt.html">PKCS7_decrypt(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2002-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


