.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CONF_CMD 3"
.TH SSL_CONF_CMD 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CONF_cmd_value_type,
SSL_CONF_cmd \- send configuration command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CONF_cmd(SSL_CONF_CTX *cctx, const char *cmd, const char *value);
\& int SSL_CONF_cmd_value_type(SSL_CONF_CTX *cctx, const char *cmd);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The function \fBSSL_CONF_cmd()\fR performs configuration operation \fBcmd\fR with
optional parameter \fBvalue\fR on \fBctx\fR. Its purpose is to simplify application
configuration of \fBSSL_CTX\fR or \fBSSL\fR structures by providing a common
framework for command line options or configuration files.
.PP
\&\fBSSL_CONF_cmd_value_type()\fR returns the type of value that \fBcmd\fR refers to.
.SH "SUPPORTED COMMAND LINE COMMANDS"
.IX Header "SUPPORTED COMMAND LINE COMMANDS"
Currently supported \fBcmd\fR names for command lines (i.e. when the
flag \fBSSL_CONF_CMDLINE\fR is set) are listed below. Note: all \fBcmd\fR names
are case sensitive. Unless otherwise stated commands can be used by
both clients and servers and the \fBvalue\fR parameter is not used. The default
prefix for command line commands is \fB\-\fR and that is reflected below.
.IP \fB\-sigalgs\fR 4
.IX Item "-sigalgs"
This sets the supported signature algorithms for TLSv1.2 and TLSv1.3.
For clients this
value is used directly for the supported signature algorithms extension. For
servers it is used to determine which signature algorithms to support.
.Sp
The \fBvalue\fR argument should be a colon separated list of signature algorithms
in order of decreasing preference of the form \fBalgorithm+hash\fR or
\&\fBsignature_scheme\fR. \fBalgorithm\fR
is one of \fBRSA\fR, \fBDSA\fR or \fBECDSA\fR and \fBhash\fR is a supported algorithm
OID short name such as \fBSHA1\fR, \fBSHA224\fR, \fBSHA256\fR, \fBSHA384\fR of \fBSHA512\fR.
Note: algorithm and hash names are case sensitive.
\&\fBsignature_scheme\fR is one of the signature schemes defined in TLSv1.3,
specified using the IETF name, e.g., \fBecdsa_secp256r1_sha256\fR, \fBed25519\fR,
or \fBrsa_pss_pss_sha256\fR.
.Sp
If this option is not set then all signature algorithms supported by the
OpenSSL library are permissible.
.Sp
Note: algorithms which specify a PKCS#1 v1.5 signature scheme (either by
using \fBRSA\fR as the \fBalgorithm\fR or by using one of the \fBrsa_pkcs1_*\fR
identifiers) are ignored in TLSv1.3 and will not be negotiated.
.IP \fB\-client_sigalgs\fR 4
.IX Item "-client_sigalgs"
This sets the supported signature algorithms associated with client
authentication for TLSv1.2 and TLSv1.3.
For servers the value is used in the
\&\fBsignature_algorithms\fR field of a \fBCertificateRequest\fR message.
For clients it is
used to determine which signature algorithm to use with the client certificate.
If a server does not request a certificate this option has no effect.
.Sp
The syntax of \fBvalue\fR is identical to \fB\-sigalgs\fR. If not set then
the value set for \fB\-sigalgs\fR will be used instead.
.IP \fB\-groups\fR 4
.IX Item "-groups"
This sets the supported groups. For clients, the groups are
sent using the supported groups extension. For servers, it is used
to determine which group to use. This setting affects groups used for
signatures (in TLSv1.2 and earlier) and key exchange. The first group listed
will also be used for the \fBkey_share\fR sent by a client in a TLSv1.3
\&\fBClientHello\fR.
.Sp
The \fBvalue\fR argument is a colon separated list of groups. The group can be
either the \fBNIST\fR name (e.g. \fBP\-256\fR), some other commonly used name where
applicable (e.g. \fBX25519\fR) or an OpenSSL OID name (e.g. \fBprime256v1\fR). Group
names are case sensitive. The list should be in order of preference with the
most preferred group first.
.IP \fB\-curves\fR 4
.IX Item "-curves"
This is a synonym for the "\-groups" command.
.IP \fB\-named_curve\fR 4
.IX Item "-named_curve"
This sets the temporary curve used for ephemeral ECDH modes. Only used by
servers
.Sp
The \fBvalue\fR argument is a curve name or the special value \fBauto\fR which
picks an appropriate curve based on client and server preferences. The curve
can be either the \fBNIST\fR name (e.g. \fBP\-256\fR) or an OpenSSL OID name
(e.g. \fBprime256v1\fR). Curve names are case sensitive.
.IP \fB\-cipher\fR 4
.IX Item "-cipher"
Sets the TLSv1.2 and below ciphersuite list to \fBvalue\fR. This list will be
combined with any configured TLSv1.3 ciphersuites. Note: syntax checking
of \fBvalue\fR is currently not performed unless a \fBSSL\fR or \fBSSL_CTX\fR structure is
associated with \fBcctx\fR.
.IP \fB\-ciphersuites\fR 4
.IX Item "-ciphersuites"
Sets the available ciphersuites for TLSv1.3 to value. This is a simple colon
(":") separated list of TLSv1.3 ciphersuite names in order of preference. This
list will be combined any configured TLSv1.2 and below ciphersuites.
See \fBciphers\fR\|(1) for more information.
.IP \fB\-cert\fR 4
.IX Item "-cert"
Attempts to use the file \fBvalue\fR as the certificate for the appropriate
context. It currently uses \fBSSL_CTX_use_certificate_chain_file()\fR if an \fBSSL_CTX\fR
structure is set or \fBSSL_use_certificate_file()\fR with filetype PEM if an \fBSSL\fR
structure is set. This option is only supported if certificate operations
are permitted.
.IP \fB\-key\fR 4
.IX Item "-key"
Attempts to use the file \fBvalue\fR as the private key for the appropriate
context. This option is only supported if certificate operations
are permitted. Note: if no \fB\-key\fR option is set then a private key is
not loaded unless the flag \fBSSL_CONF_FLAG_REQUIRE_PRIVATE\fR is set.
.IP \fB\-dhparam\fR 4
.IX Item "-dhparam"
Attempts to use the file \fBvalue\fR as the set of temporary DH parameters for
the appropriate context. This option is only supported if certificate
operations are permitted.
.IP \fB\-record_padding\fR 4
.IX Item "-record_padding"
Attempts to pad TLSv1.3 records so that they are a multiple of \fBvalue\fR in
length on send. A \fBvalue\fR of 0 or 1 turns off padding. Otherwise, the
\&\fBvalue\fR must be >1 or <=16384.
.IP \fB\-no_renegotiation\fR 4
.IX Item "-no_renegotiation"
Disables all attempts at renegotiation in TLSv1.2 and earlier, same as setting
\&\fBSSL_OP_NO_RENEGOTIATION\fR.
.IP "\fB\-min_protocol\fR, \fB\-max_protocol\fR" 4
.IX Item "-min_protocol, -max_protocol"
Sets the minimum and maximum supported protocol.
Currently supported protocol values are \fBSSLv3\fR, \fBTLSv1\fR, \fBTLSv1.1\fR,
\&\fBTLSv1.2\fR, \fBTLSv1.3\fR for TLS; \fBDTLSv1\fR, \fBDTLSv1.2\fR for DTLS, and \fBNone\fR
for no limit.
If either the lower or upper bound is not specified then only the other bound
applies, if specified.
If your application supports both TLS and DTLS you can specify any of these
options twice, once with a bound for TLS and again with an appropriate bound
for DTLS.
To restrict the supported protocol versions use these commands rather than the
deprecated alternative commands below.
.IP "\fB\-no_ssl3\fR, \fB\-no_tls1\fR, \fB\-no_tls1_1\fR, \fB\-no_tls1_2\fR, \fB\-no_tls1_3\fR" 4
.IX Item "-no_ssl3, -no_tls1, -no_tls1_1, -no_tls1_2, -no_tls1_3"
Disables protocol support for SSLv3, TLSv1.0, TLSv1.1, TLSv1.2 or TLSv1.3 by
setting the corresponding options \fBSSL_OP_NO_SSLv3\fR, \fBSSL_OP_NO_TLSv1\fR,
\&\fBSSL_OP_NO_TLSv1_1\fR, \fBSSL_OP_NO_TLSv1_2\fR and \fBSSL_OP_NO_TLSv1_3\fR
respectively. These options are deprecated, instead use \fB\-min_protocol\fR and
\&\fB\-max_protocol\fR.
.IP \fB\-bugs\fR 4
.IX Item "-bugs"
Various bug workarounds are set, same as setting \fBSSL_OP_ALL\fR.
.IP \fB\-comp\fR 4
.IX Item "-comp"
Enables support for SSL/TLS compression, same as clearing
\&\fBSSL_OP_NO_COMPRESSION\fR.
This command was introduced in OpenSSL 1.1.0.
As of OpenSSL 1.1.0, compression is off by default.
.IP \fB\-no_comp\fR 4
.IX Item "-no_comp"
Disables support for SSL/TLS compression, same as setting
\&\fBSSL_OP_NO_COMPRESSION\fR.
As of OpenSSL 1.1.0, compression is off by default.
.IP \fB\-no_ticket\fR 4
.IX Item "-no_ticket"
Disables support for session tickets, same as setting \fBSSL_OP_NO_TICKET\fR.
.IP \fB\-serverpref\fR 4
.IX Item "-serverpref"
Use server and not client preference order when determining which cipher suite,
signature algorithm or elliptic curve to use for an incoming connection.
Equivalent to \fBSSL_OP_CIPHER_SERVER_PREFERENCE\fR. Only used by servers.
.IP \fB\-prioritize_chacha\fR 4
.IX Item "-prioritize_chacha"
Prioritize ChaCha ciphers when the client has a ChaCha20 cipher at the top of
its preference list. This usually indicates a client without AES hardware
acceleration (e.g. mobile) is in use. Equivalent to \fBSSL_OP_PRIORITIZE_CHACHA\fR.
Only used by servers. Requires \fB\-serverpref\fR.
.IP \fB\-no_resumption_on_reneg\fR 4
.IX Item "-no_resumption_on_reneg"
set SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION flag. Only used by servers.
.IP \fB\-legacyrenegotiation\fR 4
.IX Item "-legacyrenegotiation"
permits the use of unsafe legacy renegotiation. Equivalent to setting
\&\fBSSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION\fR.
.IP "\fB\-legacy_server_connect\fR, \fB\-no_legacy_server_connect\fR" 4
.IX Item "-legacy_server_connect, -no_legacy_server_connect"
permits or prohibits the use of unsafe legacy renegotiation for OpenSSL
clients only. Equivalent to setting or clearing \fBSSL_OP_LEGACY_SERVER_CONNECT\fR.
Set by default.
.IP \fB\-allow_no_dhe_kex\fR 4
.IX Item "-allow_no_dhe_kex"
In TLSv1.3 allow a non\-(ec)dhe based key exchange mode on resumption. This means
that there will be no forward secrecy for the resumed session.
.IP \fB\-strict\fR 4
.IX Item "-strict"
enables strict mode protocol handling. Equivalent to setting
\&\fBSSL_CERT_FLAG_TLS_STRICT\fR.
.IP "\fB\-anti_replay\fR, \fB\-no_anti_replay\fR" 4
.IX Item "-anti_replay, -no_anti_replay"
Switches replay protection, on or off respectively. With replay protection on,
OpenSSL will automatically detect if a session ticket has been used more than
once, TLSv1.3 has been negotiated, and early data is enabled on the server. A
full handshake is forced if a session ticket is used a second or subsequent
time. Anti-Replay is on by default unless overridden by a configuration file and
is only used by servers. Anti-replay measures are required for compliance with
the TLSv1.3 specification. Some applications may be able to mitigate the replay
risks in other ways and in such cases the built-in OpenSSL functionality is not
required. Switching off anti-replay is equivalent to \fBSSL_OP_NO_ANTI_REPLAY\fR.
.SH "SUPPORTED CONFIGURATION FILE COMMANDS"
.IX Header "SUPPORTED CONFIGURATION FILE COMMANDS"
Currently supported \fBcmd\fR names for configuration files (i.e. when the
flag \fBSSL_CONF_FLAG_FILE\fR is set) are listed below. All configuration file
\&\fBcmd\fR names are case insensitive so \fBsignaturealgorithms\fR is recognised
as well as \fBSignatureAlgorithms\fR. Unless otherwise stated the \fBvalue\fR names
are also case insensitive.
.PP
Note: the command prefix (if set) alters the recognised \fBcmd\fR values.
.IP \fBCipherString\fR 4
.IX Item "CipherString"
Sets the ciphersuite list for TLSv1.2 and below to \fBvalue\fR. This list will be
combined with any configured TLSv1.3 ciphersuites. Note: syntax
checking of \fBvalue\fR is currently not performed unless an \fBSSL\fR or \fBSSL_CTX\fR
structure is associated with \fBcctx\fR.
.IP \fBCiphersuites\fR 4
.IX Item "Ciphersuites"
Sets the available ciphersuites for TLSv1.3 to \fBvalue\fR. This is a simple colon
(":") separated list of TLSv1.3 ciphersuite names in order of preference. This
list will be combined any configured TLSv1.2 and below ciphersuites.
See \fBciphers\fR\|(1) for more information.
.IP \fBCertificate\fR 4
.IX Item "Certificate"
Attempts to use the file \fBvalue\fR as the certificate for the appropriate
context. It currently uses \fBSSL_CTX_use_certificate_chain_file()\fR if an \fBSSL_CTX\fR
structure is set or \fBSSL_use_certificate_file()\fR with filetype PEM if an \fBSSL\fR
structure is set. This option is only supported if certificate operations
are permitted.
.IP \fBPrivateKey\fR 4
.IX Item "PrivateKey"
Attempts to use the file \fBvalue\fR as the private key for the appropriate
context. This option is only supported if certificate operations
are permitted. Note: if no \fBPrivateKey\fR option is set then a private key is
not loaded unless the \fBSSL_CONF_FLAG_REQUIRE_PRIVATE\fR is set.
.IP "\fBChainCAFile\fR, \fBChainCAPath\fR, \fBVerifyCAFile\fR, \fBVerifyCAPath\fR" 4
.IX Item "ChainCAFile, ChainCAPath, VerifyCAFile, VerifyCAPath"
These options indicate a file or directory used for building certificate
chains or verifying certificate chains. These options are only supported
if certificate operations are permitted.
.IP \fBRequestCAFile\fR 4
.IX Item "RequestCAFile"
This option indicates a file containing a set of certificates in PEM form.
The subject names of the certificates are sent to the peer in the
\&\fBcertificate_authorities\fR extension for TLS 1.3 (in ClientHello or
CertificateRequest) or in a certificate request for previous versions or
TLS.
.IP \fBServerInfoFile\fR 4
.IX Item "ServerInfoFile"
Attempts to use the file \fBvalue\fR in the "serverinfo" extension using the
function SSL_CTX_use_serverinfo_file.
.IP \fBDHParameters\fR 4
.IX Item "DHParameters"
Attempts to use the file \fBvalue\fR as the set of temporary DH parameters for
the appropriate context. This option is only supported if certificate
operations are permitted.
.IP \fBRecordPadding\fR 4
.IX Item "RecordPadding"
Attempts to pad TLSv1.3 records so that they are a multiple of \fBvalue\fR in
length on send. A \fBvalue\fR of 0 or 1 turns off padding. Otherwise, the
\&\fBvalue\fR must be >1 or <=16384.
.IP \fBSignatureAlgorithms\fR 4
.IX Item "SignatureAlgorithms"
This sets the supported signature algorithms for TLSv1.2 and TLSv1.3.
For clients this
value is used directly for the supported signature algorithms extension. For
servers it is used to determine which signature algorithms to support.
.Sp
The \fBvalue\fR argument should be a colon separated list of signature algorithms
in order of decreasing preference of the form \fBalgorithm+hash\fR or
\&\fBsignature_scheme\fR. \fBalgorithm\fR
is one of \fBRSA\fR, \fBDSA\fR or \fBECDSA\fR and \fBhash\fR is a supported algorithm
OID short name such as \fBSHA1\fR, \fBSHA224\fR, \fBSHA256\fR, \fBSHA384\fR of \fBSHA512\fR.
Note: algorithm and hash names are case sensitive.
\&\fBsignature_scheme\fR is one of the signature schemes defined in TLSv1.3,
specified using the IETF name, e.g., \fBecdsa_secp256r1_sha256\fR, \fBed25519\fR,
or \fBrsa_pss_pss_sha256\fR.
.Sp
If this option is not set then all signature algorithms supported by the
OpenSSL library are permissible.
.Sp
Note: algorithms which specify a PKCS#1 v1.5 signature scheme (either by
using \fBRSA\fR as the \fBalgorithm\fR or by using one of the \fBrsa_pkcs1_*\fR
identifiers) are ignored in TLSv1.3 and will not be negotiated.
.IP \fBClientSignatureAlgorithms\fR 4
.IX Item "ClientSignatureAlgorithms"
This sets the supported signature algorithms associated with client
authentication for TLSv1.2 and TLSv1.3.
For servers the value is used in the
\&\fBsignature_algorithms\fR field of a \fBCertificateRequest\fR message.
For clients it is
used to determine which signature algorithm to use with the client certificate.
If a server does not request a certificate this option has no effect.
.Sp
The syntax of \fBvalue\fR is identical to \fBSignatureAlgorithms\fR. If not set then
the value set for \fBSignatureAlgorithms\fR will be used instead.
.IP \fBGroups\fR 4
.IX Item "Groups"
This sets the supported groups. For clients, the groups are
sent using the supported groups extension. For servers, it is used
to determine which group to use. This setting affects groups used for
signatures (in TLSv1.2 and earlier) and key exchange. The first group listed
will also be used for the \fBkey_share\fR sent by a client in a TLSv1.3
\&\fBClientHello\fR.
.Sp
The \fBvalue\fR argument is a colon separated list of groups. The group can be
either the \fBNIST\fR name (e.g. \fBP\-256\fR), some other commonly used name where
applicable (e.g. \fBX25519\fR) or an OpenSSL OID name (e.g. \fBprime256v1\fR). Group
names are case sensitive. The list should be in order of preference with the
most preferred group first.
.IP \fBCurves\fR 4
.IX Item "Curves"
This is a synonym for the "Groups" command.
.IP \fBMinProtocol\fR 4
.IX Item "MinProtocol"
This sets the minimum supported SSL, TLS or DTLS version.
.Sp
Currently supported protocol values are \fBSSLv3\fR, \fBTLSv1\fR, \fBTLSv1.1\fR,
\&\fBTLSv1.2\fR, \fBTLSv1.3\fR, \fBDTLSv1\fR and \fBDTLSv1.2\fR.
The SSL and TLS bounds apply only to TLS-based contexts, while the DTLS bounds
apply only to DTLS-based contexts.
The command can be repeated with one instance setting a TLS bound, and the
other setting a DTLS bound.
The value \fBNone\fR applies to both types of contexts and disables the limits.
.IP \fBMaxProtocol\fR 4
.IX Item "MaxProtocol"
This sets the maximum supported SSL, TLS or DTLS version.
.Sp
Currently supported protocol values are \fBSSLv3\fR, \fBTLSv1\fR, \fBTLSv1.1\fR,
\&\fBTLSv1.2\fR, \fBTLSv1.3\fR, \fBDTLSv1\fR and \fBDTLSv1.2\fR.
The SSL and TLS bounds apply only to TLS-based contexts, while the DTLS bounds
apply only to DTLS-based contexts.
The command can be repeated with one instance setting a TLS bound, and the
other setting a DTLS bound.
The value \fBNone\fR applies to both types of contexts and disables the limits.
.IP \fBProtocol\fR 4
.IX Item "Protocol"
This can be used to enable or disable certain versions of the SSL,
TLS or DTLS protocol.
.Sp
The \fBvalue\fR argument is a comma separated list of supported protocols
to enable or disable.
If a protocol is preceded by \fB\-\fR that version is disabled.
.Sp
All protocol versions are enabled by default.
You need to disable at least one protocol version for this setting have any
effect.
Only enabling some protocol versions does not disable the other protocol
versions.
.Sp
Currently supported protocol values are \fBSSLv3\fR, \fBTLSv1\fR, \fBTLSv1.1\fR,
\&\fBTLSv1.2\fR, \fBTLSv1.3\fR, \fBDTLSv1\fR and \fBDTLSv1.2\fR.
The special value \fBALL\fR refers to all supported versions.
.Sp
This can't enable protocols that are disabled using \fBMinProtocol\fR
or \fBMaxProtocol\fR, but can disable protocols that are still allowed
by them.
.Sp
The \fBProtocol\fR command is fragile and deprecated; do not use it.
Use \fBMinProtocol\fR and \fBMaxProtocol\fR instead.
If you do use \fBProtocol\fR, make sure that the resulting range of enabled
protocols has no "holes", e.g. if TLS 1.0 and TLS 1.2 are both enabled, make
sure to also leave TLS 1.1 enabled.
.IP \fBOptions\fR 4
.IX Item "Options"
The \fBvalue\fR argument is a comma separated list of various flags to set.
If a flag string is preceded \fB\-\fR it is disabled.
See the \fBSSL_CTX_set_options\fR\|(3) function for more details of
individual options.
.Sp
Each option is listed below. Where an operation is enabled by default
the \fB\-flag\fR syntax is needed to disable it.
.Sp
\&\fBSessionTicket\fR: session ticket support, enabled by default. Inverse of
\&\fBSSL_OP_NO_TICKET\fR: that is \fB\-SessionTicket\fR is the same as setting
\&\fBSSL_OP_NO_TICKET\fR.
.Sp
\&\fBCompression\fR: SSL/TLS compression support, disabled by default. Inverse
of \fBSSL_OP_NO_COMPRESSION\fR.
.Sp
\&\fBEmptyFragments\fR: use empty fragments as a countermeasure against a
SSL 3.0/TLS 1.0 protocol vulnerability affecting CBC ciphers. It
is set by default. Inverse of \fBSSL_OP_DONT_INSERT_EMPTY_FRAGMENTS\fR.
.Sp
\&\fBBugs\fR: enable various bug workarounds. Same as \fBSSL_OP_ALL\fR.
.Sp
\&\fBDHSingle\fR: enable single use DH keys, set by default. Inverse of
\&\fBSSL_OP_DH_SINGLE\fR. Only used by servers.
.Sp
\&\fBECDHSingle\fR: enable single use ECDH keys, set by default. Inverse of
\&\fBSSL_OP_ECDH_SINGLE\fR. Only used by servers.
.Sp
\&\fBServerPreference\fR: use server and not client preference order when
determining which cipher suite, signature algorithm or elliptic curve
to use for an incoming connection.  Equivalent to
\&\fBSSL_OP_CIPHER_SERVER_PREFERENCE\fR. Only used by servers.
.Sp
\&\fBPrioritizeChaCha\fR: prioritizes ChaCha ciphers when the client has a
ChaCha20 cipher at the top of its preference list. This usually indicates
a mobile client is in use. Equivalent to \fBSSL_OP_PRIORITIZE_CHACHA\fR.
Only used by servers.
.Sp
\&\fBNoResumptionOnRenegotiation\fR: set
\&\fBSSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION\fR flag. Only used by servers.
.Sp
\&\fBNoRenegotiation\fR: disables all attempts at renegotiation in TLSv1.2 and
earlier, same as setting \fBSSL_OP_NO_RENEGOTIATION\fR.
.Sp
\&\fBUnsafeLegacyRenegotiation\fR: permits the use of unsafe legacy renegotiation.
Equivalent to \fBSSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION\fR.
.Sp
\&\fBUnsafeLegacyServerConnect\fR: permits the use of unsafe legacy renegotiation
for OpenSSL clients only. Equivalent to \fBSSL_OP_LEGACY_SERVER_CONNECT\fR.
Set by default.
.Sp
\&\fBEncryptThenMac\fR: use encrypt-then-mac extension, enabled by
default. Inverse of \fBSSL_OP_NO_ENCRYPT_THEN_MAC\fR: that is,
\&\fB\-EncryptThenMac\fR is the same as setting \fBSSL_OP_NO_ENCRYPT_THEN_MAC\fR.
.Sp
\&\fBAllowNoDHEKEX\fR: In TLSv1.3 allow a non\-(ec)dhe based key exchange mode on
resumption. This means that there will be no forward secrecy for the resumed
session. Equivalent to \fBSSL_OP_ALLOW_NO_DHE_KEX\fR.
.Sp
\&\fBMiddleboxCompat\fR: If set then dummy Change Cipher Spec (CCS) messages are sent
in TLSv1.3. This has the effect of making TLSv1.3 look more like TLSv1.2 so that
middleboxes that do not understand TLSv1.3 will not drop the connection. This
option is set by default. A future version of OpenSSL may not set this by
default. Equivalent to \fBSSL_OP_ENABLE_MIDDLEBOX_COMPAT\fR.
.Sp
\&\fBAntiReplay\fR: If set then OpenSSL will automatically detect if a session ticket
has been used more than once, TLSv1.3 has been negotiated, and early data is
enabled on the server. A full handshake is forced if a session ticket is used a
second or subsequent time. This option is set by default and is only used by
servers. Anti-replay measures are required to comply with the TLSv1.3
specification. Some applications may be able to mitigate the replay risks in
other ways and in such cases the built-in OpenSSL functionality is not required.
Disabling anti-replay is equivalent to setting \fBSSL_OP_NO_ANTI_REPLAY\fR.
.IP \fBVerifyMode\fR 4
.IX Item "VerifyMode"
The \fBvalue\fR argument is a comma separated list of flags to set.
.Sp
\&\fBPeer\fR enables peer verification: for clients only.
.Sp
\&\fBRequest\fR requests but does not require a certificate from the client.
Servers only.
.Sp
\&\fBRequire\fR requests and requires a certificate from the client: an error
occurs if the client does not present a certificate. Servers only.
.Sp
\&\fBOnce\fR requests a certificate from a client only on the initial connection:
not when renegotiating. Servers only.
.Sp
\&\fBRequestPostHandshake\fR configures the connection to support requests but does
not require a certificate from the client post-handshake. A certificate will
not be requested during the initial handshake. The server application must
provide a mechanism to request a certificate post-handshake. Servers only.
TLSv1.3 only.
.Sp
\&\fBRequiresPostHandshake\fR configures the connection to support requests and
requires a certificate from the client post-handshake: an error occurs if the
client does not present a certificate. A certificate will not be requested
during the initial handshake. The server application must provide a mechanism
to request a certificate post-handshake. Servers only. TLSv1.3 only.
.IP "\fBClientCAFile\fR, \fBClientCAPath\fR" 4
.IX Item "ClientCAFile, ClientCAPath"
A file or directory of certificates in PEM format whose names are used as the
set of acceptable names for client CAs. Servers only. This option is only
supported if certificate operations are permitted.
.SH "SUPPORTED COMMAND TYPES"
.IX Header "SUPPORTED COMMAND TYPES"
The function \fBSSL_CONF_cmd_value_type()\fR currently returns one of the following
types:
.IP \fBSSL_CONF_TYPE_UNKNOWN\fR 4
.IX Item "SSL_CONF_TYPE_UNKNOWN"
The \fBcmd\fR string is unrecognised, this return value can be use to flag
syntax errors.
.IP \fBSSL_CONF_TYPE_STRING\fR 4
.IX Item "SSL_CONF_TYPE_STRING"
The value is a string without any specific structure.
.IP \fBSSL_CONF_TYPE_FILE\fR 4
.IX Item "SSL_CONF_TYPE_FILE"
The value is a filename.
.IP \fBSSL_CONF_TYPE_DIR\fR 4
.IX Item "SSL_CONF_TYPE_DIR"
The value is a directory name.
.IP \fBSSL_CONF_TYPE_NONE\fR 4
.IX Item "SSL_CONF_TYPE_NONE"
The value string is not used e.g. a command line option which doesn't take an
argument.
.SH NOTES
.IX Header "NOTES"
The order of operations is significant. This can be used to set either defaults
or values which cannot be overridden. For example if an application calls:
.PP
.Vb 2
\& SSL_CONF_cmd(ctx, "Protocol", "\-SSLv3");
\& SSL_CONF_cmd(ctx, userparam, uservalue);
.Ve
.PP
it will disable SSLv3 support by default but the user can override it. If
however the call sequence is:
.PP
.Vb 2
\& SSL_CONF_cmd(ctx, userparam, uservalue);
\& SSL_CONF_cmd(ctx, "Protocol", "\-SSLv3");
.Ve
.PP
SSLv3 is \fBalways\fR disabled and attempt to override this by the user are
ignored.
.PP
By checking the return code of \fBSSL_CONF_cmd()\fR it is possible to query if a
given \fBcmd\fR is recognised, this is useful if \fBSSL_CONF_cmd()\fR values are
mixed with additional application specific operations.
.PP
For example an application might call \fBSSL_CONF_cmd()\fR and if it returns
\&\-2 (unrecognised command) continue with processing of application specific
commands.
.PP
Applications can also use \fBSSL_CONF_cmd()\fR to process command lines though the
utility function \fBSSL_CONF_cmd_argv()\fR is normally used instead. One way
to do this is to set the prefix to an appropriate value using
\&\fBSSL_CONF_CTX_set1_prefix()\fR, pass the current argument to \fBcmd\fR and the
following argument to \fBvalue\fR (which may be NULL).
.PP
In this case if the return value is positive then it is used to skip that
number of arguments as they have been processed by \fBSSL_CONF_cmd()\fR. If \-2 is
returned then \fBcmd\fR is not recognised and application specific arguments
can be checked instead. If \-3 is returned a required argument is missing
and an error is indicated. If 0 is returned some other error occurred and
this can be reported back to the user.
.PP
The function \fBSSL_CONF_cmd_value_type()\fR can be used by applications to
check for the existence of a command or to perform additional syntax
checking or translation of the command value. For example if the return
value is \fBSSL_CONF_TYPE_FILE\fR an application could translate a relative
pathname to an absolute pathname.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CONF_cmd()\fR returns 1 if the value of \fBcmd\fR is recognised and \fBvalue\fR is
\&\fBNOT\fR used and 2 if both \fBcmd\fR and \fBvalue\fR are used. In other words it
returns the number of arguments processed. This is useful when processing
command lines.
.PP
A return value of \-2 means \fBcmd\fR is not recognised.
.PP
A return value of \-3 means \fBcmd\fR is recognised and the command requires a
value but \fBvalue\fR is NULL.
.PP
A return code of 0 indicates that both \fBcmd\fR and \fBvalue\fR are valid but an
error occurred attempting to perform the operation: for example due to an
error in the syntax of \fBvalue\fR in this case the error queue may provide
additional information.
.SH EXAMPLES
.IX Header "EXAMPLES"
Set supported signature algorithms:
.PP
.Vb 1
\& SSL_CONF_cmd(ctx, "SignatureAlgorithms", "ECDSA+SHA256:RSA+SHA256:DSA+SHA256");
.Ve
.PP
There are various ways to select the supported protocols.
.PP
This set the minimum protocol version to TLSv1, and so disables SSLv3.
This is the recommended way to disable protocols.
.PP
.Vb 1
\& SSL_CONF_cmd(ctx, "MinProtocol", "TLSv1");
.Ve
.PP
The following also disables SSLv3:
.PP
.Vb 1
\& SSL_CONF_cmd(ctx, "Protocol", "\-SSLv3");
.Ve
.PP
The following will first enable all protocols, and then disable
SSLv3.
If no protocol versions were disabled before this has the same effect as
"\-SSLv3", but if some versions were disables this will re-enable them before
disabling SSLv3.
.PP
.Vb 1
\& SSL_CONF_cmd(ctx, "Protocol", "ALL,\-SSLv3");
.Ve
.PP
Only enable TLSv1.2:
.PP
.Vb 2
\& SSL_CONF_cmd(ctx, "MinProtocol", "TLSv1.2");
\& SSL_CONF_cmd(ctx, "MaxProtocol", "TLSv1.2");
.Ve
.PP
This also only enables TLSv1.2:
.PP
.Vb 1
\& SSL_CONF_cmd(ctx, "Protocol", "\-ALL,TLSv1.2");
.Ve
.PP
Disable TLS session tickets:
.PP
.Vb 1
\& SSL_CONF_cmd(ctx, "Options", "\-SessionTicket");
.Ve
.PP
Enable compression:
.PP
.Vb 1
\& SSL_CONF_cmd(ctx, "Options", "Compression");
.Ve
.PP
Set supported curves to P\-256, P\-384:
.PP
.Vb 1
\& SSL_CONF_cmd(ctx, "Curves", "P\-256:P\-384");
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CONF_CTX_new\fR\|(3),
\&\fBSSL_CONF_CTX_set_flags\fR\|(3),
\&\fBSSL_CONF_CTX_set1_prefix\fR\|(3),
\&\fBSSL_CONF_CTX_set_ssl_ctx\fR\|(3),
\&\fBSSL_CONF_cmd_argv\fR\|(3),
\&\fBSSL_CTX_set_options\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_CONF_cmd()\fR function was added in OpenSSL 1.0.2.
.PP
The \fBSSL_OP_NO_SSL2\fR option doesn't have effect since 1.1.0, but the macro
is retained for backwards compatibility.
.PP
The \fBSSL_CONF_TYPE_NONE\fR was added in OpenSSL 1.1.0. In earlier versions of
OpenSSL passing a command which didn't take an argument would return
\&\fBSSL_CONF_TYPE_UNKNOWN\fR.
.PP
\&\fBMinProtocol\fR and \fBMaxProtocol\fR where added in OpenSSL 1.1.0.
.PP
\&\fBAllowNoDHEKEX\fR and \fBPrioritizeChaCha\fR were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2012\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
