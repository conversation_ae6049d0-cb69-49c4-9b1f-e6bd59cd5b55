.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_NAME_GET0_DER 3"
.TH X509_NAME_GET0_DER 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_NAME_get0_der \- get X509_NAME DER encoding
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_NAME_get0_der(X509_NAME *nm, const unsigned char **pder,
\&                        size_t *pderlen)
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The function \fBX509_NAME_get0_der()\fR returns an internal pointer to the
encoding of an \fBX509_NAME\fR structure in \fB*pder\fR and consisting of
\&\fB*pderlen\fR bytes. It is useful for applications that wish to examine
the encoding of an \fBX509_NAME\fR structure without copying it.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The function \fBX509_NAME_get0_der()\fR returns 1 for success and 0 if an error
occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
