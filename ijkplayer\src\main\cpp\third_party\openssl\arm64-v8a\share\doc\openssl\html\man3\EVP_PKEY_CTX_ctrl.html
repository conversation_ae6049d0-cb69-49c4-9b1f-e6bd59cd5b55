<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY_CTX_ctrl</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#RSA-parameters">RSA parameters</a></li>
      <li><a href="#DSA-parameters">DSA parameters</a></li>
      <li><a href="#DH-parameters">DH parameters</a></li>
      <li><a href="#DH-key-derivation-function-parameters">DH key derivation function parameters</a></li>
      <li><a href="#EC-parameters">EC parameters</a></li>
      <li><a href="#ECDH-parameters">ECDH parameters</a></li>
      <li><a href="#ECDH-key-derivation-function-parameters">ECDH key derivation function parameters</a></li>
      <li><a href="#Other-parameters">Other parameters</a></li>
    </ul>
  </li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY_CTX_ctrl, EVP_PKEY_CTX_ctrl_str, EVP_PKEY_CTX_ctrl_uint64, EVP_PKEY_CTX_md, EVP_PKEY_CTX_set_signature_md, EVP_PKEY_CTX_get_signature_md, EVP_PKEY_CTX_set_mac_key, EVP_PKEY_CTX_set_rsa_padding, EVP_PKEY_CTX_get_rsa_padding, EVP_PKEY_CTX_set_rsa_pss_saltlen, EVP_PKEY_CTX_get_rsa_pss_saltlen, EVP_PKEY_CTX_set_rsa_keygen_bits, EVP_PKEY_CTX_set_rsa_keygen_pubexp, EVP_PKEY_CTX_set_rsa_keygen_primes, EVP_PKEY_CTX_set_rsa_mgf1_md, EVP_PKEY_CTX_get_rsa_mgf1_md, EVP_PKEY_CTX_set_rsa_oaep_md, EVP_PKEY_CTX_get_rsa_oaep_md, EVP_PKEY_CTX_set0_rsa_oaep_label, EVP_PKEY_CTX_get0_rsa_oaep_label, EVP_PKEY_CTX_set_dsa_paramgen_bits, EVP_PKEY_CTX_set_dsa_paramgen_q_bits, EVP_PKEY_CTX_set_dsa_paramgen_md, EVP_PKEY_CTX_set_dh_paramgen_prime_len, EVP_PKEY_CTX_set_dh_paramgen_subprime_len, EVP_PKEY_CTX_set_dh_paramgen_generator, EVP_PKEY_CTX_set_dh_paramgen_type, EVP_PKEY_CTX_set_dh_rfc5114, EVP_PKEY_CTX_set_dhx_rfc5114, EVP_PKEY_CTX_set_dh_pad, EVP_PKEY_CTX_set_dh_nid, EVP_PKEY_CTX_set_dh_kdf_type, EVP_PKEY_CTX_get_dh_kdf_type, EVP_PKEY_CTX_set0_dh_kdf_oid, EVP_PKEY_CTX_get0_dh_kdf_oid, EVP_PKEY_CTX_set_dh_kdf_md, EVP_PKEY_CTX_get_dh_kdf_md, EVP_PKEY_CTX_set_dh_kdf_outlen, EVP_PKEY_CTX_get_dh_kdf_outlen, EVP_PKEY_CTX_set0_dh_kdf_ukm, EVP_PKEY_CTX_get0_dh_kdf_ukm, EVP_PKEY_CTX_set_ec_paramgen_curve_nid, EVP_PKEY_CTX_set_ec_param_enc, EVP_PKEY_CTX_set_ecdh_cofactor_mode, EVP_PKEY_CTX_get_ecdh_cofactor_mode, EVP_PKEY_CTX_set_ecdh_kdf_type, EVP_PKEY_CTX_get_ecdh_kdf_type, EVP_PKEY_CTX_set_ecdh_kdf_md, EVP_PKEY_CTX_get_ecdh_kdf_md, EVP_PKEY_CTX_set_ecdh_kdf_outlen, EVP_PKEY_CTX_get_ecdh_kdf_outlen, EVP_PKEY_CTX_set0_ecdh_kdf_ukm, EVP_PKEY_CTX_get0_ecdh_kdf_ukm, EVP_PKEY_CTX_set1_id, EVP_PKEY_CTX_get1_id, EVP_PKEY_CTX_get1_id_len - algorithm specific control operations</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

int EVP_PKEY_CTX_ctrl(EVP_PKEY_CTX *ctx, int keytype, int optype,
                      int cmd, int p1, void *p2);
int EVP_PKEY_CTX_ctrl_uint64(EVP_PKEY_CTX *ctx, int keytype, int optype,
                             int cmd, uint64_t value);
int EVP_PKEY_CTX_ctrl_str(EVP_PKEY_CTX *ctx, const char *type,
                          const char *value);

int EVP_PKEY_CTX_md(EVP_PKEY_CTX *ctx, int optype, int cmd, const char *md);

int EVP_PKEY_CTX_set_signature_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
int EVP_PKEY_CTX_get_signature_md(EVP_PKEY_CTX *ctx, const EVP_MD **pmd);

int EVP_PKEY_CTX_set_mac_key(EVP_PKEY_CTX *ctx, unsigned char *key, int len);

#include &lt;openssl/rsa.h&gt;

int EVP_PKEY_CTX_set_rsa_padding(EVP_PKEY_CTX *ctx, int pad);
int EVP_PKEY_CTX_get_rsa_padding(EVP_PKEY_CTX *ctx, int *pad);
int EVP_PKEY_CTX_set_rsa_pss_saltlen(EVP_PKEY_CTX *ctx, int len);
int EVP_PKEY_CTX_get_rsa_pss_saltlen(EVP_PKEY_CTX *ctx, int *len);
int EVP_PKEY_CTX_set_rsa_keygen_bits(EVP_PKEY_CTX *ctx, int mbits);
int EVP_PKEY_CTX_set_rsa_keygen_pubexp(EVP_PKEY_CTX *ctx, BIGNUM *pubexp);
int EVP_PKEY_CTX_set_rsa_keygen_primes(EVP_PKEY_CTX *ctx, int primes);
int EVP_PKEY_CTX_set_rsa_mgf1_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
int EVP_PKEY_CTX_get_rsa_mgf1_md(EVP_PKEY_CTX *ctx, const EVP_MD **md);
int EVP_PKEY_CTX_set_rsa_oaep_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
int EVP_PKEY_CTX_get_rsa_oaep_md(EVP_PKEY_CTX *ctx, const EVP_MD **md);
int EVP_PKEY_CTX_set0_rsa_oaep_label(EVP_PKEY_CTX *ctx, unsigned char *label, int len);
int EVP_PKEY_CTX_get0_rsa_oaep_label(EVP_PKEY_CTX *ctx, unsigned char **label);

#include &lt;openssl/dsa.h&gt;

int EVP_PKEY_CTX_set_dsa_paramgen_bits(EVP_PKEY_CTX *ctx, int nbits);
int EVP_PKEY_CTX_set_dsa_paramgen_q_bits(EVP_PKEY_CTX *ctx, int qbits);
int EVP_PKEY_CTX_set_dsa_paramgen_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);

#include &lt;openssl/dh.h&gt;

int EVP_PKEY_CTX_set_dh_paramgen_prime_len(EVP_PKEY_CTX *ctx, int len);
int EVP_PKEY_CTX_set_dh_paramgen_subprime_len(EVP_PKEY_CTX *ctx, int len);
int EVP_PKEY_CTX_set_dh_paramgen_generator(EVP_PKEY_CTX *ctx, int gen);
int EVP_PKEY_CTX_set_dh_paramgen_type(EVP_PKEY_CTX *ctx, int type);
int EVP_PKEY_CTX_set_dh_pad(EVP_PKEY_CTX *ctx, int pad);
int EVP_PKEY_CTX_set_dh_nid(EVP_PKEY_CTX *ctx, int nid);
int EVP_PKEY_CTX_set_dh_rfc5114(EVP_PKEY_CTX *ctx, int rfc5114);
int EVP_PKEY_CTX_set_dhx_rfc5114(EVP_PKEY_CTX *ctx, int rfc5114);
int EVP_PKEY_CTX_set_dh_kdf_type(EVP_PKEY_CTX *ctx, int kdf);
int EVP_PKEY_CTX_get_dh_kdf_type(EVP_PKEY_CTX *ctx);
int EVP_PKEY_CTX_set0_dh_kdf_oid(EVP_PKEY_CTX *ctx, ASN1_OBJECT *oid);
int EVP_PKEY_CTX_get0_dh_kdf_oid(EVP_PKEY_CTX *ctx, ASN1_OBJECT **oid);
int EVP_PKEY_CTX_set_dh_kdf_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
int EVP_PKEY_CTX_get_dh_kdf_md(EVP_PKEY_CTX *ctx, const EVP_MD **md);
int EVP_PKEY_CTX_set_dh_kdf_outlen(EVP_PKEY_CTX *ctx, int len);
int EVP_PKEY_CTX_get_dh_kdf_outlen(EVP_PKEY_CTX *ctx, int *len);
int EVP_PKEY_CTX_set0_dh_kdf_ukm(EVP_PKEY_CTX *ctx, unsigned char *ukm, int len);
int EVP_PKEY_CTX_get0_dh_kdf_ukm(EVP_PKEY_CTX *ctx, unsigned char **ukm);

#include &lt;openssl/ec.h&gt;

int EVP_PKEY_CTX_set_ec_paramgen_curve_nid(EVP_PKEY_CTX *ctx, int nid);
int EVP_PKEY_CTX_set_ec_param_enc(EVP_PKEY_CTX *ctx, int param_enc);
int EVP_PKEY_CTX_set_ecdh_cofactor_mode(EVP_PKEY_CTX *ctx, int cofactor_mode);
int EVP_PKEY_CTX_get_ecdh_cofactor_mode(EVP_PKEY_CTX *ctx);
int EVP_PKEY_CTX_set_ecdh_kdf_type(EVP_PKEY_CTX *ctx, int kdf);
int EVP_PKEY_CTX_get_ecdh_kdf_type(EVP_PKEY_CTX *ctx);
int EVP_PKEY_CTX_set_ecdh_kdf_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
int EVP_PKEY_CTX_get_ecdh_kdf_md(EVP_PKEY_CTX *ctx, const EVP_MD **md);
int EVP_PKEY_CTX_set_ecdh_kdf_outlen(EVP_PKEY_CTX *ctx, int len);
int EVP_PKEY_CTX_get_ecdh_kdf_outlen(EVP_PKEY_CTX *ctx, int *len);
int EVP_PKEY_CTX_set0_ecdh_kdf_ukm(EVP_PKEY_CTX *ctx, unsigned char *ukm, int len);
int EVP_PKEY_CTX_get0_ecdh_kdf_ukm(EVP_PKEY_CTX *ctx, unsigned char **ukm);

int EVP_PKEY_CTX_set1_id(EVP_PKEY_CTX *ctx, void *id, size_t id_len);
int EVP_PKEY_CTX_get1_id(EVP_PKEY_CTX *ctx, void *id);
int EVP_PKEY_CTX_get1_id_len(EVP_PKEY_CTX *ctx, size_t *id_len);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The function EVP_PKEY_CTX_ctrl() sends a control operation to the context <b>ctx</b>. The key type used must match <b>keytype</b> if it is not -1. The parameter <b>optype</b> is a mask indicating which operations the control can be applied to. The control command is indicated in <b>cmd</b> and any additional arguments in <b>p1</b> and <b>p2</b>.</p>

<p>For <b>cmd</b> = <b>EVP_PKEY_CTRL_SET_MAC_KEY</b>, <b>p1</b> is the length of the MAC key, and <b>p2</b> is MAC key. This is used by Poly1305, SipHash, HMAC and CMAC.</p>

<p>Applications will not normally call EVP_PKEY_CTX_ctrl() directly but will instead call one of the algorithm specific macros below.</p>

<p>The function EVP_PKEY_CTX_ctrl_uint64() is a wrapper that directly passes a uint64 value as <b>p2</b> to EVP_PKEY_CTX_ctrl().</p>

<p>The function EVP_PKEY_CTX_ctrl_str() allows an application to send an algorithm specific control operation to a context <b>ctx</b> in string form. This is intended to be used for options specified on the command line or in text files. The commands supported are documented in the openssl utility command line pages for the option <b>-pkeyopt</b> which is supported by the <b>pkeyutl</b>, <b>genpkey</b> and <b>req</b> commands.</p>

<p>The function EVP_PKEY_CTX_md() sends a message digest control operation to the context <b>ctx</b>. The message digest is specified by its name <b>md</b>.</p>

<p>All the remaining &quot;functions&quot; are implemented as macros.</p>

<p>The EVP_PKEY_CTX_set_signature_md() macro sets the message digest type used in a signature. It can be used in the RSA, DSA and ECDSA algorithms.</p>

<p>The EVP_PKEY_CTX_get_signature_md() macro gets the message digest type used in a signature. It can be used in the RSA, DSA and ECDSA algorithms.</p>

<p>Key generation typically involves setting up parameters to be used and generating the private and public key data. Some algorithm implementations allow private key data to be set explicitly using the EVP_PKEY_CTX_set_mac_key() macro. In this case key generation is simply the process of setting up the parameters for the key and then setting the raw key data to the value explicitly provided by that macro. Normally applications would call <a href="../man3/EVP_PKEY_new_raw_private_key.html">EVP_PKEY_new_raw_private_key(3)</a> or similar functions instead of this macro.</p>

<p>The EVP_PKEY_CTX_set_mac_key() macro can be used with any of the algorithms supported by the <a href="../man3/EVP_PKEY_new_raw_private_key.html">EVP_PKEY_new_raw_private_key(3)</a> function.</p>

<h2 id="RSA-parameters">RSA parameters</h2>

<p>The EVP_PKEY_CTX_set_rsa_padding() macro sets the RSA padding mode for <b>ctx</b>. The <b>pad</b> parameter can take the value <b>RSA_PKCS1_PADDING</b> for PKCS#1 padding, <b>RSA_SSLV23_PADDING</b> for SSLv23 padding, <b>RSA_NO_PADDING</b> for no padding, <b>RSA_PKCS1_OAEP_PADDING</b> for OAEP padding (encrypt and decrypt only), <b>RSA_X931_PADDING</b> for X9.31 padding (signature operations only) and <b>RSA_PKCS1_PSS_PADDING</b> (sign and verify only).</p>

<p>Two RSA padding modes behave differently if EVP_PKEY_CTX_set_signature_md() is used. If this macro is called for PKCS#1 padding the plaintext buffer is an actual digest value and is encapsulated in a DigestInfo structure according to PKCS#1 when signing and this structure is expected (and stripped off) when verifying. If this control is not used with RSA and PKCS#1 padding then the supplied data is used directly and not encapsulated. In the case of X9.31 padding for RSA the algorithm identifier byte is added or checked and removed if this control is called. If it is not called then the first byte of the plaintext buffer is expected to be the algorithm identifier byte.</p>

<p>The EVP_PKEY_CTX_get_rsa_padding() macro gets the RSA padding mode for <b>ctx</b>.</p>

<p>The EVP_PKEY_CTX_set_rsa_pss_saltlen() macro sets the RSA PSS salt length to <b>len</b>. As its name implies it is only supported for PSS padding. Three special values are supported: <b>RSA_PSS_SALTLEN_DIGEST</b> sets the salt length to the digest length, <b>RSA_PSS_SALTLEN_MAX</b> sets the salt length to the maximum permissible value. When verifying <b>RSA_PSS_SALTLEN_AUTO</b> causes the salt length to be automatically determined based on the <b>PSS</b> block structure. If this macro is not called maximum salt length is used when signing and auto detection when verifying is used by default.</p>

<p>The EVP_PKEY_CTX_get_rsa_pss_saltlen() macro gets the RSA PSS salt length for <b>ctx</b>. The padding mode must have been set to <b>RSA_PKCS1_PSS_PADDING</b>.</p>

<p>The EVP_PKEY_CTX_set_rsa_keygen_bits() macro sets the RSA key length for RSA key generation to <b>bits</b>. If not specified 1024 bits is used.</p>

<p>The EVP_PKEY_CTX_set_rsa_keygen_pubexp() macro sets the public exponent value for RSA key generation to <b>pubexp</b>. Currently it should be an odd integer. The <b>pubexp</b> pointer is used internally by this function so it should not be modified or freed after the call. If not specified 65537 is used.</p>

<p>The EVP_PKEY_CTX_set_rsa_keygen_primes() macro sets the number of primes for RSA key generation to <b>primes</b>. If not specified 2 is used.</p>

<p>The EVP_PKEY_CTX_set_rsa_mgf1_md() macro sets the MGF1 digest for RSA padding schemes to <b>md</b>. If not explicitly set the signing digest is used. The padding mode must have been set to <b>RSA_PKCS1_OAEP_PADDING</b> or <b>RSA_PKCS1_PSS_PADDING</b>.</p>

<p>The EVP_PKEY_CTX_get_rsa_mgf1_md() macro gets the MGF1 digest for <b>ctx</b>. If not explicitly set the signing digest is used. The padding mode must have been set to <b>RSA_PKCS1_OAEP_PADDING</b> or <b>RSA_PKCS1_PSS_PADDING</b>.</p>

<p>The EVP_PKEY_CTX_set_rsa_oaep_md() macro sets the message digest type used in RSA OAEP to <b>md</b>. The padding mode must have been set to <b>RSA_PKCS1_OAEP_PADDING</b>.</p>

<p>The EVP_PKEY_CTX_get_rsa_oaep_md() macro gets the message digest type used in RSA OAEP to <b>md</b>. The padding mode must have been set to <b>RSA_PKCS1_OAEP_PADDING</b>.</p>

<p>The EVP_PKEY_CTX_set0_rsa_oaep_label() macro sets the RSA OAEP label to <b>label</b> and its length to <b>len</b>. If <b>label</b> is NULL or <b>len</b> is 0, the label is cleared. The library takes ownership of the label so the caller should not free the original memory pointed to by <b>label</b>. The padding mode must have been set to <b>RSA_PKCS1_OAEP_PADDING</b>.</p>

<p>The EVP_PKEY_CTX_get0_rsa_oaep_label() macro gets the RSA OAEP label to <b>label</b>. The return value is the label length. The padding mode must have been set to <b>RSA_PKCS1_OAEP_PADDING</b>. The resulting pointer is owned by the library and should not be freed by the caller.</p>

<h2 id="DSA-parameters">DSA parameters</h2>

<p>The EVP_PKEY_CTX_set_dsa_paramgen_bits() macro sets the number of bits used for DSA parameter generation to <b>nbits</b>. If not specified, 1024 is used.</p>

<p>The EVP_PKEY_CTX_set_dsa_paramgen_q_bits() macro sets the number of bits in the subprime parameter <b>q</b> for DSA parameter generation to <b>qbits</b>. If not specified, 160 is used. If a digest function is specified below, this parameter is ignored and instead, the number of bits in <b>q</b> matches the size of the digest.</p>

<p>The EVP_PKEY_CTX_set_dsa_paramgen_md() macro sets the digest function used for DSA parameter generation to <b>md</b>. If not specified, one of SHA-1, SHA-224, or SHA-256 is selected to match the bit length of <b>q</b> above.</p>

<h2 id="DH-parameters">DH parameters</h2>

<p>The EVP_PKEY_CTX_set_dh_paramgen_prime_len() macro sets the length of the DH prime parameter <b>p</b> for DH parameter generation. If this macro is not called then 1024 is used. Only accepts lengths greater than or equal to 256.</p>

<p>The EVP_PKEY_CTX_set_dh_paramgen_subprime_len() macro sets the length of the DH optional subprime parameter <b>q</b> for DH parameter generation. The default is 256 if the prime is at least 2048 bits long or 160 otherwise. The DH paramgen type must have been set to x9.42.</p>

<p>The EVP_PKEY_CTX_set_dh_paramgen_generator() macro sets DH generator to <b>gen</b> for DH parameter generation. If not specified 2 is used.</p>

<p>The EVP_PKEY_CTX_set_dh_paramgen_type() macro sets the key type for DH parameter generation. Use 0 for PKCS#3 DH and 1 for X9.42 DH. The default is 0.</p>

<p>The EVP_PKEY_CTX_set_dh_pad() macro sets the DH padding mode. If <b>pad</b> is 1 the shared secret is padded with zeros up to the size of the DH prime <b>p</b>. If <b>pad</b> is zero (the default) then no padding is performed.</p>

<p>EVP_PKEY_CTX_set_dh_nid() sets the DH parameters to values corresponding to <b>nid</b> as defined in RFC7919. The <b>nid</b> parameter must be <b>NID_ffdhe2048</b>, <b>NID_ffdhe3072</b>, <b>NID_ffdhe4096</b>, <b>NID_ffdhe6144</b>, <b>NID_ffdhe8192</b> or <b>NID_undef</b> to clear the stored value. This macro can be called during parameter or key generation. The nid parameter and the rfc5114 parameter are mutually exclusive.</p>

<p>The EVP_PKEY_CTX_set_dh_rfc5114() and EVP_PKEY_CTX_set_dhx_rfc5114() macros are synonymous. They set the DH parameters to the values defined in RFC5114. The <b>rfc5114</b> parameter must be 1, 2 or 3 corresponding to RFC5114 sections 2.1, 2.2 and 2.3. or 0 to clear the stored value. This macro can be called during parameter generation. The <b>ctx</b> must have a key type of <b>EVP_PKEY_DHX</b>. The rfc5114 parameter and the nid parameter are mutually exclusive.</p>

<h2 id="DH-key-derivation-function-parameters">DH key derivation function parameters</h2>

<p>Note that all of the following functions require that the <b>ctx</b> parameter has a private key type of <b>EVP_PKEY_DHX</b>. When using key derivation, the output of EVP_PKEY_derive() is the output of the KDF instead of the DH shared secret. The KDF output is typically used as a Key Encryption Key (KEK) that in turn encrypts a Content Encryption Key (CEK).</p>

<p>The EVP_PKEY_CTX_set_dh_kdf_type() macro sets the key derivation function type to <b>kdf</b> for DH key derivation. Possible values are <b>EVP_PKEY_DH_KDF_NONE</b> and <b>EVP_PKEY_DH_KDF_X9_42</b> which uses the key derivation specified in RFC2631 (based on the keying algorithm described in X9.42). When using key derivation, the <b>kdf_oid</b>, <b>kdf_md</b> and <b>kdf_outlen</b> parameters must also be specified.</p>

<p>The EVP_PKEY_CTX_get_dh_kdf_type() macro gets the key derivation function type for <b>ctx</b> used for DH key derivation. Possible values are <b>EVP_PKEY_DH_KDF_NONE</b> and <b>EVP_PKEY_DH_KDF_X9_42</b>.</p>

<p>The EVP_PKEY_CTX_set0_dh_kdf_oid() macro sets the key derivation function object identifier to <b>oid</b> for DH key derivation. This OID should identify the algorithm to be used with the Content Encryption Key. The library takes ownership of the object identifier so the caller should not free the original memory pointed to by <b>oid</b>.</p>

<p>The EVP_PKEY_CTX_get0_dh_kdf_oid() macro gets the key derivation function oid for <b>ctx</b> used for DH key derivation. The resulting pointer is owned by the library and should not be freed by the caller.</p>

<p>The EVP_PKEY_CTX_set_dh_kdf_md() macro sets the key derivation function message digest to <b>md</b> for DH key derivation. Note that RFC2631 specifies that this digest should be SHA1 but OpenSSL tolerates other digests.</p>

<p>The EVP_PKEY_CTX_get_dh_kdf_md() macro gets the key derivation function message digest for <b>ctx</b> used for DH key derivation.</p>

<p>The EVP_PKEY_CTX_set_dh_kdf_outlen() macro sets the key derivation function output length to <b>len</b> for DH key derivation.</p>

<p>The EVP_PKEY_CTX_get_dh_kdf_outlen() macro gets the key derivation function output length for <b>ctx</b> used for DH key derivation.</p>

<p>The EVP_PKEY_CTX_set0_dh_kdf_ukm() macro sets the user key material to <b>ukm</b> and its length to <b>len</b> for DH key derivation. This parameter is optional and corresponds to the partyAInfo field in RFC2631 terms. The specification requires that it is 512 bits long but this is not enforced by OpenSSL. The library takes ownership of the user key material so the caller should not free the original memory pointed to by <b>ukm</b>.</p>

<p>The EVP_PKEY_CTX_get0_dh_kdf_ukm() macro gets the user key material for <b>ctx</b>. The return value is the user key material length. The resulting pointer is owned by the library and should not be freed by the caller.</p>

<h2 id="EC-parameters">EC parameters</h2>

<p>The EVP_PKEY_CTX_set_ec_paramgen_curve_nid() sets the EC curve for EC parameter generation to <b>nid</b>. For EC parameter generation this macro must be called or an error occurs because there is no default curve. This function can also be called to set the curve explicitly when generating an EC key.</p>

<p>The EVP_PKEY_CTX_set_ec_param_enc() macro sets the EC parameter encoding to <b>param_enc</b> when generating EC parameters or an EC key. The encoding can be <b>OPENSSL_EC_EXPLICIT_CURVE</b> for explicit parameters (the default in versions of OpenSSL before 1.1.0) or <b>OPENSSL_EC_NAMED_CURVE</b> to use named curve form. For maximum compatibility the named curve form should be used. Note: the <b>OPENSSL_EC_NAMED_CURVE</b> value was added in OpenSSL 1.1.0; previous versions should use 0 instead.</p>

<h2 id="ECDH-parameters">ECDH parameters</h2>

<p>The EVP_PKEY_CTX_set_ecdh_cofactor_mode() macro sets the cofactor mode to <b>cofactor_mode</b> for ECDH key derivation. Possible values are 1 to enable cofactor key derivation, 0 to disable it and -1 to clear the stored cofactor mode and fallback to the private key cofactor mode.</p>

<p>The EVP_PKEY_CTX_get_ecdh_cofactor_mode() macro returns the cofactor mode for <b>ctx</b> used for ECDH key derivation. Possible values are 1 when cofactor key derivation is enabled and 0 otherwise.</p>

<h2 id="ECDH-key-derivation-function-parameters">ECDH key derivation function parameters</h2>

<p>The EVP_PKEY_CTX_set_ecdh_kdf_type() macro sets the key derivation function type to <b>kdf</b> for ECDH key derivation. Possible values are <b>EVP_PKEY_ECDH_KDF_NONE</b> and <b>EVP_PKEY_ECDH_KDF_X9_63</b> which uses the key derivation specified in X9.63. When using key derivation, the <b>kdf_md</b> and <b>kdf_outlen</b> parameters must also be specified.</p>

<p>The EVP_PKEY_CTX_get_ecdh_kdf_type() macro returns the key derivation function type for <b>ctx</b> used for ECDH key derivation. Possible values are <b>EVP_PKEY_ECDH_KDF_NONE</b> and <b>EVP_PKEY_ECDH_KDF_X9_63</b>.</p>

<p>The EVP_PKEY_CTX_set_ecdh_kdf_md() macro sets the key derivation function message digest to <b>md</b> for ECDH key derivation. Note that X9.63 specifies that this digest should be SHA1 but OpenSSL tolerates other digests.</p>

<p>The EVP_PKEY_CTX_get_ecdh_kdf_md() macro gets the key derivation function message digest for <b>ctx</b> used for ECDH key derivation.</p>

<p>The EVP_PKEY_CTX_set_ecdh_kdf_outlen() macro sets the key derivation function output length to <b>len</b> for ECDH key derivation.</p>

<p>The EVP_PKEY_CTX_get_ecdh_kdf_outlen() macro gets the key derivation function output length for <b>ctx</b> used for ECDH key derivation.</p>

<p>The EVP_PKEY_CTX_set0_ecdh_kdf_ukm() macro sets the user key material to <b>ukm</b> for ECDH key derivation. This parameter is optional and corresponds to the shared info in X9.63 terms. The library takes ownership of the user key material so the caller should not free the original memory pointed to by <b>ukm</b>.</p>

<p>The EVP_PKEY_CTX_get0_ecdh_kdf_ukm() macro gets the user key material for <b>ctx</b>. The return value is the user key material length. The resulting pointer is owned by the library and should not be freed by the caller.</p>

<h2 id="Other-parameters">Other parameters</h2>

<p>The EVP_PKEY_CTX_set1_id(), EVP_PKEY_CTX_get1_id() and EVP_PKEY_CTX_get1_id_len() macros are used to manipulate the special identifier field for specific signature algorithms such as SM2. The EVP_PKEY_CTX_set1_id() sets an ID pointed by <b>id</b> with the length <b>id_len</b> to the library. The library takes a copy of the id so that the caller can safely free the original memory pointed to by <b>id</b>. The EVP_PKEY_CTX_get1_id_len() macro returns the length of the ID set via a previous call to EVP_PKEY_CTX_set1_id(). The length is usually used to allocate adequate memory for further calls to EVP_PKEY_CTX_get1_id(). The EVP_PKEY_CTX_get1_id() macro returns the previously set ID value to caller in <b>id</b>. The caller should allocate adequate memory space for the <b>id</b> before calling EVP_PKEY_CTX_get1_id().</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_PKEY_CTX_ctrl() and its macros return a positive value for success and 0 or a negative value for failure. In particular a return value of -2 indicates the operation is not supported by the public key algorithm.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_PKEY_CTX_new.html">EVP_PKEY_CTX_new(3)</a>, <a href="../man3/EVP_PKEY_encrypt.html">EVP_PKEY_encrypt(3)</a>, <a href="../man3/EVP_PKEY_decrypt.html">EVP_PKEY_decrypt(3)</a>, <a href="../man3/EVP_PKEY_sign.html">EVP_PKEY_sign(3)</a>, <a href="../man3/EVP_PKEY_verify.html">EVP_PKEY_verify(3)</a>, <a href="../man3/EVP_PKEY_verify_recover.html">EVP_PKEY_verify_recover(3)</a>, <a href="../man3/EVP_PKEY_derive.html">EVP_PKEY_derive(3)</a>, <a href="../man3/EVP_PKEY_keygen.html">EVP_PKEY_keygen(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The EVP_PKEY_CTX_set1_id(), EVP_PKEY_CTX_get1_id() and EVP_PKEY_CTX_get1_id_len() macros were added in 1.1.1, other functions were added in OpenSSL 1.0.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2006-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


