<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>dhparam</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#WARNINGS">WARNINGS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-dhparam, dhparam - DH parameter manipulation and generation</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl dhparam</b> [<b>-help</b>] [<b>-inform DER|PEM</b>] [<b>-outform DER|PEM</b>] [<b>-in</b> <i>filename</i>] [<b>-out</b> <i>filename</i>] [<b>-dsaparam</b>] [<b>-check</b>] [<b>-noout</b>] [<b>-text</b>] [<b>-C</b>] [<b>-2</b>] [<b>-5</b>] [<b>-rand file...</b>] [<b>-writerand file</b>] [<b>-engine id</b>] [<i>numbits</i>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command is used to manipulate DH parameter files.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="inform-DER-PEM"><b>-inform DER|PEM</b></dt>
<dd>

<p>This specifies the input format. The <b>DER</b> option uses an ASN1 DER encoded form compatible with the PKCS#3 DHparameter structure. The PEM form is the default format: it consists of the <b>DER</b> format base64 encoded with additional header and footer lines.</p>

</dd>
<dt id="outform-DER-PEM"><b>-outform DER|PEM</b></dt>
<dd>

<p>This specifies the output format, the options have the same meaning and default as the <b>-inform</b> option.</p>

</dd>
<dt id="in-filename"><b>-in</b> <i>filename</i></dt>
<dd>

<p>This specifies the input filename to read parameters from or standard input if this option is not specified.</p>

</dd>
<dt id="out-filename"><b>-out</b> <i>filename</i></dt>
<dd>

<p>This specifies the output filename parameters to. Standard output is used if this option is not present. The output filename should <b>not</b> be the same as the input filename.</p>

</dd>
<dt id="dsaparam"><b>-dsaparam</b></dt>
<dd>

<p>If this option is used, DSA rather than DH parameters are read or created; they are converted to DH format. Otherwise, &quot;strong&quot; primes (such that (p-1)/2 is also prime) will be used for DH parameter generation.</p>

<p>DH parameter generation with the <b>-dsaparam</b> option is much faster, and the recommended exponent length is shorter, which makes DH key exchange more efficient. Beware that with such DSA-style DH parameters, a fresh DH key should be created for each use to avoid small-subgroup attacks that may be possible otherwise.</p>

</dd>
<dt id="check"><b>-check</b></dt>
<dd>

<p>Performs numerous checks to see if the supplied parameters are valid and displays a warning if not.</p>

</dd>
<dt id="pod-2--5"><b>-2</b>, <b>-5</b></dt>
<dd>

<p>The generator to use, either 2 or 5. If present then the input file is ignored and parameters are generated instead. If not present but <b>numbits</b> is present, parameters are generated with the default generator 2.</p>

</dd>
<dt id="rand-file"><b>-rand file...</b></dt>
<dd>

<p>A file or files containing random data used to seed the random number generator. Multiple files can be specified separated by an OS-dependent character. The separator is <b>;</b> for MS-Windows, <b>,</b> for OpenVMS, and <b>:</b> for all others.</p>

</dd>
<dt id="writerand-file">[<b>-writerand file</b>]</dt>
<dd>

<p>Writes random data to the specified <i>file</i> upon exit. This can be used with a subsequent <b>-rand</b> flag.</p>

</dd>
<dt id="numbits"><i>numbits</i></dt>
<dd>

<p>This option specifies that a parameter set should be generated of size <i>numbits</i>. It must be the last option. If this option is present then the input file is ignored and parameters are generated instead. If this option is not present but a generator (<b>-2</b> or <b>-5</b>) is present, parameters are generated with a default length of 2048 bits.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>This option inhibits the output of the encoded version of the parameters.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>This option prints out the DH parameters in human readable form.</p>

</dd>
<dt id="C"><b>-C</b></dt>
<dd>

<p>This option converts the parameters into C code. The parameters can then be loaded by calling the get_dhNNNN() function.</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Specifying an engine (by its unique <b>id</b> string) will cause <b>dhparam</b> to attempt to obtain a functional reference to the specified engine, thus initialising it if needed. The engine will then be set as the default for all available algorithms.</p>

</dd>
</dl>

<h1 id="WARNINGS">WARNINGS</h1>

<p>The program <b>dhparam</b> combines the functionality of the programs <b>dh</b> and <b>gendh</b> in previous versions of OpenSSL. The <b>dh</b> and <b>gendh</b> programs are retained for now but may have different purposes in future versions of OpenSSL.</p>

<h1 id="NOTES">NOTES</h1>

<p>PEM format DH parameters use the header and footer lines:</p>

<pre><code>-----BEGIN DH PARAMETERS-----
-----END DH PARAMETERS-----</code></pre>

<p>OpenSSL currently only supports the older PKCS#3 DH, not the newer X9.42 DH.</p>

<p>This program manipulates DH parameters not keys.</p>

<h1 id="BUGS">BUGS</h1>

<p>There should be a way to generate and manipulate DH keys.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/dsaparam.html">dsaparam(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2017 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


