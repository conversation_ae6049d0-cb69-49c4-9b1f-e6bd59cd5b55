.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509V3_GET_EXT_BY_NID 3"
.TH X509V3_GET_EXT_BY_NID 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509v3_get_ext_count, X509v3_get_ext, X509v3_get_ext_by_NID,
X509v3_get_ext_by_OBJ, X509v3_get_ext_by_critical, X509v3_delete_ext,
X509v3_add_ext, X509_get_ext_count, X509_get_ext,
X509_get_ext_by_NID, X509_get_ext_by_OBJ, X509_get_ext_by_critical,
X509_delete_ext, X509_add_ext, X509_CRL_get_ext_count, X509_CRL_get_ext,
X509_CRL_get_ext_by_NID, X509_CRL_get_ext_by_OBJ, X509_CRL_get_ext_by_critical,
X509_CRL_delete_ext, X509_CRL_add_ext, X509_REVOKED_get_ext_count,
X509_REVOKED_get_ext, X509_REVOKED_get_ext_by_NID, X509_REVOKED_get_ext_by_OBJ,
X509_REVOKED_get_ext_by_critical, X509_REVOKED_delete_ext,
X509_REVOKED_add_ext \- extension stack utility functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509v3_get_ext_count(const STACK_OF(X509_EXTENSION) *x);
\& X509_EXTENSION *X509v3_get_ext(const STACK_OF(X509_EXTENSION) *x, int loc);
\&
\& int X509v3_get_ext_by_NID(const STACK_OF(X509_EXTENSION) *x,
\&                           int nid, int lastpos);
\& int X509v3_get_ext_by_OBJ(const STACK_OF(X509_EXTENSION) *x,
\&                           const ASN1_OBJECT *obj, int lastpos);
\& int X509v3_get_ext_by_critical(const STACK_OF(X509_EXTENSION) *x,
\&                                int crit, int lastpos);
\& X509_EXTENSION *X509v3_delete_ext(STACK_OF(X509_EXTENSION) *x, int loc);
\& STACK_OF(X509_EXTENSION) *X509v3_add_ext(STACK_OF(X509_EXTENSION) **x,
\&                                          X509_EXTENSION *ex, int loc);
\&
\& int X509_get_ext_count(const X509 *x);
\& X509_EXTENSION *X509_get_ext(const X509 *x, int loc);
\& int X509_get_ext_by_NID(const X509 *x, int nid, int lastpos);
\& int X509_get_ext_by_OBJ(const X509 *x, const ASN1_OBJECT *obj, int lastpos);
\& int X509_get_ext_by_critical(const X509 *x, int crit, int lastpos);
\& X509_EXTENSION *X509_delete_ext(X509 *x, int loc);
\& int X509_add_ext(X509 *x, X509_EXTENSION *ex, int loc);
\&
\& int X509_CRL_get_ext_count(const X509_CRL *x);
\& X509_EXTENSION *X509_CRL_get_ext(const X509_CRL *x, int loc);
\& int X509_CRL_get_ext_by_NID(const X509_CRL *x, int nid, int lastpos);
\& int X509_CRL_get_ext_by_OBJ(const X509_CRL *x, const ASN1_OBJECT *obj, int lastpos);
\& int X509_CRL_get_ext_by_critical(const X509_CRL *x, int crit, int lastpos);
\& X509_EXTENSION *X509_CRL_delete_ext(X509_CRL *x, int loc);
\& int X509_CRL_add_ext(X509_CRL *x, X509_EXTENSION *ex, int loc);
\&
\& int X509_REVOKED_get_ext_count(const X509_REVOKED *x);
\& X509_EXTENSION *X509_REVOKED_get_ext(const X509_REVOKED *x, int loc);
\& int X509_REVOKED_get_ext_by_NID(const X509_REVOKED *x, int nid, int lastpos);
\& int X509_REVOKED_get_ext_by_OBJ(const X509_REVOKED *x, const ASN1_OBJECT *obj,
\&                                 int lastpos);
\& int X509_REVOKED_get_ext_by_critical(const X509_REVOKED *x, int crit, int lastpos);
\& X509_EXTENSION *X509_REVOKED_delete_ext(X509_REVOKED *x, int loc);
\& int X509_REVOKED_add_ext(X509_REVOKED *x, X509_EXTENSION *ex, int loc);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509v3_get_ext_count()\fR retrieves the number of extensions in \fBx\fR.
.PP
\&\fBX509v3_get_ext()\fR retrieves extension \fBloc\fR from \fBx\fR. The index \fBloc\fR
can take any value from \fB0\fR to X509_get_ext_count(x) \- 1. The returned
extension is an internal pointer which \fBmust not\fR be freed up by the
application.
.PP
\&\fBX509v3_get_ext_by_NID()\fR and \fBX509v3_get_ext_by_OBJ()\fR look for an extension
with \fBnid\fR or \fBobj\fR from extension stack \fBx\fR. The search starts from the
extension after \fBlastpos\fR or from the beginning if <lastpos> is \fB\-1\fR. If
the extension is found its index is returned otherwise \fB\-1\fR is returned.
.PP
\&\fBX509v3_get_ext_by_critical()\fR is similar to \fBX509v3_get_ext_by_NID()\fR except it
looks for an extension of criticality \fBcrit\fR. A zero value for \fBcrit\fR
looks for a non-critical extension a nonzero value looks for a critical
extension.
.PP
\&\fBX509v3_delete_ext()\fR deletes the extension with index \fBloc\fR from \fBx\fR. The
deleted extension is returned and must be freed by the caller. If \fBloc\fR
is in invalid index value \fBNULL\fR is returned.
.PP
\&\fBX509v3_add_ext()\fR adds extension \fBex\fR to stack \fB*x\fR at position \fBloc\fR. If
\&\fBloc\fR is \fB\-1\fR the new extension is added to the end. If \fB*x\fR is \fBNULL\fR
a new stack will be allocated. The passed extension \fBex\fR is duplicated
internally so it must be freed after use.
.PP
\&\fBX509_get_ext_count()\fR, \fBX509_get_ext()\fR, \fBX509_get_ext_by_NID()\fR,
\&\fBX509_get_ext_by_OBJ()\fR, \fBX509_get_ext_by_critical()\fR, \fBX509_delete_ext()\fR
and \fBX509_add_ext()\fR operate on the extensions of certificate \fBx\fR they are
otherwise identical to the X509v3 functions.
.PP
\&\fBX509_CRL_get_ext_count()\fR, \fBX509_CRL_get_ext()\fR, \fBX509_CRL_get_ext_by_NID()\fR,
\&\fBX509_CRL_get_ext_by_OBJ()\fR, \fBX509_CRL_get_ext_by_critical()\fR,
\&\fBX509_CRL_delete_ext()\fR and \fBX509_CRL_add_ext()\fR operate on the extensions of
CRL \fBx\fR they are otherwise identical to the X509v3 functions.
.PP
\&\fBX509_REVOKED_get_ext_count()\fR, \fBX509_REVOKED_get_ext()\fR,
\&\fBX509_REVOKED_get_ext_by_NID()\fR, \fBX509_REVOKED_get_ext_by_OBJ()\fR,
\&\fBX509_REVOKED_get_ext_by_critical()\fR, \fBX509_REVOKED_delete_ext()\fR and
\&\fBX509_REVOKED_add_ext()\fR operate on the extensions of CRL entry \fBx\fR
they are otherwise identical to the X509v3 functions.
.SH NOTES
.IX Header "NOTES"
These functions are used to examine stacks of extensions directly. Many
applications will want to parse or encode and add an extension: they should
use the extension encode and decode functions instead such as
\&\fBX509_add1_ext_i2d()\fR and \fBX509_get_ext_d2i()\fR.
.PP
Extension indices start from zero, so a zero index return value is \fBnot\fR an
error. These search functions start from the extension \fBafter\fR the \fBlastpos\fR
parameter so it should initially be set to \fB\-1\fR, if it is set to zero the
initial extension will not be checked.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509v3_get_ext_count()\fR returns the extension count.
.PP
\&\fBX509v3_get_ext()\fR, \fBX509v3_delete_ext()\fR and \fBX509_delete_ext()\fR return an
\&\fBX509_EXTENSION\fR pointer or \fBNULL\fR if an error occurs.
.PP
\&\fBX509v3_get_ext_by_NID()\fR \fBX509v3_get_ext_by_OBJ()\fR and
\&\fBX509v3_get_ext_by_critical()\fR return the an extension index or \fB\-1\fR if an
error occurs.
.PP
\&\fBX509v3_add_ext()\fR returns a stack of extensions or \fBNULL\fR on error.
.PP
\&\fBX509_add_ext()\fR returns 1 on success and 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509V3_get_d2i\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
