.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS7 1"
.TH PKCS7 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-pkcs7,
pkcs7 \- PKCS#7 utility
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBpkcs7\fR
[\fB\-help\fR]
[\fB\-inform PEM|DER\fR]
[\fB\-outform PEM|DER\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-print_certs\fR]
[\fB\-text\fR]
[\fB\-noout\fR]
[\fB\-engine id\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBpkcs7\fR command processes PKCS#7 files in DER or PEM format.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format. \fBDER\fR format is DER encoded PKCS#7
v1.5 structure.\fBPEM\fR (the default) is a base64 encoded version of
the DER form with header and footer lines.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format, the options have the same meaning and default
as the \fB\-inform\fR option.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read from or standard input if this
option is not specified.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
Specifies the output filename to write to or standard output by
default.
.IP \fB\-print_certs\fR 4
.IX Item "-print_certs"
Prints out any certificates or CRLs contained in the file. They are
preceded by their subject and issuer names in one line format.
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out certificates details in full rather than just subject and
issuer names.
.IP \fB\-noout\fR 4
.IX Item "-noout"
Don't output the encoded version of the PKCS#7 structure (or certificates
is \fB\-print_certs\fR is set).
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBpkcs7\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.SH EXAMPLES
.IX Header "EXAMPLES"
Convert a PKCS#7 file from PEM to DER:
.PP
.Vb 1
\& openssl pkcs7 \-in file.pem \-outform DER \-out file.der
.Ve
.PP
Output all certificates in a file:
.PP
.Vb 1
\& openssl pkcs7 \-in file.pem \-print_certs \-out certs.pem
.Ve
.SH NOTES
.IX Header "NOTES"
The PEM PKCS#7 format uses the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN PKCS7\-\-\-\-\-
\& \-\-\-\-\-END PKCS7\-\-\-\-\-
.Ve
.PP
For compatibility with some CAs it will also accept:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN CERTIFICATE\-\-\-\-\-
\& \-\-\-\-\-END CERTIFICATE\-\-\-\-\-
.Ve
.SH RESTRICTIONS
.IX Header "RESTRICTIONS"
There is no option to print out all the fields of a PKCS#7 file.
.PP
This PKCS#7 routines only understand PKCS#7 v 1.5 as specified in RFC2315 they
cannot currently parse, for example, the new CMS as described in RFC2630.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrl2pkcs7\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
