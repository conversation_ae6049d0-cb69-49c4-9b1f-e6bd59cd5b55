.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CA.PL 1"
.TH CA.PL 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CA.pl \- friendlier interface for OpenSSL certificate programs
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBCA.pl\fR
\&\fB\-?\fR |
\&\fB\-h\fR |
\&\fB\-help\fR
.PP
\&\fBCA.pl\fR
\&\fB\-newcert\fR |
\&\fB\-newreq\fR |
\&\fB\-newreq\-nodes\fR |
\&\fB\-xsign\fR |
\&\fB\-sign\fR |
\&\fB\-signCA\fR |
\&\fB\-signcert\fR |
\&\fB\-crl\fR |
\&\fB\-newca\fR
[\fB\-extra\-cmd\fR extra\-params]
.PP
\&\fBCA.pl\fR \fB\-pkcs12\fR [\fB\-extra\-pkcs12\fR extra\-params] [\fBcertname\fR]
.PP
\&\fBCA.pl\fR \fB\-verify\fR [\fB\-extra\-verify\fR extra\-params] \fBcertfile\fR...
.PP
\&\fBCA.pl\fR \fB\-revoke\fR [\fB\-extra\-ca\fR extra\-params] \fBcertfile\fR [\fBreason\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBCA.pl\fR script is a perl script that supplies the relevant command line
arguments to the \fBopenssl\fR command for some common certificate operations.
It is intended to simplify the process of certificate creation and management
by the use of some simple options.
.SH OPTIONS
.IX Header "OPTIONS"
.IP "\fB?\fR, \fB\-h\fR, \fB\-help\fR" 4
.IX Item "?, -h, -help"
Prints a usage message.
.IP \fB\-newcert\fR 4
.IX Item "-newcert"
Creates a new self signed certificate. The private key is written to the file
"newkey.pem" and the request written to the file "newreq.pem".
This argument invokes \fBopenssl req\fR command.
.IP \fB\-newreq\fR 4
.IX Item "-newreq"
Creates a new certificate request. The private key is written to the file
"newkey.pem" and the request written to the file "newreq.pem".
Executes \fBopenssl req\fR command below the hood.
.IP \fB\-newreq\-nodes\fR 4
.IX Item "-newreq-nodes"
Is like \fB\-newreq\fR except that the private key will not be encrypted.
Uses \fBopenssl req\fR command.
.IP \fB\-newca\fR 4
.IX Item "-newca"
Creates a new CA hierarchy for use with the \fBca\fR program (or the \fB\-signcert\fR
and \fB\-xsign\fR options). The user is prompted to enter the filename of the CA
certificates (which should also contain the private key) or by hitting ENTER
details of the CA will be prompted for. The relevant files and directories
are created in a directory called "demoCA" in the current directory.
\&\fBopenssl req\fR and \fBopenssl ca\fR commands are get invoked.
.IP \fB\-pkcs12\fR 4
.IX Item "-pkcs12"
Create a PKCS#12 file containing the user certificate, private key and CA
certificate. It expects the user certificate and private key to be in the
file "newcert.pem" and the CA certificate to be in the file demoCA/cacert.pem,
it creates a file "newcert.p12". This command can thus be called after the
\&\fB\-sign\fR option. The PKCS#12 file can be imported directly into a browser.
If there is an additional argument on the command line it will be used as the
"friendly name" for the certificate (which is typically displayed in the browser
list box), otherwise the name "My Certificate" is used.
Delegates work to \fBopenssl pkcs12\fR command.
.IP "\fB\-sign\fR, \fB\-signcert\fR, \fB\-xsign\fR" 4
.IX Item "-sign, -signcert, -xsign"
Calls the \fBca\fR program to sign a certificate request. It expects the request
to be in the file "newreq.pem". The new certificate is written to the file
"newcert.pem" except in the case of the \fB\-xsign\fR option when it is written
to standard output. Leverages \fBopenssl ca\fR command.
.IP \fB\-signCA\fR 4
.IX Item "-signCA"
This option is the same as the \fB\-sign\fR option except it uses the
configuration file section \fBv3_ca\fR and so makes the signed request a
valid CA certificate. This is useful when creating intermediate CA from
a root CA.  Extra params are passed on to \fBopenssl ca\fR command.
.IP \fB\-signcert\fR 4
.IX Item "-signcert"
This option is the same as \fB\-sign\fR except it expects a self signed certificate
to be present in the file "newreq.pem".
Extra params are passed on to \fBopenssl x509\fR and \fBopenssl ca\fR commands.
.IP \fB\-crl\fR 4
.IX Item "-crl"
Generate a CRL. Executes \fBopenssl ca\fR command.
.IP "\fB\-revoke certfile [reason]\fR" 4
.IX Item "-revoke certfile [reason]"
Revoke the certificate contained in the specified \fBcertfile\fR. An optional
reason may be specified, and must be one of: \fBunspecified\fR,
\&\fBkeyCompromise\fR, \fBCACompromise\fR, \fBaffiliationChanged\fR, \fBsuperseded\fR,
\&\fBcessationOfOperation\fR, \fBcertificateHold\fR, or \fBremoveFromCRL\fR.
Leverages \fBopenssl ca\fR command.
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verifies certificates against the CA certificate for "demoCA". If no
certificates are specified on the command line it tries to verify the file
"newcert.pem".  Invokes \fBopenssl verify\fR command.
.IP "\fB\-extra\-req\fR | \fB\-extra\-ca\fR | \fB\-extra\-pkcs12\fR | \fB\-extra\-x509\fR | \fB\-extra\-verify\fR <extra\-params>" 4
.IX Item "-extra-req | -extra-ca | -extra-pkcs12 | -extra-x509 | -extra-verify <extra-params>"
The purpose of these parameters is to allow optional parameters to be supplied
to \fBopenssl\fR that this command executes. The \fB\-extra\-cmd\fR are specific to the
option being used and the \fBopenssl\fR command getting invoked. For example
when this command invokes \fBopenssl req\fR extra parameters can be passed on
with the \fB\-extra\-req\fR parameter. The
\&\fBopenssl\fR commands being invoked per option are documented below.
Users should consult \fBopenssl\fR command documentation for more information.
.SH EXAMPLES
.IX Header "EXAMPLES"
Create a CA hierarchy:
.PP
.Vb 1
\& CA.pl \-newca
.Ve
.PP
Complete certificate creation example: create a CA, create a request, sign
the request and finally create a PKCS#12 file containing it.
.PP
.Vb 4
\& CA.pl \-newca
\& CA.pl \-newreq
\& CA.pl \-sign
\& CA.pl \-pkcs12 "My Test Certificate"
.Ve
.SH "DSA CERTIFICATES"
.IX Header "DSA CERTIFICATES"
Although the \fBCA.pl\fR creates RSA CAs and requests it is still possible to
use it with DSA certificates and requests using the \fBreq\fR\|(1) command
directly. The following example shows the steps that would typically be taken.
.PP
Create some DSA parameters:
.PP
.Vb 1
\& openssl dsaparam \-out dsap.pem 1024
.Ve
.PP
Create a DSA CA certificate and private key:
.PP
.Vb 1
\& openssl req \-x509 \-newkey dsa:dsap.pem \-keyout cacert.pem \-out cacert.pem
.Ve
.PP
Create the CA directories and files:
.PP
.Vb 1
\& CA.pl \-newca
.Ve
.PP
enter cacert.pem when prompted for the CA filename.
.PP
Create a DSA certificate request and private key (a different set of parameters
can optionally be created first):
.PP
.Vb 1
\& openssl req \-out newreq.pem \-newkey dsa:dsap.pem
.Ve
.PP
Sign the request:
.PP
.Vb 1
\& CA.pl \-sign
.Ve
.SH NOTES
.IX Header "NOTES"
Most of the filenames mentioned can be modified by editing the \fBCA.pl\fR script.
.PP
If the demoCA directory already exists then the \fB\-newca\fR command will not
overwrite it and will do nothing. This can happen if a previous call using
the \fB\-newca\fR option terminated abnormally. To get the correct behaviour
delete the demoCA directory if it already exists.
.PP
Under some environments it may not be possible to run the \fBCA.pl\fR script
directly (for example Win32) and the default configuration file location may
be wrong. In this case the command:
.PP
.Vb 1
\& perl \-S CA.pl
.Ve
.PP
can be used and the \fBOPENSSL_CONF\fR environment variable changed to point to
the correct path of the configuration file.
.PP
The script is intended as a simple front end for the \fBopenssl\fR program for use
by a beginner. Its behaviour isn't always what is wanted. For more control over the
behaviour of the certificate commands call the \fBopenssl\fR command directly.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBx509\fR\|(1), \fBca\fR\|(1), \fBreq\fR\|(1), \fBpkcs12\fR\|(1),
\&\fBconfig\fR\|(5)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
