.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509 1"
.TH X509 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-x509,
x509 \- Certificate display and signing utility
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBx509\fR
[\fB\-help\fR]
[\fB\-inform DER|PEM\fR]
[\fB\-outform DER|PEM\fR]
[\fB\-keyform DER|PEM|ENGINE\fR]
[\fB\-CAform DER|PEM\fR]
[\fB\-CAkeyform DER|PEM\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-serial\fR]
[\fB\-hash\fR]
[\fB\-subject_hash\fR]
[\fB\-issuer_hash\fR]
[\fB\-ocspid\fR]
[\fB\-subject\fR]
[\fB\-issuer\fR]
[\fB\-nameopt option\fR]
[\fB\-email\fR]
[\fB\-ocsp_uri\fR]
[\fB\-startdate\fR]
[\fB\-enddate\fR]
[\fB\-purpose\fR]
[\fB\-dates\fR]
[\fB\-checkend num\fR]
[\fB\-modulus\fR]
[\fB\-pubkey\fR]
[\fB\-fingerprint\fR]
[\fB\-alias\fR]
[\fB\-noout\fR]
[\fB\-trustout\fR]
[\fB\-clrtrust\fR]
[\fB\-clrreject\fR]
[\fB\-addtrust arg\fR]
[\fB\-addreject arg\fR]
[\fB\-setalias arg\fR]
[\fB\-days arg\fR]
[\fB\-set_serial n\fR]
[\fB\-signkey arg\fR]
[\fB\-passin arg\fR]
[\fB\-x509toreq\fR]
[\fB\-req\fR]
[\fB\-CA filename\fR]
[\fB\-CAkey filename\fR]
[\fB\-CAcreateserial\fR]
[\fB\-CAserial filename\fR]
[\fB\-force_pubkey key\fR]
[\fB\-text\fR]
[\fB\-ext extensions\fR]
[\fB\-certopt option\fR]
[\fB\-C\fR]
[\fB\-\fR\f(BIdigest\fR]
[\fB\-clrext\fR]
[\fB\-extfile filename\fR]
[\fB\-extensions section\fR]
[\fB\-sigopt nm:v\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-engine id\fR]
[\fB\-preserve_dates\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBx509\fR command is a multi purpose certificate utility. It can be
used to display certificate information, convert certificates to
various forms, sign certificate requests like a "mini CA" or edit
certificate trust settings.
.PP
Since there are a large number of options they will split up into
various sections.
.SH OPTIONS
.IX Header "OPTIONS"
.SS "Input, Output, and General Purpose Options"
.IX Subsection "Input, Output, and General Purpose Options"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format normally the command will expect an X509
certificate but this can change if other options such as \fB\-req\fR are
present. The DER format is the DER encoding of the certificate and PEM
is the base64 encoding of the DER encoding with header and footer lines
added. The default format is PEM.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format, the options have the same meaning and default
as the \fB\-inform\fR option.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read a certificate from or standard input
if this option is not specified.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write to or standard output by
default.
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
The digest to use.
This affects any signing or display option that uses a message
digest, such as the \fB\-fingerprint\fR, \fB\-signkey\fR and \fB\-CA\fR options.
Any digest supported by the OpenSSL \fBdgst\fR command can be used.
If not specified then SHA1 is used with \fB\-fingerprint\fR or
the default digest for the signing algorithm is used, typically SHA256.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBx509\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.IP \fB\-preserve_dates\fR 4
.IX Item "-preserve_dates"
When signing a certificate, preserve the "notBefore" and "notAfter" dates instead
of adjusting them to current time and duration. Cannot be used with the \fB\-days\fR option.
.SS "Display Options"
.IX Subsection "Display Options"
Note: the \fB\-alias\fR and \fB\-purpose\fR options are also display options
but are described in the \fBTRUST SETTINGS\fR section.
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the certificate in text form. Full details are output including the
public key, signature algorithms, issuer and subject names, serial number
any extensions present and any trust settings.
.IP "\fB\-ext extensions\fR" 4
.IX Item "-ext extensions"
Prints out the certificate extensions in text form. Extensions are specified
with a comma separated string, e.g., "subjectAltName,subjectKeyIdentifier".
See the \fBx509v3_config\fR\|(5) manual page for the extension names.
.IP "\fB\-certopt option\fR" 4
.IX Item "-certopt option"
Customise the output format used with \fB\-text\fR. The \fBoption\fR argument
can be a single option or multiple options separated by commas. The
\&\fB\-certopt\fR switch may be also be used more than once to set multiple
options. See the \fBTEXT OPTIONS\fR section for more information.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option prevents output of the encoded version of the certificate.
.IP \fB\-pubkey\fR 4
.IX Item "-pubkey"
Outputs the certificate's SubjectPublicKeyInfo block in PEM format.
.IP \fB\-modulus\fR 4
.IX Item "-modulus"
This option prints out the value of the modulus of the public key
contained in the certificate.
.IP \fB\-serial\fR 4
.IX Item "-serial"
Outputs the certificate serial number.
.IP \fB\-subject_hash\fR 4
.IX Item "-subject_hash"
Outputs the "hash" of the certificate subject name. This is used in OpenSSL to
form an index to allow certificates in a directory to be looked up by subject
name.
.IP \fB\-issuer_hash\fR 4
.IX Item "-issuer_hash"
Outputs the "hash" of the certificate issuer name.
.IP \fB\-ocspid\fR 4
.IX Item "-ocspid"
Outputs the OCSP hash values for the subject name and public key.
.IP \fB\-hash\fR 4
.IX Item "-hash"
Synonym for "\-subject_hash" for backward compatibility reasons.
.IP \fB\-subject_hash_old\fR 4
.IX Item "-subject_hash_old"
Outputs the "hash" of the certificate subject name using the older algorithm
as used by OpenSSL before version 1.0.0.
.IP \fB\-issuer_hash_old\fR 4
.IX Item "-issuer_hash_old"
Outputs the "hash" of the certificate issuer name using the older algorithm
as used by OpenSSL before version 1.0.0.
.IP \fB\-subject\fR 4
.IX Item "-subject"
Outputs the subject name.
.IP \fB\-issuer\fR 4
.IX Item "-issuer"
Outputs the issuer name.
.IP "\fB\-nameopt option\fR" 4
.IX Item "-nameopt option"
Option which determines how the subject or issuer names are displayed. The
\&\fBoption\fR argument can be a single option or multiple options separated by
commas.  Alternatively the \fB\-nameopt\fR switch may be used more than once to
set multiple options. See the \fBNAME OPTIONS\fR section for more information.
.IP \fB\-email\fR 4
.IX Item "-email"
Outputs the email address(es) if any.
.IP \fB\-ocsp_uri\fR 4
.IX Item "-ocsp_uri"
Outputs the OCSP responder address(es) if any.
.IP \fB\-startdate\fR 4
.IX Item "-startdate"
Prints out the start date of the certificate, that is the notBefore date.
.IP \fB\-enddate\fR 4
.IX Item "-enddate"
Prints out the expiry date of the certificate, that is the notAfter date.
.IP \fB\-dates\fR 4
.IX Item "-dates"
Prints out the start and expiry dates of a certificate.
.IP "\fB\-checkend arg\fR" 4
.IX Item "-checkend arg"
Checks if the certificate expires within the next \fBarg\fR seconds and exits
nonzero if yes it will expire or zero if not.
.IP \fB\-fingerprint\fR 4
.IX Item "-fingerprint"
Calculates and outputs the digest of the DER encoded version of the entire
certificate (see digest options).
This is commonly called a "fingerprint". Because of the nature of message
digests, the fingerprint of a certificate is unique to that certificate and
two certificates with the same fingerprint can be considered to be the same.
.IP \fB\-C\fR 4
.IX Item "-C"
This outputs the certificate in the form of a C source file.
.SS "Trust Settings"
.IX Subsection "Trust Settings"
A \fBtrusted certificate\fR is an ordinary certificate which has several
additional pieces of information attached to it such as the permitted
and prohibited uses of the certificate and an "alias".
.PP
Normally when a certificate is being verified at least one certificate
must be "trusted". By default a trusted certificate must be stored
locally and must be a root CA: any certificate chain ending in this CA
is then usable for any purpose.
.PP
Trust settings currently are only used with a root CA. They allow a finer
control over the purposes the root CA can be used for. For example a CA
may be trusted for SSL client but not SSL server use.
.PP
See the description of the \fBverify\fR utility for more information on the
meaning of trust settings.
.PP
Future versions of OpenSSL will recognize trust settings on any
certificate: not just root CAs.
.IP \fB\-trustout\fR 4
.IX Item "-trustout"
This causes \fBx509\fR to output a \fBtrusted\fR certificate. An ordinary
or trusted certificate can be input but by default an ordinary
certificate is output and any trust settings are discarded. With the
\&\fB\-trustout\fR option a trusted certificate is output. A trusted
certificate is automatically output if any trust settings are modified.
.IP "\fB\-setalias arg\fR" 4
.IX Item "-setalias arg"
Sets the alias of the certificate. This will allow the certificate
to be referred to using a nickname for example "Steve's Certificate".
.IP \fB\-alias\fR 4
.IX Item "-alias"
Outputs the certificate alias, if any.
.IP \fB\-clrtrust\fR 4
.IX Item "-clrtrust"
Clears all the permitted or trusted uses of the certificate.
.IP \fB\-clrreject\fR 4
.IX Item "-clrreject"
Clears all the prohibited or rejected uses of the certificate.
.IP "\fB\-addtrust arg\fR" 4
.IX Item "-addtrust arg"
Adds a trusted certificate use.
Any object name can be used here but currently only \fBclientAuth\fR (SSL client
use), \fBserverAuth\fR (SSL server use), \fBemailProtection\fR (S/MIME email) and
\&\fBanyExtendedKeyUsage\fR are used.
As of OpenSSL 1.1.0, the last of these blocks all purposes when rejected or
enables all purposes when trusted.
Other OpenSSL applications may define additional uses.
.IP "\fB\-addreject arg\fR" 4
.IX Item "-addreject arg"
Adds a prohibited use. It accepts the same values as the \fB\-addtrust\fR
option.
.IP \fB\-purpose\fR 4
.IX Item "-purpose"
This option performs tests on the certificate extensions and outputs
the results. For a more complete description see the \fBCERTIFICATE
EXTENSIONS\fR section.
.SS "Signing Options"
.IX Subsection "Signing Options"
The \fBx509\fR utility can be used to sign certificates and requests: it
can thus behave like a "mini CA".
.IP "\fB\-signkey arg\fR" 4
.IX Item "-signkey arg"
This option causes the input file to be self signed using the supplied
private key or engine. The private key's format is specified with the
\&\fB\-keyform\fR option.
.Sp
If the input file is a certificate it sets the issuer name to the
subject name (i.e.  makes it self signed) changes the public key to the
supplied value and changes the start and end dates. The start date is
set to the current time and the end date is set to a value determined
by the \fB\-days\fR option. Any certificate extensions are retained unless
the \fB\-clrext\fR option is supplied; this includes, for example, any existing
key identifier extensions.
.Sp
If the input is a certificate request then a self signed certificate
is created using the supplied private key using the subject name in
the request.
.IP "\fB\-sigopt nm:v\fR" 4
.IX Item "-sigopt nm:v"
Pass options to the signature algorithm during sign or verify operations.
Names and values of these options are algorithm-specific.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
The key password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP \fB\-clrext\fR 4
.IX Item "-clrext"
Delete any extensions from a certificate. This option is used when a
certificate is being created from another certificate (for example with
the \fB\-signkey\fR or the \fB\-CA\fR options). Normally all extensions are
retained.
.IP "\fB\-keyform PEM|DER|ENGINE\fR" 4
.IX Item "-keyform PEM|DER|ENGINE"
Specifies the format (DER or PEM) of the private key file used in the
\&\fB\-signkey\fR option.
.IP "\fB\-days arg\fR" 4
.IX Item "-days arg"
Specifies the number of days to make a certificate valid for. The default
is 30 days. Cannot be used with the \fB\-preserve_dates\fR option.
.IP \fB\-x509toreq\fR 4
.IX Item "-x509toreq"
Converts a certificate into a certificate request. The \fB\-signkey\fR option
is used to pass the required private key.
.IP \fB\-req\fR 4
.IX Item "-req"
By default a certificate is expected on input. With this option a
certificate request is expected instead.
.IP "\fB\-set_serial n\fR" 4
.IX Item "-set_serial n"
Specifies the serial number to use. This option can be used with either
the \fB\-signkey\fR or \fB\-CA\fR options. If used in conjunction with the \fB\-CA\fR
option the serial number file (as specified by the \fB\-CAserial\fR or
\&\fB\-CAcreateserial\fR options) is not used.
.Sp
The serial number can be decimal or hex (if preceded by \fB0x\fR).
.IP "\fB\-CA filename\fR" 4
.IX Item "-CA filename"
Specifies the CA certificate to be used for signing. When this option is
present \fBx509\fR behaves like a "mini CA". The input file is signed by this
CA using this option: that is its issuer name is set to the subject name
of the CA and it is digitally signed using the CAs private key.
.Sp
This option is normally combined with the \fB\-req\fR option. Without the
\&\fB\-req\fR option the input is a certificate which must be self signed.
.IP "\fB\-CAkey filename\fR" 4
.IX Item "-CAkey filename"
Sets the CA private key to sign a certificate with. If this option is
not specified then it is assumed that the CA private key is present in
the CA certificate file.
.IP "\fB\-CAserial filename\fR" 4
.IX Item "-CAserial filename"
Sets the CA serial number file to use.
.Sp
When creating a certificate with this option, and with the \fB\-CA\fR option,
the certificate serial number is stored in the given file.
This file consists of one line containing
an even number of hex digits with the serial number used last time.
After reading this number, it is incremented and used, and the file is updated.
.Sp
The default filename consists of the CA certificate file base name with
".srl" appended. For example if the CA certificate file is called
"mycacert.pem" it expects to find a serial number file called "mycacert.srl".
.Sp
If the \fB\-CA\fR option is specified and neither <\-CAserial> or <\-CAcreateserial>
is given and the default serial number file does not exist,
a random number is generated; this is the recommended practice.
.IP \fB\-CAcreateserial\fR 4
.IX Item "-CAcreateserial"
With this option and the \fB\-CA\fR option
the CA serial number file is created if it does not exist.
A random number is generated, used for the certificate,
and saved into the serial number file determined as described above.
.IP "\fB\-extfile filename\fR" 4
.IX Item "-extfile filename"
File containing certificate extensions to use. If not specified then
no extensions are added to the certificate.
.IP "\fB\-extensions section\fR" 4
.IX Item "-extensions section"
The section to add certificate extensions from. If this option is not
specified then the extensions should either be contained in the unnamed
(default) section or the default section should contain a variable called
"extensions" which contains the section to use. See the
\&\fBx509v3_config\fR\|(5) manual page for details of the
extension section format.
.IP "\fB\-force_pubkey key\fR" 4
.IX Item "-force_pubkey key"
When a certificate is created set its public key to \fBkey\fR instead of the
key in the certificate or certificate request. This option is useful for
creating certificates where the algorithm can't normally sign requests, for
example DH.
.Sp
The format or \fBkey\fR can be specified using the \fB\-keyform\fR option.
.SS "Name Options"
.IX Subsection "Name Options"
The \fBnameopt\fR command line switch determines how the subject and issuer
names are displayed. If no \fBnameopt\fR switch is present the default "oneline"
format is used which is compatible with previous versions of OpenSSL.
Each option is described in detail below, all options can be preceded by
a \fB\-\fR to turn the option off. Only the first four will normally be used.
.IP \fBcompat\fR 4
.IX Item "compat"
Use the old format.
.IP \fBRFC2253\fR 4
.IX Item "RFC2253"
Displays names compatible with RFC2253 equivalent to \fBesc_2253\fR, \fBesc_ctrl\fR,
\&\fBesc_msb\fR, \fButf8\fR, \fBdump_nostr\fR, \fBdump_unknown\fR, \fBdump_der\fR,
\&\fBsep_comma_plus\fR, \fBdn_rev\fR and \fBsname\fR.
.IP \fBoneline\fR 4
.IX Item "oneline"
A oneline format which is more readable than RFC2253. It is equivalent to
specifying the  \fBesc_2253\fR, \fBesc_ctrl\fR, \fBesc_msb\fR, \fButf8\fR, \fBdump_nostr\fR,
\&\fBdump_der\fR, \fBuse_quote\fR, \fBsep_comma_plus_space\fR, \fBspace_eq\fR and \fBsname\fR
options.  This is the \fIdefault\fR of no name options are given explicitly.
.IP \fBmultiline\fR 4
.IX Item "multiline"
A multiline format. It is equivalent \fBesc_ctrl\fR, \fBesc_msb\fR, \fBsep_multiline\fR,
\&\fBspace_eq\fR, \fBlname\fR and \fBalign\fR.
.IP \fBesc_2253\fR 4
.IX Item "esc_2253"
Escape the "special" characters required by RFC2253 in a field. That is
\&\fB,+"<>;\fR. Additionally \fB#\fR is escaped at the beginning of a string
and a space character at the beginning or end of a string.
.IP \fBesc_2254\fR 4
.IX Item "esc_2254"
Escape the "special" characters required by RFC2254 in a field. That is
the \fBNUL\fR character as well as and \fB()*\fR.
.IP \fBesc_ctrl\fR 4
.IX Item "esc_ctrl"
Escape control characters. That is those with ASCII values less than
0x20 (space) and the delete (0x7f) character. They are escaped using the
RFC2253 \eXX notation (where XX are two hex digits representing the
character value).
.IP \fBesc_msb\fR 4
.IX Item "esc_msb"
Escape characters with the MSB set, that is with ASCII values larger than
127.
.IP \fBuse_quote\fR 4
.IX Item "use_quote"
Escapes some characters by surrounding the whole string with \fB"\fR characters,
without the option all escaping is done with the \fB\e\fR character.
.IP \fButf8\fR 4
.IX Item "utf8"
Convert all strings to UTF8 format first. This is required by RFC2253. If
you are lucky enough to have a UTF8 compatible terminal then the use
of this option (and \fBnot\fR setting \fBesc_msb\fR) may result in the correct
display of multibyte (international) characters. Is this option is not
present then multibyte characters larger than 0xff will be represented
using the format \eUXXXX for 16 bits and \eWXXXXXXXX for 32 bits.
Also if this option is off any UTF8Strings will be converted to their
character form first.
.IP \fBignore_type\fR 4
.IX Item "ignore_type"
This option does not attempt to interpret multibyte characters in any
way. That is their content octets are merely dumped as though one octet
represents each character. This is useful for diagnostic purposes but
will result in rather odd looking output.
.IP \fBshow_type\fR 4
.IX Item "show_type"
Show the type of the ASN1 character string. The type precedes the
field contents. For example "BMPSTRING: Hello World".
.IP \fBdump_der\fR 4
.IX Item "dump_der"
When this option is set any fields that need to be hexdumped will
be dumped using the DER encoding of the field. Otherwise just the
content octets will be displayed. Both options use the RFC2253
\&\fB#XXXX...\fR format.
.IP \fBdump_nostr\fR 4
.IX Item "dump_nostr"
Dump non character string types (for example OCTET STRING) if this
option is not set then non character string types will be displayed
as though each content octet represents a single character.
.IP \fBdump_all\fR 4
.IX Item "dump_all"
Dump all fields. This option when used with \fBdump_der\fR allows the
DER encoding of the structure to be unambiguously determined.
.IP \fBdump_unknown\fR 4
.IX Item "dump_unknown"
Dump any field whose OID is not recognised by OpenSSL.
.IP "\fBsep_comma_plus\fR, \fBsep_comma_plus_space\fR, \fBsep_semi_plus_space\fR, \fBsep_multiline\fR" 4
.IX Item "sep_comma_plus, sep_comma_plus_space, sep_semi_plus_space, sep_multiline"
These options determine the field separators. The first character is
between RDNs and the second between multiple AVAs (multiple AVAs are
very rare and their use is discouraged). The options ending in
"space" additionally place a space after the separator to make it
more readable. The \fBsep_multiline\fR uses a linefeed character for
the RDN separator and a spaced \fB+\fR for the AVA separator. It also
indents the fields by four characters. If no field separator is specified
then \fBsep_comma_plus_space\fR is used by default.
.IP \fBdn_rev\fR 4
.IX Item "dn_rev"
Reverse the fields of the DN. This is required by RFC2253. As a side
effect this also reverses the order of multiple AVAs but this is
permissible.
.IP "\fBnofname\fR, \fBsname\fR, \fBlname\fR, \fBoid\fR" 4
.IX Item "nofname, sname, lname, oid"
These options alter how the field name is displayed. \fBnofname\fR does
not display the field at all. \fBsname\fR uses the "short name" form
(CN for commonName for example). \fBlname\fR uses the long form.
\&\fBoid\fR represents the OID in numerical form and is useful for
diagnostic purpose.
.IP \fBalign\fR 4
.IX Item "align"
Align field values for a more readable output. Only usable with
\&\fBsep_multiline\fR.
.IP \fBspace_eq\fR 4
.IX Item "space_eq"
Places spaces round the \fB=\fR character which follows the field
name.
.SS "Text Options"
.IX Subsection "Text Options"
As well as customising the name output format, it is also possible to
customise the actual fields printed using the \fBcertopt\fR options when
the \fBtext\fR option is present. The default behaviour is to print all fields.
.IP \fBcompatible\fR 4
.IX Item "compatible"
Use the old format. This is equivalent to specifying no output options at all.
.IP \fBno_header\fR 4
.IX Item "no_header"
Don't print header information: that is the lines saying "Certificate"
and "Data".
.IP \fBno_version\fR 4
.IX Item "no_version"
Don't print out the version number.
.IP \fBno_serial\fR 4
.IX Item "no_serial"
Don't print out the serial number.
.IP \fBno_signame\fR 4
.IX Item "no_signame"
Don't print out the signature algorithm used.
.IP \fBno_validity\fR 4
.IX Item "no_validity"
Don't print the validity, that is the \fBnotBefore\fR and \fBnotAfter\fR fields.
.IP \fBno_subject\fR 4
.IX Item "no_subject"
Don't print out the subject name.
.IP \fBno_issuer\fR 4
.IX Item "no_issuer"
Don't print out the issuer name.
.IP \fBno_pubkey\fR 4
.IX Item "no_pubkey"
Don't print out the public key.
.IP \fBno_sigdump\fR 4
.IX Item "no_sigdump"
Don't give a hexadecimal dump of the certificate signature.
.IP \fBno_aux\fR 4
.IX Item "no_aux"
Don't print out certificate trust information.
.IP \fBno_extensions\fR 4
.IX Item "no_extensions"
Don't print out any X509V3 extensions.
.IP \fBext_default\fR 4
.IX Item "ext_default"
Retain default extension behaviour: attempt to print out unsupported
certificate extensions.
.IP \fBext_error\fR 4
.IX Item "ext_error"
Print an error message for unsupported certificate extensions.
.IP \fBext_parse\fR 4
.IX Item "ext_parse"
ASN1 parse unsupported extensions.
.IP \fBext_dump\fR 4
.IX Item "ext_dump"
Hex dump unsupported extensions.
.IP \fBca_default\fR 4
.IX Item "ca_default"
The value used by the \fBca\fR utility, equivalent to \fBno_issuer\fR, \fBno_pubkey\fR,
\&\fBno_header\fR, and \fBno_version\fR.
.SH EXAMPLES
.IX Header "EXAMPLES"
Note: in these examples the '\e' means the example should be all on one
line.
.PP
Display the contents of a certificate:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-text
.Ve
.PP
Display the "Subject Alternative Name" extension of a certificate:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-ext subjectAltName
.Ve
.PP
Display more extensions of a certificate:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-ext subjectAltName,nsCertType
.Ve
.PP
Display the certificate serial number:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-serial
.Ve
.PP
Display the certificate subject name:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-subject
.Ve
.PP
Display the certificate subject name in RFC2253 form:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-subject \-nameopt RFC2253
.Ve
.PP
Display the certificate subject name in oneline form on a terminal
supporting UTF8:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-subject \-nameopt oneline,\-esc_msb
.Ve
.PP
Display the certificate SHA1 fingerprint:
.PP
.Vb 1
\& openssl x509 \-sha1 \-in cert.pem \-noout \-fingerprint
.Ve
.PP
Convert a certificate from PEM to DER format:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-inform PEM \-out cert.der \-outform DER
.Ve
.PP
Convert a certificate to a certificate request:
.PP
.Vb 1
\& openssl x509 \-x509toreq \-in cert.pem \-out req.pem \-signkey key.pem
.Ve
.PP
Convert a certificate request into a self signed certificate using
extensions for a CA:
.PP
.Vb 2
\& openssl x509 \-req \-in careq.pem \-extfile openssl.cnf \-extensions v3_ca \e
\&        \-signkey key.pem \-out cacert.pem
.Ve
.PP
Sign a certificate request using the CA certificate above and add user
certificate extensions:
.PP
.Vb 2
\& openssl x509 \-req \-in req.pem \-extfile openssl.cnf \-extensions v3_usr \e
\&        \-CA cacert.pem \-CAkey key.pem \-CAcreateserial
.Ve
.PP
Set a certificate to be trusted for SSL client use and change set its alias to
"Steve's Class 1 CA"
.PP
.Vb 2
\& openssl x509 \-in cert.pem \-addtrust clientAuth \e
\&        \-setalias "Steve\*(Aqs Class 1 CA" \-out trust.pem
.Ve
.SH NOTES
.IX Header "NOTES"
The PEM format uses the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN CERTIFICATE\-\-\-\-\-
\& \-\-\-\-\-END CERTIFICATE\-\-\-\-\-
.Ve
.PP
it will also handle files containing:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN X509 CERTIFICATE\-\-\-\-\-
\& \-\-\-\-\-END X509 CERTIFICATE\-\-\-\-\-
.Ve
.PP
Trusted certificates have the lines
.PP
.Vb 2
\& \-\-\-\-\-BEGIN TRUSTED CERTIFICATE\-\-\-\-\-
\& \-\-\-\-\-END TRUSTED CERTIFICATE\-\-\-\-\-
.Ve
.PP
The conversion to UTF8 format used with the name options assumes that
T61Strings use the ISO8859\-1 character set. This is wrong but Netscape
and MSIE do this as do many certificates. So although this is incorrect
it is more likely to display the majority of certificates correctly.
.PP
The \fB\-email\fR option searches the subject name and the subject alternative
name extension. Only unique email addresses will be printed out: it will
not print the same address more than once.
.SH "CERTIFICATE EXTENSIONS"
.IX Header "CERTIFICATE EXTENSIONS"
The \fB\-purpose\fR option checks the certificate extensions and determines
what the certificate can be used for. The actual checks done are rather
complex and include various hacks and workarounds to handle broken
certificates and software.
.PP
The same code is used when verifying untrusted certificates in chains
so this section is useful if a chain is rejected by the verify code.
.PP
The basicConstraints extension CA flag is used to determine whether the
certificate can be used as a CA. If the CA flag is true then it is a CA,
if the CA flag is false then it is not a CA. \fBAll\fR CAs should have the
CA flag set to true.
.PP
If the basicConstraints extension is absent then the certificate is
considered to be a "possible CA" other extensions are checked according
to the intended use of the certificate. A warning is given in this case
because the certificate should really not be regarded as a CA: however
it is allowed to be a CA to work around some broken software.
.PP
If the certificate is a V1 certificate (and thus has no extensions) and
it is self signed it is also assumed to be a CA but a warning is again
given: this is to work around the problem of Verisign roots which are V1
self signed certificates.
.PP
If the keyUsage extension is present then additional restraints are
made on the uses of the certificate. A CA certificate \fBmust\fR have the
keyCertSign bit set if the keyUsage extension is present.
.PP
The extended key usage extension places additional restrictions on the
certificate uses. If this extension is present (whether critical or not)
the key can only be used for the purposes specified.
.PP
A complete description of each test is given below. The comments about
basicConstraints and keyUsage and V1 certificates above apply to \fBall\fR
CA certificates.
.IP "\fBSSL Client\fR" 4
.IX Item "SSL Client"
The extended key usage extension must be absent or include the "web client
authentication" OID.  keyUsage must be absent or it must have the
digitalSignature bit set. Netscape certificate type must be absent or it must
have the SSL client bit set.
.IP "\fBSSL Client CA\fR" 4
.IX Item "SSL Client CA"
The extended key usage extension must be absent or include the "web client
authentication" OID. Netscape certificate type must be absent or it must have
the SSL CA bit set: this is used as a work around if the basicConstraints
extension is absent.
.IP "\fBSSL Server\fR" 4
.IX Item "SSL Server"
The extended key usage extension must be absent or include the "web server
authentication" and/or one of the SGC OIDs.  keyUsage must be absent or it
must have the digitalSignature, the keyEncipherment set or both bits set.
Netscape certificate type must be absent or have the SSL server bit set.
.IP "\fBSSL Server CA\fR" 4
.IX Item "SSL Server CA"
The extended key usage extension must be absent or include the "web server
authentication" and/or one of the SGC OIDs.  Netscape certificate type must
be absent or the SSL CA bit must be set: this is used as a work around if the
basicConstraints extension is absent.
.IP "\fBNetscape SSL Server\fR" 4
.IX Item "Netscape SSL Server"
For Netscape SSL clients to connect to an SSL server it must have the
keyEncipherment bit set if the keyUsage extension is present. This isn't
always valid because some cipher suites use the key for digital signing.
Otherwise it is the same as a normal SSL server.
.IP "\fBCommon S/MIME Client Tests\fR" 4
.IX Item "Common S/MIME Client Tests"
The extended key usage extension must be absent or include the "email
protection" OID. Netscape certificate type must be absent or should have the
S/MIME bit set. If the S/MIME bit is not set in Netscape certificate type
then the SSL client bit is tolerated as an alternative but a warning is shown:
this is because some Verisign certificates don't set the S/MIME bit.
.IP "\fBS/MIME Signing\fR" 4
.IX Item "S/MIME Signing"
In addition to the common S/MIME client tests the digitalSignature bit or
the nonRepudiation bit must be set if the keyUsage extension is present.
.IP "\fBS/MIME Encryption\fR" 4
.IX Item "S/MIME Encryption"
In addition to the common S/MIME tests the keyEncipherment bit must be set
if the keyUsage extension is present.
.IP "\fBS/MIME CA\fR" 4
.IX Item "S/MIME CA"
The extended key usage extension must be absent or include the "email
protection" OID. Netscape certificate type must be absent or must have the
S/MIME CA bit set: this is used as a work around if the basicConstraints
extension is absent.
.IP "\fBCRL Signing\fR" 4
.IX Item "CRL Signing"
The keyUsage extension must be absent or it must have the CRL signing bit
set.
.IP "\fBCRL Signing CA\fR" 4
.IX Item "CRL Signing CA"
The normal CA tests apply. Except in this case the basicConstraints extension
must be present.
.SH BUGS
.IX Header "BUGS"
Extensions in certificates are not transferred to certificate requests and
vice versa.
.PP
It is possible to produce invalid certificates or requests by specifying the
wrong private key or using inconsistent options in some cases: these should
be checked.
.PP
There should be options to explicitly set such things as start and end
dates rather than an offset from the current time.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBreq\fR\|(1), \fBca\fR\|(1), \fBgenrsa\fR\|(1),
\&\fBgendsa\fR\|(1), \fBverify\fR\|(1),
\&\fBx509v3_config\fR\|(5)
.SH HISTORY
.IX Header "HISTORY"
The hash algorithm used in the \fB\-subject_hash\fR and \fB\-issuer_hash\fR options
before OpenSSL 1.0.0 was based on the deprecated MD5 algorithm and the encoding
of the distinguished name. In OpenSSL 1.0.0 and later it is based on a
canonical version of the DN using SHA1. This means that any directories using
the old form must have their links rebuilt using \fBc_rehash\fR or similar.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
