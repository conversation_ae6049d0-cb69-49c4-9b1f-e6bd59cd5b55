.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKEYUTL 1"
.TH PKEYUTL 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-pkeyutl,
pkeyutl \- public key algorithm utility
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBpkeyutl\fR
[\fB\-help\fR]
[\fB\-in file\fR]
[\fB\-out file\fR]
[\fB\-sigfile file\fR]
[\fB\-inkey file\fR]
[\fB\-keyform PEM|DER|ENGINE\fR]
[\fB\-passin arg\fR]
[\fB\-peerkey file\fR]
[\fB\-peerform PEM|DER|ENGINE\fR]
[\fB\-pubin\fR]
[\fB\-certin\fR]
[\fB\-rev\fR]
[\fB\-sign\fR]
[\fB\-verify\fR]
[\fB\-verifyrecover\fR]
[\fB\-encrypt\fR]
[\fB\-decrypt\fR]
[\fB\-derive\fR]
[\fB\-kdf algorithm\fR]
[\fB\-kdflen length\fR]
[\fB\-pkeyopt opt:value\fR]
[\fB\-hexdump\fR]
[\fB\-asn1parse\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-engine id\fR]
[\fB\-engine_impl\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBpkeyutl\fR command can be used to perform low-level public key operations
using any supported algorithm.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read data from or standard input
if this option is not specified.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
Specifies the output filename to write to or standard output by
default.
.IP "\fB\-sigfile file\fR" 4
.IX Item "-sigfile file"
Signature file, required for \fBverify\fR operations only
.IP "\fB\-inkey file\fR" 4
.IX Item "-inkey file"
The input key file, by default it should be a private key.
.IP "\fB\-keyform PEM|DER|ENGINE\fR" 4
.IX Item "-keyform PEM|DER|ENGINE"
The key format PEM, DER or ENGINE. Default is PEM.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
The input key password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-peerkey file\fR" 4
.IX Item "-peerkey file"
The peer key file, used by key derivation (agreement) operations.
.IP "\fB\-peerform PEM|DER|ENGINE\fR" 4
.IX Item "-peerform PEM|DER|ENGINE"
The peer key format PEM, DER or ENGINE. Default is PEM.
.IP \fB\-pubin\fR 4
.IX Item "-pubin"
The input file is a public key.
.IP \fB\-certin\fR 4
.IX Item "-certin"
The input is a certificate containing a public key.
.IP \fB\-rev\fR 4
.IX Item "-rev"
Reverse the order of the input buffer. This is useful for some libraries
(such as CryptoAPI) which represent the buffer in little endian format.
.IP \fB\-sign\fR 4
.IX Item "-sign"
Sign the input data (which must be a hash) and output the signed result. This
requires a private key.
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verify the input data (which must be a hash) against the signature file and
indicate if the verification succeeded or failed.
.IP \fB\-verifyrecover\fR 4
.IX Item "-verifyrecover"
Verify the input data (which must be a hash) and output the recovered data.
.IP \fB\-encrypt\fR 4
.IX Item "-encrypt"
Encrypt the input data using a public key.
.IP \fB\-decrypt\fR 4
.IX Item "-decrypt"
Decrypt the input data using a private key.
.IP \fB\-derive\fR 4
.IX Item "-derive"
Derive a shared secret using the peer key.
.IP "\fB\-kdf algorithm\fR" 4
.IX Item "-kdf algorithm"
Use key derivation function \fBalgorithm\fR.  The supported algorithms are
at present \fBTLS1\-PRF\fR and \fBHKDF\fR.
Note: additional parameters and the KDF output length will normally have to be
set for this to work.
See \fBEVP_PKEY_CTX_set_hkdf_md\fR\|(3) and \fBEVP_PKEY_CTX_set_tls1_prf_md\fR\|(3)
for the supported string parameters of each algorithm.
.IP "\fB\-kdflen length\fR" 4
.IX Item "-kdflen length"
Set the output length for KDF.
.IP "\fB\-pkeyopt opt:value\fR" 4
.IX Item "-pkeyopt opt:value"
Public key options specified as opt:value. See NOTES below for more details.
.IP \fB\-hexdump\fR 4
.IX Item "-hexdump"
hex dump the output data.
.IP \fB\-asn1parse\fR 4
.IX Item "-asn1parse"
Parse the ASN.1 output data, this is useful when combined with the
\&\fB\-verifyrecover\fR option when an ASN1 structure is signed.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBpkeyutl\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.IP \fB\-engine_impl\fR 4
.IX Item "-engine_impl"
When used with the \fB\-engine\fR option, it specifies to also use
engine \fBid\fR for crypto operations.
.SH NOTES
.IX Header "NOTES"
The operations and options supported vary according to the key algorithm
and its implementation. The OpenSSL operations and options are indicated below.
.PP
Unless otherwise mentioned all algorithms support the \fBdigest:alg\fR option
which specifies the digest in use for sign, verify and verifyrecover operations.
The value \fBalg\fR should represent a digest name as used in the
\&\fBEVP_get_digestbyname()\fR function for example \fBsha1\fR. This value is not used to
hash the input data. It is used (by some algorithms) for sanity-checking the
lengths of data passed in to the \fBpkeyutl\fR and for creating the structures that
make up the signature (e.g. \fBDigestInfo\fR in RSASSA PKCS#1 v1.5 signatures).
.PP
This utility does not hash the input data but rather it will use the data
directly as input to the signature algorithm. Depending on the key type,
signature type, and mode of padding, the maximum acceptable lengths of input
data differ. The signed data can't be longer than the key modulus with RSA. In
case of ECDSA and DSA the data shouldn't be longer than the field
size, otherwise it will be silently truncated to the field size. In any event
the input size must not be larger than the largest supported digest size.
.PP
In other words, if the value of digest is \fBsha1\fR the input should be the 20
bytes long binary encoding of the SHA\-1 hash function output.
.PP
The Ed25519 and Ed448 signature algorithms are not supported by this utility.
They accept non-hashed input, but this utility can only be used to sign hashed
input.
.SH "RSA ALGORITHM"
.IX Header "RSA ALGORITHM"
The RSA algorithm generally supports the encrypt, decrypt, sign,
verify and verifyrecover operations. However, some padding modes
support only a subset of these operations. The following additional
\&\fBpkeyopt\fR values are supported:
.IP \fBrsa_padding_mode:mode\fR 4
.IX Item "rsa_padding_mode:mode"
This sets the RSA padding mode. Acceptable values for \fBmode\fR are \fBpkcs1\fR for
PKCS#1 padding, \fBsslv23\fR for SSLv23 padding, \fBnone\fR for no padding, \fBoaep\fR
for \fBOAEP\fR mode, \fBx931\fR for X9.31 mode and \fBpss\fR for PSS.
.Sp
In PKCS#1 padding if the message digest is not set then the supplied data is
signed or verified directly instead of using a \fBDigestInfo\fR structure. If a
digest is set then the a \fBDigestInfo\fR structure is used and its the length
must correspond to the digest type.
.Sp
For \fBoaep\fR mode only encryption and decryption is supported.
.Sp
For \fBx931\fR if the digest type is set it is used to format the block data
otherwise the first byte is used to specify the X9.31 digest ID. Sign,
verify and verifyrecover are can be performed in this mode.
.Sp
For \fBpss\fR mode only sign and verify are supported and the digest type must be
specified.
.IP \fBrsa_pss_saltlen:len\fR 4
.IX Item "rsa_pss_saltlen:len"
For \fBpss\fR mode only this option specifies the salt length. Three special
values are supported: "digest" sets the salt length to the digest length,
"max" sets the salt length to the maximum permissible value. When verifying
"auto" causes the salt length to be automatically determined based on the
\&\fBPSS\fR block structure.
.IP \fBrsa_mgf1_md:digest\fR 4
.IX Item "rsa_mgf1_md:digest"
For PSS and OAEP padding sets the MGF1 digest. If the MGF1 digest is not
explicitly set in PSS mode then the signing digest is used.
.IP \fBrsa_oaep_md:\fR\fIdigest\fR 4
.IX Item "rsa_oaep_md:digest"
Sets the digest used for the OAEP hash function. If not explicitly set then
SHA1 is used.
.SH "RSA-PSS ALGORITHM"
.IX Header "RSA-PSS ALGORITHM"
The RSA-PSS algorithm is a restricted version of the RSA algorithm which only
supports the sign and verify operations with PSS padding. The following
additional \fBpkeyopt\fR values are supported:
.IP "\fBrsa_padding_mode:mode\fR, \fBrsa_pss_saltlen:len\fR, \fBrsa_mgf1_md:digest\fR" 4
.IX Item "rsa_padding_mode:mode, rsa_pss_saltlen:len, rsa_mgf1_md:digest"
These have the same meaning as the \fBRSA\fR algorithm with some additional
restrictions. The padding mode can only be set to \fBpss\fR which is the
default value.
.Sp
If the key has parameter restrictions than the digest, MGF1
digest and salt length are set to the values specified in the parameters.
The digest and MG cannot be changed and the salt length cannot be set to a
value less than the minimum restriction.
.SH "DSA ALGORITHM"
.IX Header "DSA ALGORITHM"
The DSA algorithm supports signing and verification operations only. Currently
there are no additional \fB\-pkeyopt\fR options other than \fBdigest\fR. The SHA1
digest is assumed by default.
.SH "DH ALGORITHM"
.IX Header "DH ALGORITHM"
The DH algorithm only supports the derivation operation and no additional
\&\fB\-pkeyopt\fR options.
.SH "EC ALGORITHM"
.IX Header "EC ALGORITHM"
The EC algorithm supports sign, verify and derive operations. The sign and
verify operations use ECDSA and derive uses ECDH. SHA1 is assumed by default for
the \fB\-pkeyopt\fR \fBdigest\fR option.
.SH "X25519 and X448 ALGORITHMS"
.IX Header "X25519 and X448 ALGORITHMS"
The X25519 and X448 algorithms support key derivation only. Currently there are
no additional options.
.SH EXAMPLES
.IX Header "EXAMPLES"
Sign some data using a private key:
.PP
.Vb 1
\& openssl pkeyutl \-sign \-in file \-inkey key.pem \-out sig
.Ve
.PP
Recover the signed data (e.g. if an RSA key is used):
.PP
.Vb 1
\& openssl pkeyutl \-verifyrecover \-in sig \-inkey key.pem
.Ve
.PP
Verify the signature (e.g. a DSA key):
.PP
.Vb 1
\& openssl pkeyutl \-verify \-in file \-sigfile sig \-inkey key.pem
.Ve
.PP
Sign data using a message digest value (this is currently only valid for RSA):
.PP
.Vb 1
\& openssl pkeyutl \-sign \-in file \-inkey key.pem \-out sig \-pkeyopt digest:sha256
.Ve
.PP
Derive a shared secret value:
.PP
.Vb 1
\& openssl pkeyutl \-derive \-inkey key.pem \-peerkey pubkey.pem \-out secret
.Ve
.PP
Hexdump 48 bytes of TLS1 PRF using digest \fBSHA256\fR and shared secret and
seed consisting of the single byte 0xFF:
.PP
.Vb 2
\& openssl pkeyutl \-kdf TLS1\-PRF \-kdflen 48 \-pkeyopt md:SHA256 \e
\&    \-pkeyopt hexsecret:ff \-pkeyopt hexseed:ff \-hexdump
.Ve
.PP
Decrypt some data using a private key with OAEP padding using SHA256:
.PP
.Vb 2
\& openssl pkeyutl \-decrypt \-in file \-inkey key.pem \-out secret \e
\&    \-pkeyopt rsa_padding_mode:oaep \-pkeyopt rsa_oaep_md:sha256
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBgenpkey\fR\|(1), \fBpkey\fR\|(1), \fBrsautl\fR\|(1)
\&\fBdgst\fR\|(1), \fBrsa\fR\|(1), \fBgenrsa\fR\|(1),
\&\fBEVP_PKEY_CTX_set_hkdf_md\fR\|(3), \fBEVP_PKEY_CTX_set_tls1_prf_md\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
