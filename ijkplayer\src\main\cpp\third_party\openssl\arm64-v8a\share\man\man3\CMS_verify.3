.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_VERIFY 3"
.TH CMS_VERIFY 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_verify, CMS_get0_signers \- verify a CMS SignedData structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& int CMS_verify(CMS_ContentInfo *cms, STACK_OF(X509) *certs, X509_STORE *store,
\&                BIO *indata, BIO *out, unsigned int flags);
\&
\& STACK_OF(X509) *CMS_get0_signers(CMS_ContentInfo *cms);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_verify()\fR is very similar to \fBPKCS7_verify\fR\|(3). It verifies a
\&\fBCMS SignedData\fR structure contained in a structure of type \fBCMS_ContentInfo\fR.
\&\fIcms\fR points to the \fBCMS_ContentInfo\fR structure to verify.
The optional \fIcerts\fR parameter refers to a set of certificates
in which to search for signing certificates.
\&\fIcms\fR may contain extra untrusted CA certificates that may be used for
chain building as well as CRLs that may be used for certificate validation.
\&\fIstore\fR may be NULL or point to
the trusted certificate store to use for chain verification.
\&\fIindata\fR refers to the signed data if the content is detached from \fIcms\fR.
Otherwise \fIindata\fR should be NULL and the signed data must be in \fIcms\fR.
The content is written to the BIO \fIout\fR unless it is NULL.
\&\fIflags\fR is an optional set of flags, which can be used to modify the operation.
.PP
\&\fBCMS_get0_signers()\fR retrieves the signing certificate(s) from \fIcms\fR, it may only
be called after a successful \fBCMS_verify()\fR operation.
.SH "VERIFY PROCESS"
.IX Header "VERIFY PROCESS"
Normally the verify process proceeds as follows.
.PP
Initially some sanity checks are performed on \fIcms\fR. The type of \fIcms\fR must
be SignedData. There must be at least one signature on the data and if
the content is detached \fIindata\fR cannot be NULL.
.PP
An attempt is made to locate all the signing certificate(s), first looking in
the \fIcerts\fR parameter (if it is not NULL) and then looking in any
certificates contained in the \fIcms\fR structure unless \fBCMS_NOINTERN\fR is set.
If any signing certificate cannot be located the operation fails.
.PP
Each signing certificate is chain verified using the \fIsmimesign\fR purpose and
using the trusted certificate store \fIstore\fR if supplied.
Any internal certificates in the message, which may have been added using
\&\fBCMS_add1_cert\fR\|(3), are used as untrusted CAs.
If CRL checking is enabled in \fIstore\fR and \fBCMS_NOCRL\fR is not set,
any internal CRLs, which may have been added using \fBCMS_add1_crl\fR\|(3),
are used in addition to attempting to look them up in \fIstore\fR.
If \fIstore\fR is not NULL and any chain verify fails an error code is returned.
.PP
Finally the signed content is read (and written to \fIout\fR unless it is NULL)
and the signature is checked.
.PP
If all signatures verify correctly then the function is successful.
.PP
Any of the following flags (ored together) can be passed in the \fIflags\fR
parameter to change the default verify behaviour.
.PP
If \fBCMS_NOINTERN\fR is set the certificates in the message itself are not
searched when locating the signing certificate(s).
This means that all the signing certificates must be in the \fIcerts\fR parameter.
.PP
If \fBCMS_NOCRL\fR is set and CRL checking is enabled in \fIstore\fR then any
CRLs in the message itself are ignored.
.PP
If the \fBCMS_TEXT\fR flag is set MIME headers for type \fBtext/plain\fR are deleted
from the content. If the content is not of type \fBtext/plain\fR then an error is
returned.
.PP
If \fBCMS_NO_SIGNER_CERT_VERIFY\fR is set the signing certificates are not
chain verified.
.PP
If \fBCMS_NO_ATTR_VERIFY\fR is set the signed attributes signature is not
verified.
.PP
If \fBCMS_NO_CONTENT_VERIFY\fR is set then the content digest is not checked.
.SH NOTES
.IX Header "NOTES"
One application of \fBCMS_NOINTERN\fR is to only accept messages signed by
a small number of certificates. The acceptable certificates would be passed
in the \fIcerts\fR parameter. In this case if the signer certificate is not one
of the certificates supplied in \fIcerts\fR then the verify will fail because the
signer cannot be found.
.PP
In some cases the standard techniques for looking up and validating
certificates are not appropriate: for example an application may wish to
lookup certificates in a database or perform customised verification. This
can be achieved by setting and verifying the signer certificates manually
using the signed data utility functions.
.PP
Care should be taken when modifying the default verify behaviour, for example
setting \fBCMS_NO_CONTENT_VERIFY\fR will totally disable all content verification
and any modified content will be considered valid. This combination is however
useful if one merely wishes to write the content to \fIout\fR and its validity
is not considered important.
.PP
Chain verification should arguably be performed using the signing time rather
than the current time. However, since the signing time is supplied by the
signer it cannot be trusted without additional evidence (such as a trusted
timestamp).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_verify()\fR returns 1 for a successful verification and 0 if an error occurred.
.PP
\&\fBCMS_get0_signers()\fR returns all signers or NULL if an error occurred.
.PP
The error can be obtained from \fBERR_get_error\fR\|(3)
.SH BUGS
.IX Header "BUGS"
The trusted certificate store is not searched for the signing certificate.
This is primarily due to the inadequacies of the current \fBX509_STORE\fR
functionality.
.PP
The lack of single pass processing means that the signed content must all
be held in memory if it is not detached.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS7_verify\fR\|(3), \fBCMS_add1_cert\fR\|(3), \fBCMS_add1_crl\fR\|(3),
\&\fBOSSL_ESS_check_signing_certs\fR\|(3),
\&\fBERR_get_error\fR\|(3), \fBCMS_sign\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
