.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_GET_VERIFY_MODE 3"
.TH SSL_CTX_GET_VERIFY_MODE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_get_verify_mode, SSL_get_verify_mode, SSL_CTX_get_verify_depth, SSL_get_verify_depth, SSL_get_verify_callback, SSL_CTX_get_verify_callback \- get currently set verification parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_get_verify_mode(const SSL_CTX *ctx);
\& int SSL_get_verify_mode(const SSL *ssl);
\& int SSL_CTX_get_verify_depth(const SSL_CTX *ctx);
\& int SSL_get_verify_depth(const SSL *ssl);
\& int (*SSL_CTX_get_verify_callback(const SSL_CTX *ctx))(int, X509_STORE_CTX *);
\& int (*SSL_get_verify_callback(const SSL *ssl))(int, X509_STORE_CTX *);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_get_verify_mode()\fR returns the verification mode currently set in
\&\fBctx\fR.
.PP
\&\fBSSL_get_verify_mode()\fR returns the verification mode currently set in
\&\fBssl\fR.
.PP
\&\fBSSL_CTX_get_verify_depth()\fR returns the verification depth limit currently set
in \fBctx\fR. If no limit has been explicitly set, \-1 is returned and the
default value will be used.
.PP
\&\fBSSL_get_verify_depth()\fR returns the verification depth limit currently set
in \fBssl\fR. If no limit has been explicitly set, \-1 is returned and the
default value will be used.
.PP
\&\fBSSL_CTX_get_verify_callback()\fR returns a function pointer to the verification
callback currently set in \fBctx\fR. If no callback was explicitly set, the
NULL pointer is returned and the default callback will be used.
.PP
\&\fBSSL_get_verify_callback()\fR returns a function pointer to the verification
callback currently set in \fBssl\fR. If no callback was explicitly set, the
NULL pointer is returned and the default callback will be used.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
See DESCRIPTION
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_set_verify\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
