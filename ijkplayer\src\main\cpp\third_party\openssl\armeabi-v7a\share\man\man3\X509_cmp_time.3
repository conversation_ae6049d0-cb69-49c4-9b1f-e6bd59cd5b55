.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_CMP_TIME 3"
.TH X509_CMP_TIME 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_cmp_time, X509_cmp_current_time, X509_time_adj, X509_time_adj_ex
\&\- X509 time functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 5
\& int X509_cmp_time(const ASN1_TIME *asn1_time, time_t *in_tm);
\& int X509_cmp_current_time(const ASN1_TIME *asn1_time);
\& ASN1_TIME *X509_time_adj(ASN1_TIME *asn1_time, long offset_sec, time_t *in_tm);
\& ASN1_TIME *X509_time_adj_ex(ASN1_TIME *asn1_time, int offset_day, long
\&                             offset_sec, time_t *in_tm);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_cmp_time()\fR compares the ASN1_TIME in \fBasn1_time\fR with the time
in <cmp_time>. \fBX509_cmp_current_time()\fR compares the ASN1_TIME in
\&\fBasn1_time\fR with the current time, expressed as time_t. \fBasn1_time\fR
must satisfy the ASN1_TIME format mandated by RFC 5280, i.e., its
format must be either YYMMDDHHMMSSZ or YYYYMMDDHHMMSSZ.
.PP
\&\fBX509_time_adj_ex()\fR sets the ASN1_TIME structure \fBasn1_time\fR to the time
\&\fBoffset_day\fR and \fBoffset_sec\fR after \fBin_tm\fR.
.PP
\&\fBX509_time_adj()\fR sets the ASN1_TIME structure \fBasn1_time\fR to the time
\&\fBoffset_sec\fR after \fBin_tm\fR. This method can only handle second
offsets up to the capacity of long, so the newer \fBX509_time_adj_ex()\fR
API should be preferred.
.PP
In both methods, if \fBasn1_time\fR is NULL, a new ASN1_TIME structure
is allocated and returned.
.PP
In all methods, if \fBin_tm\fR is NULL, the current time, expressed as
time_t, is used.
.SH BUGS
.IX Header "BUGS"
Unlike many standard comparison functions, \fBX509_cmp_time()\fR and
\&\fBX509_cmp_current_time()\fR return 0 on error.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_cmp_time()\fR and \fBX509_cmp_current_time()\fR return \-1 if \fBasn1_time\fR
is earlier than, or equal to, \fBcmp_time\fR (resp. current time), and 1
otherwise. These methods return 0 on error.
.PP
\&\fBX509_time_adj()\fR and \fBX509_time_adj_ex()\fR return a pointer to the updated
ASN1_TIME structure, and NULL on error.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
