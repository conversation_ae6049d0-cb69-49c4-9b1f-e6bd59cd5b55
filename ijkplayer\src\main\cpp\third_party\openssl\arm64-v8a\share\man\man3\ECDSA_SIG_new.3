.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ECDSA_SIG_NEW 3"
.TH ECDSA_SIG_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ECDSA_SIG_get0, ECDSA_SIG_get0_r, ECDSA_SIG_get0_s, ECDSA_SIG_set0,
ECDSA_SIG_new, ECDSA_SIG_free, ECDSA_size, ECDSA_sign, ECDSA_do_sign,
ECDSA_verify, ECDSA_do_verify, ECDSA_sign_setup, ECDSA_sign_ex,
ECDSA_do_sign_ex \- low\-level elliptic curve digital signature algorithm (ECDSA)
functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ecdsa.h>
\&
\& ECDSA_SIG *ECDSA_SIG_new(void);
\& void ECDSA_SIG_free(ECDSA_SIG *sig);
\& void ECDSA_SIG_get0(const ECDSA_SIG *sig, const BIGNUM **pr, const BIGNUM **ps);
\& const BIGNUM *ECDSA_SIG_get0_r(const ECDSA_SIG *sig);
\& const BIGNUM *ECDSA_SIG_get0_s(const ECDSA_SIG *sig);
\& int ECDSA_SIG_set0(ECDSA_SIG *sig, BIGNUM *r, BIGNUM *s);
\& int ECDSA_size(const EC_KEY *eckey);
\&
\& int ECDSA_sign(int type, const unsigned char *dgst, int dgstlen,
\&                unsigned char *sig, unsigned int *siglen, EC_KEY *eckey);
\& ECDSA_SIG *ECDSA_do_sign(const unsigned char *dgst, int dgst_len,
\&                          EC_KEY *eckey);
\&
\& int ECDSA_verify(int type, const unsigned char *dgst, int dgstlen,
\&                  const unsigned char *sig, int siglen, EC_KEY *eckey);
\& int ECDSA_do_verify(const unsigned char *dgst, int dgst_len,
\&                     const ECDSA_SIG *sig, EC_KEY* eckey);
\&
\& ECDSA_SIG *ECDSA_do_sign_ex(const unsigned char *dgst, int dgstlen,
\&                             const BIGNUM *kinv, const BIGNUM *rp,
\&                             EC_KEY *eckey);
\& int ECDSA_sign_setup(EC_KEY *eckey, BN_CTX *ctx, BIGNUM **kinv, BIGNUM **rp);
\& int ECDSA_sign_ex(int type, const unsigned char *dgst, int dgstlen,
\&                   unsigned char *sig, unsigned int *siglen,
\&                   const BIGNUM *kinv, const BIGNUM *rp, EC_KEY *eckey);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Note: these functions provide a low-level interface to ECDSA. Most
applications should use the higher level \fBEVP\fR interface such as
\&\fBEVP_DigestSignInit\fR\|(3) or \fBEVP_DigestVerifyInit\fR\|(3) instead.
.PP
\&\fBECDSA_SIG\fR is an opaque structure consisting of two BIGNUMs for the
\&\fBr\fR and \fBs\fR value of an ECDSA signature (see X9.62 or FIPS 186\-2).
.PP
\&\fBECDSA_SIG_new()\fR allocates an empty \fBECDSA_SIG\fR structure. Note: before
OpenSSL 1.1.0 the: the \fBr\fR and \fBs\fR components were initialised.
.PP
\&\fBECDSA_SIG_free()\fR frees the \fBECDSA_SIG\fR structure \fBsig\fR.
.PP
\&\fBECDSA_SIG_get0()\fR returns internal pointers the \fBr\fR and \fBs\fR values contained
in \fBsig\fR and stores them in \fB*pr\fR and \fB*ps\fR, respectively.
The pointer \fBpr\fR or \fBps\fR can be NULL, in which case the corresponding value
is not returned.
.PP
The values \fBr\fR, \fBs\fR can also be retrieved separately by the corresponding
function \fBECDSA_SIG_get0_r()\fR and \fBECDSA_SIG_get0_s()\fR, respectively.
.PP
The \fBr\fR and \fBs\fR values can be set by calling \fBECDSA_SIG_set0()\fR and passing the
new values for \fBr\fR and \fBs\fR as parameters to the function. Calling this
function transfers the memory management of the values to the ECDSA_SIG object,
and therefore the values that have been passed in should not be freed directly
after this function has been called.
.PP
See \fBi2d_ECDSA_SIG\fR\|(3) and \fBd2i_ECDSA_SIG\fR\|(3) for information about encoding
and decoding ECDSA signatures to/from DER.
.PP
\&\fBECDSA_size()\fR returns the maximum length of a DER encoded ECDSA signature
created with the private EC key \fBeckey\fR.
.PP
\&\fBECDSA_sign()\fR computes a digital signature of the \fBdgstlen\fR bytes hash value
\&\fBdgst\fR using the private EC key \fBeckey\fR. The DER encoded signatures is
stored in \fBsig\fR and its length is returned in \fBsig_len\fR. Note: \fBsig\fR must
point to ECDSA_size(eckey) bytes of memory. The parameter \fBtype\fR is currently
ignored. \fBECDSA_sign()\fR is wrapper function for \fBECDSA_sign_ex()\fR with \fBkinv\fR
and \fBrp\fR set to NULL.
.PP
\&\fBECDSA_do_sign()\fR is similar to \fBECDSA_sign()\fR except the signature is returned
as a newly allocated \fBECDSA_SIG\fR structure (or NULL on error). \fBECDSA_do_sign()\fR
is a wrapper function for \fBECDSA_do_sign_ex()\fR with \fBkinv\fR and \fBrp\fR set to
NULL.
.PP
\&\fBECDSA_verify()\fR verifies that the signature in \fBsig\fR of size \fBsiglen\fR is a
valid ECDSA signature of the hash value \fBdgst\fR of size \fBdgstlen\fR using the
public key \fBeckey\fR.  The parameter \fBtype\fR is ignored.
.PP
\&\fBECDSA_do_verify()\fR is similar to \fBECDSA_verify()\fR except the signature is
presented in the form of a pointer to an \fBECDSA_SIG\fR structure.
.PP
The remaining functions utilise the internal \fBkinv\fR and \fBr\fR values used
during signature computation. Most applications will never need to call these
and some external ECDSA ENGINE implementations may not support them at all if
either \fBkinv\fR or \fBr\fR is not \fBNULL\fR.
.PP
\&\fBECDSA_sign_setup()\fR may be used to precompute parts of the signing operation.
\&\fBeckey\fR is the private EC key and \fBctx\fR is a pointer to \fBBN_CTX\fR structure
(or NULL). The precomputed values or returned in \fBkinv\fR and \fBrp\fR and can be
used in a later call to \fBECDSA_sign_ex()\fR or \fBECDSA_do_sign_ex()\fR.
.PP
\&\fBECDSA_sign_ex()\fR computes a digital signature of the \fBdgstlen\fR bytes hash value
\&\fBdgst\fR using the private EC key \fBeckey\fR and the optional pre-computed values
\&\fBkinv\fR and \fBrp\fR. The DER encoded signature is stored in \fBsig\fR and its
length is returned in \fBsig_len\fR. Note: \fBsig\fR must point to ECDSA_size(eckey)
bytes of memory. The parameter \fBtype\fR is ignored.
.PP
\&\fBECDSA_do_sign_ex()\fR is similar to \fBECDSA_sign_ex()\fR except the signature is
returned as a newly allocated \fBECDSA_SIG\fR structure (or NULL on error).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBECDSA_SIG_new()\fR returns NULL if the allocation fails.
.PP
\&\fBECDSA_SIG_set0()\fR returns 1 on success or 0 on failure.
.PP
\&\fBECDSA_SIG_get0_r()\fR and \fBECDSA_SIG_get0_s()\fR return the corresponding value,
or NULL if it is unset.
.PP
\&\fBECDSA_size()\fR returns the maximum length signature or 0 on error.
.PP
\&\fBECDSA_sign()\fR, \fBECDSA_sign_ex()\fR and \fBECDSA_sign_setup()\fR return 1 if successful
or 0 on error.
.PP
\&\fBECDSA_do_sign()\fR and \fBECDSA_do_sign_ex()\fR return a pointer to an allocated
\&\fBECDSA_SIG\fR structure or NULL on error.
.PP
\&\fBECDSA_verify()\fR and \fBECDSA_do_verify()\fR return 1 for a valid
signature, 0 for an invalid signature and \-1 on error.
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH EXAMPLES
.IX Header "EXAMPLES"
Creating an ECDSA signature of a given SHA\-256 hash value using the
named curve prime256v1 (aka P\-256).
.PP
First step: create an EC_KEY object (note: this part is \fBnot\fR ECDSA
specific)
.PP
.Vb 3
\& int ret;
\& ECDSA_SIG *sig;
\& EC_KEY *eckey;
\&
\& eckey = EC_KEY_new_by_curve_name(NID_X9_62_prime256v1);
\& if (eckey == NULL)
\&     /* error */
\& if (EC_KEY_generate_key(eckey) == 0)
\&     /* error */
.Ve
.PP
Second step: compute the ECDSA signature of a SHA\-256 hash value
using \fBECDSA_do_sign()\fR:
.PP
.Vb 3
\& sig = ECDSA_do_sign(digest, 32, eckey);
\& if (sig == NULL)
\&     /* error */
.Ve
.PP
or using \fBECDSA_sign()\fR:
.PP
.Vb 2
\& unsigned char *buffer, *pp;
\& int buf_len;
\&
\& buf_len = ECDSA_size(eckey);
\& buffer = OPENSSL_malloc(buf_len);
\& pp = buffer;
\& if (ECDSA_sign(0, dgst, dgstlen, pp, &buf_len, eckey) == 0)
\&     /* error */
.Ve
.PP
Third step: verify the created ECDSA signature using \fBECDSA_do_verify()\fR:
.PP
.Vb 1
\& ret = ECDSA_do_verify(digest, 32, sig, eckey);
.Ve
.PP
or using \fBECDSA_verify()\fR:
.PP
.Vb 1
\& ret = ECDSA_verify(0, digest, 32, buffer, buf_len, eckey);
.Ve
.PP
and finally evaluate the return value:
.PP
.Vb 6
\& if (ret == 1)
\&     /* signature ok */
\& else if (ret == 0)
\&     /* incorrect signature */
\& else
\&     /* error */
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
ANSI X9.62, US Federal Information Processing Standard FIPS 186\-2
(Digital Signature Standard, DSS)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEC_KEY_new\fR\|(3),
\&\fBEVP_DigestSignInit\fR\|(3),
\&\fBEVP_DigestVerifyInit\fR\|(3),
\&\fBi2d_ECDSA_SIG\fR\|(3),
\&\fBd2i_ECDSA_SIG\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2004\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
