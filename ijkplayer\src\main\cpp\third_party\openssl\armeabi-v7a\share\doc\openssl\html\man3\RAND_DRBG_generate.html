<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>RAND_DRBG_generate</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>RAND_DRBG_generate, RAND_DRBG_bytes - generate random bytes using the given drbg instance</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/rand_drbg.h&gt;

int RAND_DRBG_generate(RAND_DRBG *drbg,
                       unsigned char *out, size_t outlen,
                       int prediction_resistance,
                       const unsigned char *adin, size_t adinlen);

int RAND_DRBG_bytes(RAND_DRBG *drbg,
                    unsigned char *out, size_t outlen);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>RAND_DRBG_generate() generates <b>outlen</b> random bytes using the given DRBG instance <b>drbg</b> and stores them in the buffer at <b>out</b>.</p>

<p>Before generating the output, the DRBG instance checks whether the maximum number of generate requests (<i>reseed interval</i>) or the maximum timespan (<i>reseed time interval</i>) since its last seeding have been reached. If this is the case, the DRBG reseeds automatically. Additionally, an immediate reseeding can be requested by setting the <b>prediction_resistance</b> flag to 1. See NOTES section for more details.</p>

<p>The caller can optionally provide additional data to be used for reseeding by passing a pointer <b>adin</b> to a buffer of length <b>adinlen</b>. This additional data is mixed into the internal state of the random generator but does not contribute to the entropy count. The additional data can be omitted by setting <b>adin</b> to NULL and <b>adinlen</b> to 0;</p>

<p>RAND_DRBG_bytes() generates <b>outlen</b> random bytes using the given DRBG instance <b>drbg</b> and stores them in the buffer at <b>out</b>. This function is a wrapper around the RAND_DRBG_generate() call, which collects some additional data from low entropy sources (e.g., a high resolution timer) and calls RAND_DRBG_generate(drbg, out, outlen, 0, adin, adinlen).</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>RAND_DRBG_generate() and RAND_DRBG_bytes() return 1 on success, and 0 on failure.</p>

<h1 id="NOTES">NOTES</h1>

<p>The <i>reseed interval</i> and <i>reseed time interval</i> of the <b>drbg</b> are set to reasonable default values, which in general do not have to be adjusted. If necessary, they can be changed using <a href="../man3/RAND_DRBG_set_reseed_interval.html">RAND_DRBG_set_reseed_interval(3)</a> and <a href="../man3/RAND_DRBG_set_reseed_time_interval.html">RAND_DRBG_set_reseed_time_interval(3)</a>, respectively.</p>

<p>A request for prediction resistance can only be satisfied by pulling fresh entropy from one of the approved entropy sources listed in section 5.5.2 of [NIST SP 800-90C]. Since the default DRBG implementation does not have access to such an approved entropy source, a request for prediction resistance will always fail. In other words, prediction resistance is currently not supported yet by the DRBG.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/RAND_bytes.html">RAND_bytes(3)</a>, <a href="../man3/RAND_DRBG_set_reseed_interval.html">RAND_DRBG_set_reseed_interval(3)</a>, <a href="../man3/RAND_DRBG_set_reseed_time_interval.html">RAND_DRBG_set_reseed_time_interval(3)</a>, <a href="../man7/RAND_DRBG.html">RAND_DRBG(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The RAND_DRBG functions were added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2019 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


