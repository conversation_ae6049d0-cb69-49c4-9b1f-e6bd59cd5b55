.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_EXPORT_KEYING_MATERIAL 3"
.TH SSL_EXPORT_KEYING_MATERIAL 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_export_keying_material,
SSL_export_keying_material_early
\&\- obtain keying material for application use
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_export_keying_material(SSL *s, unsigned char *out, size_t olen,
\&                                const char *label, size_t llen,
\&                                const unsigned char *context,
\&                                size_t contextlen, int use_context);
\&
\& int SSL_export_keying_material_early(SSL *s, unsigned char *out, size_t olen,
\&                                      const char *label, size_t llen,
\&                                      const unsigned char *context,
\&                                      size_t contextlen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
During the creation of a TLS or DTLS connection shared keying material is
established between the two endpoints. The functions
\&\fBSSL_export_keying_material()\fR and \fBSSL_export_keying_material_early()\fR enable an
application to use some of this keying material for its own purposes in
accordance with RFC5705 (for TLSv1.2 and below) or RFC8446 (for TLSv1.3).
.PP
\&\fBSSL_export_keying_material()\fR derives keying material using
the \fIexporter_master_secret\fR established in the handshake.
.PP
\&\fBSSL_export_keying_material_early()\fR is only usable with TLSv1.3, and derives
keying material using the \fIearly_exporter_master_secret\fR (as defined in the
TLS 1.3 RFC). For the client, the \fIearly_exporter_master_secret\fR is only
available when the client attempts to send 0\-RTT data. For the server, it is
only available when the server accepts 0\-RTT data.
.PP
An application may need to securely establish the context within which this
keying material will be used. For example this may include identifiers for the
application session, application algorithms or parameters, or the lifetime of
the context. The context value is left to the application but must be the same
on both sides of the communication.
.PP
For a given SSL connection \fBs\fR, \fBolen\fR bytes of data will be written to
\&\fBout\fR. The application specific context should be supplied in the location
pointed to by \fBcontext\fR and should be \fBcontextlen\fR bytes long. Provision of
a context is optional. If the context should be omitted entirely then
\&\fBuse_context\fR should be set to 0. Otherwise it should be any other value. If
\&\fBuse_context\fR is 0 then the values of \fBcontext\fR and \fBcontextlen\fR are ignored.
Note that in TLSv1.2 and below a zero length context is treated differently from
no context at all, and will result in different keying material being returned.
In TLSv1.3 a zero length context is that same as no context at all and will
result in the same keying material being returned.
.PP
An application specific label should be provided in the location pointed to by
\&\fBlabel\fR and should be \fBllen\fR bytes long. Typically this will be a value from
the IANA Exporter Label Registry
(<https://www.iana.org/assignments/tls\-parameters/tls\-parameters.xhtml#exporter\-labels>).
Alternatively labels beginning with "EXPERIMENTAL" are permitted by the standard
to be used without registration. TLSv1.3 imposes a maximum label length of
249 bytes.
.PP
Note that this function is only defined for TLSv1.0 and above, and DTLSv1.0 and
above. Attempting to use it in SSLv3 will result in an error.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_export_keying_material()\fR returns 0 or \-1 on failure or 1 on success.
.PP
\&\fBSSL_export_keying_material_early()\fR returns 0 on failure or 1 on success.
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_export_keying_material_early()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
