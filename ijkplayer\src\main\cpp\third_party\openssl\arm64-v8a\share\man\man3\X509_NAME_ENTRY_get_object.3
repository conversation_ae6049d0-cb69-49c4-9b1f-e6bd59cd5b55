.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_NAME_ENTRY_GET_OBJECT 3"
.TH X509_NAME_ENTRY_GET_OBJECT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_NAME_ENTRY_get_object, X509_NAME_ENTRY_get_data,
X509_NAME_ENTRY_set_object, X509_NAME_ENTRY_set_data,
X509_NAME_ENTRY_create_by_txt, X509_NAME_ENTRY_create_by_NID,
X509_NAME_ENTRY_create_by_OBJ \- X509_NAME_ENTRY utility functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& ASN1_OBJECT *X509_NAME_ENTRY_get_object(const X509_NAME_ENTRY *ne);
\& ASN1_STRING *X509_NAME_ENTRY_get_data(const X509_NAME_ENTRY *ne);
\&
\& int X509_NAME_ENTRY_set_object(X509_NAME_ENTRY *ne, const ASN1_OBJECT *obj);
\& int X509_NAME_ENTRY_set_data(X509_NAME_ENTRY *ne, int type,
\&                              const unsigned char *bytes, int len);
\&
\& X509_NAME_ENTRY *X509_NAME_ENTRY_create_by_txt(X509_NAME_ENTRY **ne, const char *field,
\&                                                int type, const unsigned char *bytes,
\&                                                int len);
\& X509_NAME_ENTRY *X509_NAME_ENTRY_create_by_NID(X509_NAME_ENTRY **ne, int nid,
\&                                                int type, const unsigned char *bytes,
\&                                                int len);
\& X509_NAME_ENTRY *X509_NAME_ENTRY_create_by_OBJ(X509_NAME_ENTRY **ne,
\&                                                const ASN1_OBJECT *obj, int type,
\&                                                const unsigned char *bytes, int len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_NAME_ENTRY_get_object()\fR retrieves the field name of \fBne\fR in
and \fBASN1_OBJECT\fR structure.
.PP
\&\fBX509_NAME_ENTRY_get_data()\fR retrieves the field value of \fBne\fR in
and \fBASN1_STRING\fR structure.
.PP
\&\fBX509_NAME_ENTRY_set_object()\fR sets the field name of \fBne\fR to \fBobj\fR.
.PP
\&\fBX509_NAME_ENTRY_set_data()\fR sets the field value of \fBne\fR to string type
\&\fBtype\fR and value determined by \fBbytes\fR and \fBlen\fR.
.PP
\&\fBX509_NAME_ENTRY_create_by_txt()\fR, \fBX509_NAME_ENTRY_create_by_NID()\fR
and \fBX509_NAME_ENTRY_create_by_OBJ()\fR create and return an
\&\fBX509_NAME_ENTRY\fR structure.
.SH NOTES
.IX Header "NOTES"
\&\fBX509_NAME_ENTRY_get_object()\fR and \fBX509_NAME_ENTRY_get_data()\fR can be
used to examine an \fBX509_NAME_ENTRY\fR function as returned by
\&\fBX509_NAME_get_entry()\fR for example.
.PP
\&\fBX509_NAME_ENTRY_create_by_txt()\fR, \fBX509_NAME_ENTRY_create_by_OBJ()\fR,
\&\fBX509_NAME_ENTRY_create_by_NID()\fR and \fBX509_NAME_ENTRY_set_data()\fR
are seldom used in practice because \fBX509_NAME_ENTRY\fR structures
are almost always part of \fBX509_NAME\fR structures and the
corresponding \fBX509_NAME\fR functions are typically used to
create and add new entries in a single operation.
.PP
The arguments of these functions support similar options to the similarly
named ones of the corresponding \fBX509_NAME\fR functions such as
\&\fBX509_NAME_add_entry_by_txt()\fR. So for example \fBtype\fR can be set to
\&\fBMBSTRING_ASC\fR but in the case of \fBX509_set_data()\fR the field name must be
set first so the relevant field information can be looked up internally.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_NAME_ENTRY_get_object()\fR returns a valid \fBASN1_OBJECT\fR structure if it is
set or NULL if an error occurred.
.PP
\&\fBX509_NAME_ENTRY_get_data()\fR returns a valid \fBASN1_STRING\fR structure if it is set
or NULL if an error occurred.
.PP
\&\fBX509_NAME_ENTRY_set_object()\fR and \fBX509_NAME_ENTRY_set_data()\fR return 1 on success
or 0 on error.
.PP
\&\fBX509_NAME_ENTRY_create_by_txt()\fR, \fBX509_NAME_ENTRY_create_by_NID()\fR and
\&\fBX509_NAME_ENTRY_create_by_OBJ()\fR return a valid \fBX509_NAME_ENTRY\fR on success or
NULL if an error occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBd2i_X509_NAME\fR\|(3),
\&\fBOBJ_nid2obj\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
