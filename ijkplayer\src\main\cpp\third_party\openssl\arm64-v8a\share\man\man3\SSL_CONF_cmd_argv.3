.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CONF_CMD_ARGV 3"
.TH SSL_CONF_CMD_ARGV 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CONF_cmd_argv \- SSL configuration command line processing
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CONF_cmd_argv(SSL_CONF_CTX *cctx, int *pargc, char ***pargv);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The function \fBSSL_CONF_cmd_argv()\fR processes at most two command line
arguments from \fBpargv\fR and \fBpargc\fR. The values of \fBpargv\fR and \fBpargc\fR
are updated to reflect the number of command options processed. The \fBpargc\fR
argument can be set to \fBNULL\fR if it is not used.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CONF_cmd_argv()\fR returns the number of command arguments processed: 0, 1, 2
or a negative error code.
.PP
If \-2 is returned then an argument for a command is missing.
.PP
If \-1 is returned the command is recognised but couldn't be processed due
to an error: for example a syntax error in the argument.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CONF_CTX_new\fR\|(3),
\&\fBSSL_CONF_CTX_set_flags\fR\|(3),
\&\fBSSL_CONF_CTX_set1_prefix\fR\|(3),
\&\fBSSL_CONF_CTX_set_ssl_ctx\fR\|(3),
\&\fBSSL_CONF_cmd\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.2.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2012\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
