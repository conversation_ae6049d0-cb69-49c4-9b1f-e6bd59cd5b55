.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_PRINT 3"
.TH RSA_PRINT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_print, RSA_print_fp,
DSAparams_print, DSAparams_print_fp, DSA_print, DSA_print_fp,
DHparams_print, DHparams_print_fp \- print cryptographic parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
\&
\& int RSA_print(BIO *bp, RSA *x, int offset);
\& int RSA_print_fp(FILE *fp, RSA *x, int offset);
\&
\& #include <openssl/dsa.h>
\&
\& int DSAparams_print(BIO *bp, DSA *x);
\& int DSAparams_print_fp(FILE *fp, DSA *x);
\& int DSA_print(BIO *bp, DSA *x, int offset);
\& int DSA_print_fp(FILE *fp, DSA *x, int offset);
\&
\& #include <openssl/dh.h>
\&
\& int DHparams_print(BIO *bp, DH *x);
\& int DHparams_print_fp(FILE *fp, DH *x);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A human-readable hexadecimal output of the components of the RSA
key, DSA parameters or key or DH parameters is printed to \fBbp\fR or \fBfp\fR.
.PP
The output lines are indented by \fBoffset\fR spaces.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return 1 on success, 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBN_bn2bin\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
