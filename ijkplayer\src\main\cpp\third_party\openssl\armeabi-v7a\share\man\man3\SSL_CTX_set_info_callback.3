.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_INFO_CALLBACK 3"
.TH SSL_CTX_SET_INFO_CALLBACK 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_info_callback,
SSL_CTX_get_info_callback,
SSL_set_info_callback,
SSL_get_info_callback
\&\- handle information callback for SSL connections
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_info_callback(SSL_CTX *ctx, void (*callback)());
\& void (*SSL_CTX_get_info_callback(const SSL_CTX *ctx))();
\&
\& void SSL_set_info_callback(SSL *ssl, void (*callback)());
\& void (*SSL_get_info_callback(const SSL *ssl))();
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_info_callback()\fR sets the \fBcallback\fR function, that can be used to
obtain state information for SSL objects created from \fBctx\fR during connection
setup and use. The setting for \fBctx\fR is overridden from the setting for
a specific SSL object, if specified.
When \fBcallback\fR is NULL, no callback function is used.
.PP
\&\fBSSL_set_info_callback()\fR sets the \fBcallback\fR function, that can be used to
obtain state information for \fBssl\fR during connection setup and use.
When \fBcallback\fR is NULL, the callback setting currently valid for
\&\fBctx\fR is used.
.PP
\&\fBSSL_CTX_get_info_callback()\fR returns a pointer to the currently set information
callback function for \fBctx\fR.
.PP
\&\fBSSL_get_info_callback()\fR returns a pointer to the currently set information
callback function for \fBssl\fR.
.SH NOTES
.IX Header "NOTES"
When setting up a connection and during use, it is possible to obtain state
information from the SSL/TLS engine. When set, an information callback function
is called whenever a significant event occurs such as: the state changes,
an alert appears, or an error occurs.
.PP
The callback function is called as \fBcallback(SSL *ssl, int where, int ret)\fR.
The \fBwhere\fR argument specifies information about where (in which context)
the callback function was called. If \fBret\fR is 0, an error condition occurred.
If an alert is handled, SSL_CB_ALERT is set and \fBret\fR specifies the alert
information.
.PP
\&\fBwhere\fR is a bit mask made up of the following bits:
.IP SSL_CB_LOOP 4
.IX Item "SSL_CB_LOOP"
Callback has been called to indicate state change or some other significant
state machine event. This may mean that the callback gets invoked more than once
per state in some situations.
.IP SSL_CB_EXIT 4
.IX Item "SSL_CB_EXIT"
Callback has been called to indicate exit of a handshake function. This will
happen after the end of a handshake, but may happen at other times too such as
on error or when IO might otherwise block and nonblocking is being used.
.IP SSL_CB_READ 4
.IX Item "SSL_CB_READ"
Callback has been called during read operation.
.IP SSL_CB_WRITE 4
.IX Item "SSL_CB_WRITE"
Callback has been called during write operation.
.IP SSL_CB_ALERT 4
.IX Item "SSL_CB_ALERT"
Callback has been called due to an alert being sent or received.
.IP "SSL_CB_READ_ALERT               (SSL_CB_ALERT|SSL_CB_READ)" 4
.IX Item "SSL_CB_READ_ALERT (SSL_CB_ALERT|SSL_CB_READ)"
.PD 0
.IP "SSL_CB_WRITE_ALERT              (SSL_CB_ALERT|SSL_CB_WRITE)" 4
.IX Item "SSL_CB_WRITE_ALERT (SSL_CB_ALERT|SSL_CB_WRITE)"
.IP "SSL_CB_ACCEPT_LOOP              (SSL_ST_ACCEPT|SSL_CB_LOOP)" 4
.IX Item "SSL_CB_ACCEPT_LOOP (SSL_ST_ACCEPT|SSL_CB_LOOP)"
.IP "SSL_CB_ACCEPT_EXIT              (SSL_ST_ACCEPT|SSL_CB_EXIT)" 4
.IX Item "SSL_CB_ACCEPT_EXIT (SSL_ST_ACCEPT|SSL_CB_EXIT)"
.IP "SSL_CB_CONNECT_LOOP             (SSL_ST_CONNECT|SSL_CB_LOOP)" 4
.IX Item "SSL_CB_CONNECT_LOOP (SSL_ST_CONNECT|SSL_CB_LOOP)"
.IP "SSL_CB_CONNECT_EXIT             (SSL_ST_CONNECT|SSL_CB_EXIT)" 4
.IX Item "SSL_CB_CONNECT_EXIT (SSL_ST_CONNECT|SSL_CB_EXIT)"
.IP SSL_CB_HANDSHAKE_START 4
.IX Item "SSL_CB_HANDSHAKE_START"
.PD
Callback has been called because a new handshake is started. It also occurs when
resuming a handshake following a pause to handle early data.
.IP SSL_CB_HANDSHAKE_DONE 4
.IX Item "SSL_CB_HANDSHAKE_DONE"
Callback has been called because a handshake is finished.  It also occurs if the
handshake is paused to allow the exchange of early data.
.PP
The current state information can be obtained using the
\&\fBSSL_state_string\fR\|(3) family of functions.
.PP
The \fBret\fR information can be evaluated using the
\&\fBSSL_alert_type_string\fR\|(3) family of functions.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_set_info_callback()\fR does not provide diagnostic information.
.PP
\&\fBSSL_get_info_callback()\fR returns the current setting.
.SH EXAMPLES
.IX Header "EXAMPLES"
The following example callback function prints state strings, information
about alerts being handled and error messages to the \fBbio_err\fR BIO.
.PP
.Vb 4
\& void apps_ssl_info_callback(SSL *s, int where, int ret)
\& {
\&     const char *str;
\&     int w = where & ~SSL_ST_MASK;
\&
\&     if (w & SSL_ST_CONNECT)
\&         str = "SSL_connect";
\&     else if (w & SSL_ST_ACCEPT)
\&         str = "SSL_accept";
\&     else
\&         str = "undefined";
\&
\&     if (where & SSL_CB_LOOP) {
\&         BIO_printf(bio_err, "%s:%s\en", str, SSL_state_string_long(s));
\&     } else if (where & SSL_CB_ALERT) {
\&         str = (where & SSL_CB_READ) ? "read" : "write";
\&         BIO_printf(bio_err, "SSL3 alert %s:%s:%s\en", str,
\&                    SSL_alert_type_string_long(ret),
\&                    SSL_alert_desc_string_long(ret));
\&     } else if (where & SSL_CB_EXIT) {
\&         if (ret == 0) {
\&             BIO_printf(bio_err, "%s:failed in %s\en",
\&                        str, SSL_state_string_long(s));
\&         } else if (ret < 0) {
\&             BIO_printf(bio_err, "%s:error in %s\en",
\&                        str, SSL_state_string_long(s));
\&         }
\&     }
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_state_string\fR\|(3),
\&\fBSSL_alert_type_string\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
