.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DES_RANDOM_KEY 3"
.TH DES_RANDOM_KEY 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DES_random_key, DES_set_key, DES_key_sched, DES_set_key_checked,
DES_set_key_unchecked, DES_set_odd_parity, DES_is_weak_key,
DES_ecb_encrypt, DES_ecb2_encrypt, DES_ecb3_encrypt, DES_ncbc_encrypt,
DES_cfb_encrypt, DES_ofb_encrypt, DES_pcbc_encrypt, DES_cfb64_encrypt,
DES_ofb64_encrypt, DES_xcbc_encrypt, DES_ede2_cbc_encrypt,
DES_ede2_cfb64_encrypt, DES_ede2_ofb64_encrypt, DES_ede3_cbc_encrypt,
DES_ede3_cfb64_encrypt, DES_ede3_ofb64_encrypt,
DES_cbc_cksum, DES_quad_cksum, DES_string_to_key, DES_string_to_2keys,
DES_fcrypt, DES_crypt \- DES encryption
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/des.h>
\&
\& void DES_random_key(DES_cblock *ret);
\&
\& int DES_set_key(const_DES_cblock *key, DES_key_schedule *schedule);
\& int DES_key_sched(const_DES_cblock *key, DES_key_schedule *schedule);
\& int DES_set_key_checked(const_DES_cblock *key, DES_key_schedule *schedule);
\& void DES_set_key_unchecked(const_DES_cblock *key, DES_key_schedule *schedule);
\&
\& void DES_set_odd_parity(DES_cblock *key);
\& int DES_is_weak_key(const_DES_cblock *key);
\&
\& void DES_ecb_encrypt(const_DES_cblock *input, DES_cblock *output,
\&                      DES_key_schedule *ks, int enc);
\& void DES_ecb2_encrypt(const_DES_cblock *input, DES_cblock *output,
\&                       DES_key_schedule *ks1, DES_key_schedule *ks2, int enc);
\& void DES_ecb3_encrypt(const_DES_cblock *input, DES_cblock *output,
\&                       DES_key_schedule *ks1, DES_key_schedule *ks2,
\&                       DES_key_schedule *ks3, int enc);
\&
\& void DES_ncbc_encrypt(const unsigned char *input, unsigned char *output,
\&                       long length, DES_key_schedule *schedule, DES_cblock *ivec,
\&                       int enc);
\& void DES_cfb_encrypt(const unsigned char *in, unsigned char *out,
\&                      int numbits, long length, DES_key_schedule *schedule,
\&                      DES_cblock *ivec, int enc);
\& void DES_ofb_encrypt(const unsigned char *in, unsigned char *out,
\&                      int numbits, long length, DES_key_schedule *schedule,
\&                      DES_cblock *ivec);
\& void DES_pcbc_encrypt(const unsigned char *input, unsigned char *output,
\&                       long length, DES_key_schedule *schedule, DES_cblock *ivec,
\&                       int enc);
\& void DES_cfb64_encrypt(const unsigned char *in, unsigned char *out,
\&                        long length, DES_key_schedule *schedule, DES_cblock *ivec,
\&                        int *num, int enc);
\& void DES_ofb64_encrypt(const unsigned char *in, unsigned char *out,
\&                        long length, DES_key_schedule *schedule, DES_cblock *ivec,
\&                        int *num);
\&
\& void DES_xcbc_encrypt(const unsigned char *input, unsigned char *output,
\&                       long length, DES_key_schedule *schedule, DES_cblock *ivec,
\&                       const_DES_cblock *inw, const_DES_cblock *outw, int enc);
\&
\& void DES_ede2_cbc_encrypt(const unsigned char *input, unsigned char *output,
\&                           long length, DES_key_schedule *ks1,
\&                           DES_key_schedule *ks2, DES_cblock *ivec, int enc);
\& void DES_ede2_cfb64_encrypt(const unsigned char *in, unsigned char *out,
\&                             long length, DES_key_schedule *ks1,
\&                             DES_key_schedule *ks2, DES_cblock *ivec,
\&                             int *num, int enc);
\& void DES_ede2_ofb64_encrypt(const unsigned char *in, unsigned char *out,
\&                             long length, DES_key_schedule *ks1,
\&                             DES_key_schedule *ks2, DES_cblock *ivec, int *num);
\&
\& void DES_ede3_cbc_encrypt(const unsigned char *input, unsigned char *output,
\&                           long length, DES_key_schedule *ks1,
\&                           DES_key_schedule *ks2, DES_key_schedule *ks3,
\&                           DES_cblock *ivec, int enc);
\& void DES_ede3_cfb64_encrypt(const unsigned char *in, unsigned char *out,
\&                             long length, DES_key_schedule *ks1,
\&                             DES_key_schedule *ks2, DES_key_schedule *ks3,
\&                             DES_cblock *ivec, int *num, int enc);
\& void DES_ede3_ofb64_encrypt(const unsigned char *in, unsigned char *out,
\&                             long length, DES_key_schedule *ks1,
\&                             DES_key_schedule *ks2, DES_key_schedule *ks3,
\&                             DES_cblock *ivec, int *num);
\&
\& DES_LONG DES_cbc_cksum(const unsigned char *input, DES_cblock *output,
\&                        long length, DES_key_schedule *schedule,
\&                        const_DES_cblock *ivec);
\& DES_LONG DES_quad_cksum(const unsigned char *input, DES_cblock output[],
\&                         long length, int out_count, DES_cblock *seed);
\& void DES_string_to_key(const char *str, DES_cblock *key);
\& void DES_string_to_2keys(const char *str, DES_cblock *key1, DES_cblock *key2);
\&
\& char *DES_fcrypt(const char *buf, const char *salt, char *ret);
\& char *DES_crypt(const char *buf, const char *salt);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This library contains a fast implementation of the DES encryption
algorithm.
.PP
There are two phases to the use of DES encryption.  The first is the
generation of a \fIDES_key_schedule\fR from a key, the second is the
actual encryption.  A DES key is of type \fIDES_cblock\fR. This type
consists of 8 bytes with odd parity.  The least significant bit in
each byte is the parity bit.  The key schedule is an expanded form of
the key; it is used to speed the encryption process.
.PP
\&\fBDES_random_key()\fR generates a random key.  The random generator must be
seeded when calling this function.
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see \fBRAND\fR\|(7)), the operation will fail.
If the function fails, 0 is returned.
.PP
Before a DES key can be used, it must be converted into the
architecture dependent \fIDES_key_schedule\fR via the
\&\fBDES_set_key_checked()\fR or \fBDES_set_key_unchecked()\fR function.
.PP
\&\fBDES_set_key_checked()\fR will check that the key passed is of odd parity
and is not a weak or semi-weak key.  If the parity is wrong, then \-1
is returned.  If the key is a weak key, then \-2 is returned.  If an
error is returned, the key schedule is not generated.
.PP
\&\fBDES_set_key()\fR works like
\&\fBDES_set_key_checked()\fR if the \fIDES_check_key\fR flag is nonzero,
otherwise like \fBDES_set_key_unchecked()\fR.  These functions are available
for compatibility; it is recommended to use a function that does not
depend on a global variable.
.PP
\&\fBDES_set_odd_parity()\fR sets the parity of the passed \fIkey\fR to odd.
.PP
\&\fBDES_is_weak_key()\fR returns 1 if the passed key is a weak key, 0 if it
is ok.
.PP
The following routines mostly operate on an input and output stream of
\&\fIDES_cblock\fRs.
.PP
\&\fBDES_ecb_encrypt()\fR is the basic DES encryption routine that encrypts or
decrypts a single 8\-byte \fIDES_cblock\fR in \fIelectronic code book\fR
(ECB) mode.  It always transforms the input data, pointed to by
\&\fIinput\fR, into the output data, pointed to by the \fIoutput\fR argument.
If the \fIencrypt\fR argument is nonzero (DES_ENCRYPT), the \fIinput\fR
(cleartext) is encrypted in to the \fIoutput\fR (ciphertext) using the
key_schedule specified by the \fIschedule\fR argument, previously set via
\&\fIDES_set_key\fR. If \fIencrypt\fR is zero (DES_DECRYPT), the \fIinput\fR (now
ciphertext) is decrypted into the \fIoutput\fR (now cleartext).  Input
and output may overlap.  \fBDES_ecb_encrypt()\fR does not return a value.
.PP
\&\fBDES_ecb3_encrypt()\fR encrypts/decrypts the \fIinput\fR block by using
three-key Triple-DES encryption in ECB mode.  This involves encrypting
the input with \fIks1\fR, decrypting with the key schedule \fIks2\fR, and
then encrypting with \fIks3\fR.  This routine greatly reduces the chances
of brute force breaking of DES and has the advantage of if \fIks1\fR,
\&\fIks2\fR and \fIks3\fR are the same, it is equivalent to just encryption
using ECB mode and \fIks1\fR as the key.
.PP
The macro \fBDES_ecb2_encrypt()\fR is provided to perform two-key Triple-DES
encryption by using \fIks1\fR for the final encryption.
.PP
\&\fBDES_ncbc_encrypt()\fR encrypts/decrypts using the \fIcipher-block-chaining\fR
(CBC) mode of DES.  If the \fIencrypt\fR argument is nonzero, the
routine cipher-block-chain encrypts the cleartext data pointed to by
the \fIinput\fR argument into the ciphertext pointed to by the \fIoutput\fR
argument, using the key schedule provided by the \fIschedule\fR argument,
and initialization vector provided by the \fIivec\fR argument.  If the
\&\fIlength\fR argument is not an integral multiple of eight bytes, the
last block is copied to a temporary area and zero filled.  The output
is always an integral multiple of eight bytes.
.PP
\&\fBDES_xcbc_encrypt()\fR is RSA's DESX mode of DES.  It uses \fIinw\fR and
\&\fIoutw\fR to 'whiten' the encryption.  \fIinw\fR and \fIoutw\fR are secret
(unlike the iv) and are as such, part of the key.  So the key is sort
of 24 bytes.  This is much better than CBC DES.
.PP
\&\fBDES_ede3_cbc_encrypt()\fR implements outer triple CBC DES encryption with
three keys. This means that each DES operation inside the CBC mode is
\&\f(CW\*(C`C=E(ks3,D(ks2,E(ks1,M)))\*(C'\fR.  This mode is used by SSL.
.PP
The \fBDES_ede2_cbc_encrypt()\fR macro implements two-key Triple-DES by
reusing \fIks1\fR for the final encryption.  \f(CW\*(C`C=E(ks1,D(ks2,E(ks1,M)))\*(C'\fR.
This form of Triple-DES is used by the RSAREF library.
.PP
\&\fBDES_pcbc_encrypt()\fR encrypts/decrypts using the propagating cipher block
chaining mode used by Kerberos v4. Its parameters are the same as
\&\fBDES_ncbc_encrypt()\fR.
.PP
\&\fBDES_cfb_encrypt()\fR encrypts/decrypts using cipher feedback mode.  This
method takes an array of characters as input and outputs an array of
characters.  It does not require any padding to 8 character groups.
Note: the \fIivec\fR variable is changed and the new changed value needs to
be passed to the next call to this function.  Since this function runs
a complete DES ECB encryption per \fInumbits\fR, this function is only
suggested for use when sending a small number of characters.
.PP
\&\fBDES_cfb64_encrypt()\fR
implements CFB mode of DES with 64\-bit feedback.  Why is this
useful you ask?  Because this routine will allow you to encrypt an
arbitrary number of bytes, without 8 byte padding.  Each call to this
routine will encrypt the input bytes to output and then update ivec
and num.  num contains 'how far' we are though ivec.  If this does
not make much sense, read more about CFB mode of DES.
.PP
\&\fBDES_ede3_cfb64_encrypt()\fR and \fBDES_ede2_cfb64_encrypt()\fR is the same as
\&\fBDES_cfb64_encrypt()\fR except that Triple-DES is used.
.PP
\&\fBDES_ofb_encrypt()\fR encrypts using output feedback mode.  This method
takes an array of characters as input and outputs an array of
characters.  It does not require any padding to 8 character groups.
Note: the \fIivec\fR variable is changed and the new changed value needs to
be passed to the next call to this function.  Since this function runs
a complete DES ECB encryption per \fInumbits\fR, this function is only
suggested for use when sending a small number of characters.
.PP
\&\fBDES_ofb64_encrypt()\fR is the same as \fBDES_cfb64_encrypt()\fR using Output
Feed Back mode.
.PP
\&\fBDES_ede3_ofb64_encrypt()\fR and \fBDES_ede2_ofb64_encrypt()\fR is the same as
\&\fBDES_ofb64_encrypt()\fR, using Triple-DES.
.PP
The following functions are included in the DES library for
compatibility with the MIT Kerberos library.
.PP
\&\fBDES_cbc_cksum()\fR produces an 8 byte checksum based on the input stream
(via CBC encryption).  The last 4 bytes of the checksum are returned
and the complete 8 bytes are placed in \fIoutput\fR. This function is
used by Kerberos v4.  Other applications should use
\&\fBEVP_DigestInit\fR\|(3) etc. instead.
.PP
\&\fBDES_quad_cksum()\fR is a Kerberos v4 function.  It returns a 4 byte
checksum from the input bytes.  The algorithm can be iterated over the
input, depending on \fIout_count\fR, 1, 2, 3 or 4 times.  If \fIoutput\fR is
non-NULL, the 8 bytes generated by each pass are written into
\&\fIoutput\fR.
.PP
The following are DES-based transformations:
.PP
\&\fBDES_fcrypt()\fR is a fast version of the Unix \fBcrypt\fR\|(3) function.  This
version takes only a small amount of space relative to other fast
\&\fBcrypt()\fR implementations.  This is different to the normal \fBcrypt()\fR in
that the third parameter is the buffer that the return value is
written into.  It needs to be at least 14 bytes long.  This function
is thread safe, unlike the normal \fBcrypt()\fR.
.PP
\&\fBDES_crypt()\fR is a faster replacement for the normal system \fBcrypt()\fR.
This function calls \fBDES_fcrypt()\fR with a static array passed as the
third parameter.  This mostly emulates the normal non-thread-safe semantics
of \fBcrypt\fR\|(3).
The \fBsalt\fR must be two ASCII characters.
.PP
The values returned by \fBDES_fcrypt()\fR and \fBDES_crypt()\fR are terminated by NUL
character.
.PP
\&\fBDES_enc_write()\fR writes \fIlen\fR bytes to file descriptor \fIfd\fR from
buffer \fIbuf\fR. The data is encrypted via \fIpcbc_encrypt\fR (default)
using \fIsched\fR for the key and \fIiv\fR as a starting vector.  The actual
data send down \fIfd\fR consists of 4 bytes (in network byte order)
containing the length of the following encrypted data.  The encrypted
data then follows, padded with random data out to a multiple of 8
bytes.
.SH BUGS
.IX Header "BUGS"
\&\fBDES_cbc_encrypt()\fR does not modify \fBivec\fR; use \fBDES_ncbc_encrypt()\fR
instead.
.PP
\&\fBDES_cfb_encrypt()\fR and \fBDES_ofb_encrypt()\fR operates on input of 8 bits.
What this means is that if you set numbits to 12, and length to 2, the
first 12 bits will come from the 1st input byte and the low half of
the second input byte.  The second 12 bits will have the low 8 bits
taken from the 3rd input byte and the top 4 bits taken from the 4th
input byte.  The same holds for output.  This function has been
implemented this way because most people will be using a multiple of 8
and because once you get into pulling bytes input bytes apart things
get ugly!
.PP
\&\fBDES_string_to_key()\fR is available for backward compatibility with the
MIT library.  New applications should use a cryptographic hash function.
The same applies for \fBDES_string_to_2key()\fR.
.SH NOTES
.IX Header "NOTES"
The \fBdes\fR library was written to be source code compatible with
the MIT Kerberos library.
.PP
Applications should use the higher level functions
\&\fBEVP_EncryptInit\fR\|(3) etc. instead of calling these
functions directly.
.PP
Single-key DES is insecure due to its short key size.  ECB mode is
not suitable for most applications; see \fBdes_modes\fR\|(7).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDES_set_key()\fR, \fBDES_key_sched()\fR, \fBDES_set_key_checked()\fR and \fBDES_is_weak_key()\fR
return 0 on success or negative values on error.
.PP
\&\fBDES_cbc_cksum()\fR and \fBDES_quad_cksum()\fR return 4\-byte integer representing the
last 4 bytes of the checksum of the input.
.PP
\&\fBDES_fcrypt()\fR returns a pointer to the caller-provided buffer and \fBDES_crypt()\fR \-
to a static buffer on success; otherwise they return NULL.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBdes_modes\fR\|(7),
\&\fBEVP_EncryptInit\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The requirement that the \fBsalt\fR parameter to \fBDES_crypt()\fR and \fBDES_fcrypt()\fR
be two ASCII characters was first enforced in
OpenSSL 1.1.0.  Previous versions tried to use the letter uppercase \fBA\fR
if both character were not present, and could crash when given non-ASCII
on some platforms.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
