<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_SignInit</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_SignInit, EVP_SignInit_ex, EVP_SignUpdate, EVP_SignFinal - EVP signing functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

int EVP_SignInit_ex(EVP_MD_CTX *ctx, const EVP_MD *type, ENGINE *impl);
int EVP_SignUpdate(EVP_MD_CTX *ctx, const void *d, unsigned int cnt);
int EVP_SignFinal(EVP_MD_CTX *ctx, unsigned char *sig, unsigned int *s, EVP_PKEY *pkey);

void EVP_SignInit(EVP_MD_CTX *ctx, const EVP_MD *type);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The EVP signature routines are a high-level interface to digital signatures.</p>

<p>EVP_SignInit_ex() sets up signing context <i>ctx</i> to use digest <i>type</i> from <b>ENGINE</b> <i>impl</i>. <i>ctx</i> must be created with EVP_MD_CTX_new() before calling this function.</p>

<p>EVP_SignUpdate() hashes <i>cnt</i> bytes of data at <i>d</i> into the signature context <i>ctx</i>. This function can be called several times on the same <i>ctx</i> to include additional data.</p>

<p>EVP_SignFinal() signs the data in <i>ctx</i> using the private key <i>pkey</i> and places the signature in <i>sig</i>. <i>sig</i> must be at least <code>EVP_PKEY_size(pkey)</code> bytes in size. <i>s</i> is an OUT parameter, and not used as an IN parameter. The number of bytes of data written (i.e. the length of the signature) will be written to the integer at <i>s</i>, at most <code>EVP_PKEY_size(pkey)</code> bytes will be written.</p>

<p>EVP_SignInit() initializes a signing context <i>ctx</i> to use the default implementation of digest <i>type</i>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_SignInit_ex(), EVP_SignUpdate() and EVP_SignFinal() return 1 for success and 0 for failure.</p>

<p>The error codes can be obtained by <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>.</p>

<h1 id="NOTES">NOTES</h1>

<p>The <b>EVP</b> interface to digital signatures should almost always be used in preference to the low-level interfaces. This is because the code then becomes transparent to the algorithm used and much more flexible.</p>

<p>When signing with DSA private keys the random number generator must be seeded. If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to external circumstances (see <a href="../man7/RAND.html">RAND(7)</a>), the operation will fail. This requirement does not hold for RSA signatures.</p>

<p>The call to EVP_SignFinal() internally finalizes a copy of the digest context. This means that calls to EVP_SignUpdate() and EVP_SignFinal() can be called later to digest and sign additional data.</p>

<p>Since only a copy of the digest context is ever finalized the context must be cleaned up after use by calling EVP_MD_CTX_free() or a memory leak will occur.</p>

<h1 id="BUGS">BUGS</h1>

<p>Older versions of this documentation wrongly stated that calls to EVP_SignUpdate() could not be made after calling EVP_SignFinal().</p>

<p>Since the private key is passed in the call to EVP_SignFinal() any error relating to the private key (for example an unsuitable key and digest combination) will not be indicated until after potentially large amounts of data have been passed through EVP_SignUpdate().</p>

<p>It is not possible to change the signing parameters using these function.</p>

<p>The previous two bugs are fixed in the newer EVP_SignDigest*() function.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_PKEY_size.html">EVP_PKEY_size(3)</a>, <a href="../man3/EVP_PKEY_bits.html">EVP_PKEY_bits(3)</a>, <a href="../man3/EVP_PKEY_security_bits.html">EVP_PKEY_security_bits(3)</a>, <a href="../man3/EVP_VerifyInit.html">EVP_VerifyInit(3)</a>, <a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a>, <a href="../man7/evp.html">evp(7)</a>, <a href="../man3/HMAC.html">HMAC(3)</a>, <a href="../man3/MD2.html">MD2(3)</a>, <a href="../man3/MD5.html">MD5(3)</a>, <a href="../man3/MDC2.html">MDC2(3)</a>, <a href="../man3/RIPEMD160.html">RIPEMD160(3)</a>, <a href="../man3/SHA1.html">SHA1(3)</a>, <a href="../man1/dgst.html">dgst(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


