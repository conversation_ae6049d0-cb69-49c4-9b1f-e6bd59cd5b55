.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_NAME_PRINT_EX 3"
.TH X509_NAME_PRINT_EX 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_NAME_print_ex, X509_NAME_print_ex_fp, X509_NAME_print,
X509_NAME_oneline \- X509_NAME printing routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_NAME_print_ex(BIO *out, const X509_NAME *nm, int indent, unsigned long flags);
\& int X509_NAME_print_ex_fp(FILE *fp, const X509_NAME *nm, int indent, unsigned long flags);
\& char *X509_NAME_oneline(const X509_NAME *a, char *buf, int size);
\& int X509_NAME_print(BIO *bp, const X509_NAME *name, int obase);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_NAME_print_ex()\fR prints a human readable version of \fBnm\fR to BIO \fBout\fR. Each
line (for multiline formats) is indented by \fBindent\fR spaces. The output format
can be extensively customised by use of the \fBflags\fR parameter.
.PP
\&\fBX509_NAME_print_ex_fp()\fR is identical to \fBX509_NAME_print_ex()\fR except the output is
written to FILE pointer \fBfp\fR.
.PP
\&\fBX509_NAME_oneline()\fR prints an ASCII version of \fBa\fR to \fBbuf\fR.
If \fBbuf\fR is \fBNULL\fR then a buffer is dynamically allocated and returned, and
\&\fBsize\fR is ignored.
Otherwise, at most \fBsize\fR bytes will be written, including the ending '\e0',
and \fBbuf\fR is returned.
.PP
\&\fBX509_NAME_print()\fR prints out \fBname\fR to \fBbp\fR indenting each line by \fBobase\fR
characters. Multiple lines are used if the output (including indent) exceeds
80 characters.
.SH NOTES
.IX Header "NOTES"
The functions \fBX509_NAME_oneline()\fR and \fBX509_NAME_print()\fR
produce a non standard output form, they don't handle multi character fields and
have various quirks and inconsistencies.
Their use is strongly discouraged in new applications and they could
be deprecated in a future release.
.PP
Although there are a large number of possible flags for most purposes
\&\fBXN_FLAG_ONELINE\fR, \fBXN_FLAG_MULTILINE\fR or \fBXN_FLAG_RFC2253\fR will suffice.
As noted on the \fBASN1_STRING_print_ex\fR\|(3) manual page
for UTF8 terminals the \fBASN1_STRFLGS_ESC_MSB\fR should be unset: so for example
\&\fBXN_FLAG_ONELINE & ~ASN1_STRFLGS_ESC_MSB\fR would be used.
.PP
The complete set of the flags supported by \fBX509_NAME_print_ex()\fR is listed below.
.PP
Several options can be ored together.
.PP
The options \fBXN_FLAG_SEP_COMMA_PLUS\fR, \fBXN_FLAG_SEP_CPLUS_SPC\fR,
\&\fBXN_FLAG_SEP_SPLUS_SPC\fR and \fBXN_FLAG_SEP_MULTILINE\fR determine the field separators
to use. Two distinct separators are used between distinct RelativeDistinguishedName
components and separate values in the same RDN for a multi-valued RDN. Multi-valued
RDNs are currently very rare so the second separator will hardly ever be used.
.PP
\&\fBXN_FLAG_SEP_COMMA_PLUS\fR uses comma and plus as separators. \fBXN_FLAG_SEP_CPLUS_SPC\fR
uses comma and plus with spaces: this is more readable that plain comma and plus.
\&\fBXN_FLAG_SEP_SPLUS_SPC\fR uses spaced semicolon and plus. \fBXN_FLAG_SEP_MULTILINE\fR uses
spaced newline and plus respectively.
.PP
If \fBXN_FLAG_DN_REV\fR is set the whole DN is printed in reversed order.
.PP
The fields \fBXN_FLAG_FN_SN\fR, \fBXN_FLAG_FN_LN\fR, \fBXN_FLAG_FN_OID\fR,
\&\fBXN_FLAG_FN_NONE\fR determine how a field name is displayed. It will
use the short name (e.g. CN) the long name (e.g. commonName) always
use OID numerical form (normally OIDs are only used if the field name is not
recognised) and no field name respectively.
.PP
If \fBXN_FLAG_SPC_EQ\fR is set then spaces will be placed around the '=' character
separating field names and values.
.PP
If \fBXN_FLAG_DUMP_UNKNOWN_FIELDS\fR is set then the encoding of unknown fields is
printed instead of the values.
.PP
If \fBXN_FLAG_FN_ALIGN\fR is set then field names are padded to 20 characters: this
is only of use for multiline format.
.PP
Additionally all the options supported by \fBASN1_STRING_print_ex()\fR can be used to
control how each field value is displayed.
.PP
In addition a number options can be set for commonly used formats.
.PP
\&\fBXN_FLAG_RFC2253\fR sets options which produce an output compatible with RFC2253 it
is equivalent to:
 \fBASN1_STRFLGS_RFC2253 | XN_FLAG_SEP_COMMA_PLUS | XN_FLAG_DN_REV | XN_FLAG_FN_SN | XN_FLAG_DUMP_UNKNOWN_FIELDS\fR
.PP
\&\fBXN_FLAG_ONELINE\fR is a more readable one line format which is the same as:
 \fBASN1_STRFLGS_RFC2253 | ASN1_STRFLGS_ESC_QUOTE | XN_FLAG_SEP_CPLUS_SPC | XN_FLAG_SPC_EQ | XN_FLAG_FN_SN\fR
.PP
\&\fBXN_FLAG_MULTILINE\fR is a multiline format which is the same as:
 \fBASN1_STRFLGS_ESC_CTRL | ASN1_STRFLGS_ESC_MSB | XN_FLAG_SEP_MULTILINE | XN_FLAG_SPC_EQ | XN_FLAG_FN_LN | XN_FLAG_FN_ALIGN\fR
.PP
\&\fBXN_FLAG_COMPAT\fR uses a format identical to \fBX509_NAME_print()\fR: in fact it calls \fBX509_NAME_print()\fR internally.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_NAME_oneline()\fR returns a valid string on success or NULL on error.
.PP
\&\fBX509_NAME_print()\fR returns 1 on success or 0 on error.
.PP
\&\fBX509_NAME_print_ex()\fR and \fBX509_NAME_print_ex_fp()\fR return 1 on success or 0 on error
if the \fBXN_FLAG_COMPAT\fR is set, which is the same as \fBX509_NAME_print()\fR. Otherwise,
it returns \-1 on error or other values on success.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBASN1_STRING_print_ex\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
