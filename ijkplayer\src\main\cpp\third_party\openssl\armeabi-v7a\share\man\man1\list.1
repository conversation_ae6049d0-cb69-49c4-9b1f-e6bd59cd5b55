.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "LIST 1"
.TH LIST 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-list,
list \- list algorithms and features
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl list\fR
[\fB\-help\fR]
[\fB\-1\fR]
[\fB\-commands\fR]
[\fB\-digest\-commands\fR]
[\fB\-digest\-algorithms\fR]
[\fB\-cipher\-commands\fR]
[\fB\-cipher\-algorithms\fR]
[\fB\-public\-key\-algorithms\fR]
[\fB\-public\-key\-methods\fR]
[\fB\-disabled\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to generate list of algorithms or disabled
features.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Display a usage message.
.IP \fB\-1\fR 4
.IX Item "-1"
List the commands, digest-commands, or cipher-commands in a single column.
If used, this option must be given first.
.IP \fB\-commands\fR 4
.IX Item "-commands"
Display a list of standard commands.
.IP \fB\-digest\-commands\fR 4
.IX Item "-digest-commands"
Display a list of message digest commands, which are typically used
as input to the \fBdgst\fR\|(1) or \fBspeed\fR\|(1) commands.
.IP \fB\-digest\-algorithms\fR 4
.IX Item "-digest-algorithms"
Display a list of message digest algorithms.
If a line is of the form
  foo => bar
then \fBfoo\fR is an alias for the official algorithm name, \fBbar\fR.
.IP \fB\-cipher\-commands\fR 4
.IX Item "-cipher-commands"
Display a list of cipher commands, which are typically used as input
to the \fBdgst\fR\|(1) or \fBspeed\fR\|(1) commands.
.IP \fB\-cipher\-algorithms\fR 4
.IX Item "-cipher-algorithms"
Display a list of cipher algorithms.
If a line is of the form
  foo => bar
then \fBfoo\fR is an alias for the official algorithm name, \fBbar\fR.
.IP \fB\-public\-key\-algorithms\fR 4
.IX Item "-public-key-algorithms"
Display a list of public key algorithms, with each algorithm as
a block of multiple lines, all but the first are indented.
.IP \fB\-public\-key\-methods\fR 4
.IX Item "-public-key-methods"
Display a list of public key method OIDs: this also includes public key methods
without an associated ASN.1 method, for example, KDF algorithms.
.IP \fB\-disabled\fR 4
.IX Item "-disabled"
Display a list of disabled features, those that were compiled out
of the installation.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
