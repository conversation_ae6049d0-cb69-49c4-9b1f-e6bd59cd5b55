.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CONFIG 5"
.TH CONFIG 5 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
config \- OpenSSL CONF library configuration files
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The OpenSSL CONF library can be used to read configuration files.
It is used for the OpenSSL master configuration file \fBopenssl.cnf\fR
and in a few other places like \fBSPKAC\fR files and certificate extension
files for the \fBx509\fR utility. OpenSSL applications can also use the
CONF library for their own purposes.
.PP
A configuration file is divided into a number of sections. Each section
starts with a line \fB[ section_name ]\fR and ends when a new section is
started or end of file is reached. A section name can consist of
alphanumeric characters and underscores.
.PP
The first section of a configuration file is special and is referred
to as the \fBdefault\fR section. This section is usually unnamed and spans from the
start of file until the first named section. When a name is being looked up
it is first looked up in a named section (if any) and then the
default section.
.PP
The environment is mapped onto a section called \fBENV\fR.
.PP
Comments can be included by preceding them with the \fB#\fR character
.PP
Other files can be included using the \fB.include\fR directive followed
by a path. If the path points to a directory all files with
names ending with \fB.cnf\fR or \fB.conf\fR are included from the directory.
Recursive inclusion of directories from files in such directory is not
supported. That means the files in the included directory can also contain
\&\fB.include\fR directives but only inclusion of regular files is supported
there. The inclusion of directories is not supported on systems without
POSIX IO support.
.PP
It is strongly recommended to use absolute paths with the \fB.include\fR
directive. Relative paths are evaluated based on the application current
working directory so unless the configuration file containing the
\&\fB.include\fR directive is application specific the inclusion will not
work as expected.
.PP
There can be optional \fB=\fR character and whitespace characters between
\&\fB.include\fR directive and the path which can be useful in cases the
configuration file needs to be loaded by old OpenSSL versions which do
not support the \fB.include\fR syntax. They would bail out with error
if the \fB=\fR character is not present but with it they just ignore
the include.
.PP
Each section in a configuration file consists of a number of name and
value pairs of the form \fBname=value\fR
.PP
The \fBname\fR string can contain any alphanumeric characters as well as
a few punctuation symbols such as \fB.\fR \fB,\fR \fB;\fR and \fB_\fR.
.PP
The \fBvalue\fR string consists of the string following the \fB=\fR character
until end of line with any leading and trailing white space removed.
.PP
The value string undergoes variable expansion. This can be done by
including the form \fR\f(CB$var\fR\fB\fR or \fB${var}\fR: this will substitute the value
of the named variable in the current section. It is also possible to
substitute a value from another section using the syntax \fB\fR\f(CB$section::name\fR\fB\fR
or \fB${section::name}\fR. By using the form \fB\fR\f(CB$ENV::name\fR\fB\fR environment
variables can be substituted. It is also possible to assign values to
environment variables by using the name \fBENV::name\fR, this will work
if the program looks up environment variables using the \fBCONF\fR library
instead of calling \fBgetenv()\fR directly. The value string must not exceed 64k in
length after variable expansion. Otherwise an error will occur.
.PP
It is possible to escape certain characters by using any kind of quote
or the \fB\e\fR character. By making the last character of a line a \fB\e\fR
a \fBvalue\fR string can be spread across multiple lines. In addition
the sequences \fB\en\fR, \fB\er\fR, \fB\eb\fR and \fB\et\fR are recognized.
.PP
All expansion and escape rules as described above that apply to \fBvalue\fR
also apply to the path of the \fB.include\fR directive.
.SH "OPENSSL LIBRARY CONFIGURATION"
.IX Header "OPENSSL LIBRARY CONFIGURATION"
Applications can automatically configure certain
aspects of OpenSSL using the master OpenSSL configuration file, or optionally
an alternative configuration file. The \fBopenssl\fR utility includes this
functionality: any sub command uses the master OpenSSL configuration file
unless an option is used in the sub command to use an alternative configuration
file.
.PP
To enable library configuration the default section needs to contain an
appropriate line which points to the main configuration section. The default
name is \fBopenssl_conf\fR which is used by the \fBopenssl\fR utility. Other
applications may use an alternative name such as \fBmyapplication_conf\fR.
All library configuration lines appear in the default section at the start
of the configuration file.
.PP
The configuration section should consist of a set of name value pairs which
contain specific module configuration information. The \fBname\fR represents
the name of the \fIconfiguration module\fR. The meaning of the \fBvalue\fR is
module specific: it may, for example, represent a further configuration
section containing configuration module specific information. E.g.:
.PP
.Vb 2
\& # This must be in the default section
\& openssl_conf = openssl_init
\&
\& [openssl_init]
\&
\& oid_section = new_oids
\& engines = engine_section
\&
\& [new_oids]
\&
\& ... new oids here ...
\&
\& [engine_section]
\&
\& ... engine stuff here ...
.Ve
.PP
The features of each configuration module are described below.
.SS "ASN1 Object Configuration Module"
.IX Subsection "ASN1 Object Configuration Module"
This module has the name \fBoid_section\fR. The value of this variable points
to a section containing name value pairs of OIDs: the name is the OID short
and long name, the value is the numerical form of the OID. Although some of
the \fBopenssl\fR utility sub commands already have their own ASN1 OBJECT section
functionality not all do. By using the ASN1 OBJECT configuration module
\&\fBall\fR the \fBopenssl\fR utility sub commands can see the new objects as well
as any compliant applications. For example:
.PP
.Vb 1
\& [new_oids]
\&
\& some_new_oid = *******
\& some_other_oid = 1.2.3.5
.Ve
.PP
It is also possible to set the value to the long name followed
by a comma and the numerical OID form. For example:
.PP
.Vb 1
\& shortName = some object long name, *******
.Ve
.SS "Engine Configuration Module"
.IX Subsection "Engine Configuration Module"
This ENGINE configuration module has the name \fBengines\fR. The value of this
variable points to a section containing further ENGINE configuration
information.
.PP
The section pointed to by \fBengines\fR is a table of engine names (though see
\&\fBengine_id\fR below) and further sections containing configuration information
specific to each ENGINE.
.PP
Each ENGINE specific section is used to set default algorithms, load
dynamic, perform initialization and send ctrls. The actual operation performed
depends on the \fIcommand\fR name which is the name of the name value pair. The
currently supported commands are listed below.
.PP
For example:
.PP
.Vb 1
\& [engine_section]
\&
\& # Configure ENGINE named "foo"
\& foo = foo_section
\& # Configure ENGINE named "bar"
\& bar = bar_section
\&
\& [foo_section]
\& ... foo ENGINE specific commands ...
\&
\& [bar_section]
\& ... "bar" ENGINE specific commands ...
.Ve
.PP
The command \fBengine_id\fR is used to give the ENGINE name. If used this
command must be first. For example:
.PP
.Vb 3
\& [engine_section]
\& # This would normally handle an ENGINE named "foo"
\& foo = foo_section
\&
\& [foo_section]
\& # Override default name and use "myfoo" instead.
\& engine_id = myfoo
.Ve
.PP
The command \fBdynamic_path\fR loads and adds an ENGINE from the given path. It
is equivalent to sending the ctrls \fBSO_PATH\fR with the path argument followed
by \fBLIST_ADD\fR with value 2 and \fBLOAD\fR to the dynamic ENGINE. If this is
not the required behaviour then alternative ctrls can be sent directly
to the dynamic ENGINE using ctrl commands.
.PP
The command \fBinit\fR determines whether to initialize the ENGINE. If the value
is \fB0\fR the ENGINE will not be initialized, if \fB1\fR and attempt it made to
initialized the ENGINE immediately. If the \fBinit\fR command is not present
then an attempt will be made to initialize the ENGINE after all commands in
its section have been processed.
.PP
The command \fBdefault_algorithms\fR sets the default algorithms an ENGINE will
supply using the functions \fBENGINE_set_default_string()\fR.
.PP
If the name matches none of the above command names it is assumed to be a
ctrl command which is sent to the ENGINE. The value of the command is the
argument to the ctrl command. If the value is the string \fBEMPTY\fR then no
value is sent to the command.
.PP
For example:
.PP
.Vb 1
\& [engine_section]
\&
\& # Configure ENGINE named "foo"
\& foo = foo_section
\&
\& [foo_section]
\& # Load engine from DSO
\& dynamic_path = /some/path/fooengine.so
\& # A foo specific ctrl.
\& some_ctrl = some_value
\& # Another ctrl that doesn\*(Aqt take a value.
\& other_ctrl = EMPTY
\& # Supply all default algorithms
\& default_algorithms = ALL
.Ve
.SS "EVP Configuration Module"
.IX Subsection "EVP Configuration Module"
This modules has the name \fBalg_section\fR which points to a section containing
algorithm commands.
.PP
Currently the only algorithm command supported is \fBfips_mode\fR whose
value can only be the boolean string \fBoff\fR. If \fBfips_mode\fR is set to \fBon\fR,
an error occurs as this library version is not FIPS capable.
.SS "SSL Configuration Module"
.IX Subsection "SSL Configuration Module"
This module has the name \fBssl_conf\fR which points to a section containing
SSL configurations.
.PP
Each line in the SSL configuration section contains the name of the
configuration and the section containing it.
.PP
Each configuration section consists of command value pairs for \fBSSL_CONF\fR.
Each pair will be passed to a \fBSSL_CTX\fR or \fBSSL\fR structure if it calls
\&\fBSSL_CTX_config()\fR or \fBSSL_config()\fR with the appropriate configuration name.
.PP
Note: any characters before an initial dot in the configuration section are
ignored so the same command can be used multiple times.
.PP
For example:
.PP
.Vb 1
\& ssl_conf = ssl_sect
\&
\& [ssl_sect]
\&
\& server = server_section
\&
\& [server_section]
\&
\& RSA.Certificate = server\-rsa.pem
\& ECDSA.Certificate = server\-ecdsa.pem
\& Ciphers = ALL:!RC4
.Ve
.PP
The system default configuration with name \fBsystem_default\fR if present will
be applied during any creation of the \fBSSL_CTX\fR structure.
.PP
Example of a configuration with the system default:
.PP
.Vb 1
\& ssl_conf = ssl_sect
\&
\& [ssl_sect]
\& system_default = system_default_sect
\&
\& [system_default_sect]
\& MinProtocol = TLSv1.2
\& MinProtocol = DTLSv1.2
.Ve
.SH NOTES
.IX Header "NOTES"
If a configuration file attempts to expand a variable that doesn't exist
then an error is flagged and the file will not load. This can happen
if an attempt is made to expand an environment variable that doesn't
exist. For example in a previous version of OpenSSL the default OpenSSL
master configuration file used the value of \fBHOME\fR which may not be
defined on non Unix systems and would cause an error.
.PP
This can be worked around by including a \fBdefault\fR section to provide
a default value: then if the environment lookup fails the default value
will be used instead. For this to work properly the default value must
be defined earlier in the configuration file than the expansion. See
the \fBEXAMPLES\fR section for an example of how to do this.
.PP
If the same variable exists in the same section then all but the last
value will be silently ignored. In certain circumstances such as with
DNs the same field may occur multiple times. This is usually worked
around by ignoring any characters before an initial \fB.\fR e.g.
.PP
.Vb 2
\& 1.OU="My first OU"
\& 2.OU="My Second OU"
.Ve
.SH EXAMPLES
.IX Header "EXAMPLES"
Here is a sample configuration file using some of the features
mentioned above.
.PP
.Vb 1
\& # This is the default section.
\&
\& HOME=/temp
\& RANDFILE= ${ENV::HOME}/.rnd
\& configdir=$ENV::HOME/config
\&
\& [ section_one ]
\&
\& # We are now in section one.
\&
\& # Quotes permit leading and trailing whitespace
\& any = " any variable name "
\&
\& other = A string that can \e
\& cover several lines \e
\& by including \e\e characters
\&
\& message = Hello World\en
\&
\& [ section_two ]
\&
\& greeting = $section_one::message
.Ve
.PP
This next example shows how to expand environment variables safely.
.PP
Suppose you want a variable called \fBtmpfile\fR to refer to a
temporary filename. The directory it is placed in can determined by
the \fBTEMP\fR or \fBTMP\fR environment variables but they may not be
set to any value at all. If you just include the environment variable
names and the variable doesn't exist then this will cause an error when
an attempt is made to load the configuration file. By making use of the
default section both values can be looked up with \fBTEMP\fR taking
priority and \fB/tmp\fR used if neither is defined:
.PP
.Vb 5
\& TMP=/tmp
\& # The above value is used if TMP isn\*(Aqt in the environment
\& TEMP=$ENV::TMP
\& # The above value is used if TEMP isn\*(Aqt in the environment
\& tmpfile=${ENV::TEMP}/tmp.filename
.Ve
.PP
Simple OpenSSL library configuration example to enter FIPS mode:
.PP
.Vb 3
\& # Default appname: should match "appname" parameter (if any)
\& # supplied to CONF_modules_load_file et al.
\& openssl_conf = openssl_conf_section
\&
\& [openssl_conf_section]
\& # Configuration module list
\& alg_section = evp_sect
\&
\& [evp_sect]
\& # Set to "yes" to enter FIPS mode if supported
\& fips_mode = yes
.Ve
.PP
Note: in the above example you will get an error in non FIPS capable versions
of OpenSSL.
.PP
Simple OpenSSL library configuration to make TLS 1.2 and DTLS 1.2 the
system-default minimum TLS and DTLS versions, respectively:
.PP
.Vb 2
\& # Toplevel section for openssl (including libssl)
\& openssl_conf = default_conf_section
\&
\& [default_conf_section]
\& # We only specify configuration for the "ssl module"
\& ssl_conf = ssl_section
\&
\& [ssl_section]
\& system_default = system_default_section
\&
\& [system_default_section]
\& MinProtocol = TLSv1.2
\& MinProtocol = DTLSv1.2
.Ve
.PP
The minimum TLS protocol is applied to \fBSSL_CTX\fR objects that are TLS-based,
and the minimum DTLS protocol to those are DTLS-based.
The same applies also to maximum versions set with \fBMaxProtocol\fR.
.PP
More complex OpenSSL library configuration. Add OID and don't enter FIPS mode:
.PP
.Vb 3
\& # Default appname: should match "appname" parameter (if any)
\& # supplied to CONF_modules_load_file et al.
\& openssl_conf = openssl_conf_section
\&
\& [openssl_conf_section]
\& # Configuration module list
\& alg_section = evp_sect
\& oid_section = new_oids
\&
\& [evp_sect]
\& # This will have no effect as FIPS mode is off by default.
\& # Set to "yes" to enter FIPS mode, if supported
\& fips_mode = no
\&
\& [new_oids]
\& # New OID, just short name
\& newoid1 = *******.1
\& # New OID shortname and long name
\& newoid2 = New OID 2 long name, *******.2
.Ve
.PP
The above examples can be used with any application supporting library
configuration if "openssl_conf" is modified to match the appropriate "appname".
.PP
For example if the second sample file above is saved to "example.cnf" then
the command line:
.PP
.Vb 1
\& OPENSSL_CONF=example.cnf openssl asn1parse \-genstr OID:*******.1
.Ve
.PP
will output:
.PP
.Vb 1
\&    0:d=0  hl=2 l=   4 prim: OBJECT            :newoid1
.Ve
.PP
showing that the OID "newoid1" has been added as "*******.1".
.SH ENVIRONMENT
.IX Header "ENVIRONMENT"
.IP \fBOPENSSL_CONF\fR 4
.IX Item "OPENSSL_CONF"
The path to the config file.
Ignored in set-user-ID and set-group-ID programs.
.IP \fBOPENSSL_ENGINES\fR 4
.IX Item "OPENSSL_ENGINES"
The path to the engines directory.
Ignored in set-user-ID and set-group-ID programs.
.SH BUGS
.IX Header "BUGS"
Currently there is no way to include characters using the octal \fB\ennn\fR
form. Strings are all null terminated so nulls cannot form part of
the value.
.PP
The escaping isn't quite right: if you want to use sequences like \fB\en\fR
you can't use any quote escaping on the same line.
.PP
Files are loaded in a single pass. This means that a variable expansion
will only work if the variables referenced are defined earlier in the
file.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBx509\fR\|(1), \fBreq\fR\|(1), \fBca\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
