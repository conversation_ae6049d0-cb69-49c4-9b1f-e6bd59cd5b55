.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_CTLOG_LIST_FILE 3"
.TH SSL_CTX_SET_CTLOG_LIST_FILE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_default_ctlog_list_file, SSL_CTX_set_ctlog_list_file \-
load a Certificate Transparency log list from a file
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_set_default_ctlog_list_file(SSL_CTX *ctx);
\& int SSL_CTX_set_ctlog_list_file(SSL_CTX *ctx, const char *path);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_default_ctlog_list_file()\fR loads a list of Certificate Transparency
(CT) logs from the default file location, "ct_log_list.cnf", found in the
directory where OpenSSL is installed.
.PP
\&\fBSSL_CTX_set_ctlog_list_file()\fR loads a list of CT logs from a specific path.
See \fBCTLOG_STORE_new\fR\|(3) for the file format.
.SH NOTES
.IX Header "NOTES"
These functions will not clear the existing CT log list \- it will be appended
to. To replace the existing list, use SSL_CTX_set0_ctlog_store first.
.PP
If an error occurs whilst parsing a particular log entry in the file, that log
entry will be skipped.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_default_ctlog_list_file()\fR and \fBSSL_CTX_set_ctlog_list_file()\fR
return 1 if the log list is successfully loaded, and 0 if an error occurs. In
the case of an error, the log list may have been partially loaded.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_set_ct_validation_callback\fR\|(3),
\&\fBCTLOG_STORE_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
