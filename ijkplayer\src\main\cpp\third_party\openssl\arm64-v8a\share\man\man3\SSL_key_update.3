.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_KEY_UPDATE 3"
.TH SSL_KEY_UPDATE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_key_update,
SSL_get_key_update_type,
SSL_renegotiate,
SSL_renegotiate_abbreviated,
SSL_renegotiate_pending
\&\- initiate and obtain information about updating connection keys
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_key_update(SSL *s, int updatetype);
\& int SSL_get_key_update_type(const SSL *s);
\&
\& int SSL_renegotiate(SSL *s);
\& int SSL_renegotiate_abbreviated(SSL *s);
\& int SSL_renegotiate_pending(const SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_key_update()\fR schedules an update of the keys for the current TLS connection.
If the \fBupdatetype\fR parameter is set to \fBSSL_KEY_UPDATE_NOT_REQUESTED\fR then
the sending keys for this connection will be updated and the peer will be
informed of the change. If the \fBupdatetype\fR parameter is set to
\&\fBSSL_KEY_UPDATE_REQUESTED\fR then the sending keys for this connection will be
updated and the peer will be informed of the change along with a request for the
peer to additionally update its sending keys. It is an error if \fBupdatetype\fR is
set to \fBSSL_KEY_UPDATE_NONE\fR.
.PP
\&\fBSSL_key_update()\fR must only be called after the initial handshake has been
completed and TLSv1.3 has been negotiated. The key update will not take place
until the next time an IO operation such as \fBSSL_read_ex()\fR or \fBSSL_write_ex()\fR
takes place on the connection. Alternatively \fBSSL_do_handshake()\fR can be called to
force the update to take place immediately.
.PP
\&\fBSSL_get_key_update_type()\fR can be used to determine whether a key update
operation has been scheduled but not yet performed. The type of the pending key
update operation will be returned if there is one, or SSL_KEY_UPDATE_NONE
otherwise.
.PP
\&\fBSSL_renegotiate()\fR and \fBSSL_renegotiate_abbreviated()\fR should only be called for
connections that have negotiated TLSv1.2 or less. Calling them on any other
connection will result in an error.
.PP
When called from the client side, \fBSSL_renegotiate()\fR schedules a completely new
handshake over an existing SSL/TLS connection. The next time an IO operation
such as \fBSSL_read_ex()\fR or \fBSSL_write_ex()\fR takes place on the connection a check
will be performed to confirm that it is a suitable time to start a
renegotiation. If so, then it will be initiated immediately. OpenSSL will not
attempt to resume any session associated with the connection in the new
handshake.
.PP
When called from the client side, \fBSSL_renegotiate_abbreviated()\fR works in the
same was as \fBSSL_renegotiate()\fR except that OpenSSL will attempt to resume the
session associated with the current connection in the new handshake.
.PP
When called from the server side, \fBSSL_renegotiate()\fR and
\&\fBSSL_renegotiate_abbreviated()\fR behave identically. They both schedule a request
for a new handshake to be sent to the client. The next time an IO operation is
performed then the same checks as on the client side are performed and then, if
appropriate, the request is sent. The client may or may not respond with a new
handshake and it may or may not attempt to resume an existing session. If
a new handshake is started then this will be handled transparently by calling
any OpenSSL IO function.
.PP
If an OpenSSL client receives a renegotiation request from a server then again
this will be handled transparently through calling any OpenSSL IO function. For
a TLS connection the client will attempt to resume the current session in the
new handshake. For historical reasons, DTLS clients will not attempt to resume
the session in the new handshake.
.PP
The \fBSSL_renegotiate_pending()\fR function returns 1 if a renegotiation or
renegotiation request has been scheduled but not yet acted on, or 0 otherwise.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_key_update()\fR, \fBSSL_renegotiate()\fR and \fBSSL_renegotiate_abbreviated()\fR return 1
on success or 0 on error.
.PP
\&\fBSSL_get_key_update_type()\fR returns the update type of the pending key update
operation or SSL_KEY_UPDATE_NONE if there is none.
.PP
\&\fBSSL_renegotiate_pending()\fR returns 1 if a renegotiation or renegotiation request
has been scheduled but not yet acted on, or 0 otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_read_ex\fR\|(3),
\&\fBSSL_write_ex\fR\|(3),
\&\fBSSL_do_handshake\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_key_update()\fR and \fBSSL_get_key_update_type()\fR functions were added in
OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
