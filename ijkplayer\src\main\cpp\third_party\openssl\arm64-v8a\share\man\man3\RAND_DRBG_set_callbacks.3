.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_DRBG_SET_CALLBACKS 3"
.TH RAND_DRBG_SET_CALLBACKS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_DRBG_set_callbacks,
RAND_DRBG_get_entropy_fn,
RAND_DRBG_cleanup_entropy_fn,
RAND_DRBG_get_nonce_fn,
RAND_DRBG_cleanup_nonce_fn
\&\- set callbacks for reseeding
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand_drbg.h>
\&
\&
\& int RAND_DRBG_set_callbacks(RAND_DRBG *drbg,
\&                             RAND_DRBG_get_entropy_fn get_entropy,
\&                             RAND_DRBG_cleanup_entropy_fn cleanup_entropy,
\&                             RAND_DRBG_get_nonce_fn get_nonce,
\&                             RAND_DRBG_cleanup_nonce_fn cleanup_nonce);
.Ve
.SS "Callback Functions"
.IX Subsection "Callback Functions"
.Vb 6
\& typedef size_t (*RAND_DRBG_get_entropy_fn)(
\&                       RAND_DRBG *drbg,
\&                       unsigned char **pout,
\&                       int entropy,
\&                       size_t min_len, size_t max_len,
\&                       int prediction_resistance);
\&
\& typedef void (*RAND_DRBG_cleanup_entropy_fn)(
\&                     RAND_DRBG *drbg,
\&                     unsigned char *out, size_t outlen);
\&
\& typedef size_t (*RAND_DRBG_get_nonce_fn)(
\&                       RAND_DRBG *drbg,
\&                       unsigned char **pout,
\&                       int entropy,
\&                       size_t min_len, size_t max_len);
\&
\& typedef void (*RAND_DRBG_cleanup_nonce_fn)(
\&                     RAND_DRBG *drbg,
\&                     unsigned char *out, size_t outlen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBRAND_DRBG_set_callbacks()\fR sets the callbacks for obtaining fresh entropy and
the nonce when reseeding the given \fBdrbg\fR.
The callback functions are implemented and provided by the caller.
Their parameter lists need to match the function prototypes above.
.PP
Setting the callbacks is allowed only if the DRBG has not been initialized yet.
Otherwise, the operation will fail.
To change the settings for one of the three shared DRBGs it is necessary to call
\&\fBRAND_DRBG_uninstantiate()\fR first.
.PP
The \fBget_entropy\fR() callback is called by the \fBdrbg\fR when it requests fresh
random input.
It is expected that the callback allocates and fills a random buffer of size
\&\fBmin_len\fR <= size <= \fBmax_len\fR (in bytes) which contains at least \fBentropy\fR
bits of randomness.
The \fBprediction_resistance\fR flag indicates whether the reseeding was
triggered by a prediction resistance request.
.PP
The buffer's address is to be returned in *\fBpout\fR and the number of collected
randomness bytes as return value.
.PP
If the callback fails to acquire at least \fBentropy\fR bits of randomness,
it must indicate an error by returning a buffer length of 0.
.PP
If \fBprediction_resistance\fR was requested and the random source of the DRBG
does not satisfy the conditions requested by [NIST SP 800\-90C], then
it must also indicate an error by returning a buffer length of 0.
See NOTES section for more details.
.PP
The \fBcleanup_entropy\fR() callback is called from the \fBdrbg\fR to clear and
free the buffer allocated previously by \fBget_entropy()\fR.
The values \fBout\fR and \fBoutlen\fR are the random buffer's address and length,
as returned by the \fBget_entropy()\fR callback.
.PP
The \fBget_nonce\fR() and \fBcleanup_nonce\fR() callbacks are used to obtain a nonce
and free it again. A nonce is only required for instantiation (not for reseeding)
and only in the case where the DRBG uses a derivation function.
The callbacks are analogous to \fBget_entropy()\fR and \fBcleanup_entropy()\fR,
except for the missing prediction_resistance flag.
.PP
If the derivation function is disabled, then no nonce is used for instantiation,
and the \fBget_nonce\fR() and \fBcleanup_nonce\fR() callbacks can be omitted by
setting them to NULL.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_DRBG_set_callbacks()\fR return 1 on success, and 0 on failure
.SH NOTES
.IX Header "NOTES"
It is important that \fBcleanup_entropy\fR() and \fBcleanup_nonce\fR() clear the buffer
contents safely before freeing it, in order not to leave sensitive information
about the DRBG's state in memory.
.PP
A request for prediction resistance can only be satisfied by pulling fresh
entropy from one of the approved entropy sources listed in section 5.5.2 of
[NIST SP 800\-90C].
Since the default implementation of the get_entropy callback does not have access
to such an approved entropy source, a request for prediction resistance will
always fail.
In other words, prediction resistance is currently not supported yet by the DRBG.
.PP
The derivation function is disabled during initialization by calling the
\&\fBRAND_DRBG_set()\fR function with the RAND_DRBG_FLAG_CTR_NO_DF flag.
For more information on the derivation function and when it can be omitted,
see [NIST SP 800\-90A Rev. 1]. Roughly speaking it can be omitted if the random
source has "full entropy", i.e., contains 8 bits of entropy per byte.
.PP
Even if a nonce is required, the \fBget_nonce\fR() and \fBcleanup_nonce\fR()
callbacks can be omitted by setting them to NULL.
In this case the DRBG will automatically request an extra amount of entropy
(using the \fBget_entropy\fR() and \fBcleanup_entropy\fR() callbacks) which it will
utilize for the nonce, following the recommendations of [NIST SP 800\-90A Rev. 1],
section 8.6.7.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND_DRBG_new\fR\|(3),
\&\fBRAND_DRBG_reseed\fR\|(3),
\&\fBRAND_DRBG\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The RAND_DRBG functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
