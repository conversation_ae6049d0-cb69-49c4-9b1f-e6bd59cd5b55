.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_ALL_ASYNC_FDS 3"
.TH SSL_GET_ALL_ASYNC_FDS 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_waiting_for_async,
SSL_get_all_async_fds,
SSL_get_changed_async_fds
\&\- manage asynchronous operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/async.h>
\& #include <openssl/ssl.h>
\&
\& int SSL_waiting_for_async(SSL *s);
\& int SSL_get_all_async_fds(SSL *s, OSSL_ASYNC_FD *fd, size_t *numfds);
\& int SSL_get_changed_async_fds(SSL *s, OSSL_ASYNC_FD *addfd, size_t *numaddfds,
\&                               OSSL_ASYNC_FD *delfd, size_t *numdelfds);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_waiting_for_async()\fR determines whether an SSL connection is currently
waiting for asynchronous operations to complete (see the SSL_MODE_ASYNC mode in
\&\fBSSL_CTX_set_mode\fR\|(3)).
.PP
\&\fBSSL_get_all_async_fds()\fR returns a list of file descriptor which can be used in a
call to \fBselect()\fR or \fBpoll()\fR to determine whether the current asynchronous
operation has completed or not. A completed operation will result in data
appearing as "read ready" on the file descriptor (no actual data should be read
from the file descriptor). This function should only be called if the SSL object
is currently waiting for asynchronous work to complete (i.e.
SSL_ERROR_WANT_ASYNC has been received \- see \fBSSL_get_error\fR\|(3)). Typically the
list will only contain one file descriptor. However, if multiple asynchronous
capable engines are in use then more than one is possible. The number of file
descriptors returned is stored in \fB*numfds\fR and the file descriptors themselves
are in \fB*fds\fR. The \fBfds\fR parameter may be NULL in which case no file
descriptors are returned but \fB*numfds\fR is still populated. It is the callers
responsibility to ensure sufficient memory is allocated at \fB*fds\fR so typically
this function is called twice (once with a NULL \fBfds\fR parameter and once
without).
.PP
\&\fBSSL_get_changed_async_fds()\fR returns a list of the asynchronous file descriptors
that have been added and a list that have been deleted since the last
SSL_ERROR_WANT_ASYNC was received (or since the SSL object was created if no
SSL_ERROR_WANT_ASYNC has been received). Similar to \fBSSL_get_all_async_fds()\fR it
is the callers responsibility to ensure that \fB*addfd\fR and \fB*delfd\fR have
sufficient memory allocated, although they may be NULL. The number of added fds
and the number of deleted fds are stored in \fB*numaddfds\fR and \fB*numdelfds\fR
respectively.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_waiting_for_async()\fR will return 1 if the current SSL operation is waiting
for an async operation to complete and 0 otherwise.
.PP
\&\fBSSL_get_all_async_fds()\fR and \fBSSL_get_changed_async_fds()\fR return 1 on success or
0 on error.
.SH NOTES
.IX Header "NOTES"
On Windows platforms the openssl/async.h header is dependent on some
of the types customarily made available by including windows.h. The
application developer is likely to require control over when the latter
is included, commonly as one of the first included headers. Therefore,
it is defined as an application developer's responsibility to include
windows.h prior to async.h.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_get_error\fR\|(3), \fBSSL_CTX_set_mode\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_waiting_for_async()\fR, \fBSSL_get_all_async_fds()\fR
and \fBSSL_get_changed_async_fds()\fR functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
