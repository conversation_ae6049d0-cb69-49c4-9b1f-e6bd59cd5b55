.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_DEFAULT_PASSWD_CB 3"
.TH SSL_CTX_SET_DEFAULT_PASSWD_CB 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_default_passwd_cb, SSL_CTX_set_default_passwd_cb_userdata,
SSL_CTX_get_default_passwd_cb, SSL_CTX_get_default_passwd_cb_userdata,
SSL_set_default_passwd_cb, SSL_set_default_passwd_cb_userdata,
SSL_get_default_passwd_cb, SSL_get_default_passwd_cb_userdata \- set or
get passwd callback for encrypted PEM file handling
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_default_passwd_cb(SSL_CTX *ctx, pem_password_cb *cb);
\& void SSL_CTX_set_default_passwd_cb_userdata(SSL_CTX *ctx, void *u);
\& pem_password_cb *SSL_CTX_get_default_passwd_cb(SSL_CTX *ctx);
\& void *SSL_CTX_get_default_passwd_cb_userdata(SSL_CTX *ctx);
\&
\& void SSL_set_default_passwd_cb(SSL *s, pem_password_cb *cb);
\& void SSL_set_default_passwd_cb_userdata(SSL *s, void *u);
\& pem_password_cb *SSL_get_default_passwd_cb(SSL *s);
\& void *SSL_get_default_passwd_cb_userdata(SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_default_passwd_cb()\fR sets the default password callback called
when loading/storing a PEM certificate with encryption.
.PP
\&\fBSSL_CTX_set_default_passwd_cb_userdata()\fR sets a pointer to userdata, \fBu\fR,
which will be provided to the password callback on invocation.
.PP
\&\fBSSL_CTX_get_default_passwd_cb()\fR returns a function pointer to the password
callback currently set in \fBctx\fR. If no callback was explicitly set, the
NULL pointer is returned.
.PP
\&\fBSSL_CTX_get_default_passwd_cb_userdata()\fR returns a pointer to the userdata
currently set in \fBctx\fR. If no userdata was explicitly set, the NULL pointer
is returned.
.PP
\&\fBSSL_set_default_passwd_cb()\fR, \fBSSL_set_default_passwd_cb_userdata()\fR,
\&\fBSSL_get_default_passwd_cb()\fR and \fBSSL_get_default_passwd_cb_userdata()\fR perform
the same function as their SSL_CTX counterparts, but using an SSL object.
.PP
The password callback, which must be provided by the application, hands back the
password to be used during decryption.
On invocation a pointer to userdata
is provided. The function must store the password into the provided buffer
\&\fBbuf\fR which is of size \fBsize\fR. The actual length of the password must
be returned to the calling function. \fBrwflag\fR indicates whether the
callback is used for reading/decryption (rwflag=0) or writing/encryption
(rwflag=1).
For more details, see \fBpem_password_cb\fR\|(3).
.SH NOTES
.IX Header "NOTES"
When loading or storing private keys, a password might be supplied to
protect the private key. The way this password can be supplied may depend
on the application. If only one private key is handled, it can be practical
to have the callback handle the password dialog interactively. If several
keys have to be handled, it can be practical to ask for the password once,
then keep it in memory and use it several times. In the last case, the
password could be stored into the userdata storage and the
callback only returns the password already stored.
.PP
When asking for the password interactively, the callback can use
\&\fBrwflag\fR to check, whether an item shall be encrypted (rwflag=1).
In this case the password dialog may ask for the same password twice
for comparison in order to catch typos, that would make decryption
impossible.
.PP
Other items in PEM formatting (certificates) can also be encrypted, it is
however not usual, as certificate information is considered public.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions do not provide diagnostic information.
.SH EXAMPLES
.IX Header "EXAMPLES"
The following example returns the password provided as userdata to the
calling function. The password is considered to be a '\e0' terminated
string. If the password does not fit into the buffer, the password is
truncated.
.PP
.Vb 6
\& int my_cb(char *buf, int size, int rwflag, void *u)
\& {
\&     strncpy(buf, (char *)u, size);
\&     buf[size \- 1] = \*(Aq\e0\*(Aq;
\&     return strlen(buf);
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_use_certificate\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBSSL_CTX_get_default_passwd_cb()\fR, \fBSSL_CTX_get_default_passwd_cb_userdata()\fR,
\&\fBSSL_set_default_passwd_cb()\fR and \fBSSL_set_default_passwd_cb_userdata()\fR were
added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
