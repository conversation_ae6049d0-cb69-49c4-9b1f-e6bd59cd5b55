import { IjkMediaPlayer } from "@ohos/ijkplayer";
import type { OnPreparedListener } from "@ohos/ijkplayer";
import type { OnVideoSizeChangedListener } from "@ohos/ijkplayer";
import type { OnCompletionListener } from "@ohos/ijkplayer";
import type { OnErrorListener } from "@ohos/ijkplayer";
import { LogUtils } from "@ohos/ijkplayer";
import fs from '@ohos.file.fs';
import { BusinessError } from '@ohos.base';
import util from '@ohos.util';
import buffer from '@ohos.buffer';
import resourceManager from '@ohos.resourceManager';
import { FpsBufferUtils } from '../model/FpsBufferManager';
interface optionsFormat{
  width?: number,
  height?: number,
  pixelFormat?: string,
  framerate?: number
}
@Component
export struct Player {
  @State message: string = 'Stream Player';
  @State isPlaying: boolean = false;
  @State aspRatio: number = 16 / 9; // 默认宽高比
  @State hasStreamData: boolean = false;
  private mContext: object | null = null;
  private mIjkMediaPlayer: IjkMediaPlayer | null = null;
  private tempFilePath: string = '';
  private streamBuffer: ArrayBuffer | null = null;
  private innerResource: Resource = $rawfile('videoTest.mp4');
  @StorageLink ('FpsCount') @Watch('onFpsCount') PlayerFpsCount:number=0
  private FpsBuffer:ArrayBuffer|undefined|null=null
  async onFpsCount(){
    console.log('player_帧数在变化',this.PlayerFpsCount)
    this.FpsBuffer=FpsBufferUtils.getFpsBuffer();
    if(this.FpsBuffer){
      console.log('player_this.FpsBuffer',this.FpsBuffer)
      console.log('player_this.FpsBuffer长度',this.FpsBuffer.byteLength)
      console.log('进行处理图像')
      console.log('进行写入')
      // const result = await ImageProcessingUtils.quickProcess(this.FpsBuffer);
      // console.log(result ? '成功处理图像' : '失败处理图像');
      // xclient命令执行
      // this.publishCommandSub()
    }else{
      console.log('this.FpsBuffer为空||undefined')
    }
  }
  build() {
    Column() {
      Text(this.message)
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 20 })

      // 视频播放区域
      XComponent({
        id: 'xcomponentId',
        type: 'surface',
        libraryname: 'ijkplayer_napi'
      })
        .onLoad((context) => {
          this.mContext = context || null;
          this.initPlayer();
        })
        .onDestroy(() => {
          this.releasePlayer();
        })
        .width(200)
        .height(200)
        .margin({top:20})
        .aspectRatio(this.aspRatio)
        .backgroundColor(Color.Black)
      // 控制按钮
      Column() {
        Row() {
          Button('加载流数据')
            .onClick(() => {
              this.loadStreamData();
            })
            .margin({ right: 10 })
            .enabled(!this.hasStreamData)

          Button('清除数据')
            .onClick(() => {
              this.clearStreamData();
            })
            .enabled(this.hasStreamData)
        }
        .margin({ bottom: 10 })
        Row() {
          Button(this.isPlaying ? '暂停' : '播放')
            .onClick(() => {
              if (this.isPlaying) {
                this.pausePlay();
              } else {
                this.startPlayWithStream();
              }
            })
            .margin({ right: 10 })
            .enabled(this.hasStreamData)

          Button('停止')
            .onClick(() => {
              this.stopPlay();
            })
            .enabled(this.hasStreamData)
        }
      }
      .margin({ top: 20 })
    }
    .width('100%')
    .height('100%')
    .padding(20)
  }

  private initPlayer() {
    if (!this.mContext) {
      LogUtils.getInstance().LOGI("Context is null, cannot initialize player");
      return;
    }

    // 创建播放器实例
    this.mIjkMediaPlayer = IjkMediaPlayer.getInstance();

    // 设置XComponent上下文
    this.mIjkMediaPlayer.setContext(this.mContext, "xcomponentId");

    // 设置调试模式
    this.mIjkMediaPlayer.setDebug(true);

    // 初始化配置
    this.mIjkMediaPlayer.native_setup();

    // 设置播放器选项
    this.setupPlayerOptions();

    // 设置监听器
    this.setupListeners();

    LogUtils.getInstance().LOGI("Player initialized successfully");
  }



  private setupPlayerOptions() {
    if (!this.mIjkMediaPlayer) return;

    // YUV 原始视频格式设置
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "video_size", "1280x960");
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "pixel_format", "yuv420p");
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "framerate", "25");

    // 使用精确寻帧
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "enable-accurate-seek", "1");

    // 预读数据的缓冲区大小
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-buffer-size", "102400");

    // 停止预读的最小帧数
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "min-frames", "100");

    // 启动预加载
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", "1");

    // 设置无缓冲
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "packet-buffering", "0");

    // 跳帧处理
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "framedrop", "5");

    // 最大缓冲时间3秒
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max_cached_duration", "3000");

    // 无限制收流
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "infbuf", "1");

    // 屏幕常亮
    this.mIjkMediaPlayer.setScreenOnWhilePlaying(true);

    // 设置超时
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "timeout", "10000000");
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "connect_timeout", "10000000");
  }

  private setupListeners() {
    if (!this.mIjkMediaPlayer) return;

    const that = this;

    // 视频尺寸变化监听
    let mOnVideoSizeChangedListener: OnVideoSizeChangedListener = {
      onVideoSizeChanged(width: number, height: number, sar_num: number, sar_den: number) {
        that.aspRatio = width / height;
        LogUtils.getInstance().LOGI(`Video size changed: ${width}x${height}, aspect ratio: ${that.aspRatio}`);
      }
    };
    this.mIjkMediaPlayer.setOnVideoSizeChangedListener(mOnVideoSizeChangedListener);

    // 准备完成监听
    let mOnPreparedListener: OnPreparedListener = {
      onPrepared() {
        LogUtils.getInstance().LOGI("Player prepared, ready to play");
        that.mIjkMediaPlayer?.start();
        that.isPlaying = true;
      }
    };
    this.mIjkMediaPlayer.setOnPreparedListener(mOnPreparedListener);

    // 播放完成监听
    let mOnCompletionListener: OnCompletionListener = {
      onCompletion() {
        LogUtils.getInstance().LOGI("Playback completed");
        that.isPlaying = false;
      }
    };
    this.mIjkMediaPlayer.setOnCompletionListener(mOnCompletionListener);

    // 错误监听
    let mOnErrorListener: OnErrorListener = {
      onError(what: number, extra: number) {
        LogUtils.getInstance().LOGI(`Player error: what=${what}, extra=${extra}`);
        that.isPlaying = false;
      }
    };
    this.mIjkMediaPlayer.setOnErrorListener(mOnErrorListener);

    // 设置消息监听器
    this.mIjkMediaPlayer.setMessageListener();
  }

  private startPlay() {
    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    if (this.isPlaying) {
      LogUtils.getInstance().LOGI("Already playing");
      return;
    }

    this.mIjkMediaPlayer.start();
    this.isPlaying = true;
    LogUtils.getInstance().LOGI("Started playback");
  }

  private pausePlay() {
    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    if (!this.isPlaying) {
      LogUtils.getInstance().LOGI("Already paused");
      return;
    }

    this.mIjkMediaPlayer.pause();
    this.isPlaying = false;
    LogUtils.getInstance().LOGI("Paused playback");
  }

  private stopPlay() {
    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    this.mIjkMediaPlayer.stop();
    this.isPlaying = false;
    LogUtils.getInstance().LOGI("Stopped playback");
  }

  private releasePlayer() {
    if (this.mIjkMediaPlayer) {
      this.mIjkMediaPlayer.release();
      this.mIjkMediaPlayer = null;
      this.isPlaying = false;
      LogUtils.getInstance().LOGI("Released player resources");
    }
  }

  // 模拟加载ArrayBuffer流数据
  private loadStreamData() {
    try {
      // 这里模拟创建一个简单的测试视频数据
      // 在实际应用中，这里应该是从网络或其他来源获取的ArrayBuffer
      // this.createTestStreamData();
      this.setYuvStreamData(this.FpsBuffer)
      LogUtils.getInstance().LOGI("Stream data loaded successfully");
      this.message = 'Stream Player - 数据已加载';
      this.hasStreamData = true;
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to load stream data: ${error}`);
      this.message = 'Stream Player - 数据加载失败';
    }
  }

  // 创建测试流数据（实际应用中替换为真实的ArrayBuffer数据）
  private async  createTestStreamData() {
    // 创建一个测试用的ArrayBuffer
    // 注意：这里只是示例，实际应用中应该使用真实的视频流数据
    const testData = "Test video stream data";
    // const encoder = new TextEncoder();
    this.streamBuffer = buffer.from(testData).buffer

    // 为了演示YUV播放，我们使用YUV文件
    this.setupYuvVideoSource();
  }

  // 专门处理YUV文件的方法
  private async setupYuvVideoSource() {
    try {
      // 获取YUV文件数据
      const rawFileData = await getContext(this).resourceManager.getRawFileContent('065(49).yuv');

      // 获取应用缓存目录
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      const tempFilePath = `${cacheDir}/065(49).yuv`;

      // 写入临时文件
      const file = fs.openSync(tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      fs.writeSync(file.fd, rawFileData.buffer);
      fs.closeSync(file);

      console.log('tempFilePath', tempFilePath);

      // 设置数据源
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setDataSource(tempFilePath);
        LogUtils.getInstance().LOGI(`Set YUV video source: ${tempFilePath}`);
      }

      // 保存临时文件路径，用于清理
      this.tempFilePath = tempFilePath;

    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to setup YUV video source: ${error}`);
    }
  }

  // 处理真实的ArrayBuffer流数据的方法
  public async setStreamData(buffer: ArrayBuffer): Promise<boolean> {
    try {
      if (!buffer || buffer.byteLength === 0) {
        LogUtils.getInstance().LOGI("Invalid ArrayBuffer data");
        return false;
      }

      this.streamBuffer = buffer;

      // 将ArrayBuffer写入临时文件
      const filePath = await this.writeArrayBufferToFile(buffer);

      // 设置数据源
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setDataSource(filePath);
        LogUtils.getInstance().LOGI(`Set stream data source: ${filePath}`);
      }

      this.hasStreamData = true;
      this.message = `Stream Player - 已加载 ${buffer.byteLength} 字节数据`;

      return true;
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to set stream data: ${error}`);
      return false;
    }
  }

  // 设置测试视频源（用于演示）
  private async setupTestVideoSource() {
    try {
      await this.copyRawFileToTemp();
      // // // 方法1：获取 rawfile 的文件描述符
      // const rawFd = await getContext(this).resourceManager.getRawFd('videoTest.mp4');
      // console.log('rawFd', rawFd);
      //
      // // 构造文件描述符路径
      // const fdPath = `fd://${rawFd.fd}:${rawFd.offset}:${rawFd.length}`;
      //
      // if (this.mIjkMediaPlayer) {
      //   this.mIjkMediaPlayer.setDataSource(fdPath);
      //   LogUtils.getInstance().LOGI(`Set test video source: ${fdPath}`);
      // }
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to setup video source: ${error}`);
      // 备用方案：复制到临时文件
      // await this.copyRawFileToTemp();
    }
  }

  // 备用方案：将 rawfile 复制到临时文件
  private async copyRawFileToTemp(): Promise<void> {
    try {
      // 获取 rawfile 数据
      const rawFileData = await getContext(this).resourceManager.getRawFileContent('videoTest.mp4');
      // const rawFileData = await getContext(this).resourceManager.getRawFileContent('065(49).yuv');

      // 获取应用缓存目录
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      const tempFilePath = `${cacheDir}/temp_video.mp4`;
      // const tempFilePath = `${cacheDir}/065(49).yuv`;

      // 写入临时文件
      const file = fs.openSync(tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      fs.writeSync(file.fd, rawFileData.buffer);
      fs.closeSync(file);
      console.log('tempFilePath',tempFilePath)
      // 设置数据源
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setDataSource(tempFilePath);
        LogUtils.getInstance().LOGI(`Set test video source (temp file): ${tempFilePath}`);
      }

      // 保存临时文件路径，用于清理
      this.tempFilePath = tempFilePath;

    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to copy rawfile to temp: ${error}`);
    }
  }

  // 专门处理YUV格式的ArrayBuffer数据
  public async setYuvStreamData(buffer: ArrayBuffer, width: number = 1280, height: number = 960, pixelFormat: string = "yuv420p"): Promise<boolean> {
    try {
      if (!buffer || buffer.byteLength === 0) {
        LogUtils.getInstance().LOGI("Invalid YUV ArrayBuffer data");
        return false;
      }

      // 设置YUV格式参数
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "video_size", `${width}x${height}`);
        this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "pixel_format", pixelFormat);
        this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "framerate", "25");
      }

      this.streamBuffer = buffer;

      // 将YUV ArrayBuffer写入临时文件
      const filePath = await this.writeYuvArrayBufferToFile(buffer);

      // 设置数据源
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setDataSource(filePath);
        LogUtils.getInstance().LOGI(`Set YUV stream data source: ${filePath}`);
      }

      this.hasStreamData = true;
      this.message = `Stream Player - 已加载YUV ${buffer.byteLength} 字节数据 (${width}x${height})`;

      return true;
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to set YUV stream data: ${error}`);
      return false;
    }
  }

  // 将YUV ArrayBuffer写入临时文件
  private async writeYuvArrayBufferToFile(buffer: ArrayBuffer): Promise<string> {
    try {
      // 获取应用缓存目录
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      const fileName = `yuv_stream_${Date.now()}.yuv`;
      const tempFilePath = `${cacheDir}/${fileName}`;

      // 将ArrayBuffer写入文件
      const file = fs.openSync(tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      const writeLen = fs.writeSync(file.fd, buffer);
      fs.closeSync(file);

      LogUtils.getInstance().LOGI(`Written YUV ${writeLen} bytes to ${tempFilePath}`);
      this.tempFilePath = tempFilePath;
      return tempFilePath;
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to write YUV ArrayBuffer to file: ${error}`);
      throw new Error(`Failed to write YUV ArrayBuffer to file: ${error}`);
    }
  }

  // 智能检测ArrayBuffer格式并播放
  public async setSmartStreamData(buffer: ArrayBuffer, options?:optionsFormat ): Promise<boolean> {
    try {
      if (!buffer || buffer.byteLength === 0) {
        LogUtils.getInstance().LOGI("Invalid ArrayBuffer data");
        return false;
      }

      // 检查是否为标准视频格式（通过文件头）
      const header = new Uint8Array(buffer.slice(0, 12));

      // MP4 文件头检测
      if (this.isMP4Format(header)) {
        LogUtils.getInstance().LOGI("Detected MP4 format");
        return await this.setStreamData(buffer);
      }

      // AVI 文件头检测
      if (this.isAVIFormat(header)) {
        LogUtils.getInstance().LOGI("Detected AVI format");
        return await this.setStreamData(buffer);
      }

      // 如果不是标准格式，假设为YUV原始数据
      LogUtils.getInstance().LOGI("Assuming YUV raw format");
      const width = options?.width || 1280;
      const height = options?.height || 960;
      const pixelFormat = options?.pixelFormat || "yuv420p";

      return await this.setYuvStreamData(buffer, width, height, pixelFormat);

    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to set smart stream data: ${error}`);
      return false;
    }
  }

  // 检测MP4格式
  private isMP4Format(header: Uint8Array): boolean {
    // MP4文件通常以 "ftyp" 开头（在偏移4-7字节）
    return header[4] === 0x66 && header[5] === 0x74 &&
      header[6] === 0x79 && header[7] === 0x70;
  }

  // 检测AVI格式
  private isAVIFormat(header: Uint8Array): boolean {
    // AVI文件以 "RIFF" 开头，然后是 "AVI "
    return header[0] === 0x52 && header[1] === 0x49 &&
      header[2] === 0x46 && header[3] === 0x46 &&
      header[8] === 0x41 && header[9] === 0x56 &&
      header[10] === 0x49 && header[11] === 0x20;
  }

  // 将ArrayBuffer写入临时文件的方法（实际应用中使用）
  private async writeArrayBufferToFile(buffer: ArrayBuffer): Promise<string> {
    try {
      // 获取应用缓存目录
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      const fileName = `stream_${Date.now()}.mp4`;
      this.tempFilePath = `${cacheDir}/${fileName}`;

      // 将ArrayBuffer写入文件
      const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      const writeLen = fs.writeSync(file.fd, buffer);
      fs.closeSync(file);

      LogUtils.getInstance().LOGI(`Written ${writeLen} bytes to ${this.tempFilePath}`);
      return this.tempFilePath;
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to write ArrayBuffer to file: ${error}`);
      throw new Error(`Failed to write ArrayBuffer to file: ${error}`);
    }
  }

  // 使用流数据开始播放
  private startPlayWithStream() {
    if (!this.hasStreamData) {
      LogUtils.getInstance().LOGI("No stream data available");
      return;
    }

    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    try {
      // 准备播放
      this.mIjkMediaPlayer.prepareAsync();
      LogUtils.getInstance().LOGI("Started preparing stream for playback");
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to start playback: ${error}`);
    }
  }

  // 清除流数据
  private clearStreamData() {
    this.streamBuffer = null;
    this.hasStreamData = false;
    this.message = 'Stream Player';

    // 清理临时文件
    if (this.tempFilePath) {
      try {
        if (fs.accessSync(this.tempFilePath)) {
          fs.unlinkSync(this.tempFilePath);
          LogUtils.getInstance().LOGI(`Deleted temp file: ${this.tempFilePath}`);
        }
      } catch (error) {
        LogUtils.getInstance().LOGI(`Failed to delete temp file: ${error}`);
      }
      this.tempFilePath = '';
    }

    // 重置播放器
    if (this.mIjkMediaPlayer) {
      this.mIjkMediaPlayer.reset();
    }

    LogUtils.getInstance().LOGI("Stream data cleared");
  }
}