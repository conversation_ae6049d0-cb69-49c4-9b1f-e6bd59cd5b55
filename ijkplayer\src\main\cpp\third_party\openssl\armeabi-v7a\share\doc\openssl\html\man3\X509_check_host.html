<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_check_host</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_check_host, X509_check_email, X509_check_ip, X509_check_ip_asc - X.509 certificate matching</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509v3.h&gt;

int X509_check_host(X509 *, const char *name, size_t namelen,
                    unsigned int flags, char **peername);
int X509_check_email(X509 *, const char *address, size_t addresslen,
                     unsigned int flags);
int X509_check_ip(X509 *, const unsigned char *address, size_t addresslen,
                  unsigned int flags);
int X509_check_ip_asc(X509 *, const char *address, unsigned int flags);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The certificate matching functions are used to check whether a certificate matches a given hostname, email address, or IP address. The validity of the certificate and its trust level has to be checked by other means.</p>

<p>X509_check_host() checks if the certificate Subject Alternative Name (SAN) or Subject CommonName (CN) matches the specified hostname, which must be encoded in the preferred name syntax described in section 3.5 of RFC 1034. By default, wildcards are supported and they match only in the left-most label; but they may match part of that label with an explicit prefix or suffix. For example, by default, the host <b>name</b> &quot;www.example.com&quot; would match a certificate with a SAN or CN value of &quot;*.example.com&quot;, &quot;w*.example.com&quot; or &quot;*w.example.com&quot;.</p>

<p>Per section 6.4.2 of RFC 6125, <b>name</b> values representing international domain names must be given in A-label form. The <b>namelen</b> argument must be the number of characters in the name string or zero in which case the length is calculated with strlen(<b>name</b>). When <b>name</b> starts with a dot (e.g. &quot;.example.com&quot;), it will be matched by a certificate valid for any sub-domain of <b>name</b>, (see also <b>X509_CHECK_FLAG_SINGLE_LABEL_SUBDOMAINS</b> below).</p>

<p>When the certificate is matched, and <b>peername</b> is not NULL, a pointer to a copy of the matching SAN or CN from the peer certificate is stored at the address passed in <b>peername</b>. The application is responsible for freeing the peername via OPENSSL_free() when it is no longer needed.</p>

<p>X509_check_email() checks if the certificate matches the specified email <b>address</b>. Only the mailbox syntax of RFC 822 is supported, comments are not allowed, and no attempt is made to normalize quoted characters. The <b>addresslen</b> argument must be the number of characters in the address string or zero in which case the length is calculated with strlen(<b>address</b>).</p>

<p>X509_check_ip() checks if the certificate matches a specified IPv4 or IPv6 address. The <b>address</b> array is in binary format, in network byte order. The length is either 4 (IPv4) or 16 (IPv6). Only explicitly marked addresses in the certificates are considered; IP addresses stored in DNS names and Common Names are ignored.</p>

<p>X509_check_ip_asc() is similar, except that the NUL-terminated string <b>address</b> is first converted to the internal representation.</p>

<p>The <b>flags</b> argument is usually 0. It can be the bitwise OR of the flags:</p>

<dl>

<dt id="X509_CHECK_FLAG_ALWAYS_CHECK_SUBJECT"><b>X509_CHECK_FLAG_ALWAYS_CHECK_SUBJECT</b>,</dt>
<dd>

</dd>
<dt id="X509_CHECK_FLAG_NEVER_CHECK_SUBJECT"><b>X509_CHECK_FLAG_NEVER_CHECK_SUBJECT</b>,</dt>
<dd>

</dd>
<dt id="X509_CHECK_FLAG_NO_WILDCARDS"><b>X509_CHECK_FLAG_NO_WILDCARDS</b>,</dt>
<dd>

</dd>
<dt id="X509_CHECK_FLAG_NO_PARTIAL_WILDCARDS"><b>X509_CHECK_FLAG_NO_PARTIAL_WILDCARDS</b>,</dt>
<dd>

</dd>
<dt id="X509_CHECK_FLAG_MULTI_LABEL_WILDCARDS"><b>X509_CHECK_FLAG_MULTI_LABEL_WILDCARDS</b>.</dt>
<dd>

</dd>
<dt id="X509_CHECK_FLAG_SINGLE_LABEL_SUBDOMAINS"><b>X509_CHECK_FLAG_SINGLE_LABEL_SUBDOMAINS</b>.</dt>
<dd>

</dd>
</dl>

<p>The <b>X509_CHECK_FLAG_ALWAYS_CHECK_SUBJECT</b> flag causes the function to consider the subject DN even if the certificate contains at least one subject alternative name of the right type (DNS name or email address as appropriate); the default is to ignore the subject DN when at least one corresponding subject alternative names is present.</p>

<p>The <b>X509_CHECK_FLAG_NEVER_CHECK_SUBJECT</b> flag causes the function to never consider the subject DN even if the certificate contains no subject alternative names of the right type (DNS name or email address as appropriate); the default is to use the subject DN when no corresponding subject alternative names are present. If both <b>X509_CHECK_FLAG_ALWAYS_CHECK_SUBJECT</b> and <b>X509_CHECK_FLAG_NEVER_CHECK_SUBJECT</b> are specified, the latter takes precedence and the subject DN is not checked for matching names.</p>

<p>If set, <b>X509_CHECK_FLAG_NO_WILDCARDS</b> disables wildcard expansion; this only applies to <b>X509_check_host</b>.</p>

<p>If set, <b>X509_CHECK_FLAG_NO_PARTIAL_WILDCARDS</b> suppresses support for &quot;*&quot; as wildcard pattern in labels that have a prefix or suffix, such as: &quot;www*&quot; or &quot;*www&quot;; this only applies to <b>X509_check_host</b>.</p>

<p>If set, <b>X509_CHECK_FLAG_MULTI_LABEL_WILDCARDS</b> allows a &quot;*&quot; that constitutes the complete label of a DNS name (e.g. &quot;*.example.com&quot;) to match more than one label in <b>name</b>; this flag only applies to <b>X509_check_host</b>.</p>

<p>If set, <b>X509_CHECK_FLAG_SINGLE_LABEL_SUBDOMAINS</b> restricts <b>name</b> values which start with &quot;.&quot;, that would otherwise match any sub-domain in the peer certificate, to only match direct child sub-domains. Thus, for instance, with this flag set a <b>name</b> of &quot;.example.com&quot; would match a peer certificate with a DNS name of &quot;www.example.com&quot;, but would not match a peer certificate with a DNS name of &quot;www.sub.example.com&quot;; this flag only applies to <b>X509_check_host</b>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The functions return 1 for a successful match, 0 for a failed match and -1 for an internal error: typically a memory allocation failure or an ASN.1 decoding error.</p>

<p>All functions can also return -2 if the input is malformed. For example, X509_check_host() returns -2 if the provided <b>name</b> contains embedded NULs.</p>

<h1 id="NOTES">NOTES</h1>

<p>Applications are encouraged to use X509_VERIFY_PARAM_set1_host() rather than explicitly calling <a href="../man3/X509_check_host.html">X509_check_host(3)</a>. Host name checks may be out of scope with the DANE-EE(3) certificate usage, and the internal checks will be suppressed as appropriate when DANE support is enabled.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/SSL_get_verify_result.html">SSL_get_verify_result(3)</a>, <a href="../man3/X509_VERIFY_PARAM_set1_host.html">X509_VERIFY_PARAM_set1_host(3)</a>, <a href="../man3/X509_VERIFY_PARAM_add1_host.html">X509_VERIFY_PARAM_add1_host(3)</a>, <a href="../man3/X509_VERIFY_PARAM_set1_email.html">X509_VERIFY_PARAM_set1_email(3)</a>, <a href="../man3/X509_VERIFY_PARAM_set1_ip.html">X509_VERIFY_PARAM_set1_ip(3)</a>, <a href="../man3/X509_VERIFY_PARAM_set1_ipasc.html">X509_VERIFY_PARAM_set1_ipasc(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 1.0.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2012-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


