.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_COPY 3"
.TH BN_COPY 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_copy, BN_dup, BN_with_flags \- copy BIGNUMs
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& BIGNUM *BN_copy(BIGNUM *to, const BIGNUM *from);
\&
\& BIGNUM *BN_dup(const BIGNUM *from);
\&
\& void BN_with_flags(BIGNUM *dest, const BIGNUM *b, int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_copy()\fR copies \fBfrom\fR to \fBto\fR. \fBBN_dup()\fR creates a new \fBBIGNUM\fR
containing the value \fBfrom\fR.
.PP
BN_with_flags creates a \fBtemporary\fR shallow copy of \fBb\fR in \fBdest\fR. It places
significant restrictions on the copied data. Applications that do no adhere to
these restrictions may encounter unexpected side effects or crashes. For that
reason use of this function is discouraged. Any flags provided in \fBflags\fR will
be set in \fBdest\fR in addition to any flags already set in \fBb\fR. For example this
might commonly be used to create a temporary copy of a BIGNUM with the
\&\fBBN_FLG_CONSTTIME\fR flag set for constant time operations. The temporary copy in
\&\fBdest\fR will share some internal state with \fBb\fR. For this reason the following
restrictions apply to the use of \fBdest\fR:
.IP \(bu 2
\&\fBdest\fR should be a newly allocated BIGNUM obtained via a call to \fBBN_new()\fR. It
should not have been used for other purposes or initialised in any way.
.IP \(bu 2
\&\fBdest\fR must only be used in "read-only" operations, i.e. typically those
functions where the relevant parameter is declared "const".
.IP \(bu 2
\&\fBdest\fR must be used and freed before any further subsequent use of \fBb\fR
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_copy()\fR returns \fBto\fR on success, NULL on error. \fBBN_dup()\fR returns
the new \fBBIGNUM\fR, and NULL on error. The error codes can be obtained
by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
