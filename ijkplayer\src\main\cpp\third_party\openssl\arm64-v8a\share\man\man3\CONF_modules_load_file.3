.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CONF_MODULES_LOAD_FILE 3"
.TH CONF_MODULES_LOAD_FILE 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CONF_modules_load_file, CONF_modules_load \- OpenSSL configuration functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/conf.h>
\&
\& int CONF_modules_load_file(const char *filename, const char *appname,
\&                            unsigned long flags);
\& int CONF_modules_load(const CONF *cnf, const char *appname,
\&                       unsigned long flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The function \fBCONF_modules_load_file()\fR configures OpenSSL using file
\&\fBfilename\fR and application name \fBappname\fR. If \fBfilename\fR is NULL
the standard OpenSSL configuration file is used. If \fBappname\fR is
NULL the standard OpenSSL application name \fBopenssl_conf\fR is used.
The behaviour can be customized using \fBflags\fR.
.PP
\&\fBCONF_modules_load()\fR is identical to \fBCONF_modules_load_file()\fR except it
reads configuration information from \fBcnf\fR.
.SH NOTES
.IX Header "NOTES"
The following \fBflags\fR are currently recognized:
.PP
If \fBCONF_MFLAGS_IGNORE_ERRORS\fR is set errors returned by individual
configuration modules are ignored. If not set the first module error is
considered fatal and no further modules are loaded.
.PP
Normally any modules errors will add error information to the error queue. If
\&\fBCONF_MFLAGS_SILENT\fR is set no error information is added.
.PP
If \fBCONF_MFLAGS_IGNORE_RETURN_CODES\fR is set the function unconditionally
returns success.
This is used by default in \fBOPENSSL_init_crypto\fR\|(3) to ignore any errors in
the default system-wide configuration file, as having all OpenSSL applications
fail to start when there are potentially minor issues in the file is too risky.
Applications calling \fBCONF_modules_load_file\fR explicitly should not generally
set this flag.
.PP
If \fBCONF_MFLAGS_NO_DSO\fR is set configuration module loading from DSOs is
disabled.
.PP
\&\fBCONF_MFLAGS_IGNORE_MISSING_FILE\fR if set will make \fBCONF_load_modules_file()\fR
ignore missing configuration files. Normally a missing configuration file
return an error.
.PP
\&\fBCONF_MFLAGS_DEFAULT_SECTION\fR if set and \fBappname\fR is not NULL will use the
default section pointed to by \fBopenssl_conf\fR if \fBappname\fR does not exist.
.PP
By using \fBCONF_modules_load_file()\fR with appropriate flags an application can
customise application configuration to best suit its needs. In some cases the
use of a configuration file is optional and its absence is not an error: in
this case \fBCONF_MFLAGS_IGNORE_MISSING_FILE\fR would be set.
.PP
Errors during configuration may also be handled differently by different
applications. For example in some cases an error may simply print out a warning
message and the application continue. In other cases an application might
consider a configuration file error as fatal and exit immediately.
.PP
Applications can use the \fBCONF_modules_load()\fR function if they wish to load a
configuration file themselves and have finer control over how errors are
treated.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return 1 for success and a zero or negative value for
failure. If module errors are not ignored the return code will reflect the
return value of the failing module (this will always be zero or negative).
.SH EXAMPLES
.IX Header "EXAMPLES"
Load a configuration file and print out any errors and exit (missing file
considered fatal):
.PP
.Vb 5
\& if (CONF_modules_load_file(NULL, NULL, 0) <= 0) {
\&     fprintf(stderr, "FATAL: error loading configuration file\en");
\&     ERR_print_errors_fp(stderr);
\&     exit(1);
\& }
.Ve
.PP
Load default configuration file using the section indicated by "myapp",
tolerate missing files, but exit on other errors:
.PP
.Vb 6
\& if (CONF_modules_load_file(NULL, "myapp",
\&                            CONF_MFLAGS_IGNORE_MISSING_FILE) <= 0) {
\&     fprintf(stderr, "FATAL: error loading configuration file\en");
\&     ERR_print_errors_fp(stderr);
\&     exit(1);
\& }
.Ve
.PP
Load custom configuration file and section, only print warnings on error,
missing configuration file ignored:
.PP
.Vb 5
\& if (CONF_modules_load_file("/something/app.cnf", "myapp",
\&                            CONF_MFLAGS_IGNORE_MISSING_FILE) <= 0) {
\&     fprintf(stderr, "WARNING: error loading configuration file\en");
\&     ERR_print_errors_fp(stderr);
\& }
.Ve
.PP
Load and parse configuration file manually, custom error handling:
.PP
.Vb 3
\& FILE *fp;
\& CONF *cnf = NULL;
\& long eline;
\&
\& fp = fopen("/somepath/app.cnf", "r");
\& if (fp == NULL) {
\&     fprintf(stderr, "Error opening configuration file\en");
\&     /* Other missing configuration file behaviour */
\& } else {
\&     cnf = NCONF_new(NULL);
\&     if (NCONF_load_fp(cnf, fp, &eline) == 0) {
\&         fprintf(stderr, "Error on line %ld of configuration file\en", eline);
\&         ERR_print_errors_fp(stderr);
\&         /* Other malformed configuration file behaviour */
\&     } else if (CONF_modules_load(cnf, "appname", 0) <= 0) {
\&         fprintf(stderr, "Error configuring application\en");
\&         ERR_print_errors_fp(stderr);
\&         /* Other configuration error behaviour */
\&     }
\&     fclose(fp);
\&     NCONF_free(cnf);
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBconfig\fR\|(5), \fBOPENSSL_config\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2004\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
