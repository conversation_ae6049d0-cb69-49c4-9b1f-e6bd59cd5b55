.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_VERIFY_CERT 3"
.TH X509_VERIFY_CERT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_verify_cert \- discover and verify X509 certificate chain
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_verify_cert(X509_STORE_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBX509_verify_cert()\fR function attempts to discover and validate a
certificate chain based on parameters in \fBctx\fR. A complete description of
the process is contained in the \fBverify\fR\|(1) manual page.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If a complete chain can be built and validated this function returns 1,
otherwise it return zero, in exceptional circumstances it can also
return a negative code.
.PP
If the function fails additional error information can be obtained by
examining \fBctx\fR using, for example \fBX509_STORE_CTX_get_error()\fR.
.SH NOTES
.IX Header "NOTES"
Applications rarely call this function directly but it is used by
OpenSSL internally for certificate validation, in both the S/MIME and
SSL/TLS code.
.PP
A negative return value from \fBX509_verify_cert()\fR can occur if it is invoked
incorrectly, such as with no certificate set in \fBctx\fR, or when it is called
twice in succession without reinitialising \fBctx\fR for the second call.
A negative return value can also happen due to internal resource problems or if
a retry operation is requested during internal lookups (which never happens
with standard lookup methods).
Applications must check for <= 0 return value on error.
.SH BUGS
.IX Header "BUGS"
This function uses the header \fBx509.h\fR as opposed to most chain verification
functions which use \fBx509_vfy.h\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_STORE_CTX_get_error\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2009\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
