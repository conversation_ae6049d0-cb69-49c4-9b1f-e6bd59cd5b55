<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>RAND</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>RAND - the OpenSSL random generator</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Random numbers are a vital part of cryptography, they are needed to provide unpredictability for tasks like key generation, creating salts, and many more. Software-based generators must be seeded with external randomness before they can be used as a cryptographically-secure pseudo-random number generator (CSPRNG). The availability of common hardware with special instructions and modern operating systems, which may use items such as interrupt jitter and network packet timings, can be reasonable sources of seeding material.</p>

<p>OpenSSL comes with a default implementation of the RAND API which is based on the deterministic random bit generator (DRBG) model as described in [NIST SP 800-90A Rev. 1]. The default random generator will initialize automatically on first use and will be fully functional without having to be initialized (&#39;seeded&#39;) explicitly. It seeds and reseeds itself automatically using trusted random sources provided by the operating system.</p>

<p>As a normal application developer, you do not have to worry about any details, just use <a href="../man3/RAND_bytes.html">RAND_bytes(3)</a> to obtain random data. Having said that, there is one important rule to obey: Always check the error return value of <a href="../man3/RAND_bytes.html">RAND_bytes(3)</a> and do not take randomness for granted. Although (re-)seeding is automatic, it can fail because no trusted random source is available or the trusted source(s) temporarily fail to provide sufficient random seed material. In this case the CSPRNG enters an error state and ceases to provide output, until it is able to recover from the error by reseeding itself. For more details on reseeding and error recovery, see <a href="../man7/RAND_DRBG.html">RAND_DRBG(7)</a>.</p>

<p>For values that should remain secret, you can use <a href="../man3/RAND_priv_bytes.html">RAND_priv_bytes(3)</a> instead. This method does not provide &#39;better&#39; randomness, it uses the same type of CSPRNG. The intention behind using a dedicated CSPRNG exclusively for private values is that none of its output should be visible to an attacker (e.g., used as salt value), in order to reveal as little information as possible about its internal state, and that a compromise of the &quot;public&quot; CSPRNG instance will not affect the secrecy of these private values.</p>

<p>In the rare case where the default implementation does not satisfy your special requirements, there are two options:</p>

<ul>

<li><p>Replace the default RAND method by your own RAND method using <a href="../man3/RAND_set_rand_method.html">RAND_set_rand_method(3)</a>.</p>

</li>
<li><p>Modify the default settings of the OpenSSL RAND method by modifying the security parameters of the underlying DRBG, which is described in detail in <a href="../man7/RAND_DRBG.html">RAND_DRBG(7)</a>.</p>

</li>
</ul>

<p>Changing the default random generator or its default parameters should be necessary only in exceptional cases and is not recommended, unless you have a profound knowledge of cryptographic principles and understand the implications of your changes.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/RAND_add.html">RAND_add(3)</a>, <a href="../man3/RAND_bytes.html">RAND_bytes(3)</a>, <a href="../man3/RAND_priv_bytes.html">RAND_priv_bytes(3)</a>, <a href="../man3/RAND_get_rand_method.html">RAND_get_rand_method(3)</a>, <a href="../man3/RAND_set_rand_method.html">RAND_set_rand_method(3)</a>, <a href="../man3/RAND_OpenSSL.html">RAND_OpenSSL(3)</a>, <a href="../man7/RAND_DRBG.html">RAND_DRBG(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2018-2019 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


