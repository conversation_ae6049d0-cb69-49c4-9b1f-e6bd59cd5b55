<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>x509</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a>
    <ul>
      <li><a href="#Input-Output-and-General-Purpose-Options">Input, Output, and General Purpose Options</a></li>
      <li><a href="#Display-Options">Display Options</a></li>
      <li><a href="#Trust-Settings">Trust Settings</a></li>
      <li><a href="#Signing-Options">Signing Options</a></li>
      <li><a href="#Name-Options">Name Options</a></li>
      <li><a href="#Text-Options">Text Options</a></li>
    </ul>
  </li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#CERTIFICATE-EXTENSIONS">CERTIFICATE EXTENSIONS</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-x509, x509 - Certificate display and signing utility</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>x509</b> [<b>-help</b>] [<b>-inform DER|PEM</b>] [<b>-outform DER|PEM</b>] [<b>-keyform DER|PEM|ENGINE</b>] [<b>-CAform DER|PEM</b>] [<b>-CAkeyform DER|PEM</b>] [<b>-in filename</b>] [<b>-out filename</b>] [<b>-serial</b>] [<b>-hash</b>] [<b>-subject_hash</b>] [<b>-issuer_hash</b>] [<b>-ocspid</b>] [<b>-subject</b>] [<b>-issuer</b>] [<b>-nameopt option</b>] [<b>-email</b>] [<b>-ocsp_uri</b>] [<b>-startdate</b>] [<b>-enddate</b>] [<b>-purpose</b>] [<b>-dates</b>] [<b>-checkend num</b>] [<b>-modulus</b>] [<b>-pubkey</b>] [<b>-fingerprint</b>] [<b>-alias</b>] [<b>-noout</b>] [<b>-trustout</b>] [<b>-clrtrust</b>] [<b>-clrreject</b>] [<b>-addtrust arg</b>] [<b>-addreject arg</b>] [<b>-setalias arg</b>] [<b>-days arg</b>] [<b>-set_serial n</b>] [<b>-signkey arg</b>] [<b>-passin arg</b>] [<b>-x509toreq</b>] [<b>-req</b>] [<b>-CA filename</b>] [<b>-CAkey filename</b>] [<b>-CAcreateserial</b>] [<b>-CAserial filename</b>] [<b>-force_pubkey key</b>] [<b>-text</b>] [<b>-ext extensions</b>] [<b>-certopt option</b>] [<b>-C</b>] [<b>-<i>digest</i></b>] [<b>-clrext</b>] [<b>-extfile filename</b>] [<b>-extensions section</b>] [<b>-sigopt nm:v</b>] [<b>-rand file...</b>] [<b>-writerand file</b>] [<b>-engine id</b>] [<b>-preserve_dates</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>x509</b> command is a multi purpose certificate utility. It can be used to display certificate information, convert certificates to various forms, sign certificate requests like a &quot;mini CA&quot; or edit certificate trust settings.</p>

<p>Since there are a large number of options they will split up into various sections.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<h2 id="Input-Output-and-General-Purpose-Options">Input, Output, and General Purpose Options</h2>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="inform-DER-PEM"><b>-inform DER|PEM</b></dt>
<dd>

<p>This specifies the input format normally the command will expect an X509 certificate but this can change if other options such as <b>-req</b> are present. The DER format is the DER encoding of the certificate and PEM is the base64 encoding of the DER encoding with header and footer lines added. The default format is PEM.</p>

</dd>
<dt id="outform-DER-PEM"><b>-outform DER|PEM</b></dt>
<dd>

<p>This specifies the output format, the options have the same meaning and default as the <b>-inform</b> option.</p>

</dd>
<dt id="in-filename"><b>-in filename</b></dt>
<dd>

<p>This specifies the input filename to read a certificate from or standard input if this option is not specified.</p>

</dd>
<dt id="out-filename"><b>-out filename</b></dt>
<dd>

<p>This specifies the output filename to write to or standard output by default.</p>

</dd>
<dt id="digest"><b>-<i>digest</i></b></dt>
<dd>

<p>The digest to use. This affects any signing or display option that uses a message digest, such as the <b>-fingerprint</b>, <b>-signkey</b> and <b>-CA</b> options. Any digest supported by the OpenSSL <b>dgst</b> command can be used. If not specified then SHA1 is used with <b>-fingerprint</b> or the default digest for the signing algorithm is used, typically SHA256.</p>

</dd>
<dt id="rand-file"><b>-rand file...</b></dt>
<dd>

<p>A file or files containing random data used to seed the random number generator. Multiple files can be specified separated by an OS-dependent character. The separator is <b>;</b> for MS-Windows, <b>,</b> for OpenVMS, and <b>:</b> for all others.</p>

</dd>
<dt id="writerand-file">[<b>-writerand file</b>]</dt>
<dd>

<p>Writes random data to the specified <i>file</i> upon exit. This can be used with a subsequent <b>-rand</b> flag.</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Specifying an engine (by its unique <b>id</b> string) will cause <b>x509</b> to attempt to obtain a functional reference to the specified engine, thus initialising it if needed. The engine will then be set as the default for all available algorithms.</p>

</dd>
<dt id="preserve_dates"><b>-preserve_dates</b></dt>
<dd>

<p>When signing a certificate, preserve the &quot;notBefore&quot; and &quot;notAfter&quot; dates instead of adjusting them to current time and duration. Cannot be used with the <b>-days</b> option.</p>

</dd>
</dl>

<h2 id="Display-Options">Display Options</h2>

<p>Note: the <b>-alias</b> and <b>-purpose</b> options are also display options but are described in the <b>TRUST SETTINGS</b> section.</p>

<dl>

<dt id="text"><b>-text</b></dt>
<dd>

<p>Prints out the certificate in text form. Full details are output including the public key, signature algorithms, issuer and subject names, serial number any extensions present and any trust settings.</p>

</dd>
<dt id="ext-extensions"><b>-ext extensions</b></dt>
<dd>

<p>Prints out the certificate extensions in text form. Extensions are specified with a comma separated string, e.g., &quot;subjectAltName,subjectKeyIdentifier&quot;. See the <a href="../man5/x509v3_config.html">x509v3_config(5)</a> manual page for the extension names.</p>

</dd>
<dt id="certopt-option"><b>-certopt option</b></dt>
<dd>

<p>Customise the output format used with <b>-text</b>. The <b>option</b> argument can be a single option or multiple options separated by commas. The <b>-certopt</b> switch may be also be used more than once to set multiple options. See the <b>TEXT OPTIONS</b> section for more information.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>This option prevents output of the encoded version of the certificate.</p>

</dd>
<dt id="pubkey"><b>-pubkey</b></dt>
<dd>

<p>Outputs the certificate&#39;s SubjectPublicKeyInfo block in PEM format.</p>

</dd>
<dt id="modulus"><b>-modulus</b></dt>
<dd>

<p>This option prints out the value of the modulus of the public key contained in the certificate.</p>

</dd>
<dt id="serial"><b>-serial</b></dt>
<dd>

<p>Outputs the certificate serial number.</p>

</dd>
<dt id="subject_hash"><b>-subject_hash</b></dt>
<dd>

<p>Outputs the &quot;hash&quot; of the certificate subject name. This is used in OpenSSL to form an index to allow certificates in a directory to be looked up by subject name.</p>

</dd>
<dt id="issuer_hash"><b>-issuer_hash</b></dt>
<dd>

<p>Outputs the &quot;hash&quot; of the certificate issuer name.</p>

</dd>
<dt id="ocspid"><b>-ocspid</b></dt>
<dd>

<p>Outputs the OCSP hash values for the subject name and public key.</p>

</dd>
<dt id="hash"><b>-hash</b></dt>
<dd>

<p>Synonym for &quot;-subject_hash&quot; for backward compatibility reasons.</p>

</dd>
<dt id="subject_hash_old"><b>-subject_hash_old</b></dt>
<dd>

<p>Outputs the &quot;hash&quot; of the certificate subject name using the older algorithm as used by OpenSSL before version 1.0.0.</p>

</dd>
<dt id="issuer_hash_old"><b>-issuer_hash_old</b></dt>
<dd>

<p>Outputs the &quot;hash&quot; of the certificate issuer name using the older algorithm as used by OpenSSL before version 1.0.0.</p>

</dd>
<dt id="subject"><b>-subject</b></dt>
<dd>

<p>Outputs the subject name.</p>

</dd>
<dt id="issuer"><b>-issuer</b></dt>
<dd>

<p>Outputs the issuer name.</p>

</dd>
<dt id="nameopt-option"><b>-nameopt option</b></dt>
<dd>

<p>Option which determines how the subject or issuer names are displayed. The <b>option</b> argument can be a single option or multiple options separated by commas. Alternatively the <b>-nameopt</b> switch may be used more than once to set multiple options. See the <b>NAME OPTIONS</b> section for more information.</p>

</dd>
<dt id="email"><b>-email</b></dt>
<dd>

<p>Outputs the email address(es) if any.</p>

</dd>
<dt id="ocsp_uri"><b>-ocsp_uri</b></dt>
<dd>

<p>Outputs the OCSP responder address(es) if any.</p>

</dd>
<dt id="startdate"><b>-startdate</b></dt>
<dd>

<p>Prints out the start date of the certificate, that is the notBefore date.</p>

</dd>
<dt id="enddate"><b>-enddate</b></dt>
<dd>

<p>Prints out the expiry date of the certificate, that is the notAfter date.</p>

</dd>
<dt id="dates"><b>-dates</b></dt>
<dd>

<p>Prints out the start and expiry dates of a certificate.</p>

</dd>
<dt id="checkend-arg"><b>-checkend arg</b></dt>
<dd>

<p>Checks if the certificate expires within the next <b>arg</b> seconds and exits nonzero if yes it will expire or zero if not.</p>

</dd>
<dt id="fingerprint"><b>-fingerprint</b></dt>
<dd>

<p>Calculates and outputs the digest of the DER encoded version of the entire certificate (see digest options). This is commonly called a &quot;fingerprint&quot;. Because of the nature of message digests, the fingerprint of a certificate is unique to that certificate and two certificates with the same fingerprint can be considered to be the same.</p>

</dd>
<dt id="C"><b>-C</b></dt>
<dd>

<p>This outputs the certificate in the form of a C source file.</p>

</dd>
</dl>

<h2 id="Trust-Settings">Trust Settings</h2>

<p>A <b>trusted certificate</b> is an ordinary certificate which has several additional pieces of information attached to it such as the permitted and prohibited uses of the certificate and an &quot;alias&quot;.</p>

<p>Normally when a certificate is being verified at least one certificate must be &quot;trusted&quot;. By default a trusted certificate must be stored locally and must be a root CA: any certificate chain ending in this CA is then usable for any purpose.</p>

<p>Trust settings currently are only used with a root CA. They allow a finer control over the purposes the root CA can be used for. For example a CA may be trusted for SSL client but not SSL server use.</p>

<p>See the description of the <b>verify</b> utility for more information on the meaning of trust settings.</p>

<p>Future versions of OpenSSL will recognize trust settings on any certificate: not just root CAs.</p>

<dl>

<dt id="trustout"><b>-trustout</b></dt>
<dd>

<p>This causes <b>x509</b> to output a <b>trusted</b> certificate. An ordinary or trusted certificate can be input but by default an ordinary certificate is output and any trust settings are discarded. With the <b>-trustout</b> option a trusted certificate is output. A trusted certificate is automatically output if any trust settings are modified.</p>

</dd>
<dt id="setalias-arg"><b>-setalias arg</b></dt>
<dd>

<p>Sets the alias of the certificate. This will allow the certificate to be referred to using a nickname for example &quot;Steve&#39;s Certificate&quot;.</p>

</dd>
<dt id="alias"><b>-alias</b></dt>
<dd>

<p>Outputs the certificate alias, if any.</p>

</dd>
<dt id="clrtrust"><b>-clrtrust</b></dt>
<dd>

<p>Clears all the permitted or trusted uses of the certificate.</p>

</dd>
<dt id="clrreject"><b>-clrreject</b></dt>
<dd>

<p>Clears all the prohibited or rejected uses of the certificate.</p>

</dd>
<dt id="addtrust-arg"><b>-addtrust arg</b></dt>
<dd>

<p>Adds a trusted certificate use. Any object name can be used here but currently only <b>clientAuth</b> (SSL client use), <b>serverAuth</b> (SSL server use), <b>emailProtection</b> (S/MIME email) and <b>anyExtendedKeyUsage</b> are used. As of OpenSSL 1.1.0, the last of these blocks all purposes when rejected or enables all purposes when trusted. Other OpenSSL applications may define additional uses.</p>

</dd>
<dt id="addreject-arg"><b>-addreject arg</b></dt>
<dd>

<p>Adds a prohibited use. It accepts the same values as the <b>-addtrust</b> option.</p>

</dd>
<dt id="purpose"><b>-purpose</b></dt>
<dd>

<p>This option performs tests on the certificate extensions and outputs the results. For a more complete description see the <b>CERTIFICATE EXTENSIONS</b> section.</p>

</dd>
</dl>

<h2 id="Signing-Options">Signing Options</h2>

<p>The <b>x509</b> utility can be used to sign certificates and requests: it can thus behave like a &quot;mini CA&quot;.</p>

<dl>

<dt id="signkey-arg"><b>-signkey arg</b></dt>
<dd>

<p>This option causes the input file to be self signed using the supplied private key or engine. The private key&#39;s format is specified with the <b>-keyform</b> option.</p>

<p>If the input file is a certificate it sets the issuer name to the subject name (i.e. makes it self signed) changes the public key to the supplied value and changes the start and end dates. The start date is set to the current time and the end date is set to a value determined by the <b>-days</b> option. Any certificate extensions are retained unless the <b>-clrext</b> option is supplied; this includes, for example, any existing key identifier extensions.</p>

<p>If the input is a certificate request then a self signed certificate is created using the supplied private key using the subject name in the request.</p>

</dd>
<dt id="sigopt-nm:v"><b>-sigopt nm:v</b></dt>
<dd>

<p>Pass options to the signature algorithm during sign or verify operations. Names and values of these options are algorithm-specific.</p>

</dd>
<dt id="passin-arg"><b>-passin arg</b></dt>
<dd>

<p>The key password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="clrext"><b>-clrext</b></dt>
<dd>

<p>Delete any extensions from a certificate. This option is used when a certificate is being created from another certificate (for example with the <b>-signkey</b> or the <b>-CA</b> options). Normally all extensions are retained.</p>

</dd>
<dt id="keyform-PEM-DER-ENGINE"><b>-keyform PEM|DER|ENGINE</b></dt>
<dd>

<p>Specifies the format (DER or PEM) of the private key file used in the <b>-signkey</b> option.</p>

</dd>
<dt id="days-arg"><b>-days arg</b></dt>
<dd>

<p>Specifies the number of days to make a certificate valid for. The default is 30 days. Cannot be used with the <b>-preserve_dates</b> option.</p>

</dd>
<dt id="x509toreq"><b>-x509toreq</b></dt>
<dd>

<p>Converts a certificate into a certificate request. The <b>-signkey</b> option is used to pass the required private key.</p>

</dd>
<dt id="req"><b>-req</b></dt>
<dd>

<p>By default a certificate is expected on input. With this option a certificate request is expected instead.</p>

</dd>
<dt id="set_serial-n"><b>-set_serial n</b></dt>
<dd>

<p>Specifies the serial number to use. This option can be used with either the <b>-signkey</b> or <b>-CA</b> options. If used in conjunction with the <b>-CA</b> option the serial number file (as specified by the <b>-CAserial</b> or <b>-CAcreateserial</b> options) is not used.</p>

<p>The serial number can be decimal or hex (if preceded by <b>0x</b>).</p>

</dd>
<dt id="CA-filename"><b>-CA filename</b></dt>
<dd>

<p>Specifies the CA certificate to be used for signing. When this option is present <b>x509</b> behaves like a &quot;mini CA&quot;. The input file is signed by this CA using this option: that is its issuer name is set to the subject name of the CA and it is digitally signed using the CAs private key.</p>

<p>This option is normally combined with the <b>-req</b> option. Without the <b>-req</b> option the input is a certificate which must be self signed.</p>

</dd>
<dt id="CAkey-filename"><b>-CAkey filename</b></dt>
<dd>

<p>Sets the CA private key to sign a certificate with. If this option is not specified then it is assumed that the CA private key is present in the CA certificate file.</p>

</dd>
<dt id="CAserial-filename"><b>-CAserial filename</b></dt>
<dd>

<p>Sets the CA serial number file to use.</p>

<p>When creating a certificate with this option, and with the <b>-CA</b> option, the certificate serial number is stored in the given file. This file consists of one line containing an even number of hex digits with the serial number used last time. After reading this number, it is incremented and used, and the file is updated.</p>

<p>The default filename consists of the CA certificate file base name with &quot;.srl&quot; appended. For example if the CA certificate file is called &quot;mycacert.pem&quot; it expects to find a serial number file called &quot;mycacert.srl&quot;.</p>

<p>If the <b>-CA</b> option is specified and neither &lt;-CAserial&gt; or &lt;-CAcreateserial&gt; is given and the default serial number file does not exist, a random number is generated; this is the recommended practice.</p>

</dd>
<dt id="CAcreateserial"><b>-CAcreateserial</b></dt>
<dd>

<p>With this option and the <b>-CA</b> option the CA serial number file is created if it does not exist. A random number is generated, used for the certificate, and saved into the serial number file determined as described above.</p>

</dd>
<dt id="extfile-filename"><b>-extfile filename</b></dt>
<dd>

<p>File containing certificate extensions to use. If not specified then no extensions are added to the certificate.</p>

</dd>
<dt id="extensions-section"><b>-extensions section</b></dt>
<dd>

<p>The section to add certificate extensions from. If this option is not specified then the extensions should either be contained in the unnamed (default) section or the default section should contain a variable called &quot;extensions&quot; which contains the section to use. See the <a href="../man5/x509v3_config.html">x509v3_config(5)</a> manual page for details of the extension section format.</p>

</dd>
<dt id="force_pubkey-key"><b>-force_pubkey key</b></dt>
<dd>

<p>When a certificate is created set its public key to <b>key</b> instead of the key in the certificate or certificate request. This option is useful for creating certificates where the algorithm can&#39;t normally sign requests, for example DH.</p>

<p>The format or <b>key</b> can be specified using the <b>-keyform</b> option.</p>

</dd>
</dl>

<h2 id="Name-Options">Name Options</h2>

<p>The <b>nameopt</b> command line switch determines how the subject and issuer names are displayed. If no <b>nameopt</b> switch is present the default &quot;oneline&quot; format is used which is compatible with previous versions of OpenSSL. Each option is described in detail below, all options can be preceded by a <b>-</b> to turn the option off. Only the first four will normally be used.</p>

<dl>

<dt id="compat"><b>compat</b></dt>
<dd>

<p>Use the old format.</p>

</dd>
<dt id="RFC2253"><b>RFC2253</b></dt>
<dd>

<p>Displays names compatible with RFC2253 equivalent to <b>esc_2253</b>, <b>esc_ctrl</b>, <b>esc_msb</b>, <b>utf8</b>, <b>dump_nostr</b>, <b>dump_unknown</b>, <b>dump_der</b>, <b>sep_comma_plus</b>, <b>dn_rev</b> and <b>sname</b>.</p>

</dd>
<dt id="oneline"><b>oneline</b></dt>
<dd>

<p>A oneline format which is more readable than RFC2253. It is equivalent to specifying the <b>esc_2253</b>, <b>esc_ctrl</b>, <b>esc_msb</b>, <b>utf8</b>, <b>dump_nostr</b>, <b>dump_der</b>, <b>use_quote</b>, <b>sep_comma_plus_space</b>, <b>space_eq</b> and <b>sname</b> options. This is the <i>default</i> of no name options are given explicitly.</p>

</dd>
<dt id="multiline"><b>multiline</b></dt>
<dd>

<p>A multiline format. It is equivalent <b>esc_ctrl</b>, <b>esc_msb</b>, <b>sep_multiline</b>, <b>space_eq</b>, <b>lname</b> and <b>align</b>.</p>

</dd>
<dt id="esc_2253"><b>esc_2253</b></dt>
<dd>

<p>Escape the &quot;special&quot; characters required by RFC2253 in a field. That is <b>,+&quot;&lt;&gt;;</b>. Additionally <b>#</b> is escaped at the beginning of a string and a space character at the beginning or end of a string.</p>

</dd>
<dt id="esc_2254"><b>esc_2254</b></dt>
<dd>

<p>Escape the &quot;special&quot; characters required by RFC2254 in a field. That is the <b>NUL</b> character as well as and <b>()*</b>.</p>

</dd>
<dt id="esc_ctrl"><b>esc_ctrl</b></dt>
<dd>

<p>Escape control characters. That is those with ASCII values less than 0x20 (space) and the delete (0x7f) character. They are escaped using the RFC2253 \XX notation (where XX are two hex digits representing the character value).</p>

</dd>
<dt id="esc_msb"><b>esc_msb</b></dt>
<dd>

<p>Escape characters with the MSB set, that is with ASCII values larger than 127.</p>

</dd>
<dt id="use_quote"><b>use_quote</b></dt>
<dd>

<p>Escapes some characters by surrounding the whole string with <b>&quot;</b> characters, without the option all escaping is done with the <b>\</b> character.</p>

</dd>
<dt id="utf8"><b>utf8</b></dt>
<dd>

<p>Convert all strings to UTF8 format first. This is required by RFC2253. If you are lucky enough to have a UTF8 compatible terminal then the use of this option (and <b>not</b> setting <b>esc_msb</b>) may result in the correct display of multibyte (international) characters. Is this option is not present then multibyte characters larger than 0xff will be represented using the format \UXXXX for 16 bits and \WXXXXXXXX for 32 bits. Also if this option is off any UTF8Strings will be converted to their character form first.</p>

</dd>
<dt id="ignore_type"><b>ignore_type</b></dt>
<dd>

<p>This option does not attempt to interpret multibyte characters in any way. That is their content octets are merely dumped as though one octet represents each character. This is useful for diagnostic purposes but will result in rather odd looking output.</p>

</dd>
<dt id="show_type"><b>show_type</b></dt>
<dd>

<p>Show the type of the ASN1 character string. The type precedes the field contents. For example &quot;BMPSTRING: Hello World&quot;.</p>

</dd>
<dt id="dump_der"><b>dump_der</b></dt>
<dd>

<p>When this option is set any fields that need to be hexdumped will be dumped using the DER encoding of the field. Otherwise just the content octets will be displayed. Both options use the RFC2253 <b>#XXXX...</b> format.</p>

</dd>
<dt id="dump_nostr"><b>dump_nostr</b></dt>
<dd>

<p>Dump non character string types (for example OCTET STRING) if this option is not set then non character string types will be displayed as though each content octet represents a single character.</p>

</dd>
<dt id="dump_all"><b>dump_all</b></dt>
<dd>

<p>Dump all fields. This option when used with <b>dump_der</b> allows the DER encoding of the structure to be unambiguously determined.</p>

</dd>
<dt id="dump_unknown"><b>dump_unknown</b></dt>
<dd>

<p>Dump any field whose OID is not recognised by OpenSSL.</p>

</dd>
<dt id="sep_comma_plus-sep_comma_plus_space-sep_semi_plus_space-sep_multiline"><b>sep_comma_plus</b>, <b>sep_comma_plus_space</b>, <b>sep_semi_plus_space</b>, <b>sep_multiline</b></dt>
<dd>

<p>These options determine the field separators. The first character is between RDNs and the second between multiple AVAs (multiple AVAs are very rare and their use is discouraged). The options ending in &quot;space&quot; additionally place a space after the separator to make it more readable. The <b>sep_multiline</b> uses a linefeed character for the RDN separator and a spaced <b>+</b> for the AVA separator. It also indents the fields by four characters. If no field separator is specified then <b>sep_comma_plus_space</b> is used by default.</p>

</dd>
<dt id="dn_rev"><b>dn_rev</b></dt>
<dd>

<p>Reverse the fields of the DN. This is required by RFC2253. As a side effect this also reverses the order of multiple AVAs but this is permissible.</p>

</dd>
<dt id="nofname-sname-lname-oid"><b>nofname</b>, <b>sname</b>, <b>lname</b>, <b>oid</b></dt>
<dd>

<p>These options alter how the field name is displayed. <b>nofname</b> does not display the field at all. <b>sname</b> uses the &quot;short name&quot; form (CN for commonName for example). <b>lname</b> uses the long form. <b>oid</b> represents the OID in numerical form and is useful for diagnostic purpose.</p>

</dd>
<dt id="align"><b>align</b></dt>
<dd>

<p>Align field values for a more readable output. Only usable with <b>sep_multiline</b>.</p>

</dd>
<dt id="space_eq"><b>space_eq</b></dt>
<dd>

<p>Places spaces round the <b>=</b> character which follows the field name.</p>

</dd>
</dl>

<h2 id="Text-Options">Text Options</h2>

<p>As well as customising the name output format, it is also possible to customise the actual fields printed using the <b>certopt</b> options when the <b>text</b> option is present. The default behaviour is to print all fields.</p>

<dl>

<dt id="compatible"><b>compatible</b></dt>
<dd>

<p>Use the old format. This is equivalent to specifying no output options at all.</p>

</dd>
<dt id="no_header"><b>no_header</b></dt>
<dd>

<p>Don&#39;t print header information: that is the lines saying &quot;Certificate&quot; and &quot;Data&quot;.</p>

</dd>
<dt id="no_version"><b>no_version</b></dt>
<dd>

<p>Don&#39;t print out the version number.</p>

</dd>
<dt id="no_serial"><b>no_serial</b></dt>
<dd>

<p>Don&#39;t print out the serial number.</p>

</dd>
<dt id="no_signame"><b>no_signame</b></dt>
<dd>

<p>Don&#39;t print out the signature algorithm used.</p>

</dd>
<dt id="no_validity"><b>no_validity</b></dt>
<dd>

<p>Don&#39;t print the validity, that is the <b>notBefore</b> and <b>notAfter</b> fields.</p>

</dd>
<dt id="no_subject"><b>no_subject</b></dt>
<dd>

<p>Don&#39;t print out the subject name.</p>

</dd>
<dt id="no_issuer"><b>no_issuer</b></dt>
<dd>

<p>Don&#39;t print out the issuer name.</p>

</dd>
<dt id="no_pubkey"><b>no_pubkey</b></dt>
<dd>

<p>Don&#39;t print out the public key.</p>

</dd>
<dt id="no_sigdump"><b>no_sigdump</b></dt>
<dd>

<p>Don&#39;t give a hexadecimal dump of the certificate signature.</p>

</dd>
<dt id="no_aux"><b>no_aux</b></dt>
<dd>

<p>Don&#39;t print out certificate trust information.</p>

</dd>
<dt id="no_extensions"><b>no_extensions</b></dt>
<dd>

<p>Don&#39;t print out any X509V3 extensions.</p>

</dd>
<dt id="ext_default"><b>ext_default</b></dt>
<dd>

<p>Retain default extension behaviour: attempt to print out unsupported certificate extensions.</p>

</dd>
<dt id="ext_error"><b>ext_error</b></dt>
<dd>

<p>Print an error message for unsupported certificate extensions.</p>

</dd>
<dt id="ext_parse"><b>ext_parse</b></dt>
<dd>

<p>ASN1 parse unsupported extensions.</p>

</dd>
<dt id="ext_dump"><b>ext_dump</b></dt>
<dd>

<p>Hex dump unsupported extensions.</p>

</dd>
<dt id="ca_default"><b>ca_default</b></dt>
<dd>

<p>The value used by the <b>ca</b> utility, equivalent to <b>no_issuer</b>, <b>no_pubkey</b>, <b>no_header</b>, and <b>no_version</b>.</p>

</dd>
</dl>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Note: in these examples the &#39;\&#39; means the example should be all on one line.</p>

<p>Display the contents of a certificate:</p>

<pre><code>openssl x509 -in cert.pem -noout -text</code></pre>

<p>Display the &quot;Subject Alternative Name&quot; extension of a certificate:</p>

<pre><code>openssl x509 -in cert.pem -noout -ext subjectAltName</code></pre>

<p>Display more extensions of a certificate:</p>

<pre><code>openssl x509 -in cert.pem -noout -ext subjectAltName,nsCertType</code></pre>

<p>Display the certificate serial number:</p>

<pre><code>openssl x509 -in cert.pem -noout -serial</code></pre>

<p>Display the certificate subject name:</p>

<pre><code>openssl x509 -in cert.pem -noout -subject</code></pre>

<p>Display the certificate subject name in RFC2253 form:</p>

<pre><code>openssl x509 -in cert.pem -noout -subject -nameopt RFC2253</code></pre>

<p>Display the certificate subject name in oneline form on a terminal supporting UTF8:</p>

<pre><code>openssl x509 -in cert.pem -noout -subject -nameopt oneline,-esc_msb</code></pre>

<p>Display the certificate SHA1 fingerprint:</p>

<pre><code>openssl x509 -sha1 -in cert.pem -noout -fingerprint</code></pre>

<p>Convert a certificate from PEM to DER format:</p>

<pre><code>openssl x509 -in cert.pem -inform PEM -out cert.der -outform DER</code></pre>

<p>Convert a certificate to a certificate request:</p>

<pre><code>openssl x509 -x509toreq -in cert.pem -out req.pem -signkey key.pem</code></pre>

<p>Convert a certificate request into a self signed certificate using extensions for a CA:</p>

<pre><code>openssl x509 -req -in careq.pem -extfile openssl.cnf -extensions v3_ca \
       -signkey key.pem -out cacert.pem</code></pre>

<p>Sign a certificate request using the CA certificate above and add user certificate extensions:</p>

<pre><code>openssl x509 -req -in req.pem -extfile openssl.cnf -extensions v3_usr \
       -CA cacert.pem -CAkey key.pem -CAcreateserial</code></pre>

<p>Set a certificate to be trusted for SSL client use and change set its alias to &quot;Steve&#39;s Class 1 CA&quot;</p>

<pre><code>openssl x509 -in cert.pem -addtrust clientAuth \
       -setalias &quot;Steve&#39;s Class 1 CA&quot; -out trust.pem</code></pre>

<h1 id="NOTES">NOTES</h1>

<p>The PEM format uses the header and footer lines:</p>

<pre><code>-----BEGIN CERTIFICATE-----
-----END CERTIFICATE-----</code></pre>

<p>it will also handle files containing:</p>

<pre><code>-----BEGIN X509 CERTIFICATE-----
-----END X509 CERTIFICATE-----</code></pre>

<p>Trusted certificates have the lines</p>

<pre><code>-----BEGIN TRUSTED CERTIFICATE-----
-----END TRUSTED CERTIFICATE-----</code></pre>

<p>The conversion to UTF8 format used with the name options assumes that T61Strings use the ISO8859-1 character set. This is wrong but Netscape and MSIE do this as do many certificates. So although this is incorrect it is more likely to display the majority of certificates correctly.</p>

<p>The <b>-email</b> option searches the subject name and the subject alternative name extension. Only unique email addresses will be printed out: it will not print the same address more than once.</p>

<h1 id="CERTIFICATE-EXTENSIONS">CERTIFICATE EXTENSIONS</h1>

<p>The <b>-purpose</b> option checks the certificate extensions and determines what the certificate can be used for. The actual checks done are rather complex and include various hacks and workarounds to handle broken certificates and software.</p>

<p>The same code is used when verifying untrusted certificates in chains so this section is useful if a chain is rejected by the verify code.</p>

<p>The basicConstraints extension CA flag is used to determine whether the certificate can be used as a CA. If the CA flag is true then it is a CA, if the CA flag is false then it is not a CA. <b>All</b> CAs should have the CA flag set to true.</p>

<p>If the basicConstraints extension is absent then the certificate is considered to be a &quot;possible CA&quot; other extensions are checked according to the intended use of the certificate. A warning is given in this case because the certificate should really not be regarded as a CA: however it is allowed to be a CA to work around some broken software.</p>

<p>If the certificate is a V1 certificate (and thus has no extensions) and it is self signed it is also assumed to be a CA but a warning is again given: this is to work around the problem of Verisign roots which are V1 self signed certificates.</p>

<p>If the keyUsage extension is present then additional restraints are made on the uses of the certificate. A CA certificate <b>must</b> have the keyCertSign bit set if the keyUsage extension is present.</p>

<p>The extended key usage extension places additional restrictions on the certificate uses. If this extension is present (whether critical or not) the key can only be used for the purposes specified.</p>

<p>A complete description of each test is given below. The comments about basicConstraints and keyUsage and V1 certificates above apply to <b>all</b> CA certificates.</p>

<dl>

<dt id="SSL-Client"><b>SSL Client</b></dt>
<dd>

<p>The extended key usage extension must be absent or include the &quot;web client authentication&quot; OID. keyUsage must be absent or it must have the digitalSignature bit set. Netscape certificate type must be absent or it must have the SSL client bit set.</p>

</dd>
<dt id="SSL-Client-CA"><b>SSL Client CA</b></dt>
<dd>

<p>The extended key usage extension must be absent or include the &quot;web client authentication&quot; OID. Netscape certificate type must be absent or it must have the SSL CA bit set: this is used as a work around if the basicConstraints extension is absent.</p>

</dd>
<dt id="SSL-Server"><b>SSL Server</b></dt>
<dd>

<p>The extended key usage extension must be absent or include the &quot;web server authentication&quot; and/or one of the SGC OIDs. keyUsage must be absent or it must have the digitalSignature, the keyEncipherment set or both bits set. Netscape certificate type must be absent or have the SSL server bit set.</p>

</dd>
<dt id="SSL-Server-CA"><b>SSL Server CA</b></dt>
<dd>

<p>The extended key usage extension must be absent or include the &quot;web server authentication&quot; and/or one of the SGC OIDs. Netscape certificate type must be absent or the SSL CA bit must be set: this is used as a work around if the basicConstraints extension is absent.</p>

</dd>
<dt id="Netscape-SSL-Server"><b>Netscape SSL Server</b></dt>
<dd>

<p>For Netscape SSL clients to connect to an SSL server it must have the keyEncipherment bit set if the keyUsage extension is present. This isn&#39;t always valid because some cipher suites use the key for digital signing. Otherwise it is the same as a normal SSL server.</p>

</dd>
<dt id="Common-S-MIME-Client-Tests"><b>Common S/MIME Client Tests</b></dt>
<dd>

<p>The extended key usage extension must be absent or include the &quot;email protection&quot; OID. Netscape certificate type must be absent or should have the S/MIME bit set. If the S/MIME bit is not set in Netscape certificate type then the SSL client bit is tolerated as an alternative but a warning is shown: this is because some Verisign certificates don&#39;t set the S/MIME bit.</p>

</dd>
<dt id="S-MIME-Signing"><b>S/MIME Signing</b></dt>
<dd>

<p>In addition to the common S/MIME client tests the digitalSignature bit or the nonRepudiation bit must be set if the keyUsage extension is present.</p>

</dd>
<dt id="S-MIME-Encryption"><b>S/MIME Encryption</b></dt>
<dd>

<p>In addition to the common S/MIME tests the keyEncipherment bit must be set if the keyUsage extension is present.</p>

</dd>
<dt id="S-MIME-CA"><b>S/MIME CA</b></dt>
<dd>

<p>The extended key usage extension must be absent or include the &quot;email protection&quot; OID. Netscape certificate type must be absent or must have the S/MIME CA bit set: this is used as a work around if the basicConstraints extension is absent.</p>

</dd>
<dt id="CRL-Signing"><b>CRL Signing</b></dt>
<dd>

<p>The keyUsage extension must be absent or it must have the CRL signing bit set.</p>

</dd>
<dt id="CRL-Signing-CA"><b>CRL Signing CA</b></dt>
<dd>

<p>The normal CA tests apply. Except in this case the basicConstraints extension must be present.</p>

</dd>
</dl>

<h1 id="BUGS">BUGS</h1>

<p>Extensions in certificates are not transferred to certificate requests and vice versa.</p>

<p>It is possible to produce invalid certificates or requests by specifying the wrong private key or using inconsistent options in some cases: these should be checked.</p>

<p>There should be options to explicitly set such things as start and end dates rather than an offset from the current time.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/req.html">req(1)</a>, <a href="../man1/ca.html">ca(1)</a>, <a href="../man1/genrsa.html">genrsa(1)</a>, <a href="../man1/gendsa.html">gendsa(1)</a>, <a href="../man1/verify.html">verify(1)</a>, <a href="../man5/x509v3_config.html">x509v3_config(5)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The hash algorithm used in the <b>-subject_hash</b> and <b>-issuer_hash</b> options before OpenSSL 1.0.0 was based on the deprecated MD5 algorithm and the encoding of the distinguished name. In OpenSSL 1.0.0 and later it is based on a canonical version of the DN using SHA1. This means that any directories using the old form must have their links rebuilt using <b>c_rehash</b> or similar.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


