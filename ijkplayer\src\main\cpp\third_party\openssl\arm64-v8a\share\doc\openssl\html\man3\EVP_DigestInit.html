<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_DigestInit</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#CONTROLS">CONTROLS</a></li>
  <li><a href="#FLAGS">FLAGS</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_MD_CTX_new, EVP_MD_CTX_reset, EVP_MD_CTX_free, EVP_MD_CTX_copy, EVP_MD_CTX_copy_ex, EVP_MD_CTX_ctrl, EVP_MD_CTX_set_flags, EVP_MD_CTX_clear_flags, EVP_MD_CTX_test_flags, EVP_Digest, EVP_DigestInit_ex, EVP_DigestInit, EVP_DigestUpdate, EVP_DigestFinal_ex, EVP_DigestFinalXOF, EVP_DigestFinal, EVP_MD_type, EVP_MD_pkey_type, EVP_MD_size, EVP_MD_block_size, EVP_MD_flags, EVP_MD_CTX_md, EVP_MD_CTX_type, EVP_MD_CTX_size, EVP_MD_CTX_block_size, EVP_MD_CTX_md_data, EVP_MD_CTX_update_fn, EVP_MD_CTX_set_update_fn, EVP_md_null, EVP_get_digestbyname, EVP_get_digestbynid, EVP_get_digestbyobj, EVP_MD_CTX_pkey_ctx, EVP_MD_CTX_set_pkey_ctx - EVP digest routines</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

EVP_MD_CTX *EVP_MD_CTX_new(void);
int EVP_MD_CTX_reset(EVP_MD_CTX *ctx);
void EVP_MD_CTX_free(EVP_MD_CTX *ctx);
void EVP_MD_CTX_ctrl(EVP_MD_CTX *ctx, int cmd, int p1, void* p2);
void EVP_MD_CTX_set_flags(EVP_MD_CTX *ctx, int flags);
void EVP_MD_CTX_clear_flags(EVP_MD_CTX *ctx, int flags);
int EVP_MD_CTX_test_flags(const EVP_MD_CTX *ctx, int flags);

int EVP_Digest(const void *data, size_t count, unsigned char *md,
               unsigned int *size, const EVP_MD *type, ENGINE *impl);
int EVP_DigestInit_ex(EVP_MD_CTX *ctx, const EVP_MD *type, ENGINE *impl);
int EVP_DigestUpdate(EVP_MD_CTX *ctx, const void *d, size_t cnt);
int EVP_DigestFinal_ex(EVP_MD_CTX *ctx, unsigned char *md, unsigned int *s);
int EVP_DigestFinalXOF(EVP_MD_CTX *ctx, unsigned char *md, size_t len);

int EVP_MD_CTX_copy_ex(EVP_MD_CTX *out, const EVP_MD_CTX *in);

int EVP_DigestInit(EVP_MD_CTX *ctx, const EVP_MD *type);
int EVP_DigestFinal(EVP_MD_CTX *ctx, unsigned char *md, unsigned int *s);

int EVP_MD_CTX_copy(EVP_MD_CTX *out, EVP_MD_CTX *in);

int EVP_MD_type(const EVP_MD *md);
int EVP_MD_pkey_type(const EVP_MD *md);
int EVP_MD_size(const EVP_MD *md);
int EVP_MD_block_size(const EVP_MD *md);
unsigned long EVP_MD_flags(const EVP_MD *md);

const EVP_MD *EVP_MD_CTX_md(const EVP_MD_CTX *ctx);
int EVP_MD_CTX_size(const EVP_MD_CTX *ctx);
int EVP_MD_CTX_block_size(const EVP_MD_CTX *ctx);
int EVP_MD_CTX_type(const EVP_MD_CTX *ctx);
void *EVP_MD_CTX_md_data(const EVP_MD_CTX *ctx);
int (*EVP_MD_CTX_update_fn(EVP_MD_CTX *ctx))(EVP_MD_CTX *ctx,
                                             const void *data, size_t count);
void EVP_MD_CTX_set_update_fn(EVP_MD_CTX *ctx,
                              int (*update)(EVP_MD_CTX *ctx,
                                            const void *data, size_t count));

const EVP_MD *EVP_md_null(void);

const EVP_MD *EVP_get_digestbyname(const char *name);
const EVP_MD *EVP_get_digestbynid(int type);
const EVP_MD *EVP_get_digestbyobj(const ASN1_OBJECT *o);

EVP_PKEY_CTX *EVP_MD_CTX_pkey_ctx(const EVP_MD_CTX *ctx);
void EVP_MD_CTX_set_pkey_ctx(EVP_MD_CTX *ctx, EVP_PKEY_CTX *pctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The EVP digest routines are a high-level interface to message digests, and should be used instead of the cipher-specific functions.</p>

<dl>

<dt id="EVP_MD_CTX_new">EVP_MD_CTX_new()</dt>
<dd>

<p>Allocates and returns a digest context.</p>

</dd>
<dt id="EVP_MD_CTX_reset">EVP_MD_CTX_reset()</dt>
<dd>

<p>Resets the digest context <b>ctx</b>. This can be used to reuse an already existing context.</p>

</dd>
<dt id="EVP_MD_CTX_free">EVP_MD_CTX_free()</dt>
<dd>

<p>Cleans up digest context <b>ctx</b> and frees up the space allocated to it.</p>

</dd>
<dt id="EVP_MD_CTX_ctrl">EVP_MD_CTX_ctrl()</dt>
<dd>

<p>Performs digest-specific control actions on context <b>ctx</b>. The control command is indicated in <b>cmd</b> and any additional arguments in <b>p1</b> and <b>p2</b>. EVP_MD_CTX_ctrl() must be called after EVP_DigestInit_ex(). Other restrictions may apply depending on the control type and digest implementation. See <a href="#CONTROLS">&quot;CONTROLS&quot;</a> below for more information.</p>

</dd>
<dt id="EVP_MD_CTX_set_flags-EVP_MD_CTX_clear_flags-EVP_MD_CTX_test_flags">EVP_MD_CTX_set_flags(), EVP_MD_CTX_clear_flags(), EVP_MD_CTX_test_flags()</dt>
<dd>

<p>Sets, clears and tests <b>ctx</b> flags. See <a href="#FLAGS">&quot;FLAGS&quot;</a> below for more information.</p>

</dd>
<dt id="EVP_Digest">EVP_Digest()</dt>
<dd>

<p>A wrapper around the Digest Init_ex, Update and Final_ex functions. Hashes <b>count</b> bytes of data at <b>data</b> using a digest <b>type</b> from ENGINE <b>impl</b>. The digest value is placed in <b>md</b> and its length is written at <b>size</b> if the pointer is not NULL. At most <b>EVP_MAX_MD_SIZE</b> bytes will be written. If <b>impl</b> is NULL the default implementation of digest <b>type</b> is used.</p>

</dd>
<dt id="EVP_DigestInit_ex">EVP_DigestInit_ex()</dt>
<dd>

<p>Sets up digest context <b>ctx</b> to use a digest <b>type</b> from ENGINE <b>impl</b>. <b>type</b> will typically be supplied by a function such as EVP_sha1(). If <b>impl</b> is NULL then the default implementation of digest <b>type</b> is used.</p>

</dd>
<dt id="EVP_DigestUpdate">EVP_DigestUpdate()</dt>
<dd>

<p>Hashes <b>cnt</b> bytes of data at <b>d</b> into the digest context <b>ctx</b>. This function can be called several times on the same <b>ctx</b> to hash additional data.</p>

</dd>
<dt id="EVP_DigestFinal_ex">EVP_DigestFinal_ex()</dt>
<dd>

<p>Retrieves the digest value from <b>ctx</b> and places it in <b>md</b>. If the <b>s</b> parameter is not NULL then the number of bytes of data written (i.e. the length of the digest) will be written to the integer at <b>s</b>, at most <b>EVP_MAX_MD_SIZE</b> bytes will be written. After calling EVP_DigestFinal_ex() no additional calls to EVP_DigestUpdate() can be made, but EVP_DigestInit_ex() can be called to initialize a new digest operation.</p>

</dd>
<dt id="EVP_DigestFinalXOF">EVP_DigestFinalXOF()</dt>
<dd>

<p>Interfaces to extendable-output functions, XOFs, such as SHAKE128 and SHAKE256. It retrieves the digest value from <b>ctx</b> and places it in <b>len</b>-sized &lt;B&gt;md. After calling this function no additional calls to EVP_DigestUpdate() can be made, but EVP_DigestInit_ex() can be called to initialize a new operation.</p>

</dd>
<dt id="EVP_MD_CTX_copy_ex">EVP_MD_CTX_copy_ex()</dt>
<dd>

<p>Can be used to copy the message digest state from <b>in</b> to <b>out</b>. This is useful if large amounts of data are to be hashed which only differ in the last few bytes.</p>

</dd>
<dt id="EVP_DigestInit">EVP_DigestInit()</dt>
<dd>

<p>Behaves in the same way as EVP_DigestInit_ex() except it always uses the default digest implementation and calls EVP_MD_CTX_reset().</p>

</dd>
<dt id="EVP_DigestFinal">EVP_DigestFinal()</dt>
<dd>

<p>Similar to EVP_DigestFinal_ex() except the digest context <b>ctx</b> is automatically cleaned up.</p>

</dd>
<dt id="EVP_MD_CTX_copy">EVP_MD_CTX_copy()</dt>
<dd>

<p>Similar to EVP_MD_CTX_copy_ex() except the destination <b>out</b> does not have to be initialized.</p>

</dd>
<dt id="EVP_MD_size-EVP_MD_CTX_size">EVP_MD_size(), EVP_MD_CTX_size()</dt>
<dd>

<p>Return the size of the message digest when passed an <b>EVP_MD</b> or an <b>EVP_MD_CTX</b> structure, i.e. the size of the hash.</p>

</dd>
<dt id="EVP_MD_block_size-EVP_MD_CTX_block_size">EVP_MD_block_size(), EVP_MD_CTX_block_size()</dt>
<dd>

<p>Return the block size of the message digest when passed an <b>EVP_MD</b> or an <b>EVP_MD_CTX</b> structure.</p>

</dd>
<dt id="EVP_MD_type-EVP_MD_CTX_type">EVP_MD_type(), EVP_MD_CTX_type()</dt>
<dd>

<p>Return the NID of the OBJECT IDENTIFIER representing the given message digest when passed an <b>EVP_MD</b> structure. For example, <code>EVP_MD_type(EVP_sha1())</code> returns <b>NID_sha1</b>. This function is normally used when setting ASN1 OIDs.</p>

</dd>
<dt id="EVP_MD_CTX_md_data">EVP_MD_CTX_md_data()</dt>
<dd>

<p>Return the digest method private data for the passed <b>EVP_MD_CTX</b>. The space is allocated by OpenSSL and has the size originally set with EVP_MD_meth_set_app_datasize().</p>

</dd>
<dt id="EVP_MD_CTX_md">EVP_MD_CTX_md()</dt>
<dd>

<p>Returns the <b>EVP_MD</b> structure corresponding to the passed <b>EVP_MD_CTX</b>.</p>

</dd>
<dt id="EVP_MD_CTX_set_update_fn">EVP_MD_CTX_set_update_fn()</dt>
<dd>

<p>Sets the update function for <b>ctx</b> to <b>update</b>. This is the function that is called by EVP_DigestUpdate. If not set, the update function from the <b>EVP_MD</b> type specified at initialization is used.</p>

</dd>
<dt id="EVP_MD_CTX_update_fn">EVP_MD_CTX_update_fn()</dt>
<dd>

<p>Returns the update function for <b>ctx</b>.</p>

</dd>
<dt id="EVP_MD_flags">EVP_MD_flags()</dt>
<dd>

<p>Returns the <b>md</b> flags. Note that these are different from the <b>EVP_MD_CTX</b> ones. See <a href="../man3/EVP_MD_meth_set_flags.html">EVP_MD_meth_set_flags(3)</a> for more information.</p>

</dd>
<dt id="EVP_MD_pkey_type">EVP_MD_pkey_type()</dt>
<dd>

<p>Returns the NID of the public key signing algorithm associated with this digest. For example EVP_sha1() is associated with RSA so this will return <b>NID_sha1WithRSAEncryption</b>. Since digests and signature algorithms are no longer linked this function is only retained for compatibility reasons.</p>

</dd>
<dt id="EVP_md_null">EVP_md_null()</dt>
<dd>

<p>A &quot;null&quot; message digest that does nothing: i.e. the hash it returns is of zero length.</p>

</dd>
<dt id="EVP_get_digestbyname-EVP_get_digestbynid-EVP_get_digestbyobj">EVP_get_digestbyname(), EVP_get_digestbynid(), EVP_get_digestbyobj()</dt>
<dd>

<p>Returns an <b>EVP_MD</b> structure when passed a digest name, a digest <b>NID</b> or an <b>ASN1_OBJECT</b> structure respectively.</p>

</dd>
<dt id="EVP_MD_CTX_pkey_ctx">EVP_MD_CTX_pkey_ctx()</dt>
<dd>

<p>Returns the <b>EVP_PKEY_CTX</b> assigned to <b>ctx</b>. The returned pointer should not be freed by the caller.</p>

</dd>
<dt id="EVP_MD_CTX_set_pkey_ctx">EVP_MD_CTX_set_pkey_ctx()</dt>
<dd>

<p>Assigns an <b>EVP_PKEY_CTX</b> to <b>EVP_MD_CTX</b>. This is usually used to provide a customized <b>EVP_PKEY_CTX</b> to <a href="../man3/EVP_DigestSignInit.html">EVP_DigestSignInit(3)</a> or <a href="../man3/EVP_DigestVerifyInit.html">EVP_DigestVerifyInit(3)</a>. The <b>pctx</b> passed to this function should be freed by the caller. A NULL <b>pctx</b> pointer is also allowed to clear the <b>EVP_PKEY_CTX</b> assigned to <b>ctx</b>. In such case, freeing the cleared <b>EVP_PKEY_CTX</b> or not depends on how the <b>EVP_PKEY_CTX</b> is created.</p>

</dd>
</dl>

<h1 id="CONTROLS">CONTROLS</h1>

<p>EVP_MD_CTX_ctrl() can be used to send the following standard controls:</p>

<dl>

<dt id="EVP_MD_CTRL_MICALG">EVP_MD_CTRL_MICALG</dt>
<dd>

<p>Gets the digest Message Integrity Check algorithm string. This is used when creating S/MIME multipart/signed messages, as specified in RFC 3851. The string value is written to <b>p2</b>.</p>

</dd>
<dt id="EVP_MD_CTRL_XOF_LEN">EVP_MD_CTRL_XOF_LEN</dt>
<dd>

<p>This control sets the digest length for extendable output functions to <b>p1</b>. Sending this control directly should not be necessary, the use of <code>EVP_DigestFinalXOF()</code> is preferred. Currently used by SHAKE.</p>

</dd>
</dl>

<h1 id="FLAGS">FLAGS</h1>

<p>EVP_MD_CTX_set_flags(), EVP_MD_CTX_clear_flags() and EVP_MD_CTX_test_flags() can be used the manipulate and test these <b>EVP_MD_CTX</b> flags:</p>

<dl>

<dt id="EVP_MD_CTX_FLAG_ONESHOT">EVP_MD_CTX_FLAG_ONESHOT</dt>
<dd>

<p>This flag instructs the digest to optimize for one update only, if possible.</p>

</dd>
<dt id="EVP_MD_CTX_FLAG_NO_INIT">EVP_MD_CTX_FLAG_NO_INIT</dt>
<dd>

<p>This flag instructs EVP_DigestInit() and similar not to initialise the implementation specific data.</p>

</dd>
<dt id="EVP_MD_CTX_FLAG_FINALISE">EVP_MD_CTX_FLAG_FINALISE</dt>
<dd>

<p>Some functions such as EVP_DigestSign only finalise copies of internal contexts so additional data can be included after the finalisation call. This is inefficient if this functionality is not required, and can be disabled with this flag.</p>

</dd>
</dl>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<dl>

<dt id="EVP_DigestInit_ex-EVP_DigestUpdate-EVP_DigestFinal_ex">EVP_DigestInit_ex(), EVP_DigestUpdate(), EVP_DigestFinal_ex()</dt>
<dd>

<p>Returns 1 for success and 0 for failure.</p>

</dd>
<dt id="EVP_MD_CTX_ctrl1">EVP_MD_CTX_ctrl()</dt>
<dd>

<p>Returns 1 if successful or 0 for failure.</p>

</dd>
<dt id="EVP_MD_CTX_copy_ex1">EVP_MD_CTX_copy_ex()</dt>
<dd>

<p>Returns 1 if successful or 0 for failure.</p>

</dd>
<dt id="EVP_MD_type-EVP_MD_pkey_type">EVP_MD_type(), EVP_MD_pkey_type()</dt>
<dd>

<p>Returns the NID of the corresponding OBJECT IDENTIFIER or NID_undef if none exists.</p>

</dd>
<dt id="EVP_MD_size-EVP_MD_block_size-EVP_MD_CTX_size-EVP_MD_CTX_block_size">EVP_MD_size(), EVP_MD_block_size(), EVP_MD_CTX_size(), EVP_MD_CTX_block_size()</dt>
<dd>

<p>Returns the digest or block size in bytes.</p>

</dd>
<dt id="EVP_md_null1">EVP_md_null()</dt>
<dd>

<p>Returns a pointer to the <b>EVP_MD</b> structure of the &quot;null&quot; message digest.</p>

</dd>
<dt id="EVP_get_digestbyname-EVP_get_digestbynid-EVP_get_digestbyobj1">EVP_get_digestbyname(), EVP_get_digestbynid(), EVP_get_digestbyobj()</dt>
<dd>

<p>Returns either an <b>EVP_MD</b> structure or NULL if an error occurs.</p>

</dd>
<dt id="EVP_MD_CTX_set_pkey_ctx1">EVP_MD_CTX_set_pkey_ctx()</dt>
<dd>

<p>This function has no return value.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>The <b>EVP</b> interface to message digests should almost always be used in preference to the low-level interfaces. This is because the code then becomes transparent to the digest used and much more flexible.</p>

<p>New applications should use the SHA-2 (such as <a href="../man3/EVP_sha256.html">EVP_sha256(3)</a>) or the SHA-3 digest algorithms (such as <a href="../man3/EVP_sha3_512.html">EVP_sha3_512(3)</a>). The other digest algorithms are still in common use.</p>

<p>For most applications the <b>impl</b> parameter to EVP_DigestInit_ex() will be set to NULL to use the default digest implementation.</p>

<p>The functions EVP_DigestInit(), EVP_DigestFinal() and EVP_MD_CTX_copy() are obsolete but are retained to maintain compatibility with existing code. New applications should use EVP_DigestInit_ex(), EVP_DigestFinal_ex() and EVP_MD_CTX_copy_ex() because they can efficiently reuse a digest context instead of initializing and cleaning it up on each call and allow non default implementations of digests to be specified.</p>

<p>If digest contexts are not cleaned up after use, memory leaks will occur.</p>

<p>EVP_MD_CTX_size(), EVP_MD_CTX_block_size(), EVP_MD_CTX_type(), EVP_get_digestbynid() and EVP_get_digestbyobj() are defined as macros.</p>

<p>EVP_MD_CTX_ctrl() sends commands to message digests for additional configuration or control.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>This example digests the data &quot;Test Message\n&quot; and &quot;Hello World\n&quot;, using the digest name passed on the command line.</p>

<pre><code>#include &lt;stdio.h&gt;
#include &lt;string.h&gt;
#include &lt;openssl/evp.h&gt;

int main(int argc, char *argv[])
{
    EVP_MD_CTX *mdctx;
    const EVP_MD *md;
    char mess1[] = &quot;Test Message\n&quot;;
    char mess2[] = &quot;Hello World\n&quot;;
    unsigned char md_value[EVP_MAX_MD_SIZE];
    unsigned int md_len, i;

    if (argv[1] == NULL) {
        printf(&quot;Usage: mdtest digestname\n&quot;);
        exit(1);
    }

    md = EVP_get_digestbyname(argv[1]);
    if (md == NULL) {
        printf(&quot;Unknown message digest %s\n&quot;, argv[1]);
        exit(1);
    }

    mdctx = EVP_MD_CTX_new();
    EVP_DigestInit_ex(mdctx, md, NULL);
    EVP_DigestUpdate(mdctx, mess1, strlen(mess1));
    EVP_DigestUpdate(mdctx, mess2, strlen(mess2));
    EVP_DigestFinal_ex(mdctx, md_value, &amp;md_len);
    EVP_MD_CTX_free(mdctx);

    printf(&quot;Digest is: &quot;);
    for (i = 0; i &lt; md_len; i++)
        printf(&quot;%02x&quot;, md_value[i]);
    printf(&quot;\n&quot;);

    exit(0);
}</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_MD_meth_new.html">EVP_MD_meth_new(3)</a>, <a href="../man1/dgst.html">dgst(1)</a>, <a href="../man7/evp.html">evp(7)</a></p>

<p>The full list of digest algorithms are provided below.</p>

<p><a href="../man3/EVP_blake2b512.html">EVP_blake2b512(3)</a>, <a href="../man3/EVP_md2.html">EVP_md2(3)</a>, <a href="../man3/EVP_md4.html">EVP_md4(3)</a>, <a href="../man3/EVP_md5.html">EVP_md5(3)</a>, <a href="../man3/EVP_mdc2.html">EVP_mdc2(3)</a>, <a href="../man3/EVP_ripemd160.html">EVP_ripemd160(3)</a>, <a href="../man3/EVP_sha1.html">EVP_sha1(3)</a>, <a href="../man3/EVP_sha224.html">EVP_sha224(3)</a>, <a href="../man3/EVP_sha3_224.html">EVP_sha3_224(3)</a>, <a href="../man3/EVP_sm3.html">EVP_sm3(3)</a>, <a href="../man3/EVP_whirlpool.html">EVP_whirlpool(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The EVP_MD_CTX_create() and EVP_MD_CTX_destroy() functions were renamed to EVP_MD_CTX_new() and EVP_MD_CTX_free() in OpenSSL 1.1.0, respectively.</p>

<p>The link between digests and signing algorithms was fixed in OpenSSL 1.0 and later, so now EVP_sha1() can be used with RSA and DSA.</p>

<p>The EVP_dss1() function was removed in OpenSSL 1.1.0.</p>

<p>The EVP_MD_CTX_set_pkey_ctx() function was added in 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


