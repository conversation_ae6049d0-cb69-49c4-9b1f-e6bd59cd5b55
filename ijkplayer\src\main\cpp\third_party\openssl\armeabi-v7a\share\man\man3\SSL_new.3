.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_NEW 3"
.TH SSL_NEW 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_dup, SSL_new, SSL_up_ref \- create an SSL structure for a connection
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& SSL *SSL_dup(SSL *s);
\& SSL *SSL_new(SSL_CTX *ctx);
\& int SSL_up_ref(SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_new()\fR creates a new \fBSSL\fR structure which is needed to hold the
data for a TLS/SSL connection. The new structure inherits the settings
of the underlying context \fBctx\fR: connection method,
options, verification settings, timeout settings. An \fBSSL\fR structure is
reference counted. Creating an \fBSSL\fR structure for the first time increments
the reference count. Freeing it (using SSL_free) decrements it. When the
reference count drops to zero, any memory or resources allocated to the \fBSSL\fR
structure are freed.
.PP
\&\fBSSL_up_ref()\fR increments the reference count for an
existing \fBSSL\fR structure.
.PP
The function \fBSSL_dup()\fR creates and returns a new \fBSSL\fR structure from the same
\&\fBSSL_CTX\fR that was used to create \fIs\fR. It additionally duplicates a subset of
the settings in \fIs\fR into the new \fBSSL\fR object.
.PP
For \fBSSL_dup()\fR to work, the connection MUST be in its initial state and
MUST NOT have yet started the SSL handshake.  For connections that are not in
their initial state \fBSSL_dup()\fR just increments an internal
reference count and returns the \fIsame\fR handle.  It may be possible to
use \fBSSL_clear\fR\|(3) to recycle an SSL handle that is not in its initial
state for re-use, but this is best avoided.  Instead, save and restore
the session, if desired, and construct a fresh handle for each connection.
.PP
The subset of settings in \fIs\fR that are duplicated are:
.IP "any session data if configured (including the session_id_context)" 4
.IX Item "any session data if configured (including the session_id_context)"
.PD 0
.IP "any tmp_dh settings set via \fBSSL_set_tmp_dh\fR\|(3), \fBSSL_set_tmp_dh_callback\fR\|(3), or \fBSSL_set_dh_auto\fR\|(3)" 4
.IX Item "any tmp_dh settings set via SSL_set_tmp_dh, SSL_set_tmp_dh_callback, or SSL_set_dh_auto"
.IP "any configured certificates, private keys or certificate chains" 4
.IX Item "any configured certificates, private keys or certificate chains"
.IP "any configured signature algorithms, or client signature algorithms" 4
.IX Item "any configured signature algorithms, or client signature algorithms"
.IP "any DANE settings" 4
.IX Item "any DANE settings"
.IP "any Options set via \fBSSL_set_options\fR\|(3)" 4
.IX Item "any Options set via SSL_set_options"
.IP "any Mode set via \fBSSL_set_mode\fR\|(3)" 4
.IX Item "any Mode set via SSL_set_mode"
.IP "any minimum or maximum protocol settings set via \fBSSL_set_min_proto_version\fR\|(3) or \fBSSL_set_max_proto_version\fR\|(3) (Note: Only from OpenSSL 1.1.1h and above)" 4
.IX Item "any minimum or maximum protocol settings set via SSL_set_min_proto_version or SSL_set_max_proto_version (Note: Only from OpenSSL 1.1.1h and above)"
.IP "any Verify mode, callback or depth set via \fBSSL_set_verify\fR\|(3) or \fBSSL_set_verify_depth\fR\|(3) or any configured X509 verification parameters" 4
.IX Item "any Verify mode, callback or depth set via SSL_set_verify or SSL_set_verify_depth or any configured X509 verification parameters"
.IP "any msg callback or info callback set via \fBSSL_set_msg_callback\fR\|(3) or \fBSSL_set_info_callback\fR\|(3)" 4
.IX Item "any msg callback or info callback set via SSL_set_msg_callback or SSL_set_info_callback"
.IP "any default password callback set via \fBSSL_set_default_passwd_cb\fR\|(3)" 4
.IX Item "any default password callback set via SSL_set_default_passwd_cb"
.IP "any session id generation callback set via \fBSSL_set_generate_session_id\fR\|(3)" 4
.IX Item "any session id generation callback set via SSL_set_generate_session_id"
.IP "any configured Cipher List" 4
.IX Item "any configured Cipher List"
.IP "initial accept (server) or connect (client) state" 4
.IX Item "initial accept (server) or connect (client) state"
.IP "the max cert list value set via \fBSSL_set_max_cert_list\fR\|(3)" 4
.IX Item "the max cert list value set via SSL_set_max_cert_list"
.IP "the read_ahead value set via \fBSSL_set_read_ahead\fR\|(3)" 4
.IX Item "the read_ahead value set via SSL_set_read_ahead"
.IP "application specific data set via \fBSSL_set_ex_data\fR\|(3)" 4
.IX Item "application specific data set via SSL_set_ex_data"
.IP "any CA list or client CA list set via \fBSSL_set0_CA_list\fR\|(3), \fBSSL_set0_client_CA_list()\fR or similar functions" 4
.IX Item "any CA list or client CA list set via SSL_set0_CA_list, SSL_set0_client_CA_list() or similar functions"
.IP "any security level settings or callbacks" 4
.IX Item "any security level settings or callbacks"
.IP "any configured serverinfo data" 4
.IX Item "any configured serverinfo data"
.IP "any configured PSK identity hint" 4
.IX Item "any configured PSK identity hint"
.IP "any configured custom extensions" 4
.IX Item "any configured custom extensions"
.IP "any client certificate types configured via SSL_set1_client_certificate_types" 4
.IX Item "any client certificate types configured via SSL_set1_client_certificate_types"
.PD
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur:
.IP NULL 4
.IX Item "NULL"
The creation of a new SSL structure failed. Check the error stack to
find out the reason.
.IP "Pointer to an SSL structure" 4
.IX Item "Pointer to an SSL structure"
The return value points to an allocated SSL structure.
.Sp
\&\fBSSL_up_ref()\fR returns 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_free\fR\|(3), \fBSSL_clear\fR\|(3),
\&\fBSSL_CTX_set_options\fR\|(3),
\&\fBSSL_get_SSL_CTX\fR\|(3),
\&\fBssl\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
