<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_des</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_des_cbc, EVP_des_cfb, EVP_des_cfb1, EVP_des_cfb8, EVP_des_cfb64, EVP_des_ecb, EVP_des_ofb, EVP_des_ede, EVP_des_ede_cbc, EVP_des_ede_cfb, EVP_des_ede_cfb64, EVP_des_ede_ecb, EVP_des_ede_ofb, EVP_des_ede3, EVP_des_ede3_cbc, EVP_des_ede3_cfb, EVP_des_ede3_cfb1, EVP_des_ede3_cfb8, EVP_des_ede3_cfb64, EVP_des_ede3_ecb, EVP_des_ede3_ofb, EVP_des_ede3_wrap - EVP DES cipher</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

const EVP_CIPHER *EVP_ciphername(void)</code></pre>

<p><i>EVP_ciphername</i> is used a placeholder for any of the described cipher functions, such as <i>EVP_des_cbc</i>.</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The DES encryption algorithm for EVP.</p>

<dl>

<dt id="EVP_des_cbc-EVP_des_ecb-EVP_des_cfb-EVP_des_cfb1-EVP_des_cfb8-EVP_des_cfb64-EVP_des_ofb">EVP_des_cbc(), EVP_des_ecb(), EVP_des_cfb(), EVP_des_cfb1(), EVP_des_cfb8(), EVP_des_cfb64(), EVP_des_ofb()</dt>
<dd>

<p>DES in CBC, ECB, CFB with 64-bit shift, CFB with 1-bit shift, CFB with 8-bit shift and OFB modes.</p>

</dd>
<dt id="EVP_des_ede-EVP_des_ede_cbc-EVP_des_ede_cfb-EVP_des_ede_cfb64-EVP_des_ede_ecb-EVP_des_ede_ofb">EVP_des_ede(), EVP_des_ede_cbc(), EVP_des_ede_cfb(), EVP_des_ede_cfb64(), EVP_des_ede_ecb(), EVP_des_ede_ofb()</dt>
<dd>

<p>Two key triple DES in ECB, CBC, CFB with 64-bit shift and OFB modes.</p>

</dd>
<dt id="EVP_des_ede3-EVP_des_ede3_cbc-EVP_des_ede3_cfb-EVP_des_ede3_cfb1-EVP_des_ede3_cfb8-EVP_des_ede3_cfb64-EVP_des_ede3_ecb-EVP_des_ede3_ofb">EVP_des_ede3(), EVP_des_ede3_cbc(), EVP_des_ede3_cfb(), EVP_des_ede3_cfb1(), EVP_des_ede3_cfb8(), EVP_des_ede3_cfb64(), EVP_des_ede3_ecb(), EVP_des_ede3_ofb()</dt>
<dd>

<p>Three-key triple DES in ECB, CBC, CFB with 64-bit shift, CFB with 1-bit shift, CFB with 8-bit shift and OFB modes.</p>

</dd>
<dt id="EVP_des_ede3_wrap">EVP_des_ede3_wrap()</dt>
<dd>

<p>Triple-DES key wrap according to RFC 3217 Section 3.</p>

</dd>
</dl>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>These functions return an <b>EVP_CIPHER</b> structure that contains the implementation of the symmetric cipher. See <a href="../man3/EVP_CIPHER_meth_new.html">EVP_CIPHER_meth_new(3)</a> for details of the <b>EVP_CIPHER</b> structure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/evp.html">evp(7)</a>, <a href="../man3/EVP_EncryptInit.html">EVP_EncryptInit(3)</a>, <a href="../man3/EVP_CIPHER_meth_new.html">EVP_CIPHER_meth_new(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


