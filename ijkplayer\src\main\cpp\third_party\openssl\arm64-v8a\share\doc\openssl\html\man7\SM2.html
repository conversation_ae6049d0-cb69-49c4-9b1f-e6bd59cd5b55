<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SM2</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SM2 - Chinese SM2 signature and encryption algorithm support</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>SM2</b> algorithm was first defined by the Chinese national standard GM/T 0003-2012 and was later standardized by ISO as ISO/IEC 14888. <b>SM2</b> is actually an elliptic curve based algorithm. The current implementation in OpenSSL supports both signature and encryption schemes via the EVP interface.</p>

<p>When doing the <b>SM2</b> signature algorithm, it requires a distinguishing identifier to form the message prefix which is hashed before the real message is hashed.</p>

<h1 id="NOTES">NOTES</h1>

<p><b>SM2</b> signatures can be generated by using the &#39;DigestSign&#39; series of APIs, for instance, EVP_DigestSignInit(), EVP_DigestSignUpdate() and EVP_DigestSignFinal(). Ditto for the verification process by calling the &#39;DigestVerify&#39; series of APIs.</p>

<p>There are several special steps that need to be done before computing an <b>SM2</b> signature.</p>

<p>The <b>EVP_PKEY</b> structure will default to using ECDSA for signatures when it is created. It should be set to <b>EVP_PKEY_SM2</b> by calling:</p>

<pre><code>EVP_PKEY_set_alias_type(pkey, EVP_PKEY_SM2);</code></pre>

<p>Then an ID should be set by calling:</p>

<pre><code>EVP_PKEY_CTX_set1_id(pctx, id, id_len);</code></pre>

<p>When calling the EVP_DigestSignInit() or EVP_DigestVerifyInit() functions, a preallocated <b>EVP_PKEY_CTX</b> should be assigned to the <b>EVP_MD_CTX</b>. This is done by calling:</p>

<pre><code>EVP_MD_CTX_set_pkey_ctx(mctx, pctx);</code></pre>

<p>And normally there is no need to pass a <b>pctx</b> parameter to EVP_DigestSignInit() or EVP_DigestVerifyInit() in such a scenario.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>This example demonstrates the calling sequence for using an <b>EVP_PKEY</b> to verify a message with the SM2 signature algorithm and the SM3 hash algorithm:</p>

<pre><code>#include &lt;openssl/evp.h&gt;

/* obtain an EVP_PKEY using whatever methods... */
EVP_PKEY_set_alias_type(pkey, EVP_PKEY_SM2);
mctx = EVP_MD_CTX_new();
pctx = EVP_PKEY_CTX_new(pkey, NULL);
EVP_PKEY_CTX_set1_id(pctx, id, id_len);
EVP_MD_CTX_set_pkey_ctx(mctx, pctx);;
EVP_DigestVerifyInit(mctx, NULL, EVP_sm3(), NULL, pkey);
EVP_DigestVerifyUpdate(mctx, msg, msg_len);
EVP_DigestVerifyFinal(mctx, sig, sig_len)</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_PKEY_CTX_new.html">EVP_PKEY_CTX_new(3)</a>, <a href="../man3/EVP_PKEY_set_alias_type.html">EVP_PKEY_set_alias_type(3)</a>, <a href="../man3/EVP_DigestSignInit.html">EVP_DigestSignInit(3)</a>, <a href="../man3/EVP_DigestVerifyInit.html">EVP_DigestVerifyInit(3)</a>, <a href="../man3/EVP_PKEY_CTX_set1_id.html">EVP_PKEY_CTX_set1_id(3)</a>, <a href="../man3/EVP_MD_CTX_set_pkey_ctx.html">EVP_MD_CTX_set_pkey_ctx(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2018-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


