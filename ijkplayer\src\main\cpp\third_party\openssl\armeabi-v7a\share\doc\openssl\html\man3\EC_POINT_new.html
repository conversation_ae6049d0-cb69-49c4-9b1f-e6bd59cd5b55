<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EC_POINT_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EC_POINT_set_Jprojective_coordinates_GFp, EC_POINT_point2buf, EC_POINT_new, EC_POINT_free, EC_POINT_clear_free, EC_POINT_copy, EC_POINT_dup, EC_POINT_method_of, EC_POINT_set_to_infinity, EC_POINT_get_Jprojective_coordinates_GFp, EC_POINT_set_affine_coordinates, EC_POINT_get_affine_coordinates, EC_POINT_set_compressed_coordinates, EC_POINT_set_affine_coordinates_GFp, EC_POINT_get_affine_coordinates_GFp, EC_POINT_set_compressed_coordinates_GFp, EC_POINT_set_affine_coordinates_GF2m, EC_POINT_get_affine_coordinates_GF2m, EC_POINT_set_compressed_coordinates_GF2m, EC_POINT_point2oct, EC_POINT_oct2point, EC_POINT_point2bn, EC_POINT_bn2point, EC_POINT_point2hex, EC_POINT_hex2point - Functions for creating, destroying and manipulating EC_POINT objects</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ec.h&gt;

EC_POINT *EC_POINT_new(const EC_GROUP *group);
void EC_POINT_free(EC_POINT *point);
void EC_POINT_clear_free(EC_POINT *point);
int EC_POINT_copy(EC_POINT *dst, const EC_POINT *src);
EC_POINT *EC_POINT_dup(const EC_POINT *src, const EC_GROUP *group);
const EC_METHOD *EC_POINT_method_of(const EC_POINT *point);
int EC_POINT_set_to_infinity(const EC_GROUP *group, EC_POINT *point);
int EC_POINT_set_Jprojective_coordinates_GFp(const EC_GROUP *group,
                                             EC_POINT *p,
                                             const BIGNUM *x, const BIGNUM *y,
                                             const BIGNUM *z, BN_CTX *ctx);
int EC_POINT_get_Jprojective_coordinates_GFp(const EC_GROUP *group,
                                             const EC_POINT *p,
                                             BIGNUM *x, BIGNUM *y, BIGNUM *z,
                                             BN_CTX *ctx);
int EC_POINT_set_affine_coordinates(const EC_GROUP *group, EC_POINT *p,
                                    const BIGNUM *x, const BIGNUM *y,
                                    BN_CTX *ctx);
int EC_POINT_get_affine_coordinates(const EC_GROUP *group, const EC_POINT *p,
                                    BIGNUM *x, BIGNUM *y, BN_CTX *ctx);
int EC_POINT_set_compressed_coordinates(const EC_GROUP *group, EC_POINT *p,
                                        const BIGNUM *x, int y_bit,
                                        BN_CTX *ctx);
int EC_POINT_set_affine_coordinates_GFp(const EC_GROUP *group, EC_POINT *p,
                                        const BIGNUM *x, const BIGNUM *y,
                                        BN_CTX *ctx);
int EC_POINT_get_affine_coordinates_GFp(const EC_GROUP *group,
                                        const EC_POINT *p,
                                        BIGNUM *x, BIGNUM *y, BN_CTX *ctx);
int EC_POINT_set_compressed_coordinates_GFp(const EC_GROUP *group,
                                            EC_POINT *p,
                                            const BIGNUM *x, int y_bit,
                                            BN_CTX *ctx);
int EC_POINT_set_affine_coordinates_GF2m(const EC_GROUP *group, EC_POINT *p,
                                         const BIGNUM *x, const BIGNUM *y,
                                         BN_CTX *ctx);
int EC_POINT_get_affine_coordinates_GF2m(const EC_GROUP *group,
                                         const EC_POINT *p,
                                         BIGNUM *x, BIGNUM *y, BN_CTX *ctx);
int EC_POINT_set_compressed_coordinates_GF2m(const EC_GROUP *group,
                                             EC_POINT *p,
                                             const BIGNUM *x, int y_bit,
                                             BN_CTX *ctx);
size_t EC_POINT_point2oct(const EC_GROUP *group, const EC_POINT *p,
                          point_conversion_form_t form,
                          unsigned char *buf, size_t len, BN_CTX *ctx);
size_t EC_POINT_point2buf(const EC_GROUP *group, const EC_POINT *point,
                          point_conversion_form_t form,
                          unsigned char **pbuf, BN_CTX *ctx);
int EC_POINT_oct2point(const EC_GROUP *group, EC_POINT *p,
                       const unsigned char *buf, size_t len, BN_CTX *ctx);
BIGNUM *EC_POINT_point2bn(const EC_GROUP *group, const EC_POINT *p,
                          point_conversion_form_t form, BIGNUM *bn,
                          BN_CTX *ctx);
EC_POINT *EC_POINT_bn2point(const EC_GROUP *group, const BIGNUM *bn,
                            EC_POINT *p, BN_CTX *ctx);
char *EC_POINT_point2hex(const EC_GROUP *group, const EC_POINT *p,
                         point_conversion_form_t form, BN_CTX *ctx);
EC_POINT *EC_POINT_hex2point(const EC_GROUP *group, const char *hex,
                             EC_POINT *p, BN_CTX *ctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>An <b>EC_POINT</b> structure represents a point on a curve. A new point is constructed by calling the function EC_POINT_new() and providing the <b>group</b> object that the point relates to.</p>

<p>EC_POINT_free() frees the memory associated with the <b>EC_POINT</b>. if <b>point</b> is NULL nothing is done.</p>

<p>EC_POINT_clear_free() destroys any sensitive data held within the EC_POINT and then frees its memory. If <b>point</b> is NULL nothing is done.</p>

<p>EC_POINT_copy() copies the point <b>src</b> into <b>dst</b>. Both <b>src</b> and <b>dst</b> must use the same <b>EC_METHOD</b>.</p>

<p>EC_POINT_dup() creates a new <b>EC_POINT</b> object and copies the content from <b>src</b> to the newly created <b>EC_POINT</b> object.</p>

<p>EC_POINT_method_of() obtains the <b>EC_METHOD</b> associated with <b>point</b>.</p>

<p>A valid point on a curve is the special point at infinity. A point is set to be at infinity by calling EC_POINT_set_to_infinity().</p>

<p>The affine co-ordinates for a point describe a point in terms of its x and y position. The function EC_POINT_set_affine_coordinates() sets the <b>x</b> and <b>y</b> co-ordinates for the point <b>p</b> defined over the curve given in <b>group</b>. The function EC_POINT_get_affine_coordinates() sets <b>x</b> and <b>y</b>, either of which may be NULL, to the corresponding coordinates of <b>p</b>.</p>

<p>The functions EC_POINT_set_affine_coordinates_GFp() and EC_POINT_set_affine_coordinates_GF2m() are synonyms for EC_POINT_set_affine_coordinates(). They are defined for backwards compatibility only and should not be used.</p>

<p>The functions EC_POINT_get_affine_coordinates_GFp() and EC_POINT_get_affine_coordinates_GF2m() are synonyms for EC_POINT_get_affine_coordinates(). They are defined for backwards compatibility only and should not be used.</p>

<p>As well as the affine co-ordinates, a point can alternatively be described in terms of its Jacobian projective co-ordinates (for Fp curves only). Jacobian projective co-ordinates are expressed as three values x, y and z. Working in this co-ordinate system provides more efficient point multiplication operations. A mapping exists between Jacobian projective co-ordinates and affine co-ordinates. A Jacobian projective co-ordinate (x, y, z) can be written as an affine co-ordinate as (x/(z^2), y/(z^3)). Conversion to Jacobian projective from affine co-ordinates is simple. The co-ordinate (x, y) is mapped to (x, y, 1). To set or get the projective co-ordinates use EC_POINT_set_Jprojective_coordinates_GFp() and EC_POINT_get_Jprojective_coordinates_GFp() respectively.</p>

<p>Points can also be described in terms of their compressed co-ordinates. For a point (x, y), for any given value for x such that the point is on the curve there will only ever be two possible values for y. Therefore, a point can be set using the EC_POINT_set_compressed_coordinates() function where <b>x</b> is the x co-ordinate and <b>y_bit</b> is a value 0 or 1 to identify which of the two possible values for y should be used.</p>

<p>The functions EC_POINT_set_compressed_coordinates_GFp() and EC_POINT_set_compressed_coordinates_GF2m() are synonyms for EC_POINT_set_compressed_coordinates(). They are defined for backwards compatibility only and should not be used.</p>

<p>In addition <b>EC_POINT</b> can be converted to and from various external representations. The octet form is the binary encoding of the <b>ECPoint</b> structure (as defined in RFC5480 and used in certificates and TLS records): only the content octets are present, the <b>OCTET STRING</b> tag and length are not included. <b>BIGNUM</b> form is the octet form interpreted as a big endian integer converted to a <b>BIGNUM</b> structure. Hexadecimal form is the octet form converted to a NULL terminated character string where each character is one of the printable values 0-9 or A-F (or a-f).</p>

<p>The functions EC_POINT_point2oct(), EC_POINT_oct2point(), EC_POINT_point2bn(), EC_POINT_bn2point(), EC_POINT_point2hex() and EC_POINT_hex2point() convert from and to EC_POINTs for the formats: octet, BIGNUM and hexadecimal respectively.</p>

<p>The function EC_POINT_point2oct() encodes the given curve point <b>p</b> as an octet string into the buffer <b>buf</b> of size <b>len</b>, using the specified conversion form <b>form</b>. The encoding conforms with Sec. 2.3.3 of the SECG SEC 1 (&quot;Elliptic Curve Cryptography&quot;) standard. Similarly the function EC_POINT_oct2point() decodes a curve point into <b>p</b> from the octet string contained in the given buffer <b>buf</b> of size <b>len</b>, conforming to Sec. 2.3.4 of the SECG SEC 1 (&quot;Elliptic Curve Cryptography&quot;) standard.</p>

<p>The functions EC_POINT_point2hex() and EC_POINT_point2bn() convert a point <b>p</b>, respectively, to the hexadecimal or BIGNUM representation of the same encoding of the function EC_POINT_point2oct(). Vice versa, similarly to the function EC_POINT_oct2point(), the functions EC_POINT_hex2point() and EC_POINT_point2bn() decode the hexadecimal or BIGNUM representation into the EC_POINT <b>p</b>.</p>

<p>Notice that, according to the standard, the octet string encoding of the point at infinity for a given curve is fixed to a single octet of value zero and that, vice versa, a single octet of size zero is decoded as the point at infinity.</p>

<p>The function EC_POINT_point2oct() must be supplied with a buffer long enough to store the octet form. The return value provides the number of octets stored. Calling the function with a NULL buffer will not perform the conversion but will still return the required buffer length.</p>

<p>The function EC_POINT_point2buf() allocates a buffer of suitable length and writes an EC_POINT to it in octet format. The allocated buffer is written to <b>*pbuf</b> and its length is returned. The caller must free up the allocated buffer with a call to OPENSSL_free(). Since the allocated buffer value is written to <b>*pbuf</b> the <b>pbuf</b> parameter <b>MUST NOT</b> be <b>NULL</b>.</p>

<p>The function EC_POINT_point2hex() will allocate sufficient memory to store the hexadecimal string. It is the caller&#39;s responsibility to free this memory with a subsequent call to OPENSSL_free().</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EC_POINT_new() and EC_POINT_dup() return the newly allocated EC_POINT or NULL on error.</p>

<p>The following functions return 1 on success or 0 on error: EC_POINT_copy(), EC_POINT_set_to_infinity(), EC_POINT_set_Jprojective_coordinates_GFp(), EC_POINT_get_Jprojective_coordinates_GFp(), EC_POINT_set_affine_coordinates_GFp(), EC_POINT_get_affine_coordinates_GFp(), EC_POINT_set_compressed_coordinates_GFp(), EC_POINT_set_affine_coordinates_GF2m(), EC_POINT_get_affine_coordinates_GF2m(), EC_POINT_set_compressed_coordinates_GF2m() and EC_POINT_oct2point().</p>

<p>EC_POINT_method_of returns the EC_METHOD associated with the supplied EC_POINT.</p>

<p>EC_POINT_point2oct() and EC_POINT_point2buf() return the length of the required buffer or 0 on error.</p>

<p>EC_POINT_point2bn() returns the pointer to the BIGNUM supplied, or NULL on error.</p>

<p>EC_POINT_bn2point() returns the pointer to the EC_POINT supplied, or NULL on error.</p>

<p>EC_POINT_point2hex() returns a pointer to the hex string, or NULL on error.</p>

<p>EC_POINT_hex2point() returns the pointer to the EC_POINT supplied, or NULL on error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/crypto.html">crypto(7)</a>, <a href="../man3/EC_GROUP_new.html">EC_GROUP_new(3)</a>, <a href="../man3/EC_GROUP_copy.html">EC_GROUP_copy(3)</a>, <a href="../man3/EC_POINT_add.html">EC_POINT_add(3)</a>, <a href="../man3/EC_KEY_new.html">EC_KEY_new(3)</a>, <a href="../man3/EC_GFp_simple_method.html">EC_GFp_simple_method(3)</a>, <a href="../man3/d2i_ECPKParameters.html">d2i_ECPKParameters(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2013-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


