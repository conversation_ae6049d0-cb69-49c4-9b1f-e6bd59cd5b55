.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_AES 3"
.TH EVP_AES 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_aes_128_cbc,
EVP_aes_192_cbc,
EVP_aes_256_cbc,
EVP_aes_128_cfb,
EVP_aes_192_cfb,
EVP_aes_256_cfb,
EVP_aes_128_cfb1,
EVP_aes_192_cfb1,
EVP_aes_256_cfb1,
EVP_aes_128_cfb8,
EVP_aes_192_cfb8,
EVP_aes_256_cfb8,
EVP_aes_128_cfb128,
EVP_aes_192_cfb128,
EVP_aes_256_cfb128,
EVP_aes_128_ctr,
EVP_aes_192_ctr,
EVP_aes_256_ctr,
EVP_aes_128_ecb,
EVP_aes_192_ecb,
EVP_aes_256_ecb,
EVP_aes_128_ofb,
EVP_aes_192_ofb,
EVP_aes_256_ofb,
EVP_aes_128_cbc_hmac_sha1,
EVP_aes_256_cbc_hmac_sha1,
EVP_aes_128_cbc_hmac_sha256,
EVP_aes_256_cbc_hmac_sha256,
EVP_aes_128_ccm,
EVP_aes_192_ccm,
EVP_aes_256_ccm,
EVP_aes_128_gcm,
EVP_aes_192_gcm,
EVP_aes_256_gcm,
EVP_aes_128_ocb,
EVP_aes_192_ocb,
EVP_aes_256_ocb,
EVP_aes_128_wrap,
EVP_aes_192_wrap,
EVP_aes_256_wrap,
EVP_aes_128_wrap_pad,
EVP_aes_192_wrap_pad,
EVP_aes_256_wrap_pad,
EVP_aes_128_xts,
EVP_aes_256_xts
\&\- EVP AES cipher
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& const EVP_CIPHER *EVP_ciphername(void)
.Ve
.PP
\&\fIEVP_ciphername\fR is used a placeholder for any of the described cipher
functions, such as \fIEVP_aes_128_cbc\fR.
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The AES encryption algorithm for EVP.
.IP "\fBEVP_aes_128_cbc()\fR, \fBEVP_aes_192_cbc()\fR, \fBEVP_aes_256_cbc()\fR, \fBEVP_aes_128_cfb()\fR, \fBEVP_aes_192_cfb()\fR, \fBEVP_aes_256_cfb()\fR, \fBEVP_aes_128_cfb1()\fR, \fBEVP_aes_192_cfb1()\fR, \fBEVP_aes_256_cfb1()\fR, \fBEVP_aes_128_cfb8()\fR, \fBEVP_aes_192_cfb8()\fR, \fBEVP_aes_256_cfb8()\fR, \fBEVP_aes_128_cfb128()\fR, \fBEVP_aes_192_cfb128()\fR, \fBEVP_aes_256_cfb128()\fR, \fBEVP_aes_128_ctr()\fR, \fBEVP_aes_192_ctr()\fR, \fBEVP_aes_256_ctr()\fR, \fBEVP_aes_128_ecb()\fR, \fBEVP_aes_192_ecb()\fR, \fBEVP_aes_256_ecb()\fR, \fBEVP_aes_128_ofb()\fR, \fBEVP_aes_192_ofb()\fR, \fBEVP_aes_256_ofb()\fR" 4
.IX Item "EVP_aes_128_cbc(), EVP_aes_192_cbc(), EVP_aes_256_cbc(), EVP_aes_128_cfb(), EVP_aes_192_cfb(), EVP_aes_256_cfb(), EVP_aes_128_cfb1(), EVP_aes_192_cfb1(), EVP_aes_256_cfb1(), EVP_aes_128_cfb8(), EVP_aes_192_cfb8(), EVP_aes_256_cfb8(), EVP_aes_128_cfb128(), EVP_aes_192_cfb128(), EVP_aes_256_cfb128(), EVP_aes_128_ctr(), EVP_aes_192_ctr(), EVP_aes_256_ctr(), EVP_aes_128_ecb(), EVP_aes_192_ecb(), EVP_aes_256_ecb(), EVP_aes_128_ofb(), EVP_aes_192_ofb(), EVP_aes_256_ofb()"
AES for 128, 192 and 256 bit keys in the following modes: CBC, CFB with 128\-bit
shift, CFB with 1\-bit shift, CFB with 8\-bit shift, CTR, ECB, and OFB.
.IP "\fBEVP_aes_128_cbc_hmac_sha1()\fR, \fBEVP_aes_256_cbc_hmac_sha1()\fR" 4
.IX Item "EVP_aes_128_cbc_hmac_sha1(), EVP_aes_256_cbc_hmac_sha1()"
Authenticated encryption with AES in CBC mode using SHA\-1 as HMAC, with keys of
128 and 256 bits length respectively. The authentication tag is 160 bits long.
.Sp
WARNING: this is not intended for usage outside of TLS and requires calling of
some undocumented ctrl functions. These ciphers do not conform to the EVP AEAD
interface.
.IP "\fBEVP_aes_128_cbc_hmac_sha256()\fR, \fBEVP_aes_256_cbc_hmac_sha256()\fR" 4
.IX Item "EVP_aes_128_cbc_hmac_sha256(), EVP_aes_256_cbc_hmac_sha256()"
Authenticated encryption with AES in CBC mode using SHA256 (SHA\-2, 256\-bits) as
HMAC, with keys of 128 and 256 bits length respectively. The authentication tag
is 256 bits long.
.Sp
WARNING: this is not intended for usage outside of TLS and requires calling of
some undocumented ctrl functions. These ciphers do not conform to the EVP AEAD
interface.
.IP "\fBEVP_aes_128_ccm()\fR, \fBEVP_aes_192_ccm()\fR, \fBEVP_aes_256_ccm()\fR, \fBEVP_aes_128_gcm()\fR, \fBEVP_aes_192_gcm()\fR, \fBEVP_aes_256_gcm()\fR, \fBEVP_aes_128_ocb()\fR, \fBEVP_aes_192_ocb()\fR, \fBEVP_aes_256_ocb()\fR" 4
.IX Item "EVP_aes_128_ccm(), EVP_aes_192_ccm(), EVP_aes_256_ccm(), EVP_aes_128_gcm(), EVP_aes_192_gcm(), EVP_aes_256_gcm(), EVP_aes_128_ocb(), EVP_aes_192_ocb(), EVP_aes_256_ocb()"
AES for 128, 192 and 256 bit keys in CBC-MAC Mode (CCM), Galois Counter Mode
(GCM) and OCB Mode respectively. These ciphers require additional control
operations to function correctly, see the "AEAD Interface" in \fBEVP_EncryptInit\fR\|(3)
section for details.
.IP "\fBEVP_aes_128_wrap()\fR, \fBEVP_aes_192_wrap()\fR, \fBEVP_aes_256_wrap()\fR, \fBEVP_aes_128_wrap_pad()\fR, \fBEVP_aes_128_wrap()\fR, \fBEVP_aes_192_wrap()\fR, \fBEVP_aes_256_wrap()\fR, \fBEVP_aes_192_wrap_pad()\fR, \fBEVP_aes_128_wrap()\fR, \fBEVP_aes_192_wrap()\fR, \fBEVP_aes_256_wrap()\fR, \fBEVP_aes_256_wrap_pad()\fR" 4
.IX Item "EVP_aes_128_wrap(), EVP_aes_192_wrap(), EVP_aes_256_wrap(), EVP_aes_128_wrap_pad(), EVP_aes_128_wrap(), EVP_aes_192_wrap(), EVP_aes_256_wrap(), EVP_aes_192_wrap_pad(), EVP_aes_128_wrap(), EVP_aes_192_wrap(), EVP_aes_256_wrap(), EVP_aes_256_wrap_pad()"
AES key wrap with 128, 192 and 256 bit keys, as according to RFC 3394 section
2.2.1 ("wrap") and RFC 5649 section 4.1 ("wrap with padding") respectively.
.IP "\fBEVP_aes_128_xts()\fR, \fBEVP_aes_256_xts()\fR" 4
.IX Item "EVP_aes_128_xts(), EVP_aes_256_xts()"
AES XTS mode (XTS-AES) is standardized in IEEE Std. 1619\-2007 and described in NIST
SP 800\-38E. The XTS (XEX-based tweaked-codebook mode with ciphertext stealing)
mode was designed by Prof. Phillip Rogaway of University of California, Davis,
intended for encrypting data on a storage device.
.Sp
XTS-AES provides confidentiality but not authentication of data. It also
requires a key of double-length for protection of a certain key size.
In particular, XTS\-AES\-128 (\fBEVP_aes_128_xts\fR) takes input of a 256\-bit key to
achieve AES 128\-bit security, and XTS\-AES\-256 (\fBEVP_aes_256_xts\fR) takes input
of a 512\-bit key to achieve AES 256\-bit security.
.Sp
The XTS implementation in OpenSSL does not support streaming. That is there must
only be one \fBEVP_EncryptUpdate\fR\|(3) call per \fBEVP_EncryptInit_ex\fR\|(3) call (and
similarly with the "Decrypt" functions).
.Sp
The \fIiv\fR parameter to \fBEVP_EncryptInit_ex\fR\|(3) or \fBEVP_DecryptInit_ex\fR\|(3) is
the XTS "tweak" value.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return an \fBEVP_CIPHER\fR structure that contains the
implementation of the symmetric cipher. See \fBEVP_CIPHER_meth_new\fR\|(3) for
details of the \fBEVP_CIPHER\fR structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7),
\&\fBEVP_EncryptInit\fR\|(3),
\&\fBEVP_CIPHER_meth_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
