<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_cmp_time</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_cmp_time, X509_cmp_current_time, X509_time_adj, X509_time_adj_ex - X509 time functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>int X509_cmp_time(const ASN1_TIME *asn1_time, time_t *in_tm);
int X509_cmp_current_time(const ASN1_TIME *asn1_time);
ASN1_TIME *X509_time_adj(ASN1_TIME *asn1_time, long offset_sec, time_t *in_tm);
ASN1_TIME *X509_time_adj_ex(ASN1_TIME *asn1_time, int offset_day, long
                            offset_sec, time_t *in_tm);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>X509_cmp_time() compares the ASN1_TIME in <b>asn1_time</b> with the time in &lt;cmp_time&gt;. X509_cmp_current_time() compares the ASN1_TIME in <b>asn1_time</b> with the current time, expressed as time_t. <b>asn1_time</b> must satisfy the ASN1_TIME format mandated by RFC 5280, i.e., its format must be either YYMMDDHHMMSSZ or YYYYMMDDHHMMSSZ.</p>

<p>X509_time_adj_ex() sets the ASN1_TIME structure <b>asn1_time</b> to the time <b>offset_day</b> and <b>offset_sec</b> after <b>in_tm</b>.</p>

<p>X509_time_adj() sets the ASN1_TIME structure <b>asn1_time</b> to the time <b>offset_sec</b> after <b>in_tm</b>. This method can only handle second offsets up to the capacity of long, so the newer X509_time_adj_ex() API should be preferred.</p>

<p>In both methods, if <b>asn1_time</b> is NULL, a new ASN1_TIME structure is allocated and returned.</p>

<p>In all methods, if <b>in_tm</b> is NULL, the current time, expressed as time_t, is used.</p>

<h1 id="BUGS">BUGS</h1>

<p>Unlike many standard comparison functions, X509_cmp_time() and X509_cmp_current_time() return 0 on error.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>X509_cmp_time() and X509_cmp_current_time() return -1 if <b>asn1_time</b> is earlier than, or equal to, <b>cmp_time</b> (resp. current time), and 1 otherwise. These methods return 0 on error.</p>

<p>X509_time_adj() and X509_time_adj_ex() return a pointer to the updated ASN1_TIME structure, and NULL on error.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


