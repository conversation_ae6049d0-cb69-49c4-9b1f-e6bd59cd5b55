.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ERRSTR 1"
.TH ERRSTR 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-errstr,
errstr \- lookup error codes
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl errstr error_code\fR
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Sometimes an application will not load error message and only
numerical forms will be available. The \fBerrstr\fR utility can be used to
display the meaning of the hex code. The hex code is the hex digits after the
second colon.
.SH OPTIONS
.IX Header "OPTIONS"
None.
.SH EXAMPLES
.IX Header "EXAMPLES"
The error code:
.PP
.Vb 1
\& 27594:error:2006D080:lib(32):func(109):reason(128):bss_file.c:107:
.Ve
.PP
can be displayed with:
.PP
.Vb 1
\& openssl errstr 2006D080
.Ve
.PP
to produce the error message:
.PP
.Vb 1
\& error:2006D080:BIO routines:BIO_new_file:no such file
.Ve
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2004\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
