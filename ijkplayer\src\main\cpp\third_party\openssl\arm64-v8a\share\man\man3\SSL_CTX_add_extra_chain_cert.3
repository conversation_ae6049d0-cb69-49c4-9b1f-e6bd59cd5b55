.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_ADD_EXTRA_CHAIN_CERT 3"
.TH SSL_CTX_ADD_EXTRA_CHAIN_CERT 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_add_extra_chain_cert, SSL_CTX_clear_extra_chain_certs \- add or clear
extra chain certificates
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_add_extra_chain_cert(SSL_CTX *ctx, X509 *x509);
\& long SSL_CTX_clear_extra_chain_certs(SSL_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_add_extra_chain_cert()\fR adds the certificate \fBx509\fR to the extra chain
certificates associated with \fBctx\fR. Several certificates can be added one
after another.
.PP
\&\fBSSL_CTX_clear_extra_chain_certs()\fR clears all extra chain certificates
associated with \fBctx\fR.
.PP
These functions are implemented as macros.
.SH NOTES
.IX Header "NOTES"
When sending a certificate chain, extra chain certificates are sent in order
following the end entity certificate.
.PP
If no chain is specified, the library will try to complete the chain from the
available CA certificates in the trusted CA storage, see
\&\fBSSL_CTX_load_verify_locations\fR\|(3).
.PP
The \fBx509\fR certificate provided to \fBSSL_CTX_add_extra_chain_cert()\fR will be
freed by the library when the \fBSSL_CTX\fR is destroyed. An application
\&\fBshould not\fR free the \fBx509\fR object.
.SH RESTRICTIONS
.IX Header "RESTRICTIONS"
Only one set of extra chain certificates can be specified per SSL_CTX
structure. Different chains for different certificates (for example if both
RSA and DSA certificates are specified by the same server) or different SSL
structures with the same parent SSL_CTX cannot be specified using this
function. For more flexibility functions such as \fBSSL_add1_chain_cert()\fR should
be used instead.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_add_extra_chain_cert()\fR and \fBSSL_CTX_clear_extra_chain_certs()\fR return
1 on success and 0 for failure. Check out the error stack to find out the
reason for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_use_certificate\fR\|(3),
\&\fBSSL_CTX_set_client_cert_cb\fR\|(3),
\&\fBSSL_CTX_load_verify_locations\fR\|(3)
\&\fBSSL_CTX_set0_chain\fR\|(3)
\&\fBSSL_CTX_set1_chain\fR\|(3)
\&\fBSSL_CTX_add0_chain_cert\fR\|(3)
\&\fBSSL_CTX_add1_chain_cert\fR\|(3)
\&\fBSSL_set0_chain\fR\|(3)
\&\fBSSL_set1_chain\fR\|(3)
\&\fBSSL_add0_chain_cert\fR\|(3)
\&\fBSSL_add1_chain_cert\fR\|(3)
\&\fBSSL_CTX_build_cert_chain\fR\|(3)
\&\fBSSL_build_cert_chain\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
