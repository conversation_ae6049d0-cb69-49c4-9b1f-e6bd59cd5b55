.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "S_CLIENT 1"
.TH S_CLIENT 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-s_client,
s_client \- SSL/TLS client program
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBs_client\fR
[\fB\-help\fR]
[\fB\-connect host:port\fR]
[\fB\-bind host:port\fR]
[\fB\-proxy host:port\fR]
[\fB\-unix path\fR]
[\fB\-4\fR]
[\fB\-6\fR]
[\fB\-servername name\fR]
[\fB\-noservername\fR]
[\fB\-verify depth\fR]
[\fB\-verify_return_error\fR]
[\fB\-cert filename\fR]
[\fB\-certform DER|PEM\fR]
[\fB\-key filename\fR]
[\fB\-keyform DER|PEM\fR]
[\fB\-cert_chain filename\fR]
[\fB\-build_chain\fR]
[\fB\-xkey\fR]
[\fB\-xcert\fR]
[\fB\-xchain\fR]
[\fB\-xchain_build\fR]
[\fB\-xcertform PEM|DER\fR]
[\fB\-xkeyform PEM|DER\fR]
[\fB\-pass arg\fR]
[\fB\-CApath directory\fR]
[\fB\-CAfile filename\fR]
[\fB\-chainCApath directory\fR]
[\fB\-chainCAfile filename\fR]
[\fB\-no\-CAfile\fR]
[\fB\-no\-CApath\fR]
[\fB\-requestCAfile filename\fR]
[\fB\-dane_tlsa_domain domain\fR]
[\fB\-dane_tlsa_rrdata rrdata\fR]
[\fB\-dane_ee_no_namechecks\fR]
[\fB\-attime timestamp\fR]
[\fB\-check_ss_sig\fR]
[\fB\-crl_check\fR]
[\fB\-crl_check_all\fR]
[\fB\-explicit_policy\fR]
[\fB\-extended_crl\fR]
[\fB\-ignore_critical\fR]
[\fB\-inhibit_any\fR]
[\fB\-inhibit_map\fR]
[\fB\-no_check_time\fR]
[\fB\-partial_chain\fR]
[\fB\-policy arg\fR]
[\fB\-policy_check\fR]
[\fB\-policy_print\fR]
[\fB\-purpose purpose\fR]
[\fB\-suiteB_128\fR]
[\fB\-suiteB_128_only\fR]
[\fB\-suiteB_192\fR]
[\fB\-trusted_first\fR]
[\fB\-no_alt_chains\fR]
[\fB\-use_deltas\fR]
[\fB\-auth_level num\fR]
[\fB\-nameopt option\fR]
[\fB\-verify_depth num\fR]
[\fB\-verify_email email\fR]
[\fB\-verify_hostname hostname\fR]
[\fB\-verify_ip ip\fR]
[\fB\-verify_name name\fR]
[\fB\-build_chain\fR]
[\fB\-x509_strict\fR]
[\fB\-reconnect\fR]
[\fB\-showcerts\fR]
[\fB\-debug\fR]
[\fB\-msg\fR]
[\fB\-nbio_test\fR]
[\fB\-state\fR]
[\fB\-nbio\fR]
[\fB\-crlf\fR]
[\fB\-ign_eof\fR]
[\fB\-no_ign_eof\fR]
[\fB\-psk_identity identity\fR]
[\fB\-psk key\fR]
[\fB\-psk_session file\fR]
[\fB\-quiet\fR]
[\fB\-ssl3\fR]
[\fB\-tls1\fR]
[\fB\-tls1_1\fR]
[\fB\-tls1_2\fR]
[\fB\-tls1_3\fR]
[\fB\-no_ssl3\fR]
[\fB\-no_tls1\fR]
[\fB\-no_tls1_1\fR]
[\fB\-no_tls1_2\fR]
[\fB\-no_tls1_3\fR]
[\fB\-dtls\fR]
[\fB\-dtls1\fR]
[\fB\-dtls1_2\fR]
[\fB\-sctp\fR]
[\fB\-sctp_label_bug\fR]
[\fB\-fallback_scsv\fR]
[\fB\-async\fR]
[\fB\-max_send_frag\fR]
[\fB\-split_send_frag\fR]
[\fB\-max_pipelines\fR]
[\fB\-read_buf\fR]
[\fB\-bugs\fR]
[\fB\-comp\fR]
[\fB\-no_comp\fR]
[\fB\-allow_no_dhe_kex\fR]
[\fB\-sigalgs sigalglist\fR]
[\fB\-curves curvelist\fR]
[\fB\-cipher cipherlist\fR]
[\fB\-ciphersuites val\fR]
[\fB\-serverpref\fR]
[\fB\-starttls protocol\fR]
[\fB\-xmpphost hostname\fR]
[\fB\-name hostname\fR]
[\fB\-engine id\fR]
[\fB\-tlsextdebug\fR]
[\fB\-no_ticket\fR]
[\fB\-sess_out filename\fR]
[\fB\-sess_in filename\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-serverinfo types\fR]
[\fB\-status\fR]
[\fB\-alpn protocols\fR]
[\fB\-nextprotoneg protocols\fR]
[\fB\-ct\fR]
[\fB\-noct\fR]
[\fB\-ctlogfile\fR]
[\fB\-keylogfile file\fR]
[\fB\-early_data file\fR]
[\fB\-enable_pha\fR]
[\fBtarget\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBs_client\fR command implements a generic SSL/TLS client which connects
to a remote host using SSL/TLS. It is a \fIvery\fR useful diagnostic tool for
SSL servers.
.SH OPTIONS
.IX Header "OPTIONS"
In addition to the options below the \fBs_client\fR utility also supports the
common and client only options documented
in the "Supported Command Line Commands" section of the \fBSSL_CONF_cmd\fR\|(3)
manual page.
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-connect host:port\fR" 4
.IX Item "-connect host:port"
This specifies the host and optional port to connect to. It is possible to
select the host and port using the optional target positional argument instead.
If neither this nor the target positional argument are specified then an attempt
is made to connect to the local host on port 4433.
.IP "\fB\-bind host:port\fR]" 4
.IX Item "-bind host:port]"
This specifies the host address and or port to bind as the source for the
connection.  For Unix-domain sockets the port is ignored and the host is
used as the source socket address.
.IP "\fB\-proxy host:port\fR" 4
.IX Item "-proxy host:port"
When used with the \fB\-connect\fR flag, the program uses the host and port
specified with this flag and issues an HTTP CONNECT command to connect
to the desired server.
.IP "\fB\-unix path\fR" 4
.IX Item "-unix path"
Connect over the specified Unix-domain socket.
.IP \fB\-4\fR 4
.IX Item "-4"
Use IPv4 only.
.IP \fB\-6\fR 4
.IX Item "-6"
Use IPv6 only.
.IP "\fB\-servername name\fR" 4
.IX Item "-servername name"
Set the TLS SNI (Server Name Indication) extension in the ClientHello message to
the given value. 
If \fB\-servername\fR is not provided, the TLS SNI extension will be populated with 
the name given to \fB\-connect\fR if it follows a DNS name format. If \fB\-connect\fR is 
not provided either, the SNI is set to "localhost".
This is the default since OpenSSL 1.1.1.
.Sp
Even though SNI should normally be a DNS name and not an IP address, if 
\&\fB\-servername\fR is provided then that name will be sent, regardless of whether 
it is a DNS name or not.
.Sp
This option cannot be used in conjunction with \fB\-noservername\fR.
.IP \fB\-noservername\fR 4
.IX Item "-noservername"
Suppresses sending of the SNI (Server Name Indication) extension in the
ClientHello message. Cannot be used in conjunction with the \fB\-servername\fR or
<\-dane_tlsa_domain> options.
.IP "\fB\-cert certname\fR" 4
.IX Item "-cert certname"
The certificate to use, if one is requested by the server. The default is
not to use a certificate.
.IP "\fB\-certform format\fR" 4
.IX Item "-certform format"
The certificate format to use: DER or PEM. PEM is the default.
.IP "\fB\-key keyfile\fR" 4
.IX Item "-key keyfile"
The private key to use. If not specified then the certificate file will
be used.
.IP "\fB\-keyform format\fR" 4
.IX Item "-keyform format"
The private format to use: DER or PEM. PEM is the default.
.IP \fB\-cert_chain\fR 4
.IX Item "-cert_chain"
A file containing trusted certificates to use when attempting to build the
client/server certificate chain related to the certificate specified via the
\&\fB\-cert\fR option.
.IP \fB\-build_chain\fR 4
.IX Item "-build_chain"
Specify whether the application should build the certificate chain to be
provided to the server.
.IP "\fB\-xkey infile\fR, \fB\-xcert infile\fR, \fB\-xchain\fR" 4
.IX Item "-xkey infile, -xcert infile, -xchain"
Specify an extra certificate, private key and certificate chain. These behave
in the same manner as the \fB\-cert\fR, \fB\-key\fR and \fB\-cert_chain\fR options.  When
specified, the callback returning the first valid chain will be in use by the
client.
.IP \fB\-xchain_build\fR 4
.IX Item "-xchain_build"
Specify whether the application should build the certificate chain to be
provided to the server for the extra certificates provided via \fB\-xkey infile\fR,
\&\fB\-xcert infile\fR, \fB\-xchain\fR options.
.IP "\fB\-xcertform PEM|DER\fR, \fB\-xkeyform PEM|DER\fR" 4
.IX Item "-xcertform PEM|DER, -xkeyform PEM|DER"
Extra certificate and private key format respectively.
.IP "\fB\-pass arg\fR" 4
.IX Item "-pass arg"
the private key password source. For more information about the format of \fBarg\fR
see "Pass Phrase Options" in \fBopenssl\fR\|(1).
.IP "\fB\-verify depth\fR" 4
.IX Item "-verify depth"
The verify depth to use. This specifies the maximum length of the
server certificate chain and turns on server certificate verification.
Currently the verify operation continues after errors so all the problems
with a certificate chain can be seen. As a side effect the connection
will never fail due to a server certificate verify failure.
.IP \fB\-verify_return_error\fR 4
.IX Item "-verify_return_error"
Return verification errors instead of continuing. This will typically
abort the handshake with a fatal error.
.IP "\fB\-nameopt option\fR" 4
.IX Item "-nameopt option"
Option which determines how the subject or issuer names are displayed. The
\&\fBoption\fR argument can be a single option or multiple options separated by
commas.  Alternatively the \fB\-nameopt\fR switch may be used more than once to
set multiple options. See the \fBx509\fR\|(1) manual page for details.
.IP "\fB\-CApath directory\fR" 4
.IX Item "-CApath directory"
The directory to use for server certificate verification. This directory
must be in "hash format", see \fBverify\fR\|(1) for more information. These are
also used when building the client certificate chain.
.IP "\fB\-CAfile file\fR" 4
.IX Item "-CAfile file"
A file containing trusted certificates to use during server authentication
and to use when attempting to build the client certificate chain.
.IP "\fB\-chainCApath directory\fR" 4
.IX Item "-chainCApath directory"
The directory to use for building the chain provided to the server. This
directory must be in "hash format", see \fBverify\fR\|(1) for more information.
.IP "\fB\-chainCAfile file\fR" 4
.IX Item "-chainCAfile file"
A file containing trusted certificates to use when attempting to build the
client certificate chain.
.IP \fB\-no\-CAfile\fR 4
.IX Item "-no-CAfile"
Do not load the trusted CA certificates from the default file location
.IP \fB\-no\-CApath\fR 4
.IX Item "-no-CApath"
Do not load the trusted CA certificates from the default directory location
.IP "\fB\-requestCAfile file\fR" 4
.IX Item "-requestCAfile file"
A file containing a list of certificates whose subject names will be sent
to the server in the \fBcertificate_authorities\fR extension. Only supported
for TLS 1.3
.IP "\fB\-dane_tlsa_domain domain\fR" 4
.IX Item "-dane_tlsa_domain domain"
Enable RFC6698/RFC7671 DANE TLSA authentication and specify the
TLSA base domain which becomes the default SNI hint and the primary
reference identifier for hostname checks.  This must be used in
combination with at least one instance of the \fB\-dane_tlsa_rrdata\fR
option below.
.Sp
When DANE authentication succeeds, the diagnostic output will include
the lowest (closest to 0) depth at which a TLSA record authenticated
a chain certificate.  When that TLSA record is a "2 1 0" trust
anchor public key that signed (rather than matched) the top-most
certificate of the chain, the result is reported as "TA public key
verified".  Otherwise, either the TLSA record "matched TA certificate"
at a positive depth or else "matched EE certificate" at depth 0.
.IP "\fB\-dane_tlsa_rrdata rrdata\fR" 4
.IX Item "-dane_tlsa_rrdata rrdata"
Use one or more times to specify the RRDATA fields of the DANE TLSA
RRset associated with the target service.  The \fBrrdata\fR value is
specified in "presentation form", that is four whitespace separated
fields that specify the usage, selector, matching type and associated
data, with the last of these encoded in hexadecimal.  Optional
whitespace is ignored in the associated data field.  For example:
.Sp
.Vb 12
\&  $ openssl s_client \-brief \-starttls smtp \e
\&    \-connect smtp.example.com:25 \e
\&    \-dane_tlsa_domain smtp.example.com \e
\&    \-dane_tlsa_rrdata "2 1 1
\&      B111DD8A1C2091A89BD4FD60C57F0716CCE50FEEFF8137CDBEE0326E 02CF362B" \e
\&    \-dane_tlsa_rrdata "2 1 1
\&      60B87575447DCBA2A36B7D11AC09FB24A9DB406FEE12D2CC90180517 616E8A18"
\&  ...
\&  Verification: OK
\&  Verified peername: smtp.example.com
\&  DANE TLSA 2 1 1 ...ee12d2cc90180517616e8a18 matched TA certificate at depth 1
\&  ...
.Ve
.IP \fB\-dane_ee_no_namechecks\fR 4
.IX Item "-dane_ee_no_namechecks"
This disables server name checks when authenticating via \fBDANE\-EE\fR\|(3) TLSA
records.
For some applications, primarily web browsers, it is not safe to disable name
checks due to "unknown key share" attacks, in which a malicious server can
convince a client that a connection to a victim server is instead a secure
connection to the malicious server.
The malicious server may then be able to violate cross-origin scripting
restrictions.
Thus, despite the text of RFC7671, name checks are by default enabled for
\&\fBDANE\-EE\fR\|(3) TLSA records, and can be disabled in applications where it is safe
to do so.
In particular, SMTP and XMPP clients should set this option as SRV and MX
records already make it possible for a remote domain to redirect client
connections to any server of its choice, and in any case SMTP and XMPP clients
do not execute scripts downloaded from remote servers.
.IP "\fB\-attime\fR, \fB\-check_ss_sig\fR, \fB\-crl_check\fR, \fB\-crl_check_all\fR, \fB\-explicit_policy\fR, \fB\-extended_crl\fR, \fB\-ignore_critical\fR, \fB\-inhibit_any\fR, \fB\-inhibit_map\fR, \fB\-no_alt_chains\fR, \fB\-no_check_time\fR, \fB\-partial_chain\fR, \fB\-policy\fR, \fB\-policy_check\fR, \fB\-policy_print\fR, \fB\-purpose\fR, \fB\-suiteB_128\fR, \fB\-suiteB_128_only\fR, \fB\-suiteB_192\fR, \fB\-trusted_first\fR, \fB\-use_deltas\fR, \fB\-auth_level\fR, \fB\-verify_depth\fR, \fB\-verify_email\fR, \fB\-verify_hostname\fR, \fB\-verify_ip\fR, \fB\-verify_name\fR, \fB\-x509_strict\fR" 4
.IX Item "-attime, -check_ss_sig, -crl_check, -crl_check_all, -explicit_policy, -extended_crl, -ignore_critical, -inhibit_any, -inhibit_map, -no_alt_chains, -no_check_time, -partial_chain, -policy, -policy_check, -policy_print, -purpose, -suiteB_128, -suiteB_128_only, -suiteB_192, -trusted_first, -use_deltas, -auth_level, -verify_depth, -verify_email, -verify_hostname, -verify_ip, -verify_name, -x509_strict"
Set various certificate chain validation options. See the
\&\fBverify\fR\|(1) manual page for details.
.IP \fB\-reconnect\fR 4
.IX Item "-reconnect"
Reconnects to the same server 5 times using the same session ID, this can
be used as a test that session caching is working.
.IP \fB\-showcerts\fR 4
.IX Item "-showcerts"
Displays the server certificate list as sent by the server: it only consists of
certificates the server has sent (in the order the server has sent them). It is
\&\fBnot\fR a verified chain.
.IP \fB\-prexit\fR 4
.IX Item "-prexit"
Print session information when the program exits. This will always attempt
to print out information even if the connection fails. Normally information
will only be printed out once if the connection succeeds. This option is useful
because the cipher in use may be renegotiated or the connection may fail
because a client certificate is required or is requested only after an
attempt is made to access a certain URL. Note: the output produced by this
option is not always accurate because a connection might never have been
established.
.IP \fB\-state\fR 4
.IX Item "-state"
Prints out the SSL session states.
.IP \fB\-debug\fR 4
.IX Item "-debug"
Print extensive debugging information including a hex dump of all traffic.
.IP \fB\-msg\fR 4
.IX Item "-msg"
Show all protocol messages with hex dump.
.IP \fB\-trace\fR 4
.IX Item "-trace"
Show verbose trace output of protocol messages. OpenSSL needs to be compiled
with \fBenable-ssl-trace\fR for this option to work.
.IP \fB\-msgfile\fR 4
.IX Item "-msgfile"
File to send output of \fB\-msg\fR or \fB\-trace\fR to, default standard output.
.IP \fB\-nbio_test\fR 4
.IX Item "-nbio_test"
Tests nonblocking I/O
.IP \fB\-nbio\fR 4
.IX Item "-nbio"
Turns on nonblocking I/O
.IP \fB\-crlf\fR 4
.IX Item "-crlf"
This option translated a line feed from the terminal into CR+LF as required
by some servers.
.IP \fB\-ign_eof\fR 4
.IX Item "-ign_eof"
Inhibit shutting down the connection when end of file is reached in the
input.
.IP \fB\-quiet\fR 4
.IX Item "-quiet"
Inhibit printing of session and certificate information.  This implicitly
turns on \fB\-ign_eof\fR as well.
.IP \fB\-no_ign_eof\fR 4
.IX Item "-no_ign_eof"
Shut down the connection when end of file is reached in the input.
Can be used to override the implicit \fB\-ign_eof\fR after \fB\-quiet\fR.
.IP "\fB\-psk_identity identity\fR" 4
.IX Item "-psk_identity identity"
Use the PSK identity \fBidentity\fR when using a PSK cipher suite.
The default value is "Client_identity" (without the quotes).
.IP "\fB\-psk key\fR" 4
.IX Item "-psk key"
Use the PSK key \fBkey\fR when using a PSK cipher suite. The key is
given as a hexadecimal number without leading 0x, for example \-psk
1a2b3c4d.
This option must be provided in order to use a PSK cipher.
.IP "\fB\-psk_session file\fR" 4
.IX Item "-psk_session file"
Use the pem encoded SSL_SESSION data stored in \fBfile\fR as the basis of a PSK.
Note that this will only work if TLSv1.3 is negotiated.
.IP "\fB\-ssl3\fR, \fB\-tls1\fR, \fB\-tls1_1\fR, \fB\-tls1_2\fR, \fB\-tls1_3\fR, \fB\-no_ssl3\fR, \fB\-no_tls1\fR, \fB\-no_tls1_1\fR, \fB\-no_tls1_2\fR, \fB\-no_tls1_3\fR" 4
.IX Item "-ssl3, -tls1, -tls1_1, -tls1_2, -tls1_3, -no_ssl3, -no_tls1, -no_tls1_1, -no_tls1_2, -no_tls1_3"
These options require or disable the use of the specified SSL or TLS protocols.
By default \fBs_client\fR will negotiate the highest mutually supported protocol
version.
When a specific TLS version is required, only that version will be offered to
and accepted from the server.
Note that not all protocols and flags may be available, depending on how
OpenSSL was built.
.IP "\fB\-dtls\fR, \fB\-dtls1\fR, \fB\-dtls1_2\fR" 4
.IX Item "-dtls, -dtls1, -dtls1_2"
These options make \fBs_client\fR use DTLS protocols instead of TLS.
With \fB\-dtls\fR, \fBs_client\fR will negotiate any supported DTLS protocol version,
whilst \fB\-dtls1\fR and \fB\-dtls1_2\fR will only support DTLS1.0 and DTLS1.2
respectively.
.IP \fB\-sctp\fR 4
.IX Item "-sctp"
Use SCTP for the transport protocol instead of UDP in DTLS. Must be used in
conjunction with \fB\-dtls\fR, \fB\-dtls1\fR or \fB\-dtls1_2\fR. This option is only
available where OpenSSL has support for SCTP enabled.
.IP \fB\-sctp_label_bug\fR 4
.IX Item "-sctp_label_bug"
Use the incorrect behaviour of older OpenSSL implementations when computing
endpoint-pair shared secrets for DTLS/SCTP. This allows communication with
older broken implementations but breaks interoperability with correct
implementations. Must be used in conjunction with \fB\-sctp\fR. This option is only
available where OpenSSL has support for SCTP enabled.
.IP \fB\-fallback_scsv\fR 4
.IX Item "-fallback_scsv"
Send TLS_FALLBACK_SCSV in the ClientHello.
.IP \fB\-async\fR 4
.IX Item "-async"
Switch on asynchronous mode. Cryptographic operations will be performed
asynchronously. This will only have an effect if an asynchronous capable engine
is also used via the \fB\-engine\fR option. For test purposes the dummy async engine
(dasync) can be used (if available).
.IP "\fB\-max_send_frag int\fR" 4
.IX Item "-max_send_frag int"
The maximum size of data fragment to send.
See \fBSSL_CTX_set_max_send_fragment\fR\|(3) for further information.
.IP "\fB\-split_send_frag int\fR" 4
.IX Item "-split_send_frag int"
The size used to split data for encrypt pipelines. If more data is written in
one go than this value then it will be split into multiple pipelines, up to the
maximum number of pipelines defined by max_pipelines. This only has an effect if
a suitable cipher suite has been negotiated, an engine that supports pipelining
has been loaded, and max_pipelines is greater than 1. See
\&\fBSSL_CTX_set_split_send_fragment\fR\|(3) for further information.
.IP "\fB\-max_pipelines int\fR" 4
.IX Item "-max_pipelines int"
The maximum number of encrypt/decrypt pipelines to be used. This will only have
an effect if an engine has been loaded that supports pipelining (e.g. the dasync
engine) and a suitable cipher suite has been negotiated. The default value is 1.
See \fBSSL_CTX_set_max_pipelines\fR\|(3) for further information.
.IP "\fB\-read_buf int\fR" 4
.IX Item "-read_buf int"
The default read buffer size to be used for connections. This will only have an
effect if the buffer size is larger than the size that would otherwise be used
and pipelining is in use (see \fBSSL_CTX_set_default_read_buffer_len\fR\|(3) for
further information).
.IP \fB\-bugs\fR 4
.IX Item "-bugs"
There are several known bugs in SSL and TLS implementations. Adding this
option enables various workarounds.
.IP \fB\-comp\fR 4
.IX Item "-comp"
Enables support for SSL/TLS compression.
This option was introduced in OpenSSL 1.1.0.
TLS compression is not recommended and is off by default as of
OpenSSL 1.1.0.
.IP \fB\-no_comp\fR 4
.IX Item "-no_comp"
Disables support for SSL/TLS compression.
TLS compression is not recommended and is off by default as of
OpenSSL 1.1.0.
.IP \fB\-brief\fR 4
.IX Item "-brief"
Only provide a brief summary of connection parameters instead of the
normal verbose output.
.IP "\fB\-sigalgs sigalglist\fR" 4
.IX Item "-sigalgs sigalglist"
Specifies the list of signature algorithms that are sent by the client.
The server selects one entry in the list based on its preferences.
For example strings, see \fBSSL_CTX_set1_sigalgs\fR\|(3)
.IP "\fB\-curves curvelist\fR" 4
.IX Item "-curves curvelist"
Specifies the list of supported curves to be sent by the client. The curve is
ultimately selected by the server. For a list of all curves, use:
.Sp
.Vb 1
\&    $ openssl ecparam \-list_curves
.Ve
.IP "\fB\-cipher cipherlist\fR" 4
.IX Item "-cipher cipherlist"
This allows the TLSv1.2 and below cipher list sent by the client to be modified.
This list will be combined with any TLSv1.3 ciphersuites that have been
configured. Although the server determines which ciphersuite is used it should
take the first supported cipher in the list sent by the client. See the
\&\fBciphers\fR command for more information.
.IP "\fB\-ciphersuites val\fR" 4
.IX Item "-ciphersuites val"
This allows the TLSv1.3 ciphersuites sent by the client to be modified. This
list will be combined with any TLSv1.2 and below ciphersuites that have been
configured. Although the server determines which cipher suite is used it should
take the first supported cipher in the list sent by the client. See the
\&\fBciphers\fR command for more information. The format for this list is a simple
colon (":") separated list of TLSv1.3 ciphersuite names.
.IP "\fB\-starttls protocol\fR" 4
.IX Item "-starttls protocol"
Send the protocol-specific message(s) to switch to TLS for communication.
\&\fBprotocol\fR is a keyword for the intended protocol.  Currently, the only
supported keywords are "smtp", "pop3", "imap", "ftp", "xmpp", "xmpp-server",
"irc", "postgres", "mysql", "lmtp", "nntp", "sieve" and "ldap".
.IP "\fB\-xmpphost hostname\fR" 4
.IX Item "-xmpphost hostname"
This option, when used with "\-starttls xmpp" or "\-starttls xmpp-server",
specifies the host for the "to" attribute of the stream element.
If this option is not specified, then the host specified with "\-connect"
will be used.
.Sp
This option is an alias of the \fB\-name\fR option for "xmpp" and "xmpp-server".
.IP "\fB\-name hostname\fR" 4
.IX Item "-name hostname"
This option is used to specify hostname information for various protocols
used with \fB\-starttls\fR option. Currently only "xmpp", "xmpp-server",
"smtp" and "lmtp" can utilize this \fB\-name\fR option.
.Sp
If this option is used with "\-starttls xmpp" or "\-starttls xmpp-server",
if specifies the host for the "to" attribute of the stream element. If this
option is not specified, then the host specified with "\-connect" will be used.
.Sp
If this option is used with "\-starttls lmtp" or "\-starttls smtp", it specifies
the name to use in the "LMTP LHLO" or "SMTP EHLO" message, respectively. If
this option is not specified, then "mail.example.com" will be used.
.IP \fB\-tlsextdebug\fR 4
.IX Item "-tlsextdebug"
Print out a hex dump of any TLS extensions received from the server.
.IP \fB\-no_ticket\fR 4
.IX Item "-no_ticket"
Disable RFC4507bis session ticket support.
.IP "\fB\-sess_out filename\fR" 4
.IX Item "-sess_out filename"
Output SSL session to \fBfilename\fR.
.IP "\fB\-sess_in sess.pem\fR" 4
.IX Item "-sess_in sess.pem"
Load SSL session from \fBfilename\fR. The client will attempt to resume a
connection from this session.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBs_client\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP "\fB\-serverinfo types\fR" 4
.IX Item "-serverinfo types"
A list of comma-separated TLS Extension Types (numbers between 0 and
65535).  Each type will be sent as an empty ClientHello TLS Extension.
The server's response (if any) will be encoded and displayed as a PEM
file.
.IP \fB\-status\fR 4
.IX Item "-status"
Sends a certificate status request to the server (OCSP stapling). The server
response (if any) is printed out.
.IP "\fB\-alpn protocols\fR, \fB\-nextprotoneg protocols\fR" 4
.IX Item "-alpn protocols, -nextprotoneg protocols"
These flags enable the Enable the Application-Layer Protocol Negotiation
or Next Protocol Negotiation (NPN) extension, respectively. ALPN is the
IETF standard and replaces NPN.
The \fBprotocols\fR list is a comma-separated list of protocol names that
the client should advertise support for. The list should contain the most
desirable protocols first.  Protocol names are printable ASCII strings,
for example "http/1.1" or "spdy/3".
An empty list of protocols is treated specially and will cause the
client to advertise support for the TLS extension but disconnect just
after receiving ServerHello with a list of server supported protocols.
The flag \fB\-nextprotoneg\fR cannot be specified if \fB\-tls1_3\fR is used.
.IP "\fB\-ct\fR, \fB\-noct\fR" 4
.IX Item "-ct, -noct"
Use one of these two options to control whether Certificate Transparency (CT)
is enabled (\fB\-ct\fR) or disabled (\fB\-noct\fR).
If CT is enabled, signed certificate timestamps (SCTs) will be requested from
the server and reported at handshake completion.
.Sp
Enabling CT also enables OCSP stapling, as this is one possible delivery method
for SCTs.
.IP \fB\-ctlogfile\fR 4
.IX Item "-ctlogfile"
A file containing a list of known Certificate Transparency logs. See
\&\fBSSL_CTX_set_ctlog_list_file\fR\|(3) for the expected file format.
.IP "\fB\-keylogfile file\fR" 4
.IX Item "-keylogfile file"
Appends TLS secrets to the specified keylog file such that external programs
(like Wireshark) can decrypt TLS connections.
.IP "\fB\-early_data file\fR" 4
.IX Item "-early_data file"
Reads the contents of the specified file and attempts to send it as early data
to the server. This will only work with resumed sessions that support early
data and when the server accepts the early data.
.IP \fB\-enable_pha\fR 4
.IX Item "-enable_pha"
For TLSv1.3 only, send the Post-Handshake Authentication extension. This will
happen whether or not a certificate has been provided via \fB\-cert\fR.
.IP \fB[target]\fR 4
.IX Item "[target]"
Rather than providing \fB\-connect\fR, the target hostname and optional port may
be provided as a single positional argument after all options. If neither this
nor \fB\-connect\fR are provided, falls back to attempting to connect to localhost
on port 4433.
.SH "CONNECTED COMMANDS"
.IX Header "CONNECTED COMMANDS"
If a connection is established with an SSL server then any data received
from the server is displayed and any key presses will be sent to the
server. If end of file is reached then the connection will be closed down. When
used interactively (which means neither \fB\-quiet\fR nor \fB\-ign_eof\fR have been
given), then certain commands are also recognized which perform special
operations. These commands are a letter which must appear at the start of a
line. They are listed below.
.IP \fBQ\fR 4
.IX Item "Q"
End the current SSL connection and exit.
.IP \fBR\fR 4
.IX Item "R"
Renegotiate the SSL session (TLSv1.2 and below only).
.IP \fBB\fR 4
.IX Item "B"
Send a heartbeat message to the server (DTLS only)
.IP \fBk\fR 4
.IX Item "k"
Send a key update message to the server (TLSv1.3 only)
.IP \fBK\fR 4
.IX Item "K"
Send a key update message to the server and request one back (TLSv1.3 only)
.SH NOTES
.IX Header "NOTES"
\&\fBs_client\fR can be used to debug SSL servers. To connect to an SSL HTTP
server the command:
.PP
.Vb 1
\& openssl s_client \-connect servername:443
.Ve
.PP
would typically be used (https uses port 443). If the connection succeeds
then an HTTP command can be given such as "GET /" to retrieve a web page.
.PP
If the handshake fails then there are several possible causes, if it is
nothing obvious like no client certificate then the \fB\-bugs\fR,
\&\fB\-ssl3\fR, \fB\-tls1\fR, \fB\-no_ssl3\fR, \fB\-no_tls1\fR options can be tried
in case it is a buggy server. In particular you should play with these
options \fBbefore\fR submitting a bug report to an OpenSSL mailing list.
.PP
A frequent problem when attempting to get client certificates working
is that a web client complains it has no certificates or gives an empty
list to choose from. This is normally because the server is not sending
the clients certificate authority in its "acceptable CA list" when it
requests a certificate. By using \fBs_client\fR the CA list can be viewed
and checked. However, some servers only request client authentication
after a specific URL is requested. To obtain the list in this case it
is necessary to use the \fB\-prexit\fR option and send an HTTP request
for an appropriate page.
.PP
If a certificate is specified on the command line using the \fB\-cert\fR
option it will not be used unless the server specifically requests
a client certificate. Therefore, merely including a client certificate
on the command line is no guarantee that the certificate works.
.PP
If there are problems verifying a server certificate then the
\&\fB\-showcerts\fR option can be used to show all the certificates sent by the
server.
.PP
The \fBs_client\fR utility is a test tool and is designed to continue the
handshake after any certificate verification errors. As a result it will
accept any certificate chain (trusted or not) sent by the peer. Non-test
applications should \fBnot\fR do this as it makes them vulnerable to a MITM
attack. This behaviour can be changed by with the \fB\-verify_return_error\fR
option: any verify errors are then returned aborting the handshake.
.PP
The \fB\-bind\fR option may be useful if the server or a firewall requires
connections to come from some particular address and or port.
.SH BUGS
.IX Header "BUGS"
Because this program has a lot of options and also because some of the
techniques used are rather old, the C source of \fBs_client\fR is rather hard to
read and not a model of how things should be done.
A typical SSL client program would be much simpler.
.PP
The \fB\-prexit\fR option is a bit of a hack. We should really report
information whenever a session is renegotiated.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CONF_cmd\fR\|(3), \fBsess_id\fR\|(1), \fBs_server\fR\|(1), \fBciphers\fR\|(1),
\&\fBSSL_CTX_set_max_send_fragment\fR\|(3), \fBSSL_CTX_set_split_send_fragment\fR\|(3),
\&\fBSSL_CTX_set_max_pipelines\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fB\-no_alt_chains\fR option was added in OpenSSL 1.1.0.
The \fB\-name\fR option was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
