.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_ADD_SESSION 3"
.TH SSL_CTX_ADD_SESSION 3 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_add_session, SSL_CTX_remove_session \- manipulate session cache
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_add_session(SSL_CTX *ctx, SSL_SESSION *c);
\&
\& int SSL_CTX_remove_session(SSL_CTX *ctx, SSL_SESSION *c);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_add_session()\fR adds the session \fBc\fR to the context \fBctx\fR. The
reference count for session \fBc\fR is incremented by 1. If a session with
the same session id already exists, the old session is removed by calling
\&\fBSSL_SESSION_free\fR\|(3).
.PP
\&\fBSSL_CTX_remove_session()\fR removes the session \fBc\fR from the context \fBctx\fR and
marks it as non-resumable. \fBSSL_SESSION_free\fR\|(3) is called once for \fBc\fR.
.SH NOTES
.IX Header "NOTES"
When adding a new session to the internal session cache, it is examined
whether a session with the same session id already exists. In this case
it is assumed that both sessions are identical. If the same session is
stored in a different SSL_SESSION object, The old session is
removed and replaced by the new session. If the session is actually
identical (the SSL_SESSION object is identical), \fBSSL_CTX_add_session()\fR
is a no-op, and the return value is 0.
.PP
If a server SSL_CTX is configured with the SSL_SESS_CACHE_NO_INTERNAL_STORE
flag then the internal cache will not be populated automatically by new
sessions negotiated by the SSL/TLS implementation, even though the internal
cache will be searched automatically for session-resume requests (the
latter can be suppressed by SSL_SESS_CACHE_NO_INTERNAL_LOOKUP). So the
application can use \fBSSL_CTX_add_session()\fR directly to have full control
over the sessions that can be resumed if desired.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following values are returned by all functions:
.IP 0 4
The operation failed. In case of the add operation, it was tried to add
the same (identical) session twice. In case of the remove operation, the
session was not found in the cache.
.IP 1 4
.IX Item "1"
The operation succeeded.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_set_session_cache_mode\fR\|(3),
\&\fBSSL_SESSION_free\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
