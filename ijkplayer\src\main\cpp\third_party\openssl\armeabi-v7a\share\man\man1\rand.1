.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND 1"
.TH RAND 1 2023-09-11 1.1.1w OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-rand,
rand \- generate pseudo\-random bytes
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl rand\fR
[\fB\-help\fR]
[\fB\-out\fR \fIfile\fR]
[\fB\-rand file...\fR]
[\fB\-writerand file\fR]
[\fB\-base64\fR]
[\fB\-hex\fR]
\&\fInum\fR
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command generates \fInum\fR random bytes using a cryptographically
secure pseudo random number generator (CSPRNG).
.PP
The random bytes are generated using the \fBRAND_bytes\fR\|(3) function,
which provides a security level of 256 bits, provided it managed to
seed itself successfully from a trusted operating system entropy source.
Otherwise, the command will fail with a nonzero error code.
For more details, see \fBRAND_bytes\fR\|(3), \fBRAND\fR\|(7), and \fBRAND_DRBG\fR\|(7).
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-out file\fR" 4
.IX Item "-out file"
Write to \fIfile\fR instead of standard output.
.IP "\fB\-rand file...\fR" 4
.IX Item "-rand file..."
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \fB;\fR for MS-Windows, \fB,\fR for OpenVMS, and \fB:\fR for
all others.
Explicitly specifying a seed file is in general not necessary, see the
"NOTES" section for more information.
.IP "[\fB\-writerand file\fR]" 4
.IX Item "[-writerand file]"
Writes random data to the specified \fIfile\fR upon exit.
This can be used with a subsequent \fB\-rand\fR flag.
.IP \fB\-base64\fR 4
.IX Item "-base64"
Perform base64 encoding on the output.
.IP \fB\-hex\fR 4
.IX Item "-hex"
Show the output as a hex string.
.SH NOTES
.IX Header "NOTES"
Prior to OpenSSL 1.1.1, it was common for applications to store information
about the state of the random-number generator in a file that was loaded
at startup and rewritten upon exit. On modern operating systems, this is
generally no longer necessary as OpenSSL will seed itself from a trusted
entropy source provided by the operating system. The \fB\-rand\fR  and
\&\fB\-writerand\fR  flags are still supported for special platforms or
circumstances that might require them.
.PP
It is generally an error to use the same seed file more than once and
every use of \fB\-rand\fR should be paired with \fB\-writerand\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND_bytes\fR\|(3),
\&\fBRAND\fR\|(7),
\&\fBRAND_DRBG\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
